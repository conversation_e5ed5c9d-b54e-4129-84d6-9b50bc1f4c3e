<template>
	<fs-page class="article-content-analysis-page">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<!-- 自定义转载来源渲染 -->
			<template #cell_source="scope">
				<el-tag v-if="!scope.row.source" type="success" size="small">原创</el-tag>
				<span v-else>{{ scope.row.source }}</span>
			</template>
			<template #cell_article_title="scope">
				<el-tooltip :show-after="1000" v-if="scope.row.article_title && scope.row.article_title.length > 20" :content="scope.row.article_title" placement="top">
					{{ scope.row.article_title.slice(0, 20) }}...
				</el-tooltip>
				<span v-else>{{ scope.row.article_title }}</span>
			</template>
			<template #cell_create_meta_info_reference="scope">
				<el-tooltip popper-class="tool-tip" effect="light" :show-after="1000" v-if="scope.row.create_meta_info_reference && scope.row.create_meta_info_reference.length > 50" :content="scope.row.create_meta_info_reference" placement="top">
					{{ scope.row.create_meta_info_reference.slice(0, 50) }}...
				</el-tooltip>
				<span v-else>{{ scope.row.create_meta_info_reference }}</span>
			</template>
			<!-- 自定义创作元信息渲染 -->
			<template #cell_create_meta_info="scope">
				<div v-if="scope.row.create_meta_info" class="meta-info-container">
					<template v-if="typeof scope.row.create_meta_info === 'object'">
						<el-tag
							v-for="(value, key) in scope.row.create_meta_info"
							:key="key"
							type="info"
							size="small"
							style="margin-right: 5px; margin-bottom: 5px"
						>
							{{ key }}: {{ value }}
						</el-tag>
					</template>
					<template v-else>
						{{ scope.row.create_meta_info }}
					</template>
				</div>
				<span v-else>-</span>
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
	name: 'ArticleContentAnalysis',
	setup() {
		// crud组件的ref
		const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

		// 在组件挂载后加载数据
		onMounted(() => {
			crudExpose.doRefresh();
		});

		return {
			crudBinding,
			crudRef,
			crudExpose,
		};
	},
});
</script>

<style scoped>
:deep(.fs-page) {
	padding: 0;
}

.meta-info-container {
	display: flex;
	flex-wrap: wrap;
}
</style> 
<style>
.tool-tip {
  max-width: 400px;
}
</style>

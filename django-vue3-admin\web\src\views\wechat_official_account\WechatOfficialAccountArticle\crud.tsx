import { asyncCompute, CrudOptions, CrudExpose, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud'
import * as api from './api'
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { dictionary } from '/@/utils/dictionary'
import { useRoute } from 'vue-router'
import * as tenantApi from '/@/views/tenant/api'
import { tranNumber } from '/@/utils/tranNum';
import router from '/@/router/index';
import { auth } from "/@/utils/authFunction"
export default function ({ crudExpose, context }: { crudExpose: CrudExpose, context?: any }): CreateCrudOptionsRet {
    const route = useRoute()
    const id = route.query.id
    const unique_id = route.query.unique_id
    const tenantId = route.query.tenant_id as string
    const urlPlatformType = route.query.platform_type
    const tabType = route.query.tab_type

    // 从外部传入或使用默认值
    const currentPlatformType = ref(tabType || urlPlatformType || 'wechat');
    // 添加导出按钮的loading状态
    const exportLoading = ref(false);

    const pageRequest = async (query: any) => {
        // 添加platform_type到查询参数
        query.platform_type = currentPlatformType.value;
        query.wechat_official_account = currentPlatformType.value === urlPlatformType ? id : undefined;
        console.log('作品查询参数:', query);
        return await api.getWechatOfficialAccountArticleList(query)
    }

    // 数字转换函数
    const tranNum = (num: number) => {
        if (num !== undefined && num !== null) {
            return tranNumber(num);
        }
        return '';
    };
    return {
        currentPlatformType, // 将currentPlatformType导出，以便在index.vue中使用
        crudOptions: {
            request: {
                pageRequest
            },
            search: {
                initialForm: {
                    wechat_official_account: currentPlatformType.value !== 'wechat' ? id : undefined,
                    wechat_unique_id: currentPlatformType.value === 'wechat' ? unique_id : undefined,
                    tenant_id: tenantId ? Number(tenantId) : undefined
                },
                onReset: () => {
                    // 重置时清空路由参数中的tenant_id
                    const currentQuery = { ...route.query };
                    delete currentQuery.tenant_id;
                    router.replace({ query: currentQuery });
                }
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    },
                    export: {
                        text: '导出作品数据',
                        type: "primary",
                        loading: exportLoading,
                        show: computed(() => {
                            if (currentPlatformType.value === 'wechat') {
                                return auth('WorkData:Export')
                            } else if (currentPlatformType.value === 'weibo') {
                                return auth('WbPosts:Export')
                            } else if (currentPlatformType.value === 'douyin') {
                                return auth('Dyworks:Export')
                            } else {
                                return false
                            }
                        }),
                        click: () => {
                            // 触发导出弹窗
                            context?.showExportDialog?.(currentPlatformType.value);
                        }
                    }
                }
            },
            toolbar: {
                show: false,
            },
            rowHandle: {
                show: false
            },
            columns: {
                id: {
                    title: '作品ID',
                    type: 'text',
                    column: {
                        width: 120,
                        show: computed(() => currentPlatformType.value === 'wechat' || currentPlatformType.value === 'douyin')
                    }
                },
                weibo_id: {
                    title: '微博作品ID',
                    type: 'text',
                    column: {
                        width: 120,
                        show: computed(() => currentPlatformType.value === 'weibo')
                    }
                },
                title: {
                    title: '作品标题',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'wechat' || currentPlatformType.value === 'douyin'), autoSearchTrigger: false },
                    column: {
                        width: 200,
                        show: computed(() => currentPlatformType.value === 'wechat' || currentPlatformType.value === 'douyin')
                    }
                },
                content_text: {
                    title: '作品标题',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'weibo'), autoSearchTrigger: false },
                    column: {
                        width: 200,
                        show: computed(() => currentPlatformType.value === 'weibo')
                    }
                },
                // wechat_official_account: {
                //     title: '公众号ID',
                //     type: 'number',
                //     search: { show: computed(() => currentPlatformType.value === 'wechat') },
                //     column: { show: computed(() => currentPlatformType.value === 'wechat') }
                // },
                wechat_unique_id: {
                    title: '公众号唯一标识',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'wechat') },
                    column: { show: computed(() => currentPlatformType.value === 'wechat'), width: 150 }
                },
                wechat_official_account_name: {
                    title: '公众号名称',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'wechat') },
                    column: { show: computed(() => currentPlatformType.value === 'wechat'), width: 200 },

                },
                account_nickname: {
                    title: '账号昵称',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'weibo' || currentPlatformType.value === 'douyin') },
                    column: { show: computed(() => currentPlatformType.value === 'weibo' || currentPlatformType.value === 'douyin'), width: 200 }
                },

                idx: {
                    title: '位置',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat'),
                        formatter({ value }) {
                            if (value === 1) {
                                return '头条';
                            } else {
                                return `第${value}条`;
                            }
                        }
                    }
                },
                video_play_count: {
                    title: '视频播放量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'weibo'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }
                },
                read_count: {
                    title: '阅读量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }
                },
                viewing_count: {
                    title: '在看量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }
                },
                like_count: {
                    title: '点赞量',
                    type: 'number',
                    column: {
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }
                },
                forward_count: {
                    title: '转发量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }
                },
                reward_count: {
                    title: '打赏量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }
                },
                collect_count: {
                    title: '收藏量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }

                },
                comment_count: {
                    title: '评论量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin' || currentPlatformType.value === 'weibo'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }

                },
                share_count: {
                    title: '分享量',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin' || currentPlatformType.value === 'weibo'),
                        formatter({ value }) {
                            return value ? tranNum(value) : 0;
                        }
                    }

                },
                content_type: {
                    title: '内容类型',
                    type: 'dict-select',
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin'),
                        formatter({ value }) {
                            return value === 0 ? '视频' : '图文';
                        }
                    }
                },
                tenant_id: {
                    title: '租户',
                    type: 'dict-select',
                    search: {
                        show: auth('all_list'),
                        component: {
                            props: {
                                clearable: true, // 可清除
                                filterable: true, // 可过滤
                                options: asyncCompute({
                                    asyncFn: async () => {
                                        if (auth('all_list')) {
                                            const res = await tenantApi.getAllTenants()
                                            return res.data.map((item: any) => ({
                                                label: item.name,
                                                value: item.id
                                            })) // 转换数据格式
                                        }
                                    }
                                })
                            }
                        }
                    },
                    column: {
                        minWidth: 120,
                        show: false
                    },
                    form: { show: false }
                },
                is_original: {
                    title: '是否原创',
                    type: 'dict-select',
                    dict: dict({
                        data: dictionary('is_earliest_article'),
                    }),
                    column: {
                        minWidth: 100,
                        show: computed(() => currentPlatformType.value === 'wechat' || currentPlatformType.value === 'weibo'),
                    },
                    component: { props: { color: 'auto' } }
                },
                is_video: {
                    title: '发文类型',
                    type: 'text',
                    column: {
                        minWidth: 100,
                        show: computed(() => currentPlatformType.value === 'weibo'),
                        formatter({ value }) {
                            return value ? '视频' : '图文';
                        }
                    },
                },
                issue_time: {
                    title: '发布时间',
                    type: 'datetime',
                    search: { show: true },
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime'
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime'
                }
            }
        }
    }
} 
import { request } from '/@/utils/service';

// 监管账号数据接口
export interface MediaAccountInfo {
	media_account_id: number; // 改为实际的字段名
	account_id: string;
	account_name: string;
	platform_type: string;
	avatar_url?: string;
	signature?: string;
	fans_count?: number;
	is_authenticated?: boolean;
	status?: number;
	crawl_priority?: number;
	create_time?: string;
	account_update_time?: string;
	earliest_post_time?: string;
	tenant_id?: number;
	tenant_name?: string;
	tenant_priority?: number;
	// 运营类型 - 多选布尔字段
	is_same_type?: boolean; // 是否同类
	is_operating?: boolean; // 是否本级
	is_manage?: boolean; // 是否为下级
}

// 查询监管账号列表参数
export interface QueryMediaAccountsParams {
	tenant_id?: number;
	platform_type: string; // 'wechat' | 'douyin' | 'weibo'
	page?: number;
	limit?: number;
	account_name?: string;
	account_id?: string;
}

// 批量设置运营类型参数
export interface BatchOperatingTypeParams {
	media_account_ids: number[];
	is_same_type?: boolean;
	is_operating?: boolean;
	is_manage?: boolean;
}

// 单个设置运营类型参数
export interface SetOperatingTypeParams {
	media_account_id: number;
	is_same_type?: boolean;
	is_operating?: boolean;
	is_manage?: boolean;
}

// 监管账号列表响应
export interface MediaAccountListResponse {
	code: number;
	message: string;
	data: MediaAccountInfo[];
	page: number;
	limit: number;
	total: number;
	is_next: boolean;
	is_previous: boolean;
}

// API前缀
export const tenantMediaAccountsPrefix = '/api/hyqm/tenant-media-accounts/';

/**
 * 获取租户监管账号列表
 * @param params 查询参数
 * @returns 监管账号列表
 */
export async function getMediaAccountsList(params: QueryMediaAccountsParams): Promise<MediaAccountListResponse> {
	return request({
		url: '/api/hyqm/media-accounts/',
		method: 'get',
		params,
	});
}

/**
 * 设置单个账号的运营类型
 * @param params 设置参数
 * @returns 操作结果
 */
export async function setOperatingType(params: SetOperatingTypeParams): Promise<any> {
	return request({
		url: tenantMediaAccountsPrefix + 'set_operating_type/',
		method: 'post',
		data: {
			media_account_id: params.media_account_id,
			is_same_type: params.is_same_type,
			is_operating: params.is_operating,
			is_manage: params.is_manage,
		},
	});
}

/**
 * 批量设置账号的运营类型
 * @param params 批量设置参数
 * @returns 操作结果
 */
export async function batchSetOperatingType(params: BatchOperatingTypeParams): Promise<any> {
	return request({
		url: tenantMediaAccountsPrefix + 'batch_operating_type/',
		method: 'post',
		data: {
			media_account_ids: params.media_account_ids,
			is_same_type: params.is_same_type,
			is_operating: params.is_operating,
			is_manage: params.is_manage,
		},
	});
}

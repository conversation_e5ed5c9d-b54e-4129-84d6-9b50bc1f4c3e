import { request } from '/@/utils/service';

// 定义稿件数据类型接口
export interface ManuscriptType {
	id: number;
	title: string; // 稿件标题
	rich_content: string; // 富文本内容
	attachment_links?: string; // 附件链接
	description?: string; // 描述
	type: string; // 稿件类型
	status: string; // 稿件状态
	audit_status: string; // 审核状态
	audit_result: string; // 审核结果
	audit_comment?: string; // 审核意见
	audit_time?: string; // 审核时间
	creator?: number; // 创建人
	modifier?: string; // 修改人
	dept_belong_id?: string; // 数据归属部门
	tenant_id?: number; // 租户ID
	create_time: string; // 创建时间
	update_time: string; // 更新时间
}

// 定义查询参数接口
export interface QueryParams {
	page?: number;
	limit?: number;
	title?: string;
	status?: string;
	type?: string;
	creator?: number;
	creator_name?: string; // 新增投稿人名称
	query_type?: 'draft' | 'submitted'; // 新增查询类型
}

// 定义API前缀
export const apiPrefix = '/api/manuscript/contents/';

// 获取稿件列表
export async function getList(query: QueryParams) {
	return await request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 创建稿件
export async function createData(obj: Partial<ManuscriptType>) {
	return await request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新稿件
export async function updateData(obj: Partial<ManuscriptType>) {
	return await request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 删除稿件
export async function deleteData(id: number) {
	return await request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}

// 修改稿件投稿状态
export async function updateStatus(id: number, status: string) {
	return await request({
		url: apiPrefix + id + '/status/',
		method: 'put',
		data: { status },
	});
}

// 获取流程列表
export function getFlowList() {
	return request({
		url: '/api/dvadmin3_flow/flow_info/select_list/',
		method: 'get',
	});
}

// 提交审核流程
export const submitToFlow = (
	id: string | number,
	data: {
		flow_info_id: string | number;
		model_type: string;
	}
) => {
	return request({
		url: apiPrefix + `${id}/submit_to_flow/`,
		method: 'post',
		data,
	});
};

// 投稿接口
export const submitContribution = (manuscript_id: number, dept_id: number) => {
	return request({
		url: apiPrefix + 'submit_contribution/',
		method: 'post',
		data: {
			manuscript_id,
			dept_id,
		},
	});
};

// 获取投稿到部门列表 
export function getContributionsToDept(query: QueryParams) {
	return request({
		url: apiPrefix + 'contributions_to_dept/',
		method: 'get',
		params: query,
	});
};
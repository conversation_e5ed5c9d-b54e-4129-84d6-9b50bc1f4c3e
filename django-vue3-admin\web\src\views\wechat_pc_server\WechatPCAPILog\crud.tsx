import { CrudO<PERSON>s, AddReq, DelReq, EditReq, Crud<PERSON>xpose, <PERSON>rPageQuery, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { request } from '/@/utils/service'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag } from 'element-plus'

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    const pageRequest = async (query: any) => {
        return await api.getWechatPCAPILogList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateWechatPCAPILog(form)
    }
    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteWechatPCAPILog(row.id)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createWechatPCAPILog(form)
    }

    // 获取微信账号列表
    const getWechatPCAccounts = async () => {
        const res = await request({
            url: '/api/wechat_pc_server/wechat_pc_account/',
            method: 'get'
        })
        return res.data.map((item: any) => {
            return {
                value: item.wechat_id,
                label: `${item.nick_name}(${item.wechat_id})`
            }
        })
    }

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('WechatPCAPILog:Create')
                    }
                }
            },
            rowHandle: {
                width: 260,
                buttons: {
                    edit: {
                        show: auth('WechatPCAPILog:Update')
                    },
                    remove: {
                        show: auth('WechatPCAPILog:Delete')
                    }
                }
            },
            columns: {
                id: {
                    title: 'ID',
                    type: 'number',
                    form: { show: false },
                    column: { width: 80 }
                },
                wechat_pc_account: {
                    title: '微信账号',
                    type: 'text',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '微信账号必填' }]
                    }
                },
                wechat_pc_account_nick_name: {
                    title: '微信昵称',
                    type: 'text',
                    column: { width: 120 },
                    form: { show: false }
                },
                business_type: {
                    title: '业务类型',
                    type: 'select',
                    search: { show: true },
                    form: {
                        component: {
                            options: [
                                { value: 0, label: '获取历史文章' },
                                { value: 1, label: '获取文章五维数据' }
                            ]
                        },
                        rules: [{ required: true, message: '业务类型必填' }]
                    }
                },
                request_time: {
                    title: '请求时间',
                    type: 'datetime',
                    form: { show: false }
                },
                request_data: {
                    title: '请求数据',
                    type: 'textarea',
                    column: {
                        width: 200,
                        ellipsis: true
                    },
                    form: {
                        component: {
                            maxlength: 2000,
                            showWordLimit: true,
                            rows: 5
                        }
                    }
                },
                response_data: {
                    title: '响应数据',
                    type: 'textarea',
                    column: {
                        width: 200,
                        ellipsis: true
                    },
                    form: {
                        component: {
                            maxlength: 2000,
                            showWordLimit: true,
                            rows: 5
                        }
                    }
                },
                request_status: {
                    title: '状态码',
                    type: 'select',
                    search: { show: true },
                    form: {
                        component: {
                            options: [
                                { value: 200, label: '成功' },
                                { value: 400, label: '失败' },
                                { value: 500, label: '服务器错误' }
                            ]
                        }
                    },
                    column: {
                        width: 100,
                        formatter: (row: any) => {
                            const status = row.request_status
                            const success = status === 200
                            const color = success ? 'success' : 'danger'
                            const text = success ? '成功' : '失败'
                            return h(ElTag, { type: color }, () => text)
                        }
                    }
                }
            }
        }
    }
}
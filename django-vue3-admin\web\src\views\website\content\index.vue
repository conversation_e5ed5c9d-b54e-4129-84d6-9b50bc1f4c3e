<template>
  <fs-page class="PageWebsiteContentManage">
    <!-- 统计信息面板 -->
    <el-row :gutter="20" class="stats-panel">
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.total_contents || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-file-alt"></i>
              内容总数
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number success">{{ statistics?.extracted_contents || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-check-circle"></i>
              已提取
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number warning">{{ statistics?.pending_contents || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-clock"></i>
              待处理
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number danger">{{ statistics?.failed_contents || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-exclamation-triangle"></i>
              提取失败
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number info">{{ statistics?.manual_contents || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-hand-paper"></i>
              人工处理
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.extraction_success_rate || 0 }}%</div>
            <div class="stat-label">
              <i class="fas fa-chart-line"></i>
              提取成功率
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作面板 -->
    <el-card class="quick-actions-card">
      <template #header>
        <div class="card-header">
          <i class="fas fa-bolt"></i>
          <span>快速操作</span>
        </div>
      </template>
      <div class="quick-actions-content">
        <div class="action-group">
          <div class="action-info">
            <i class="fas fa-info-circle text-info"></i>
            <span>针对待处理和失败的内容，批量重新进行提取处理</span>
          </div>
          <div class="action-buttons">
            <el-button-group>
              <el-button type="success" @click="quickBatchExtract('pending')">
                <i class="fas fa-sync"></i>
                重新提取待处理
              </el-button>
              <el-button type="warning" @click="quickBatchExtract('failed')">
                <i class="fas fa-redo"></i>
                重新提取失败
              </el-button>
            </el-button-group>
          </div>
        </div>
        <div class="action-group">
          <div class="action-info">
            <i class="fas fa-info-circle text-warning"></i>
            <span>管理无法自动处理的内容，标记为人工处理或重置状态</span>
          </div>
          <div class="action-buttons">
            <el-button-group>
              <el-button type="info" @click="quickBatchMark('manual')">
                <i class="fas fa-hand-paper"></i>
                标记人工处理
              </el-button>
              <el-button type="primary" @click="refreshStatistics">
                <i class="fas fa-refresh"></i>
                刷新统计
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 内容列表 -->
    <fs-crud ref="crudRef" v-bind="crudBinding">
    </fs-crud>
  </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import createCrudOptions from './crud';
import * as api from './api';

export default defineComponent({
  name: 'WebsiteContentManage',
  setup() {
    // 统计信息
    const statistics = ref<any>(null);
    
    // 初始化CRUD
    const { crudBinding, crudRef, crudExpose } = useFs({ 
      createCrudOptions,
    });
    
    // 加载统计信息
    const loadStatistics = async () => {
      try {
        const res = await api.getContentStatistics();
        statistics.value = res.data;
      } catch (error) {
        console.error('加载统计信息失败:', error);
      }
    };
    
    // 刷新统计信息
    const refreshStatistics = async () => {
      ElMessage.info('正在刷新统计信息...');
      await loadStatistics();
      ElMessage.success('统计信息已刷新');
    };
    
    // 快速批量提取
    const quickBatchExtract = async (statusType: string) => {
      try {
        const statusMap = {
          'pending': { status: 'pending', label: '待处理' },
          'failed': { status: 'failed', label: '失败' }
        };
        
        const targetStatus = statusMap[statusType];
        if (!targetStatus) return;
        
        // 获取对应状态的内容列表
        const listRes = await api.getContentList({ 
          status: targetStatus.status, 
          page_size: 100 
        });
        
        const targetContents = listRes.data.results;
        
        if (targetContents.length === 0) {
          ElMessage.info(`没有${targetStatus.label}的内容`);
          return;
        }
        
        await ElMessageBox.confirm(
          `确定要重新提取所有${targetStatus.label}的内容吗？找到 ${targetContents.length} 个内容，这可能需要一些时间。`,
          `批量重新提取${targetStatus.label}内容`
        );
        
        const contentIds = targetContents.map(content => content.id);
        ElMessage.info(`开始批量重新提取 ${contentIds.length} 个内容...`);
        
        const res = await api.batchExtractContent(contentIds);
        ElMessage.success(res.msg);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('快速批量提取失败:', error);
          ElMessage.error('批量操作失败');
        }
      }
    };
    
    // 快速批量标记
    const quickBatchMark = async (action: string) => {
      try {
        let statusFilter = '';
        let actionLabel = '';
        
        if (action === 'manual') {
          statusFilter = 'no_rule_match,rule_generation_failed';
          actionLabel = '人工处理';
        }
        
        // 获取符合条件的内容
        const listRes = await api.getContentList({ 
          status: statusFilter, 
          page_size: 100 
        });
        
        const targetContents = listRes.data.results;
        
        if (targetContents.length === 0) {
          ElMessage.info('没有需要标记的内容');
          return;
        }
        
        await ElMessageBox.confirm(
          `确定要将所有规则问题的内容标记为${actionLabel}吗？找到 ${targetContents.length} 个内容。`,
          `批量标记${actionLabel}`
        );
        
        const contentIds = targetContents.map(content => content.id);
        const res = await api.batchUpdateContentStatus(contentIds, `mark_${action}`);
        ElMessage.success(res.msg);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('快速批量标记失败:', error);
          ElMessage.error('批量操作失败');
        }
      }
    };
    
    // 生命周期
    onMounted(() => {
      console.log('WebsiteContentManage组件已挂载');
      // 页面加载后自动获取列表数据
      crudExpose.doRefresh();
      loadStatistics();
    });

    return {
      statistics,
      crudBinding,
      crudRef,
      crudExpose,
      refreshStatistics,
      quickBatchExtract,
      quickBatchMark,
    };
  },
});
</script>

<style lang="scss" scoped>
.PageWebsiteContentManage {
  padding: 20px;
}

.stats-panel {
  margin-bottom: 20px;
  
  .stat-card {
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
        
        &.success {
          color: #67c23a;
        }
        
        &.warning {
          color: #e6a23c;
        }
        
        &.danger {
          color: #f56c6c;
        }
        
        &.info {
          color: #909399;
        }
      }
      
      .stat-label {
        font-size: 14px;
        color: #606266;
        
        i {
          margin-right: 4px;
          color: #409eff;
        }
      }
    }
  }
}

.quick-actions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
    color: #409eff;
  }
}

.quick-actions-content {
  .action-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f0f2f6;
    }
    
    .action-info {
      flex: 1;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 10px;
        font-size: 16px;
        
        &.text-info {
          color: #409eff;
        }
        
        &.text-warning {
          color: #e6a23c;
        }
      }
      
      span {
        color: #606266;
        line-height: 1.5;
      }
    }
    
    .action-buttons {
      margin-left: 20px;
    }
  }
}

// 表格自定义样式
:deep(.fs-crud) {
  .el-tag {
    font-size: 12px;
  }
  
  .text-primary {
    color: #409eff;
  }
  
  .text-muted {
    color: #909399;
  }
  
  .text-success {
    color: #67c23a;
  }
  
  .text-warning {
    color: #e6a23c;
  }
  
  .text-danger {
    color: #f56c6c;
  }
  
  .text-info {
    color: #17a2b8;
  }
}
</style>
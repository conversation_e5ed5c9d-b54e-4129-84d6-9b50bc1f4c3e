<template>
	<fs-page class="PageOperateReport">
		<div class="tab-container">
			<!-- 平台类型切换 -->
			<common-tabs v-model="currentTab" :items="platformItems" @change="handleTabChange" :size="'large'" />
		</div>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell_status="scope">
				<dict-tag
					:options="$getEnumDatas($ENUM.OPERATION_REPORT_STATUS)"
					:value="scope.row.status"
					:color-type="['info', 'primary', 'success', 'warning']"
				/>
			</template>
		</fs-crud>

		<!-- AI报告生成弹窗 -->
		<el-dialog v-model="showAccountLevelDialog" title="选择账号级别" width="30%">
			<div>
				<el-select v-model="selectedAccountLevel" placeholder="请选择要生成AI报告的账号级别" style="width: 100%; margin-top: 10px">
					<el-option v-for="item in accountLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleAccountLevelCancel">取消</el-button>
					<el-button type="primary" @click="handleAccountLevelConfirm"> 确定 </el-button>
				</span>
			</template>
		</el-dialog>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, defineAsyncComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { OPERATE_REPORT_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions, TabItem } from '/@/utils/platformTabsHelper';
import { ElDialog, ElButton } from 'element-plus'; // 引入 ElDialog 和 ElButton

// 动态导入CommonTabs组件
const CommonTabs = defineAsyncComponent(() => import('/@/components/CommonTabs/index.vue'));

export default defineComponent({
	name: 'OperateReport',
	components: {
		CommonTabs,
		ElDialog, // 注册 ElDialog
		ElButton, // 注册 ElButton
	},
	setup() {
		// 当前选中的平台Tab
		const currentTab = ref('wechat');
		// 当前选中的报告类型Tab
		const currentReportType = ref(3); // 默认为月报
		// 定义当前平台类型
		const currentPlatformType = ref(currentTab.value);
		// 平台类型选项数据
		const platformItems = ref<TabItem[]>([]);

		// 获取平台类型数据
		const loadPlatformTypes = () => {
			platformItems.value = generatePlatformTabsWithPermissions(OPERATE_REPORT_PERMISSIONS);
		};

		const {
			crudBinding,
			crudRef,
			crudExpose,
			// 从 createCrudOptions 返回值中解构出弹窗相关的状态和方法
			showAccountLevelDialog,
			selectedAccountLevel,
			handleAccountLevelConfirm,
			handleAccountLevelCancel,
			accountLevelOptions,
		} = useFs({
			createCrudOptions,
			context: {
				currentPlatformType,
			},
		});

		// 平台Tab变更处理函数
		const handleTabChange = (value: string) => {
			console.log(value, '切换平台类型');
			// 更新当前平台类型
			currentPlatformType.value = value;

			// 设置查询参数
			crudExpose.setSearchFormData({
				form: {
					platform_type: value,
					report_type: currentReportType.value,
				},
			});

			// 刷新数据
			crudExpose.doRefresh();
		};

		// 页面打开后获取列表数据
		onMounted(() => {
			loadPlatformTypes();
			// 初始化查询参数
			crudExpose.setSearchFormData({
				form: {
					platform_type: currentPlatformType.value,
					report_type: currentReportType.value,
				},
			});
			crudExpose.doRefresh();
		});

		return {
			crudBinding,
			crudRef,
			currentTab,
			currentReportType,
			platformItems,
			handleTabChange,
			// 将弹窗相关的状态和方法返回，供模板使用
			showAccountLevelDialog,
			selectedAccountLevel,
			handleAccountLevelConfirm,
			handleAccountLevelCancel,
			accountLevelOptions,
		};
	},
});
</script>

<style lang="scss" scoped>
.PageOperateReport {
	:deep(.el-tag) {
		margin: 0;
		padding: 0;
	}

	.tab-container {
		display: flex;
		flex-direction: column;
		gap: 12px;
		margin: 30px 20px 10px; /* 添加左侧缩进 */
	}
}
</style> 
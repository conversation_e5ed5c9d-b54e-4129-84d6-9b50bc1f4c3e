# 技术栈概览

## 🛠 核心技术栈
- **框架**: Vue 3.4.38 + Composition API + TypeScript 4.9.4
- **构建工具**: Vite 5.4.1 + Node.js >=16.0.0
- **UI组件库**: Element Plus 2.8.0
- **CRUD框架**: Fast-CRUD 1.21.2 (核心功能框架)
- **状态管理**: Pinia 2.0.28 + 持久化插件
- **路由管理**: Vue Router 4.4.3
- **国际化**: Vue I18n 9.14.0
- **HTTP客户端**: Axios 1.7.4
- **图表库**: ECharts 5.5.1
- **样式框架**: TailwindCSS 3.2.7 + SCSS

## 主要特性
- 🔐 **RBAC权限控制** (角色-按钮-列级别权限)
- 📱 **响应式布局** (支持多种布局模式)
- 🌍 **多语言支持** (中英文国际化)
- 🎨 **主题切换** (亮色/暗色主题)
- 📊 **丰富组件** (表格、表单、图表等)
- 🚀 **开发效率** (Fast-CRUD快速CRUD开发)

## 版本兼容性
- Node.js >= 16.0.0
- Vue >= 3.4.0
- TypeScript >= 4.9.0
- Element Plus >= 2.8.0

## 开发工具推荐
- **IDE**: VS Code + Volar插件
- **调试**: Vue DevTools
- **API测试**: Postman 或 Apifox
- **版本控制**: Git + GitLab/GitHub
import { RouteRecordRaw } from 'vue-router';

/**
 * celery插件路由
 */
export const celeryRoutes: Array<RouteRecordRaw> = [
  {
    path: '/celery',
    name: 'celery',
    redirect: '/celery/task',
    meta: {
      title: 'Celery任务管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      roles: ['admin', 'common'],
      icon: 'iconfont icon-caidan',
    },
    children: [
      {
        path: '/celery/task',
        name: 'celeryTask',
        component: () => import('./src/taskManage/index.vue'),
        meta: {
          title: '任务管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          roles: ['admin', 'common'],
          icon: 'iconfont icon-shouye',
        },
      },
      {
        path: '/celery/crontab',
        name: 'celeryCrontab',
        component: () => import('./src/scheduleManage/component/crontabManage/index.vue'),
        meta: {
          title: 'Crontab管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          roles: ['admin', 'common'],
          icon: 'iconfont icon-shouye',
        },
      },
      {
        path: '/celery/interval',
        name: 'celeryInterval',
        component: () => import('./src/scheduleManage/component/intervalManage/index.vue'),
        meta: {
          title: 'Interval管理',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          roles: ['admin', 'common'],
          icon: 'iconfont icon-shouye',
        },
      },
    ],
  },
];

export default celeryRoutes; 
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜图片导出</title>
    <style>
        @font-face {
            font-family: 'MiSans-Medium';
            src: url('/src/assets/iconfont/MiSans-Medium.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'MiSans-Medium', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            font-size: 15px;
        }

        #app {
            min-height: 100vh;
            padding: 20px;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }

        .error {
            max-width: 600px;
            margin: 100px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .error h2 {
            color: #f56c6c;
            margin-top: 0;
        }

        .error p {
            margin: 20px 0;
        }

        .error button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .error button:hover {
            background-color: #66b1ff;
        }

        .rank-image-export {
            width: 880px;
            margin: 0 auto;
            background: linear-gradient(0deg, #5CCC82, #5CCC80);
            color: white;
            font-family: 'MiSans-Medium', 'Microsoft YaHei', sans-serif;
            font-weight: 800;
            padding: 30px;
            box-sizing: border-box;
            border-radius: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            font-size: 15px;
        }

        /* 微信平台样式（默认绿色） */
        .rank-image-export.platform-wechat {
            background: linear-gradient(0deg, #5CCC82, #5CCC80);
        }

        .rank-image-export.platform-wechat::after {
            content: '';
            position: absolute;
            top: 21px;
            right: 0;
            width: 204px;
            height: 303px;
            background-image: url('/rank/phone-decoration.png');
            background-size: contain;
            background-repeat: no-repeat;
            z-index: 0;
        }

        /* 抖音平台样式（深蓝色，加宽容器） */
        .rank-image-export.platform-douyin {
            width: 1000px;
            background: #020C25;
        }

        .rank-image-export.platform-douyin::after {
            content: '';
            position: absolute;
            top: 28px;
            right: 0;
            width: 241.4px;
            height: 303px;
            background-image: url('/rank/douyin-decoration.png');
            background-size: contain;
            background-repeat: no-repeat;
            z-index: 0;
        }

        /* 微博平台样式（橙色渐变） */
        .rank-image-export.platform-weibo {
            background: linear-gradient(0deg, #FA6222 0%, #FAAA31 100%);
        }

        .rank-image-export.platform-weibo::after {
            content: '';
            position: absolute;
            top: 21px;
            right: 0;
            width: 220.2px;
            height: 313.3px;
            background-image: url('/rank/weibo-decoration.png');
            background-size: contain;
            background-repeat: no-repeat;
            z-index: 0;
        }

        .controls {
            text-align: center;
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .controls button {
            margin: 0 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .download-btn {
            background-color: #409eff;
            color: white;
        }

        .back-btn {
            background-color: #909399;
            color: white;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="loading">正在加载数据，请稍候...</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const app = document.getElementById('app');

            // 清理过期的localStorage数据
            cleanupExpiredData();

            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const exportId = urlParams.get('id');

            if (!exportId) {
                showError('缺少导出数据ID参数，请返回排行榜页面重新导出');
                return;
            }

            // 从localStorage获取数据
            const storedData = localStorage.getItem(exportId);
            if (!storedData) {
                showError('找不到导出数据，可能已过期或已被清理。请返回排行榜页面重新导出。');
                return;
            }

            try {
                // 解析数据
                const exportData = JSON.parse(storedData);

                // 验证数据结构
                if (!exportData || !exportData.rankData || !Array.isArray(exportData.rankData) || exportData.rankData.length === 0) {
                    throw new Error('导出数据格式无效或为空');
                }

                // 渲染页面
                renderExportPage(exportData, exportId);

            } catch (error) {
                showError('数据格式错误，无法解析: ' + error.message);
            }
        });

        // 清理过期的localStorage数据
        function cleanupExpiredData() {
            const now = Date.now();
            const expireTime = 24 * 60 * 60 * 1000; // 24小时过期

            // 遍历所有localStorage项
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (key && key.startsWith('rank-export-')) {
                    try {
                        const data = JSON.parse(localStorage.getItem(key));
                        // 检查是否有时间戳且已过期
                        if (data && data.timestamp && (now - data.timestamp > expireTime)) {
                            localStorage.removeItem(key);
                            console.log('已清理过期数据:', key);
                        }
                    } catch (e) {
                        // 如果解析失败，也删除这个项
                        localStorage.removeItem(key);
                        console.log('已清理无效数据:', key);
                    }
                }
            }
        }

        function showError(message) {
            const app = document.getElementById('app');
            app.innerHTML = `
        <div class="error">
          <h2>加载失败</h2>
          <p>${message}</p>
          <button onclick="window.close()">关闭页面</button>
        </div>
      `;
        }

        function renderExportPage(data, exportId) {
            const app = document.getElementById('app');

            // 显示控制按钮
            const controls = document.createElement('div');
            controls.className = 'controls';
            controls.innerHTML = `
        <button class="download-btn" onclick="downloadImage()">下载图片</button>
        <button class="back-btn" onclick="window.close()">返回</button>
      `;

            // 创建排行榜容器
            const exportContainer = document.createElement('div');
            exportContainer.id = 'rankImageExport';
            exportContainer.className = 'rank-image-export';

            // 根据平台添加对应的CSS类
            if (data.platform) {
                exportContainer.classList.add(`platform-${data.platform}`);
            }

            // 渲染排行榜内容
            exportContainer.innerHTML = generateRankHTML(data);

            // 清空并添加新内容
            app.innerHTML = '';
            app.appendChild(controls);
            app.appendChild(exportContainer);

            // 设置页面标题
            document.title = data.title || '排行榜图片导出';

            // 添加下载图片功能
            window.downloadImage = function () {
                const downloadBtn = document.querySelector('.download-btn');
                downloadBtn.textContent = '正在生成图片...';
                downloadBtn.disabled = true;

                // 检查html2canvas是否已经加载
                if (typeof html2canvas !== 'undefined') {
                    // 直接使用已加载的html2canvas
                    generateAndDownloadImage(downloadBtn);
                    return;
                }

                // 如果没有加载，动态加载html2canvas（使用本地文件）
                const script = document.createElement('script');
                script.src = '/src/assets/js/html2canvas.min.js';
                script.onload = function () {
                    const element = document.getElementById('rankImageExport');

                    html2canvas(element, {
                        backgroundColor: null,
                        scale: 3,
                        useCORS: true,
                        allowTaint: true,
                        logging: false,
                    }).then(function (canvas) {
                        // 创建下载链接
                        const link = document.createElement('a');
                        link.download = `${data.title || '排行榜'}_${data.timePeriod.replace(/~/g, '-')}.png`;
                        link.href = canvas.toDataURL('image/png', 1.0);

                        // 触发下载
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // 恢复按钮状态
                        downloadBtn.textContent = '下载图片';
                        downloadBtn.disabled = false;

                        // 显示成功消息
                        alert('图片下载成功！');

                        // 使用后删除数据，避免内存泄漏
                        window.addEventListener('beforeunload', () => {
                            localStorage.removeItem(exportId);
                        });
                    }).catch(function (error) {
                        alert('下载失败: ' + error.message);
                        downloadBtn.textContent = '下载图片';
                        downloadBtn.disabled = false;
                    });
                };

                script.onerror = function () {
                    // 如果本地加载失败，尝试从备用CDN加载
                    const fallbackScript = document.createElement('script');
                    fallbackScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                    fallbackScript.onload = function () {
                        // 使用备用CDN重新尝试生成图片
                        const element = document.getElementById('rankImageExport');
                        html2canvas(element, {
                            backgroundColor: null,
                            scale: 3,
                            useCORS: true,
                            allowTaint: true,
                            logging: false,
                        }).then(function (canvas) {
                            const link = document.createElement('a');
                            link.download = `${data.title || '排行榜'}_${data.timePeriod.replace(/~/g, '-')}.png`;
                            link.href = canvas.toDataURL('image/png', 1.0);
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            downloadBtn.textContent = '下载图片';
                            downloadBtn.disabled = false;
                            alert('图片下载成功！');
                        }).catch(function (error) {
                            alert('生成图片失败: ' + error.message);
                            downloadBtn.textContent = '下载图片';
                            downloadBtn.disabled = false;
                        });
                    };
                    fallbackScript.onerror = function () {
                        alert('无法加载图片生成工具，请检查网络连接或联系管理员');
                        downloadBtn.textContent = '下载图片';
                        downloadBtn.disabled = false;
                    };
                    document.body.appendChild(fallbackScript);
                };

                document.body.appendChild(script);
            };
        }

        // 格式化时间显示
        function formatTimePeriod(timePeriod) {
            if (!timePeriod || !timePeriod.includes('~')) {
                return timePeriod;
            }

            try {
                const [startDate, endDate] = timePeriod.split('~');
                const start = new Date(startDate);
                const end = new Date(endDate);

                const formatDate = (date) => {
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    const day = date.getDate();
                    return `${year}年${month}月${day}日`;
                };

                return `${formatDate(start)}~${formatDate(end)}`;
            } catch (error) {
                return timePeriod; // 如果转换失败，返回原始格式
            }
        }

        function generateRankHTML(data) {
            const { rankData, platform, exportCount, title, timePeriod, titleLine1, titleLine2, titleLine3 } = data;

            // 处理显示数据
            let displayData = rankData;
            if (exportCount !== 'all') {
                const count = parseInt(exportCount);
                if (!isNaN(count) && count > 0) {
                    displayData = rankData.slice(0, count);
                }
            }

            // 获取平台名称
            const platformMap = {
                wechat: '微信公众号',
                douyin: '抖音',
                weibo: '微博',
            };
            const platformName = platformMap[platform] || '微信公众号';

            // 获取列配置
            const columnsMap = {
                wechat: [
                    { key: 'readCount', label: '阅读总数' },
                    { key: 'forwardCount', label: '转发总数' },
                    { key: 'viewCount', label: '推荐总数' },
                    { key: 'likeCount', label: '点赞总数' },
                ],
                douyin: [
                    { key: 'fansCount', label: '粉丝数' },
                    { key: 'likeCount', label: '点赞总数' },
                    { key: 'commentCount', label: '评论总数' },
                    { key: 'shareCount', label: '分享总数' },
                    { key: 'collectCount', label: '收藏总数' },
                ],
                weibo: [
                    { key: 'likeCount', label: '点赞总数' },
                    { key: 'commentCount', label: '评论总数' },
                    { key: 'forwardCount', label: '转发总数' },
                ],
            };
            const columns = columnsMap[platform] || columnsMap.wechat;

            // 获取账号列名称
            const accountColumnMap = {
                wechat: '公众号名称',
                douyin: '抖音号名称',
                weibo: '微博号名称',
            };
            const accountColumnName = accountColumnMap[platform] || '公众号名称';

            // 获取发布列名称
            const publishColumnMap = {
                wechat: '发文数',
                douyin: '发布数',
                weibo: '发博数',
            };
            const publishColumnName = publishColumnMap[platform] || '发文数';

            // 获取指数列名称
            const indexColumnMap = {
                wechat: 'WCI',
                douyin: 'DCI',
                weibo: 'BCI',
            };
            const indexColumnName = indexColumnMap[platform] || 'WCI';

            // 生成表头
            let tableHeader = `
        <tr class="table-header">
          <th class="rank-col">排名</th>
          <th class="account-col">${accountColumnName}</th>
          <th class="publish-col">${publishColumnName}</th>
      `;

            columns.forEach(col => {
                tableHeader += `<th class="data-col">${col.label}</th>`;
            });

            tableHeader += `<th class="index-col">${indexColumnName}</th></tr>`;

            // 生成表格行
            let tableRows = '';
            displayData.forEach((item, index) => {
                // 获取排行指数
                let rankIndex = 0;
                if (platform === 'wechat') {
                    rankIndex = item.rankIndex || item.wci || 0;
                } else if (platform === 'douyin') {
                    rankIndex = item.dci || item.rankIndex || 0;
                } else if (platform === 'weibo') {
                    rankIndex = item.bci || item.rankIndex || 0;
                }

                tableRows += `
          <tr class="table-row">
            <td class="rank-col">
              <span class="rank-number">${index + 1}</span>
            </td>
            <td class="account-col">
              <div class="account-info">
                <span class="account-name">${item.name}</span>
              </div>
            </td>
            <td class="publish-col">${item.publishCount}</td>
        `;

                columns.forEach(col => {
                    const value = item[col.key];
                    const formattedValue = formatNumber(value);
                    tableRows += `<td class="data-col">${formattedValue}</td>`;
                });

                tableRows += `
            <td class="index-col">
              <span class="rank-index">${rankIndex}</span>
            </td>
          </tr>
        `;
            });

            // 根据平台和周期确定使用哪个图片
            let logoSrc = '/rank/week.png'; // 默认周榜图片

            if (platform === 'wechat') {
                logoSrc = data.period === 'weekly' ? '/rank/week.png' : '/rank/month.png';
            } else if (platform === 'douyin') {
                logoSrc = data.period === 'weekly' ? '/rank/douyin-week.png' : '/rank/douyin-month.png';
            } else if (platform === 'weibo') {
                logoSrc = data.period === 'weekly' ? '/rank/weibo-week.png' : '/rank/weibo-month.png';
            }

            // 生成标题HTML
            let titleHTML = '';
            let titleLines = [];

            // 收集标题行（允许为空）
            if (titleLine1 && typeof titleLine1 === 'string' && titleLine1.length > 0) {
                titleLines.push(titleLine1);
            }
            if (titleLine2 && typeof titleLine2 === 'string' && titleLine2.length > 0) {
                titleLines.push(titleLine2);
            }
            if (titleLine3 && typeof titleLine3 === 'string' && titleLine3.length > 0) {
                titleLines.push(titleLine3);
            }

            // 如果没有任何自定义标题行，使用默认标题
            if (titleLines.length === 0) {
                titleLines.push(title || `${platformName}官方账号`);
            }

            // 生成标题HTML，给最后一个h1添加特殊类名
            titleLines.forEach((line, index) => {
                const isLast = index === titleLines.length - 1;
                titleHTML += `<h1${isLast ? ' class="last-title-line"' : ''}>${line}</h1>`;
            });

            // 添加固定的h2标题
            titleHTML += `<h2>影响力排行榜</h2>`;

            // 组合HTML
            return `
        <!-- 头部信息 -->
        <div class="export-header">
          <div class="header-logo">
            <img src="${logoSrc}" alt="${data.period === 'weekly' ? '周榜' : '月榜'}" class="logo-img" />
          </div>
          <div class="header-title">
            ${titleHTML}
            <div class="subtitle">
              <span class="time-period">统计时间：${formatTimePeriod(timePeriod)}</span>
            </div>
          </div>
        </div>

        <!-- 排行榜表格 -->
        <div class="table-container">
          <div class="export-table">
            <table class="rank-table">
              <thead>
                ${tableHeader}
              </thead>
              <tbody>
                ${tableRows}
              </tbody>
            </table>
          </div>
        </div>

        <!-- 底部信息 -->
        <div class="export-footer">
          <span class="footer-text">数据由汇远轻媒提供，仅供参考</span>
        </div>
      `;
        }

        function formatNumber(num) {
            if (num === undefined || num === null || num === '') {
                return '0';
            }

            // 尝试转换为数字
            const numValue = Number(num);
            if (isNaN(numValue)) {
                return num;
            }

            // 只对10万以上的数字进行格式化
            if (numValue >= 100000) {
                return (numValue / 10000).toFixed(1) + 'w';
            }

            return numValue.toString();
        }
    </script>

    <style>
        /* 表格样式 */
        .export-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header-logo {
            position: relative;
            height: 123px;
            margin-bottom: 67px;
        }

        .logo-img {
            width: 374px;
            height: 43px;
            object-fit: contain;
            position: absolute;
            top: 80px;
            left: 60px;
        }

        /* 抖音平台logo样式 */
        .platform-douyin .logo-img {
            width: 275px;
            height: 44px;
            top: 81px;
            left: 66px;
        }

        /* 微博平台logo样式 */
        .platform-weibo .logo-img {
            width: 295px;
            height: 45px;
            top: 80px;
            left: 46px;
        }



        .header-title h1 {
            margin: 0 auto 0 auto;
            font-size: 50px;
            letter-spacing: 2px;
            font-weight: 800;
            text-shadow: 0px 8px 20px rgba(9, 90, 35, 0.7);
            font-family: 'MiSans-Medium', 'Microsoft YaHei', sans-serif;
            text-align: center;
        }

        .header-title h1.last-title-line {
            margin: 0 auto 67px auto;
        }

        .header-title h2 {
            margin: 0 0 20px 0;
            font-size: 40px;
            text-shadow: 0px 6px 15px rgba(9, 90, 35, 0.6);
            letter-spacing: 3px;
            font-family: 'MiSans-Medium';
            font-weight: 800;
        }

        /* 抖音平台标题样式（无阴影） */
        .platform-douyin .header-title h1 {
            text-shadow: none;
        }

        .platform-douyin .header-title h2 {
            text-shadow: none;
        }

        /* 微博平台标题样式 */
        .platform-weibo .header-title h1 {
            text-shadow: 0px 3px 7px rgba(215, 68, 3, 0.6);
        }

        .platform-weibo .header-title h2 {
            text-shadow: 0px 3px 7px rgba(215, 68, 3, 0.6);
        }



        .subtitle {
            font-size: 17px;
            display: inline-block;
            background: rgba(255, 255, 255, 0.5);
            padding: 15px 30px;
            border-radius: 25px;
            font-family: 'MiSans-Medium';
            font-weight: 800;
            color: #2E944F;
        }

        /* 抖音平台副标题样式 */
        .platform-douyin .subtitle {
            background: rgba(255, 255, 255, 0.5);
            color: #344435;
        }

        /* 微博平台副标题样式 */
        .platform-weibo .subtitle {
            background: rgba(255, 255, 255, 0.5);
            color: #DE6E0A;
        }

        .table-container {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow-x: hidden;
            width: 100%;
            box-sizing: border-box;
        }

        .export-table {
            overflow: hidden;
        }

        .rank-table {
            width: 100%;
            border-collapse: collapse;
            color: #333;
            table-layout: fixed;
            max-width: 100%;
            overflow-x: hidden;
            font-family: 'MiSans-Medium', sans-serif;
            font-weight: 800;
        }

        .table-header {
            background: #43b369;
            /* 微信平台默认表头颜色 */
            color: white;
        }

        /* 抖音平台表头样式 */
        .platform-douyin .table-header {
            background: #040E28;
        }

        /* 微博平台表头样式 */
        .platform-weibo .table-header {
            background: #FC841B;
        }

        .table-header th {
            padding: 12px 8px;
            font-size: 15px;
            text-align: center;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: 'MiSans-Medium', sans-serif;
            font-weight: 800;
        }

        .table-header th:last-child {
            border-right: none;
        }

        .table-row {
            border-bottom: 1px solid #f0f0f0;
        }

        .table-row:hover {
            background-color: #f8f9ff;
        }

        /* 偶数行背景色 */
        .table-row:nth-child(even) {
            background-color: #e7f6eb;
        }

        /* 抖音平台偶数行背景色 */
        .platform-douyin .table-row:nth-child(even) {
            background-color: #f2f2f2;
        }

        /* 微博平台偶数行背景色 */
        .platform-weibo .table-row:nth-child(even) {
            background-color: #ffe3ca;
        }

        .table-row td {
            padding: 12px 8px;
            text-align: center;
            font-size: 15px;
            border-right: 1px solid #f0f0f0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: 'MiSans-Medium', sans-serif;
        }

        .table-row td.data-col {
            text-align: center;
            padding-right: 8px;
        }

        .table-row td:last-child {
            border-right: none;
        }

        .rank-col {
            width: 40px;
            font-weight: normal;
        }

        .account-col {
            width: 220px;
            text-align: center !important;
        }

        /* 抖音平台账号列加宽 */
        .platform-douyin .account-col {
            width: 240px;
        }

        .publish-col {
            width: 60px;
            font-weight: normal;
        }

        .data-col {
            width: 65px;
            font-weight: normal;
        }

        .index-col {
            width: 70px;
        }

        .rank-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            line-height: 1;
            text-align: center;
        }

        .account-info {
            max-width: 100%;
        }

        .account-name {
            font-weight: 500;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
            font-family: 'MiSans-Medium', sans-serif;
            font-size: 15px;
        }

        .rank-index {
            color: #4ba373;
            font-weight: 800;
            /* 微信平台WCI数据颜色 */
        }

        /* 抖音平台DCI指数颜色 */
        .platform-douyin .rank-index {
            color: #000;
        }

        /* 微博平台BCI指数颜色 */
        .platform-weibo .rank-index {
            color: #df611e;
        }

        .export-footer {
            text-align: right;
            font-size: 16px;
            opacity: 0.8;
            padding-right: 10px;
        }
    </style>
</body>

</html>
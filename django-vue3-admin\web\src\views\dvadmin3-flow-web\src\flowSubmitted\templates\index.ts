// 流程模板接口定义
export interface FlowField {
	key: string;
	label: string;
	type: 'input' | 'textarea' | 'select' | 'date' | 'number' | 'file' | 'datetime';
	required: boolean;
	options?: Array<{ label: string; value: any }>;
	placeholder?: string;
	validation?: string;
	span?: number; // 表单占用列数，默认24
}

export interface FlowTemplate {
	id: string;
	name: string;
	description: string;
	category: string;
	icon?: string;
	fields: FlowField[];
}

// 预定义的流程模板
export const FLOW_TEMPLATES: FlowTemplate[] = [
	{
		id: 'three_review_template',
		name: '三审三校流程',
		description: '适用于重要文档的审核和校验流程，包含三级审核和三级校验',
		category: 'document_review',
		icon: 'Document',
		fields: [
			{
				key: 'document_title',
				label: '文档标题',
				type: 'input',
				required: true,
				placeholder: '请输入文档标题',
				span: 24,
			},

			{
				key: 'document_content',
				label: '文档内容',
				type: 'textarea',
				required: true,
				placeholder: '请输入文档内容或上传文档文件',
				span: 24,
			},

			// {
			// 	key: 'priority',
			// 	label: '优先级',
			// 	type: 'select',
			// 	required: true,
			// 	span: 24,
			// 	options: [
			// 		{ label: '低', value: 'low' },
			// 		{ label: '中', value: 'medium' },
			// 		{ label: '高', value: 'high' },
			// 	],
			// },
		],
	},
];

// 按分类获取模板
export const getTemplatesByCategory = (category?: string): FlowTemplate[] => {
	if (!category) return FLOW_TEMPLATES;
	return FLOW_TEMPLATES.filter((template) => template.category === category);
};

// 根据ID获取模板
export const getTemplateById = (id: string): FlowTemplate | undefined => {
	return FLOW_TEMPLATES.find((template) => template.id === id);
};

// 获取所有分类
export const getCategories = (): Array<{ label: string; value: string }> => {
	const categories = [...new Set(FLOW_TEMPLATES.map((template) => template.category))];
	const categoryLabels: Record<string, string> = {
		document_review: '文档审核',
		hr_management: '人事管理',
		purchase_management: '采购管理',
	};

	return categories.map((category) => ({
		label: categoryLabels[category] || category,
		value: category,
	}));
};

// 生成 pre_change_content 数据
export const generatePreChangeContent = (template: FlowTemplate, formData: Record<string, any>) => {
	return {
		type: 'create',
		flow_type: template.id,
		template_name: template.name,
		form_data: formData,
		created_at: new Date().toISOString(),
	};
};

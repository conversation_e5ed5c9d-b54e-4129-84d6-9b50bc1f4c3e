<template>
	<el-dialog v-model="dialogVisible" :title="dialogTitle" width="50%" :close-on-click-modal="false" destroy-on-close>
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="export-form">
			<el-form-item label="导出目标" prop="target_type" v-if="!props.taskId && !props.taskName && !props.targetId && !props.targetName">
				<el-radio-group v-model="form.target_type">
					<el-radio :label="'task'">任务</el-radio>
					<el-radio :label="'target'">目标</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item v-if="form.target_type === 'task' && !props.targetId && !props.taskId" label="选择任务" prop="task_id">
				<el-select v-model="form.task_id" placeholder="请选择任务" filterable clearable style="width: 100%">
					<el-option v-for="item in taskOptions" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>

			<el-form-item label="选择目标" prop="target_id" v-else-if="form.target_type === 'task' && !props.targetId && !props.taskId">
				<el-select v-model="form.target_id" placeholder="请选择目标" filterable clearable style="width: 100%">
					<el-option v-for="item in targetOptions" :key="item.id" :label="item.wechat_name" :value="item.id" />
				</el-select>
			</el-form-item>

			<el-form-item label="错误类型" prop="error_type">
				<div class="export-header">
					<el-checkbox v-model="allErrorTypesSelected" @change="handleSelectAllErrorTypes">全选</el-checkbox>
				</div>
				<el-checkbox-group v-model="form.error_type" class="checkbox-grid" style="width: 100%">
					<el-checkbox v-for="item in errorTypes" :key="item.value" :label="item.value">
						{{ item.label }}
					</el-checkbox>
				</el-checkbox-group>
			</el-form-item>

			<el-form-item label="AI校对结果" prop="ai_judge">
				<el-checkbox-group v-model="form.ai_judge" class="checkbox-grid" style="width: 100%">
					<el-checkbox v-for="item in $getEnumDatas($ENUM.JUDGE_STATUS)" :key="item.value" :label="item.value">
						{{ item.label }}
					</el-checkbox>
				</el-checkbox-group>
			</el-form-item>

			<el-form-item label="人工校对结果" prop="human_judge">
				<el-checkbox-group v-model="form.human_judge" class="checkbox-grid" style="width: 100%">
					<el-checkbox v-for="item in $getEnumDatas($ENUM.JUDGE_STATUS)" :key="item.value" :label="item.value">
						{{ item.label }}
					</el-checkbox>
				</el-checkbox-group>
			</el-form-item>

			<el-form-item label="导出字段" prop="export_columns">
				<div class="export-header">
					<el-checkbox v-model="allColumnsSelected" @change="handleSelectAllColumns">全选</el-checkbox>
				</div>
				<div class="export-columns-container">
					<el-checkbox-group v-model="checkedColumns" @change="handleColumnsChange" class="checkbox-grid">
						<el-checkbox v-for="item in exportOptions" :key="item.value" :label="item.value">
							{{ item.label }}
						</el-checkbox>
					</el-checkbox-group>
				</div>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="submitLoading">导出</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, PropType } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { getTaskAll, type CorrectionTaskInfo, type CorrectionTargetInfo } from '../proofread_task/api';
import { getTargetList } from '../proofread_target/api';
import { exportResult, getErrorTypes } from '../article_error_sentence/correction';
import download from '/@/utils/download';
import { DictionaryStore } from '/@/stores/dictionary';

// 定义字典选项类型
interface DictionaryOption {
	label: string;
	value: string | number;
}

// 获取字典数据
const dictionaryStore = DictionaryStore();

// 定义属性
const props = defineProps({
	visible: {
		type: Boolean,
		required: true,
	},
	targetName: {
		type: String,
		default: '',
	},
	targetId: {
		type: Number,
		default: undefined,
	},
	taskName: {
		type: String,
		default: '',
	},
	taskId: {
		type: Number,
		default: undefined,
	},
	loadTask: {
		type: Boolean,
		default: false,
	},
	loadTarget: {
		type: Boolean,
		default: false,
	},
});

// 定义事件
const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref<FormInstance>();

// 状态
const dialogVisible = ref(props.visible);
const submitLoading = ref(false);
const taskOptions = ref<CorrectionTaskInfo[]>([]);
const targetOptions = ref<CorrectionTargetInfo[]>([]);
const errorTypes = ref<DictionaryOption[]>([]);
const checkedColumns = ref<(string | number)[]>([]);

// 对话框标题
const dialogTitle = computed(() => {
	if (props.taskName) {
		return `导出错误 - 任务：${props.taskName}`;
	} else if (props.targetName) {
		return `导出错误 - 目标：${props.targetName}`;
	} else {
		return '导出配置';
	}
});

// 获取错误类型列表
const fetchErrorTypes = async () => {
	try {
		const res = await getErrorTypes();
		errorTypes.value =
			res.data.map((item: string) => ({
				label: item,
				value: item,
			})) || [];
	} catch (error) {
		console.error('获取错误类型失败', error);
		errorTypes.value = [];
	}
};

// 监听外部visible属性变化
watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val;
		if (val) {
			// 打开弹窗时初始化数据
			resetForm();
			// 获取错误类型
		}
	}
);

// 监听本地dialogVisible状态变化，同步回父组件
watch(dialogVisible, (val) => {
	emit('update:visible', val);
	if (!val) {
		// 关闭弹窗时重置表单
		resetForm();
	}
});

// 导出选项列表
const exportOptions = computed<DictionaryOption[]>(() => {
	if (dictionaryStore.data.export_columns) {
		// 将字典数据转换为key-value对象
		return dictionaryStore.data.export_columns;
	} else {

		return [
				{
					value: 'account_nick_name',
					label: '公众号名称',
				},
				{
					value: 'article_title',
					label: '文章标题',
				},
				{
					value: 'sentence',
					label: '错误句子',
				},
				{
					value: 'error_type',
					label: '错误类型',
				},
				{
					value: 'wrong_word',
					label: '错误词',
				},
				{
					value: 'correct_word',
					label: '建议修改',
				},
				{
					value: 'issue_time',
					label: '文章发布时间',
				},
				{
					value: 'url',
					label: '文章链接',
				},
		];
	}
	});


// 全选状态
const allColumnsSelected = computed({
	get: () => {
		const allOptions = exportOptions.value.map((item) => item.value);
		return checkedColumns.value.length === allOptions.length;
	},
	set: (val) => {
		handleSelectAllColumns(val);
	},
});

// 错误类型全选状态
const allErrorTypesSelected = computed({
	get: () => {
		return form.value.error_type.length === errorTypes.value.length;
	},
	set: (val) => {
		handleSelectAllErrorTypes(val);
	},
});

// 处理错误类型全选
const handleSelectAllErrorTypes = (val: boolean) => {
	if (val) {
		form.value.error_type = errorTypes.value.map((item) => item.value);
	} else {
		form.value.error_type = [];
	}
};

// 处理列选择变化
const handleColumnsChange = (values: (string | number)[]) => {
	checkedColumns.value = values;
	form.value.export_columns = values.map((val) => String(val)).join(',');
};

// 工具方法：将数组转换为字符串
const arrayToString = (arr: (string | number)[] | undefined): string => {
	if (!arr || arr.length === 0) return '';
	return arr.map((val) => String(val)).join(',');
};

// 表单数据
const form = ref({
	target_type: 'task',
	task_id: undefined as number | undefined,
	target_id: undefined as number | undefined,
	error_type: [] as (string | number)[],
	export_columns: '',
	human_judge: [] as (string | number)[],
	ai_judge: [] as (string | number)[],
});

// 表单验证规则
const rules = {
	task_id: [
		{
			required: true,
			message: '请选择任务',
			trigger: 'change',
			validator: (rule: any, value: any, callback: any) => {
				if (form.value.target_type === 'task' && !value) {
					callback(new Error('请选择任务'));
				} else {
					callback();
				}
			},
		},
	],
	target_id: [
		{
			required: true,
			message: '请选择目标',
			trigger: 'change',
			validator: (rule: any, value: any, callback: any) => {
				if (form.value.target_type === 'target' && !value) {
					callback(new Error('请选择目标'));
				} else {
					callback();
				}
			},
		},
	],
	export_columns: [
		{
			required: true,
			message: '请至少选择一个导出字段',
			trigger: 'change',
		},
	],
};

// 处理全选
const handleSelectAllColumns = (val: boolean) => {
	if (val) {
		const allValues = exportOptions.value.map((item) => item.value);
		checkedColumns.value = [...allValues];
		form.value.export_columns = allValues.map((val) => String(val)).join(',');
	} else {
		checkedColumns.value = [];
		form.value.export_columns = '';
	}
};

// 重置表单
const resetForm = () => {
	if (formRef.value) {
		formRef.value.resetFields();
	}

	// 设置默认值并根据prop传入的值初始化
	form.value = {
		target_type: props.taskId ? 'task' : props.targetId ? 'target' : 'task',
		task_id: props.taskId,
		target_id: props.targetId,
		error_type: [],
		export_columns: '',
		human_judge: [],
		ai_judge: [],
	};

	// 重置导出字段选择
	const defaultColumns = exportOptions.value.map((item) => item.value);
	checkedColumns.value = [...defaultColumns];
	form.value.export_columns = defaultColumns.map((val) => String(val)).join(',');
};

// 表单提交
const handleSubmit = async () => {
	if (!formRef.value) return;

	try {
		await formRef.value.validate();

		submitLoading.value = true;

		// 构建导出参数
		const exportParams = {
			export_name: undefined,
			task_id: props.taskId || (form.value.target_type === 'task' ? Number(form.value.task_id) : undefined),
			target_id: props.targetId || (form.value.target_type === 'target' ? Number(form.value.target_id) : undefined),
			error_type: form.value.error_type.length ? arrayToString(form.value.error_type) : undefined,
			export_columns: form.value.export_columns,
			human_judge: form.value.human_judge.length ? arrayToString(form.value.human_judge) : undefined,
			ai_judge: form.value.ai_judge.length ? arrayToString(form.value.ai_judge) : undefined,
		};

		// 确保至少有 task_id 或 target_id 之一
		if (!exportParams.task_id && !exportParams.target_id) {
			ElMessage.warning('请选择导出的任务或目标');
			submitLoading.value = false;
			return;
		}

		try {
			const res = await exportResult(exportParams);
			const downloadResult = await download.excel(
				res.data,
				// 导出文件名格式为 20250319101010_校对任务错误情况.xlsx
				`${
					props.taskName ||
					'校对任务'
				}全部微信公众号发文错误情况明细表_${new Date().toLocaleDateString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit',
				}).replace(/\D/g, '')}.xlsx`
			);

			if (downloadResult.success) {
				dialogVisible.value = false;
				emit('success');
				ElMessage.success('导出成功');
			} else {
				ElMessage.warning(downloadResult.message);
			}
		} catch (error: any) {
			console.error('导出失败', error);
			ElMessage.warning(error.message || '导出失败：未知错误');
		}
	} catch (error: any) {
		console.error('表单验证失败', error);
		ElMessage.warning('请检查导出配置');
	} finally {
		submitLoading.value = false;
	}
};

// 获取任务选项
const fetchTaskOptions = async () => {
	try {
		const res = await getTaskAll();
		if (res.code === 2000) {
			taskOptions.value = res.data;
		}
	} catch (error) {
		console.error('获取任务列表失败', error);
	}
};

// 获取目标选项
const fetchTargetOptions = async () => {
	try {
		const res = await getTargetList({ page: 1, limit: 100 });
		if (res.code === 2000 && res.data) {
			targetOptions.value = Array.isArray(res.data) ? res.data : [];
		}
	} catch (error) {
		console.error('获取目标列表失败', error);
	}
};

// 初始化
onMounted(() => {
	// 初始化默认选择所有导出字段
	const defaultColumns = exportOptions.value.map((item) => item.value);
	checkedColumns.value = [...defaultColumns];
	form.value.export_columns = defaultColumns.map((val) => String(val)).join(',');

	// 获取错误类型
	fetchErrorTypes();

	// 获取任务和目标数据，只在组件挂载时获取一次
	if (props.loadTask && props.loadTarget) {
		fetchTaskOptions();
		fetchTargetOptions();
	}
});

// 导出组件接口
defineExpose({
	dialogVisible,
	handleSubmit,
	resetForm,
});
</script>

<style lang="scss" scoped>
.export-form {
	max-height: 65vh;
	overflow-y: auto;
	padding-right: 10px;
}

.export-header {
	margin-bottom: 8px;
	margin-right: 20px;
}

.export-columns-container,
.checkbox-container {
	padding: 10px 15px;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	background-color: #f9fafc;
	margin-top: 5px;
	width: 100%;
}

.checkbox-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 12px 20px;

	.el-checkbox {
		margin-right: 0;
		display: flex;
		align-items: center;

		:deep(.el-checkbox__label) {
			font-size: 14px;
		}
	}

	@media (max-width: 768px) {
		grid-template-columns: repeat(2, 1fr);
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}
</style> 
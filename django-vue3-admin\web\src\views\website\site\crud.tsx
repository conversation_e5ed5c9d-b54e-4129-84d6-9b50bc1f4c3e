import * as api from './api';
import { SiteType, SiteStatus } from './api';
import { CreateCrudOptionsProps, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed } from 'vue';

export default function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      request: {
        pageRequest: async (query: any) => {
          const res = await api.getSiteList(query);
          // 转换后端数据格式为Fast-CRUD期望的格式
          return {
            ...res,
            data: {
              results: res.data || [],
              pagination: {
                page: res.page || 1,
                page_size: res.limit || 20,
                total: res.total || 0,
                total_pages: Math.ceil((res.total || 0) / (res.limit || 20))
              }
            }
          };
        },
        addRequest: async ({ form }: any) => {
          const res = await api.createSite(form);
          ElMessage.success(res.msg || '站点创建成功');
          return res.data;
        },
        editRequest: async ({ form, row }: any) => {
          const res = await api.updateSite(row.id, form);
          ElMessage.success(res.msg || '站点更新成功');
          return res.data;
        },
        delRequest: async ({ row }: any) => {
          const res = await api.deleteSite(row.id);
          ElMessage.success(res.msg || '站点删除成功');
          return res.data;
        },
      },
      
      // 页面配置
      container: {
        is: 'fs-layout-card'
      },
      
      // 表格配置
      table: {
        size: 'default',
        stripe: true,
        border: false,
      },
      
      // 搜索表单配置
      search: {
        show: true,
        initialForm: {},
        options: {
          labelWidth: '100px',
        },
      },
      
      // 操作栏配置
      actionbar: {
        buttons: {
          add: {
            text: '添加站点',
            type: 'primary',
          },
          // 批量操作按钮
          batchActivate: {
            text: '批量激活',
            type: 'success',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的站点');
                return;
              }
              
              try {
                await ElMessageBox.confirm(`确定要激活选中的 ${selectedRowKeys.length} 个站点吗？`, '批量激活');
                const res = await api.batchUpdateSiteStatus(selectedRowKeys, 'activate');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量激活失败:', error);
                }
              }
            }
          },
          batchDeactivate: {
            text: '批量停用',
            type: 'warning',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的站点');
                return;
              }
              
              try {
                await ElMessageBox.confirm(`确定要停用选中的 ${selectedRowKeys.length} 个站点吗？`, '批量停用');
                const res = await api.batchUpdateSiteStatus(selectedRowKeys, 'deactivate');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量停用失败:', error);
                }
              }
            }
          }
        }
      },
      
      // 行操作配置
      rowHandle: {
        width: 300,
        buttons: {
          edit: { text: '编辑', type: 'primary' },
          remove: { text: '删除', type: 'danger' },
          // 自定义操作按钮
          viewDetail: {
            text: '查看详情',
            type: 'info',
            click: ({ row }: { row: SiteType }) => {
              // 这里可以打开详情页面或模态框
              console.log('查看站点详情:', row);
              ElMessage.info('查看详情功能开发中');
            }
          },
          initialize: {
            text: '初始化',
            type: 'success',
            show: ({ row }: { row: SiteType }) => {
              return !row.initialization_status.is_initialized;
            },
            click: async ({ row }: { row: SiteType }) => {
              try {
                await ElMessageBox.confirm(`确定要初始化站点 "${row.name}" 吗？\\n\\n这将抓取站点地图并进行AI分类，可能需要几分钟时间。`, '确认初始化');
                const res = await api.initializeSite(row.id);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('初始化失败:', error);
                }
              }
            }
          },
          syncMediaAccount: {
            text: '同步MediaAccount',
            type: 'warning',
            show: ({ row }: { row: SiteType }) => {
              return !row.media_account_status.is_synced;
            },
            click: async ({ row }: { row: SiteType }) => {
              try {
                await ElMessageBox.confirm(`确定要将站点 "${row.name}" 同步到MediaAccount吗？`, '确认同步');
                const res = await api.syncSiteToMediaAccount(row.id);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('同步失败:', error);
                }
              }
            }
          }
        }
      },

      columns: {
        // 多选框
        _index: {
          title: '选择',
          form: { show: false },
          column: {
            type: 'selection',
            width: 60,
            align: 'center'
          }
        },
        
        id: {
          title: 'ID',
          type: 'number',
          form: { show: false },
          column: { 
            width: 80,
            sortable: true 
          }
        },
        
        name: {
          title: '站点名称',
          type: 'input',
          search: { show: true },
          form: {
            rules: [{ required: true, message: '请输入站点名称' }],
          },
          column: { 
            minWidth: 150,
            showOverflowTooltip: true,
            formatter: ({ row, value }: { row: SiteType; value: any }) => {
              // 如果站点名称为空，使用域名作为备用显示
              if (!value || value.trim() === '') {
                return `${row.domain} (无名称)`;
              }
              return value;
            }
          }
        },
        
        domain: {
          title: '域名',
          type: 'input',
          search: { 
            show: true,
            component: {
              placeholder: '域名关键词'
            }
          },
          form: {
            rules: [{ required: true, message: '请输入域名' }],
          },
          column: { 
            minWidth: 200,
            showOverflowTooltip: true
          }
        },
        
        start_url: {
          title: '起始URL',
          type: 'input',
          form: {
            component: {
              placeholder: 'https://example.com'
            },
            helper: '站点的起始抓取URL'
          },
          column: { 
            show: false // 默认隐藏，可通过列设置显示
          }
        },
        
        description: {
          title: '描述',
          type: 'textarea',
          form: {
            component: {
              placeholder: '站点描述信息',
              rows: 3
            }
          },
          column: { 
            show: false,
            minWidth: 200,
            showOverflowTooltip: true
          }
        },
        
        status: {
          title: '状态',
          type: 'dict-select',
          dict: dict({
            data: [
              { value: SiteStatus.ACTIVE, label: '正常运行', color: 'success' },
              { value: SiteStatus.INACTIVE, label: '暂停使用', color: 'info' },
              { value: SiteStatus.MAINTENANCE, label: '维护中', color: 'warning' },
              { value: SiteStatus.ARCHIVED, label: '已归档', color: 'info' },
              { value: SiteStatus.ERROR, label: '异常', color: 'danger' },
              { value: SiteStatus.PENDING, label: '待审核', color: 'primary' },
            ]
          }),
          search: { 
            show: true,
            component: {
              placeholder: '选择状态',
              clearable: true,
              style: { width: '120px' }
            }
          },
          form: {
            value: SiteStatus.ACTIVE
          },
          column: {
            width: 120,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            }
          }
        },
        
        sitemap_count: {
          title: '页面数量',
          type: 'number',
          form: { show: false },
          column: { 
            width: 100,
            align: 'center'
          }
        },
        
        initialization_status: {
          title: '初始化状态',
          form: { show: false },
          column: {
            width: 120,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            },
            formatter: ({ row, value }: { row: SiteType; value: any }) => {
              if (typeof value === 'object' && value !== null) {
                if (value.is_initialized) {
                  return `已初始化 (${value.sitemap_count}页)`;
                } else {
                  return value.status_display || '未初始化';
                }
              }
              return value || '未初始化';
            }
          }
        },
        
        media_account_status: {
          title: 'MediaAccount',
          form: { show: false },
          column: {
            width: 120,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            },
            formatter: ({ row, value }: { row: SiteType; value: any }) => {
              if (typeof value === 'object' && value !== null) {
                if (value.is_synced) {
                  return value.sync_time ? `已同步 (${new Date(value.sync_time).toLocaleDateString()})` : '已同步';
                } else {
                  return '未同步';
                }
              }
              return value || '未同步';
            }
          }
        },
        
        created_at: {
          title: '创建时间',
          type: 'datetime',
          form: { show: false },
          column: { 
            width: 160,
            sortable: true
          }
        },
      },
    },
  };
}
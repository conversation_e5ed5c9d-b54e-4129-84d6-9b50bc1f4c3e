# 前端页面测试规范手册

## 🎯 测试目标
确保每个页面的所有功能组件都正常工作，不遗漏任何细节问题。

## 📋 页面测试检查清单

### 1. 页面加载检查
- [ ] 页面是否正常加载（无404、500错误）
- [ ] 页面标题是否正确显示
- [ ] 面包屑导航是否正确
- [ ] 页面整体布局是否完整

### 2. 数据显示检查
- [ ] **表格数据**：
  - [ ] 数据是否正确加载（不是"暂无数据"）
  - [ ] 字段值是否正确显示（不是[object Object]）
  - [ ] 时间格式是否正确
  - [ ] 状态字段是否显示中文标签而非英文值
  - [ ] 数字字段格式是否正确
- [ ] **统计面板**（如果存在）：
  - [ ] 统计数据是否正确
  - [ ] 各项统计是否合理
- [ ] **分页**：
  - [ ] 总记录数显示是否正确
  - [ ] 分页控件是否正常工作
  - [ ] 每页条数选择是否生效

### 3. 搜索和筛选功能检查
- [ ] **搜索框**：
  - [ ] 输入框是否可用
  - [ ] 搜索按钮是否响应
  - [ ] 搜索结果是否正确
- [ ] **筛选下拉框**：
  - [ ] 下拉框是否可以正常展开
  - [ ] 选项列表是否正确加载
  - [ ] 选择选项后筛选是否生效
  - [ ] 清空筛选是否正常工作
- [ ] **日期选择器**（如果存在）：
  - [ ] 日期选择器是否正常打开
  - [ ] 选择日期后筛选是否生效

### 4. 操作按钮功能检查
- [ ] **顶部操作栏**：
  - [ ] 添加按钮是否可点击且正常工作
  - [ ] 导出按钮是否可点击且正常工作
  - [ ] 刷新按钮是否正常工作
  - [ ] 批量操作按钮是否正常显示和隐藏
- [ ] **行操作按钮**：
  - [ ] 编辑按钮是否可点击
  - [ ] 删除按钮是否可点击
  - [ ] 其他业务按钮是否正常显示和工作
- [ ] **批量操作**：
  - [ ] 选择框是否可以正常选择
  - [ ] 全选/取消全选是否正常
  - [ ] 批量操作按钮是否根据选择状态显示

### 5. 表单功能检查（添加/编辑对话框）
- [ ] **对话框打开**：
  - [ ] 点击添加/编辑按钮是否正常打开对话框
  - [ ] 对话框标题是否正确
  - [ ] 表单字段是否正确显示
- [ ] **表单组件**：
  - [ ] 输入框是否可以正常输入
  - [ ] 下拉选择框是否可以正常选择
  - [ ] 日期选择器是否正常工作
  - [ ] 文件上传组件是否正常工作
- [ ] **表单验证**：
  - [ ] 必填字段验证是否生效
  - [ ] 格式验证是否正确
  - [ ] 错误提示是否清晰
- [ ] **提交功能**：
  - [ ] 提交按钮是否可点击
  - [ ] 提交后是否有正确的成功提示
  - [ ] 数据是否正确保存并刷新列表

### 6. 错误处理检查
- [ ] **网络错误**：
  - [ ] 网络中断时是否有合适提示
  - [ ] API错误是否有用户友好的提示
- [ ] **数据错误**：
  - [ ] 空数据状态显示是否合理
  - [ ] 异常数据是否有容错处理

### 7. 用户体验检查
- [ ] **加载状态**：
  - [ ] 数据加载时是否有Loading动画
  - [ ] 长时间操作是否有进度提示
- [ ] **响应速度**：
  - [ ] 页面切换是否流畅
  - [ ] 数据刷新是否及时
- [ ] **界面细节**：
  - [ ] 文字是否有截断处理（tooltip）
  - [ ] 颜色和样式是否统一
  - [ ] 图标是否正确显示

## 🔧 MCP Browser Testing 具体操作流程

### 步骤1: 导航到页面
```javascript
// 导航到指定页面
await puppeteer_navigate({ url: "目标URL" });
```

### 步骤2: 等待页面完全加载
```javascript
await puppeteer_evaluate({
  script: `
    // 等待页面加载完成
    await new Promise(resolve => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
      }
    });
    
    // 等待可能的异步数据加载
    await new Promise(resolve => setTimeout(resolve, 2000));
  `
});
```

### 步骤3: 全屏截图记录
```javascript
await puppeteer_screenshot({ 
  name: "页面名称_初始状态",
  height: 800,
  width: 1200 
});
```

### 步骤4: 检查数据显示
```javascript
await puppeteer_evaluate({
  script: `
    // 检查是否有 [object Object] 显示
    const objectTexts = Array.from(document.querySelectorAll('*'))
      .filter(el => el.innerText && el.innerText.includes('[object Object]'));
    
    // 检查是否有"暂无数据"显示  
    const noDataTexts = Array.from(document.querySelectorAll('*'))
      .filter(el => el.innerText && el.innerText.includes('暂无数据'));
    
    // 检查表格数据
    const tableRows = document.querySelectorAll('tbody tr');
    
    console.log('发现[object Object]:', objectTexts.length);
    console.log('发现"暂无数据":', noDataTexts.length);
    console.log('表格行数:', tableRows.length);
    
    return {
      hasObjectDisplay: objectTexts.length > 0,
      hasNoDataDisplay: noDataTexts.length > 0,
      tableRowCount: tableRows.length,
      objectElements: objectTexts.map(el => el.innerText).slice(0, 5)
    };
  `
});
```

### 步骤5: 测试筛选功能
```javascript
// 查找并测试下拉筛选框
await puppeteer_evaluate({
  script: `
    // 查找状态筛选下拉框
    const statusSelect = document.querySelector('[placeholder="选择状态"], [placeholder*="状态"]');
    if (statusSelect) {
      statusSelect.click();
      // 等待下拉选项加载
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 检查是否有选项
      const options = document.querySelectorAll('.el-select-dropdown .el-select-dropdown__item');
      console.log('下拉选项数量:', options.length);
      return { hasDropdown: true, optionCount: options.length };
    }
    return { hasDropdown: false };
  `
});
```

### 步骤6: 测试搜索功能
```javascript
// 测试搜索功能
await puppeteer_fill({ selector: 'input[placeholder*="搜索"], input[placeholder*="名称"]', value: 'test' });
await puppeteer_click({ selector: 'button[type="submit"], .el-button--primary' });

// 等待搜索结果
await new Promise(resolve => setTimeout(resolve, 1000));
await puppeteer_screenshot({ name: "页面名称_搜索结果" });
```

### 步骤7: 测试添加功能
```javascript
// 点击添加按钮
await puppeteer_click({ selector: 'button:contains("添加"), .el-button--primary:contains("添加")' });

// 等待对话框打开
await new Promise(resolve => setTimeout(resolve, 500));
await puppeteer_screenshot({ name: "页面名称_添加对话框" });

// 检查表单字段
await puppeteer_evaluate({
  script: `
    const dialog = document.querySelector('.el-dialog');
    if (dialog && dialog.style.display !== 'none') {
      const inputs = dialog.querySelectorAll('input, select, textarea');
      console.log('表单字段数量:', inputs.length);
      return { hasDialog: true, fieldCount: inputs.length };
    }
    return { hasDialog: false };
  `
});
```

## 📊 测试结果记录模板

### 页面基本信息
- **页面名称**: 
- **测试URL**: 
- **测试时间**: 
- **预期功能**:

### 问题发现记录
```
严重问题:
1. [问题描述] - [截图文件名]
2. [问题描述] - [截图文件名]

中等问题:
1. [问题描述] - [截图文件名]
2. [问题描述] - [截图文件名]

轻微问题:
1. [问题描述] - [截图文件名]
2. [问题描述] - [截图文件名]
```

### 功能状态统计
- ✅ 正常功能: X项
- ⚠️ 部分异常: X项  
- ❌ 完全失效: X项
- 🔍 需要进一步验证: X项

## 🎯 每个页面的详细测试流程

### 测试执行标准
1. **彻底性**: 每个可见元素都要检查
2. **细致性**: 关注数据格式、显示效果、交互反馈
3. **记录性**: 截图记录每个状态，详细记录问题
4. **分类性**: 按严重程度分类问题
5. **可重现性**: 记录重现步骤

### 测试完成标准  
- [ ] 所有功能点都经过测试
- [ ] 所有问题都有截图证据
- [ ] 问题都有详细描述和重现步骤
- [ ] 按严重程度进行了分类
- [ ] 生成了详细的测试报告

---

**使用此规范确保不遗漏任何问题，提供用户可信赖的测试结果**
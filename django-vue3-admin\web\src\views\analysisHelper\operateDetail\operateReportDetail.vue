<template>
	<div style="background-color: #fff">
		<!-- 将整个内容分成多个部分，以便分页导出 -->
		<!-- 同类排行榜部分 -->
		<div v-if="sameAccountWciRank[0] !== undefined">
			<div id="sub-title-1">
				<div class="wx-top">
					<div class="top-title">{{ props.title.split(' ')[0] }} {{ props.title.split(' ')[1].replace('月', '') }}月同类微信公众号运营报表</div>
					<!-- <div style="font-size: 16px; margin-top: 20px">统计时间：{{ reportDate }}</div>
					<div style="font-size: 14px; float: right; color: #4678ab">汇远轻媒出品</div> -->
				</div>
			</div>
			<!-- 同类账号AI分析报告 - 移到排行榜之前 -->
			<div v-if="sameAccountAiReport" class="report-section">
				<div class="wx-title-wrapper"><div class="wx-titletop">同类账号AI分析报告</div></div>
				<div id="same-ai-report">
					<div class="markdown-report-content">
						<MarkdownRenderer :content="sameAccountAiReport" />
					</div>
				</div>
			</div>
			<div v-for="(table, index) in chunkedSameAccountWci" :key="index">
				<div class="report-section" :id="`sub-operate-9-${index + 1}`">
					<div class="wx-title-wrapper" v-if="index === 0"><div class="wx-titletop">同类账号WCI排行榜</div></div>
					<div class="wx-title2" v-if="index === 0">指数排行榜</div>
					<div class="wx-table-wrapper">
						<el-table :data="table" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }" :cell-style="{ height: '55px' }">
							<el-table-column label="排序" align="center" min-width="85px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ scope.row.rank }}</span>
										<!-- 隐藏排序的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.rank_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.rank_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.rank_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.rank_compare_trend == 1 ? 'green' : 'red' }">{{
												scope.row.rank_compare_count
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="250px" />
							<el-table-column label="发文量" align="center" prop="publish_count" min-width="100px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.publish_count) }}</span>
										<!-- 隐藏发文量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.publish_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.publish_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.publish_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.publish_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.publish_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="阅读量" align="center" prop="read_count" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.read_count) }}</span>
										<!-- 隐藏阅读量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.read_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.read_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.read_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.read_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.read_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="头条阅读量" align="center" prop="headline_read_count" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.headline_read_count) }}</span>
										<!-- 隐藏头条阅读量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.headline_read_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.headline_read_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.headline_read_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.headline_read_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.headline_read_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="点赞量" align="center" prop="likes" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.like_count) }}</span>
										<!-- 隐藏点赞量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.like_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.like_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.like_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.like_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.like_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="推荐量" align="center" prop="viewings" min-width="100px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.viewing_count) }}</span>
										<!-- 隐藏推荐量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.viewing_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.viewing_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.viewing_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.viewing_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.viewing_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="分享量" align="center" prop="forward_count" min-width="100px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.forward_count) }}</span>
										<!-- 隐藏分享量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.forward_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.forward_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.forward_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.forward_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.forward_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="传播指数" align="center" prop="wci" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.wci) }}</span>
										<!-- 隐藏传播指数的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.wci_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.wci_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.wci_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.wci_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.wci_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</div>
		</div>
		<div id="sub-title-2">
			<div class="wx-top">
				<!-- 2025-03 变为2025年03 -->
				<div class="top-title">{{ props.title.split(' ')[0] }} {{ props.title.split(' ')[1].replace('月', '') }}月下级微信公众号运营报告</div>
				<!-- <div style="font-size: 16px; margin-top: 20px">统计时间：{{ reportDate }}</div>
				<div style="font-size: 14px; float: right; color: #4678ab">汇远轻媒出品</div> -->
			</div>
		</div>
		<div v-if="wciDesc[0] === undefined">
			<div style="width: 100%; margin: 10px auto; font-size: 26px; text-align: center">无绑定账号</div>
		</div>
		<div v-else>
			<!-- AI分析报告模块 - 移到账号分析之前 -->
			<div v-if="aiReportContent" class="report-section">
				<div class="wx-title-wrapper"><div class="wx-title1">AI分析报告</div></div>

				<div id="sub-ai-report" class="markdown-report-content">
					<MarkdownRenderer :content="aiReportContent" />
				</div>
			</div>
			<div v-for="(table, index) in chunkedWci" :key="index">
				<div class="report-section" :id="`sub-operatetable-${index + 1}`">
					<div class="wx-title-wrapper" v-if="index === 0"><div class="wx-title1">账号分析</div></div>
					<div class="wx-title2" v-if="index === 0">指数排行榜</div>
					<div class="wx-table-wrapper">
						<el-table :data="table" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }" :cell-style="{ height: '55px' }">
							<el-table-column label="排序" align="center" min-width="85px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ scope.row.rank }}</span>
										<!-- 隐藏排序的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.rank_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.rank_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.rank_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.rank_compare_trend == 1 ? 'green' : 'red' }">{{
												scope.row.rank_compare_count
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="250px" />
							<el-table-column label="发文量" align="center" prop="publish_count" min-width="100px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.publish_count) }}</span>
										<!-- 隐藏发文量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.publish_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.publish_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.publish_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.publish_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.publish_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="阅读量" align="center" prop="read_count" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.read_count) }}</span>
										<!-- 隐藏阅读量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.read_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.read_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.read_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.read_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.read_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="头条阅读量" align="center" prop="headline_read_count" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.headline_read_count) }}</span>
										<!-- 隐藏头条阅读量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.headline_read_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.headline_read_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.headline_read_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.headline_read_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.headline_read_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="点赞量" align="center" prop="likes" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.like_count) }}</span>
										<!-- 隐藏点赞量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.like_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.like_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.like_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.like_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.like_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="推荐量" align="center" prop="viewings" min-width="100px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.viewing_count) }}</span>
										<!-- 隐藏推荐量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.viewing_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.viewing_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.viewing_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.viewing_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.viewing_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="分享量" align="center" prop="forward_count" min-width="100px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.forward_count) }}</span>
										<!-- 隐藏分享量的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.forward_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.forward_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.forward_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.forward_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.forward_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
							<el-table-column label="传播指数" align="center" prop="wci" min-width="130px">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.wci) }}</span>
										<!-- 隐藏传播指数的上下箭头和变化数值 -->
										<!-- <div style="margin-left: 10px; display: flex" v-if="scope.row.wci_compare_trend != 0">
											<el-icon class="green-icon" v-if="scope.row.wci_compare_trend == 1"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.wci_compare_trend == -1"><Bottom /></el-icon>
											<span style="margin-left: 5px" :style="{ color: scope.row.wci_compare_trend == 1 ? 'green' : 'red' }">{{
												tranNum(scope.row.wci_compare_count)
											}}</span>
										</div> -->
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</div>
			<!-- 内容传播部分 -->
			<div class="report-section" v-if="wciDesc[0] !== undefined">
				<div id="sub-operate-3" class="report-section">
					<div class="wx-title-wrapper"><div class="wx-title1">内容传播</div></div>
					<div class="wx-title2">发文量 TOP3</div>
					<div class="wx-table-wrapper">
						<el-table
							:data="articleCountDesc.slice(0, 3)"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" width="80">
								<template #default="scope">
									<span>{{ scope.$index + 1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="200px" />
							<el-table-column label="发文量" align="center" prop="publish_count" min-width="100px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.publish_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="较上月" align="center" prop="compare_count" min-width="120px">
								<template #default="scope">
									<span
										>{{ scope.row.compare_trend > 0 ? '' : scope.row.compare_trend < 0 ? '-' : '持平' }} {{ tranNum(scope.row.compare_count) }}</span
									>
								</template>
							</el-table-column>
							<el-table-column label="环比增长率" align="center" prop="compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.compare_rate }}%</span>
								</template>
							</el-table-column>
							<el-table-column label="日均发文量" align="center" prop="daily_publish_count" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.daily_publish_count }}篇</span>
								</template>
							</el-table-column>
							<el-table-column label="活跃天数" align="center" prop="publish_days" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.publish_days }}天</span>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<div class="wx-title2">阅读量Top3</div>
					<div class="wx-table-wrapper">
						<el-table
							:data="articleReadsDesc.slice(0, 3)"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" width="80">
								<template #default="scope">
									<span>{{ scope.$index + 1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="200px" />
							<el-table-column label="阅读量" align="center" prop="read_count" min-width="120px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.read_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="较上月" align="center" prop="compare_count" min-width="120px">
								<template #default="scope">
									<span
										>{{ scope.row.compare_trend > 0 ? '' : scope.row.compare_trend < 0 ? '-' : '持平' }}
										{{ tranNum(Math.abs(scope.row.compare_count)) }}</span
									>
								</template>
							</el-table-column>
							<el-table-column label="环比增长率" align="center" prop="compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.compare_rate }}%</span>
								</template>
							</el-table-column>
							<el-table-column label="日均阅读" align="center" prop="daily_read_count" min-width="120px" />
							<el-table-column label="篇均阅读" align="center" prop="article_read_count" min-width="120px" />
						</el-table>
					</div>
				</div>
				<div id="sub-operate-4" class="report-section">
					<div class="wx-title2">点赞量Top3</div>
					<div class="wx-table-wrapper">
						<el-table
							:data="articleLikesDesc.slice(0, 3)"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" width="80">
								<template #default="scope">
									<span>{{ scope.$index + 1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="200px" />
							<el-table-column label="点赞量" align="center" prop="like_count" min-width="120px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.like_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="较上月" align="center" prop="compare_count" min-width="120px">
								<template #default="scope">
									<span
										>{{ scope.row.compare_trend > 0 ? '' : scope.row.compare_trend < 0 ? '-' : '持平' }}
										{{ tranNum(Math.abs(scope.row.compare_count)) }}</span
									>
								</template>
							</el-table-column>
							<el-table-column label="环比增长率" align="center" prop="compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.compare_rate }}%</span>
								</template>
							</el-table-column>
							<el-table-column label="篇均点赞" align="center" prop="article_like_count" min-width="120px" />
							<el-table-column label="篇均环比增长率" align="center" prop="article_like_compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.article_like_compare_rate }}%</span>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<div class="wx-title2">推荐量Top3</div>
					<div class="wx-table-wrapper">
						<el-table
							:data="articleViewingsDesc.slice(0, 3)"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" width="80">
								<template #default="scope">
									<span>{{ scope.$index + 1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="200px" />
							<el-table-column label="推荐量" align="center" prop="viewing_count" min-width="120px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.viewing_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="较上月" align="center" prop="compare_count" min-width="120px">
								<template #default="scope">
									<span
										>{{ scope.row.compare_trend > 0 ? '' : scope.row.compare_trend < 0 ? '-' : '持平' }}
										{{ tranNum(Math.abs(scope.row.compare_count)) }}</span
									>
								</template>
							</el-table-column>
							<el-table-column label="环比增长率" align="center" prop="compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.compare_rate }}%</span>
								</template>
							</el-table-column>
							<el-table-column label="篇均推荐" align="center" prop="article_viewing_count" min-width="120px" />
							<el-table-column label="篇均环比增长率" align="center" prop="article_viewing_compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.article_viewing_compare_rate }}%</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div v-if="articleForwardDesc.length < 3">
						<div class="wx-title2">分享量Top3</div>
						<div class="wx-table-wrapper">
							<el-table
								:data="articleForwardDesc.slice(0, 3)"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="排名" align="center" width="80">
									<template #default="scope">
										<span>{{ scope.$index + 1 }}</span>
									</template>
								</el-table-column>
								<el-table-column label="账号名称" align="center" prop="account_name" min-width="200px" />
								<el-table-column label="分享量" align="center" prop="forward_count" min-width="120px">
									<template #default="scope">
										<span>{{ tranNum(scope.row.forward_count) }}</span>
									</template>
								</el-table-column>
								<el-table-column label="较上月" align="center" prop="compare_count" min-width="120px">
									<template #default="scope">
										<span
											>{{ scope.row.compare_trend > 0 ? '' : scope.row.compare_trend < 0 ? '-' : '持平' }}
											{{ tranNum(Math.abs(scope.row.compare_count)) }}</span
										>
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="compare_rate" min-width="120px">
									<template #default="scope">
										<span>{{ scope.row.compare_rate }}%</span>
									</template>
								</el-table-column>
								<el-table-column label="篇均分享" align="center" prop="article_forward_count" min-width="120px" />
								<el-table-column label="篇均环比增长率" align="center" prop="article_forward_compare_rate" min-width="120px">
									<template #default="scope">
										<span>{{ scope.row.article_forward_compare_rate }}%</span>
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<div id="sub-operate-10" class="report-section" v-if="articleForwardDesc.length > 2">
					<div class="wx-title2">分享量Top3</div>
					<div class="wx-table-wrapper">
						<el-table
							:data="articleForwardDesc.slice(0, 3)"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" width="80">
								<template #default="scope">
									<span>{{ scope.$index + 1 }}</span>
								</template>
							</el-table-column>
							<el-table-column label="账号名称" align="center" prop="account_name" min-width="200px" />
							<el-table-column label="分享量" align="center" prop="forward_count" min-width="120px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.forward_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="较上月" align="center" prop="compare_count" min-width="120px">
								<template #default="scope">
									<span
										>{{ scope.row.compare_trend > 0 ? '' : scope.row.compare_trend < 0 ? '-' : '持平' }}
										{{ tranNum(Math.abs(scope.row.compare_count)) }}</span
									>
								</template>
							</el-table-column>
							<el-table-column label="环比增长率" align="center" prop="compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.compare_rate }}%</span>
								</template>
							</el-table-column>
							<el-table-column label="篇均分享" align="center" prop="article_forward_count" min-width="120px" />
							<el-table-column label="篇均环比增长率" align="center" prop="article_forward_compare_rate" min-width="120px">
								<template #default="scope">
									<span>{{ scope.row.article_forward_compare_rate }}%</span>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</div>

			<!-- 具体文章部分 -->
			<div id="sub-operate-5" class="report-section">
				<div class="wx-title-wrapper"><div class="wx-title1">具体文章</div></div>
				<div class="wx-title2">阅读量Top 10</div>
				<div class="wx-table-wrapper">
					<el-table
						:data="articleReadTop10"
						border
						:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
						:cell-style="{ height: '45px' }"
					>
						<el-table-column label="文章名称" align="center" prop="article_title" min-width="400px" />
						<el-table-column label="阅读量" align="center" prop="article_read_count" min-width="80px">
							<template #default="scope">
								<span>{{ tranNum(scope.row.article_read_count) }}</span>
							</template>
						</el-table-column>
						<el-table-column label="来源账号" align="center" prop="account_name" min-width="150px" />
						<el-table-column label="发布时间" align="center" prop="article_publish_time" min-width="100px">
							<!-- <template #default="scope">
									<span>{{ formatTime(scope.row.article_publish_time) }}</span>
								</template> -->
						</el-table-column>
					</el-table>
				</div>
			</div>
			<div id="sub-operate-6" class="report-section">
				<div class="wx-title2">点赞量Top 10</div>
				<div class="wx-table-wrapper">
					<el-table
						:data="articleLikeTop10"
						border
						:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
						:cell-style="{ height: '45px' }"
					>
						<el-table-column label="文章名称" align="center" prop="article_title" min-width="400px" />
						<el-table-column label="点赞量" align="center" prop="article_like_count" min-width="80px">
							<template #default="scope">
								<span>{{ tranNum(scope.row.article_like_count) }}</span>
							</template>
						</el-table-column>
						<el-table-column label="来源账号" align="center" prop="account_name" min-width="150px" />
						<el-table-column label="发布时间" align="center" prop="article_publish_time" min-width="100px">
							<!-- <template #default="scope">
									<span>{{ formatTime(scope.row.article_publish_time) }}</span>
								</template> -->
						</el-table-column>
					</el-table>
				</div>
			</div>
			<div id="sub-operate-7" class="report-section">
				<div class="wx-title2">推荐量Top 10</div>
				<div class="wx-table-wrapper">
					<el-table
						:data="articleViewTop10"
						border
						:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
						:cell-style="{ height: '45px' }"
					>
						<el-table-column label="文章名称" align="center" prop="article_title" min-width="400px" />
						<el-table-column label="推荐量" align="center" prop="article_viewing_count" min-width="80px">
							<template #default="scope">
								<span>{{ tranNum(scope.row.article_viewing_count) }}</span>
							</template>
						</el-table-column>
						<el-table-column label="来源账号" align="center" prop="account_name" min-width="150px" />
						<el-table-column label="发布时间" align="center" prop="article_publish_time" min-width="100px">
							<!-- <template #default="scope">
									<span>{{ formatTime(scope.row.article_publish_time) }}</span>
								</template> -->
						</el-table-column>
					</el-table>
				</div>
			</div>
			<div id="sub-operate-8" class="report-section">
				<div class="wx-title2">分享量Top 10</div>
				<div class="wx-table-wrapper">
					<el-table
						:data="articleShareTop10"
						border
						:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
						:cell-style="{ height: '45px' }"
					>
						<el-table-column label="文章名称" align="center" prop="article_title" min-width="400px" />
						<el-table-column label="分享量" align="center" prop="article_forward_count" min-width="80px">
							<template #default="scope">
								<span>{{ tranNum(scope.row.article_forward_count) }}</span>
							</template>
						</el-table-column>
						<el-table-column label="来源账号" align="center" prop="account_name" min-width="150px" />
						<el-table-column label="发布时间" align="center" prop="article_publish_time" min-width="100px">
							<!-- <template #default="scope">
									<span>{{ formatTime(scope.row.article_publish_time) }}</span>
								</template> -->
						</el-table-column>
					</el-table>
				</div>
			</div>
		</div>

		<div v-if="noUpdateAccount[0] !== undefined">
			<div id="sub-title-3">
				<div class="wx-top" style="text-align: center">
					<div class="top-title">超一个月未更新微信公众号</div>
					<!-- <div style="font-size: 14px; float: right; color: #4678ab">汇远轻媒出品</div> -->
				</div>
			</div>
			<div v-for="(table, index) in chunkedNoUpdateAccount" :key="index">
				<div class="report-section" :id="`no-update-${index + 1}`">
					<div class="wx-title-wrapper" v-if="index === 0">
						<div class="wx-title-top">{{ reportMonth }}及更久未更新微信公众号列表</div>
					</div>
					<div class="wx-table-wrapper">
						<el-table :data="table" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }" :cell-style="{ height: '45px' }">
							<el-table-column label="序号" width="80" align="center">
								<template #default="scope">
									<span>{{ scope.$index + 1 + (index === 0 ? 0 : 9 + (index - 1) * 12) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="账号" align="center" prop="account_name" />
							<el-table-column label="不更新天数" align="center" prop="no_update_days" width="150">
								<template #default="scope"> {{ scope.row.no_update_days }}天 </template>
							</el-table-column>
							<el-table-column label="最后发文时间" align="center" prop="last_update_date" />
						</el-table>
					</div>
					<div
						v-if="index === chunkedNoUpdateAccount.length - 1"
						style="text-align: right; font-size: 16px; color: #000; margin: 20px 30px; padding-bottom: 20px"
					>
						<p>注：不更新天数计算，截止日期为{{ props.date.end_date }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { tranNumber } from '/@/utils/tranNum';
import MarkdownRenderer from '/@/components/MarkdownRenderer/index.vue';

const props = defineProps({
	reportId: {
		type: [String, Number],
		default: '',
	},
	subReportData: {
		type: Object,
		default: () => ({}),
	},
	title: {
		type: String,
		default: '',
	},
	date: {
		type: Object,
		default: () => ({}),
	},
	sameAccount: {
		type: Object,
		default: () => ({}),
	},
	noUpdateAccount: {
		type: Array,
		default: () => [],
	},
	createTime: {
		type: String,
		default: '',
	},
});

// 数据定义
const pageLoading = ref(true);
const reportDate = ref(null);
const reportMonth = ref('');
const wciDesc = ref([{}, {}, {}]);
const wciList = ref([]);
const articleCount = ref(null);
const articleCountDesc = ref([{}, {}, {}, {}]);
const articleCountleast = ref({});
const articleReadsDesc = ref([{}, {}, {}, {}]);
const articleReadsleast = ref({});

const articleLikesDesc = ref([{}, {}, {}, {}]);
const articleLikesleast = ref({});

const articleViewingsDesc = ref([{}, {}, {}, {}]);
const articleViewingsleast = ref({});

const articleForwardDesc = ref([{}, {}, {}, {}]);
const articleForwardleast = ref({});

const articleReadTop10 = ref([]);
const articleLikeTop10 = ref([]);
const articleViewTop10 = ref([]);
const articleShareTop10 = ref([]);
const sameAccountWciRank = ref([]);
const sameAccountAiReport = ref('');
const noUpdateAccount = ref([]);
const createTime = ref('');
const aiReportContent = ref('');

createTime.value = props.createTime ? props.createTime.split(' ')[0] : '';
noUpdateAccount.value = props.noUpdateAccount;

// 将同类账号数据按每10个分割成一个新数组，仅用于导出
const chunkedSameAccountWci = computed(() => {
	const tables = [];
	const list = sameAccountWciRank.value || [];

	// 先截取前8个
	if (list.length > 0) {
		const firstChunk = list.slice(0, 9);
		tables.push(firstChunk);
	}

	// 从第8个开始，每次截取12个
	for (let i = 9; i < list.length; i += 12) {
		const chunk = list.slice(i, i + 12);
		if (chunk.length > 0) {
			tables.push(chunk);
		}
	}
	return tables;
});

// 将数据按每10个分割成一个新数组，仅用于导出
const chunkedWci = computed(() => {
	const tables = [];
	const list = wciList.value || [];

	// 先截取前8个
	if (list.length > 0) {
		const firstChunk = list.slice(0, 9);
		tables.push(firstChunk);
	}

	// 从第8个开始，每次截取12个
	for (let i = 9; i < list.length; i += 12) {
		const chunk = list.slice(i, i + 12);
		if (chunk.length > 0) {
			tables.push(chunk);
		}
	}
	return tables;
});

// 将不更新账号按每10个分割成一个新数组，仅用于导出
const chunkedNoUpdateAccount = computed(() => {
	const tables = [];
	const list = noUpdateAccount.value || [];

	// 先截取前9个
	if (list.length > 0) {
		const firstChunk = list.slice(0, 9);
		tables.push(firstChunk);
	}

	// 从第9个开始，每次截取12个
	for (let i = 9; i < list.length; i += 12) {
		const chunk = list.slice(i, i + 12);
		if (chunk.length > 0) {
			tables.push(chunk);
		}
	}
	return tables;
});

// 数字转换函数
const tranNum = (num) => {
	if (num !== undefined && num !== null && num !== '') {
		return tranNumber(num, 1);
	}
	return '';
};

// 处理下级运营报告数据
const processSubReportData = (data) => {
	pageLoading.value = true;
	try {
		if (!data) {
			console.error('下级运营报告数据为空');
			return;
		}

		// 设置日期范围
		reportDate.value = `${props.date.start_date} 至 ${props.date.end_date}`;

		// 从月份中提取信息
		const startDate = new Date(props.date.start_date);
		reportMonth.value = `${startDate.getFullYear()}年${startDate.getMonth() + 1}月`;

		// 只有当sub_data存在时再处理下级数据
		if (data) {
			// 设置指数榜数据
			wciDesc.value = data.account_wci || [];
			wciList.value = data.wci_rank || [];

			// 设置发文量数据
			articleCount.value = data.publish?.total;
			articleCountDesc.value = data.publish?.publish_top3 || [];
			articleCountleast.value = data.publish?.publish_least || {};

			// 设置阅读量数据
			articleReadsDesc.value = data.read_count_stats?.read_top3 || [];
			articleReadsleast.value = data.read_count_stats?.read_least || {};
			// 设置点赞量数据
			articleLikesDesc.value = data.like_count_stats?.like_top3 || [];
			articleLikesleast.value = data.like_count_stats?.like_least || {};

			// 设置推荐量数据
			articleViewingsDesc.value = data.viewing_count_stats?.viewing_top3 || [];
			articleViewingsleast.value = data.viewing_count_stats?.viewing_least || {};

			// 设置分享量数据
			articleForwardDesc.value = data.forward_count_stats?.forward_top3 || [];
			articleForwardleast.value = data.forward_count_stats?.forward_least || {};

			// 设置阅读量Top 10数据
			articleReadTop10.value = data.read_top10 || [];
			// 设置点赞量Top 10数据
			articleLikeTop10.value = data.like_top10 || [];
			// 设置推荐量Top 10数据
			articleViewTop10.value = data.viewing_top10 || [];
			// 设置分享量Top 10数据
			articleShareTop10.value = data.forward_top10 || [];

			// 设置AI报告内容
			aiReportContent.value = data.ai_report;
		}
	} catch (error) {
		console.error('处理下级运营报告数据失败:', error);
	} finally {
		pageLoading.value = false;
	}
};
// 定义模块ID列表供父组件使用
const moduleIds = computed(() => {
	const modules = [];

	// 同类账号AI报告（优先显示）
	if (sameAccountAiReport.value) {
		modules.push({
			id: 'same-ai-report',
			name: '同类账号AI分析报告',
			type: 'markdown',
			markdownContent: sameAccountAiReport.value,
			category: 'same',
		});
	}

	// 同类排行榜模块（合并为一个模块选项）
	if (chunkedSameAccountWci.value && chunkedSameAccountWci.value.length > 0) {
		// 收集所有表格ID
		const sameTableIds = [];
		for (let i = 0; i < chunkedSameAccountWci.value.length; i++) {
			if (chunkedSameAccountWci.value[i] && chunkedSameAccountWci.value[i].length > 0) {
				sameTableIds.push(`sub-same-operatetable-${i + 1}`);
			}
		}

		if (sameTableIds.length > 0) {
			modules.push({
				id: sameTableIds, // 传递所有表格ID的数组
				name: '同类微信公众号WCI排行榜',
				type: 'html',
				category: 'same',
				multipleIds: true, // 标记为多ID模块
			});
		}
	}

	// 下级账号AI分析报告（优先显示）
	if (aiReportContent.value) {
		modules.push({
			id: 'sub-ai-report',
			name: '下级账号AI分析报告',
			type: 'markdown',
			markdownContent: aiReportContent.value,
			category: 'ai',
		});
	}

	// 指数排行榜表格模块（合并为一个模块选项）
	if (chunkedWci.value && chunkedWci.value.length > 0) {
		// 收集所有表格ID
		const tableIds = [];
		for (let i = 0; i < chunkedWci.value.length; i++) {
			if (chunkedWci.value[i] && chunkedWci.value[i].length > 0) {
				tableIds.push(`sub-operatetable-${i + 1}`);
			}
		}

		if (tableIds.length > 0) {
			modules.push({
				id: tableIds, // 传递所有表格ID的数组
				name: '下级账号指数排行榜',
				type: 'html',
				category: 'ranking',
				multipleIds: true, // 标记为多ID模块
			});
		}
	}

	// 内容传播分析模块
	if (wciDesc.value && wciDesc.value.length > 0) {
		modules.push({
			id: 'sub-operate-3',
			name: '发文量/阅读量分析',
			type: 'html',
			category: 'content',
		});

		modules.push({
			id: 'sub-operate-4',
			name: '点赞量/推荐量/分享量分析',
			type: 'html',
			category: 'content',
		});
		if (articleForwardDesc.value.length > 2) {
			modules.push({
				id: 'sub-operate-10',
				name: '分享量分析',
				type: 'html',
				category: 'content',
			});
		}
	}

	// Top 10 文章模块
	modules.push(
		{
			id: 'sub-operate-5',
			name: '阅读量Top 10',
			type: 'html',
			category: 'article',
		},
		{
			id: 'sub-operate-6',
			name: '点赞量Top 10',
			type: 'html',
			category: 'article',
		},
		{
			id: 'sub-operate-7',
			name: '推荐量Top 10',
			type: 'html',
			category: 'article',
		},
		{
			id: 'sub-operate-8',
			name: '分享量Top 10',
			type: 'html',
			category: 'article',
		}
	);

	// 未更新账号模块
	if (noUpdateAccount.value && noUpdateAccount.value.length > 0) {
		if (chunkedNoUpdateAccount.value && chunkedNoUpdateAccount.value.length > 0) {
			// 收集所有不更新账号表格ID
			const noUpdateTableIds = [];
			for (let i = 0; i < chunkedNoUpdateAccount.value.length; i++) {
				if (chunkedNoUpdateAccount.value[i] && chunkedNoUpdateAccount.value[i].length > 0) {
					noUpdateTableIds.push(`no-update-${i + 1}`);
				}
			}

			if (noUpdateTableIds.length > 0) {
				modules.push({
					id: noUpdateTableIds, // 传递所有不更新账号表格ID的数组
					name: '超一个月未更新账号列表',
					type: 'html',
					category: 'other',
					multipleIds: true, // 标记为多ID模块
				});
			}
		}
	}

	return modules;
});

// 暴露模块ID列表和分页ID获取方法
defineExpose({
	moduleIds,
});
// 监听subReportData变化
watch(
	() => props.sameAccount,
	(newVal) => {
		if (newVal) {
			sameAccountWciRank.value = newVal.wci_rank;
			sameAccountAiReport.value = newVal.ai_report;
		}
	},
	{ immediate: true, deep: true }
);
watch(
	() => props.subReportData,
	(newVal) => {
		if (newVal) {
			processSubReportData(newVal);
		}
	},
	{ immediate: true, deep: true }
);
</script>

<style scoped>
.wx-top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	overflow: hidden;
	height: 200px;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}
.wx-title-wrapper {
	width: 100%;
	/* padding: 20px; */
}
.wx-title1 {
	font-size: 30px;
	width: 20%;
	margin: 0 auto 10px;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 10px;
	line-height: 1.5;
}

.wx-titletop {
	font-size: 30px;
	width: 45%;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	margin: 0 auto 10px;
	padding-bottom: 10px;
	line-height: 1.5;
}
.wx-title-top {
	font-size: 30px;
	width: 55%;
	margin: 0 auto;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 15px;
	line-height: 1.5;
}
.wx-title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	margin: 0px auto 10px;
	text-align: center;
	line-height: 1.5;
}
.wx-title3 {
	font-size: 24px;
	margin: 0 auto;
	text-align: center;
	color: #008ccc;
	font-weight: bold;
	margin-bottom: 15px;
	margin-top: 15px;
	display: block;
	width: 100%;
}
.wx-title4 {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}
.wx-content-wrapper {
	position: relative;
	margin: 0 30px 20px;
	padding: 7px 15px;
	border: #9d9c9a 1px solid;
	overflow: visible;
	line-height: 30px;
}
.wx-content::first-line {
	text-indent: 2em; /* 设置为两个空格的宽度 */
}
.wx-content {
	font-size: 18px;
	line-height: 2;
	white-space: inherit !important;
}
.wx-table-wrapper {
	margin: 20px 30px;
}
.wx-chunked-table {
	margin-bottom: 30px;
}
.wx-table-title {
	font-size: 18px;
	color: #008ccc;
	text-align: center;
	margin-bottom: 15px;
}
.text-bold {
	font-weight: bold;
}
.text-blue {
	color: #00abf9;
}
.green-icon {
	padding-top: 15px;
	color: green;
}
.red-icon {
	padding-top: 15px;
	color: red;
}
:deep(.el-loading-spinner) {
	top: 300px;
}

/* 表格样式优化 */
:deep(.el-table) {
	width: 100% !important;
	table-layout: fixed;
	margin-bottom: 15px;
	border-collapse: separate;
	border-spacing: 0;
}

:deep(.el-table th) {
	font-weight: bold;
	background-color: #f8f8f9;
	color: #000;
	padding: 15px 4px !important;
	height: 45px !important;
	line-height: 2 !important;
}

:deep(.el-table td) {
	padding: 15px 4px !important;
	height: 45px !important;
	line-height: 2 !important;
}

:deep(.el-table__row) {
	height: 45px !important;
}

:deep(.el-table__cell) {
	padding: 15px 4px !important;
	line-height: 1.5 !important;
	vertical-align: middle !important;
	height: 45px !important;
}

:deep(.el-table__header-cell) {
	padding: 15px 0 !important;
	height: 45px !important;
	line-height: 1.5 !important;
}

:deep(.el-table__body),
:deep(.el-table__header) {
	table-layout: fixed !important;
}
:deep(.el-table .cell) {
	color: #000;
	font-size: 18px;
}
.wx-title-top {
	font-size: 30px;
	width: 55%;
	margin: 0 auto;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 15px;
	line-height: 1.5;
}
.btn-wrapper {
	position: fixed;
	right: 20px;
	top: 80px;
	z-index: 1000;
}
.el-row {
	display: block;
}
#export-content {
	background-color: #fff;
}

/* 导出PDF时的样式优化 */
.report-section {
	background-color: #fff;
	text-rendering: optimizeQuality;
	margin-bottom: 30px;
	overflow: visible;
	page-break-inside: avoid;
}

* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

/* AI 分析报告 Markdown 内容样式 */
.markdown-report-content {
	padding: 7px 20px 20px 20px;
	margin: 0 30px 20px;
}
</style> 
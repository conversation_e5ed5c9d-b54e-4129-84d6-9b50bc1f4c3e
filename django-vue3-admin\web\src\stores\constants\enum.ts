export const ENUM_TYPE = {
	// 任务相关
	TASK_TYPE: 'regulate.TaskType', // 任务类型
	TASK_STATUS: 'regulate.TaskStatus', // 任务状态
	DATA_CONFIG_TYPE: 'regulate.DataConfigType', // 校对类型
	EXPORT_STATUS: 'regulate.ExportStatus', // 导出状态
	JUDGE_STATUS: 'regulate.JudgeStatus', // 校对状态
	NOTIFY_CHANNEL_TYPE: 'regulate.NotifyChannelType', // 通知类型
	OPERATION_TYPE: 'regulate.OperationType', // 操作类型
	TARGET_STATUS: 'regulate.TargetStatus', // 目标状态
	TASK_TIME_TYPE: 'regulate.TaskTimeType', // 任务时间类型
	WORD_TYPE: 'regulate.WordType', // 词典类型
	REPORT_STATUS: 'regulate.ReportStatus', // 监管月报生成状态
	SENSITIVITY_LEVEL: 'regulate.SensitivityLevel', // 敏感度

	// 汇远轻媒
	TENANT_MEDIA_PERMISSION: 'hyqm.TenantMediaPermission', // 租户菜单权限
	PLATFORM_TYPE: 'hyqm.PlatformType', // 平台类型
	CUSTOMER_STAGE: 'hyqm.CustomerStage', // 客户阶段
	CUSTOMER_TYPE: 'hyqm.CustomerType', // 客户类型


	// AI校对
	AI_JIAO_ACCOUNT: 'aijiaodui.AccountType', // 爱校对账号类型
	SHOW_TYPE: 'aijiaodui.ShowType', // 爱校对页面展示类型
	FUNCTION_TYPE: 'aijiaodui.FunctionType', // 爱校对校对类型

	// 微信账号相关
	CRAWL_TASK_STATUS: 'wechat_official_account.CrawlTaskStatus',
	CRAWL_TASK_TYPE: 'wechat_official_account.CrawlTaskType',
	ACCOUNT_STATUS: 'wechat_official_account.AccountStatus', // 微信公众号账号状态

	// 校对相关
	DICT_WORD_TYPE: 'regulate_jiaodui.DictWordType', // 词条类型
	CHECK_TYPE: 'regulate_jiaodui.CheckType', // 检查类型
	CORRECT_DICT_WORD_TYPE: 'regulate_jiaodui.CorrectDictWordType', // 正确词条类型
	ENABLE_STATUS: 'regulate_jiaodui.EnableStatus', // 启用状态

	// 运营报表
	OPERATION_REPORT_STATUS: 'operation_report.ReportStatus', // 运营报表生成状态
	OPERATION_REPORT_TYPE: 'operation_report.ReportType', // 运营报表类型
	OPERATION_REPORT_ACCOUNT_LEVEL: 'hyqm.MediaAccountType', // 运营报表账号级别
	// 排行榜
	RANKING_TYPE: 'rank.RankingType', // 排行榜类型
	RANKING_PERIOD: 'rank.RankingPeriod', // 排行榜周期
	RANKING_STATUS: 'rank.RankingStatus', // 排行榜状态

	// 稿件相关
	MANUSCRIPT_STATUS: 'manuscript.ManuscriptStatus', // 稿件状态
	// 审核状态
	MANUSCRIPT_AUDIT_STATUS: 'manuscript.AuditStatus', // 审核状态
	// 审核结果
	MANUSCRIPT_AUDIT_RESULT: 'manuscript.AuditResult',
};

// 值到颜色的映射关系
export const VALUE_TO_COLOR_MAPPING: any = {
	0: 'info',
	1: 'success',
	2: 'warning',
	3: 'danger',
	true: 'success',
	false: 'danger',
};

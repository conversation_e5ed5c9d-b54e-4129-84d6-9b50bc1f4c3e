import { request } from '/@/utils/service';
import {
	type CorrectionTargetInfo as TaskTargetInfo,
	type CorrectionTaskInfo,
	type TargetQueryParams,
	type CreateTargetParams,
	type ApiResponse,
	getTargetList,
	getTaskAll,
	createTargets,
} from '../proofread_task/api';

const targetPrefix = '/api/regulate/targets/';

// 为了确保类型定义包含status属性，重新定义CorrectionTargetInfo
export interface CorrectionTargetInfo extends TaskTargetInfo {
	status: number;
}

// 获取校对目标详情
export async function getTargetDetail(id: number): Promise<ApiResponse<CorrectionTargetInfo>> {
	return request({
		url: targetPrefix + id + '/',
		method: 'get',
	});
}

// 删除校对目标
export async function deleteTarget(id: number): Promise<ApiResponse<null>> {
	return request({
		url: targetPrefix + id + '/',
		method: 'delete',
	});
}

// 更新校对目标
export async function updateTarget(id: number, data: Partial<CorrectionTargetInfo>): Promise<ApiResponse<CorrectionTargetInfo>> {
	return request({
		url: targetPrefix + id + '/',
		method: 'put',
		data,
	});
}

// 更新校对目标状态（开启目标）api/regulate/targets/9999/start_task/

export async function startTarget(id: number): Promise<ApiResponse<CorrectionTargetInfo>> {
	return request({
		// url: targetPrefix + id + '/start_task/',
		url: targetPrefix + id + '/start_task/',
		method: 'get',
	});
}

// 更新校对目标状态（暂停目标）api/regulate/targets/9999/stop_task/
export async function stopTarget(id: number): Promise<ApiResponse<CorrectionTargetInfo>> {
	return request({
		url: targetPrefix + id + '/pause_task/',
		method: 'get',
	});
}

// 重新导出 proofread_task/api 中的函数和类型
export { getTargetList, getTaskAll, createTargets, type CorrectionTaskInfo, type TargetQueryParams, type CreateTargetParams, type ApiResponse };

// 目标查询参数接口
export interface TargetQueryParams {
	id?: number; // 目标ID
	page?: number;
	limit?: number;
	task_id?: number; // 任务ID
	media_account_id?: string; // 媒体账号ID
	priority?: number; // 优先级
	wechat_name?: string; // 校对账号名称
	status?: number; // 目标状态
}

import { request } from '/@/utils/service';

// 微信公众号运营报表接口返回数据结构
export interface WechatOfficialAccountReport {
	id: number;
	modifier_name: string;
	creator_name: string;
	create_datetime: string;
	update_datetime: string;
	tenant_name: string;
	title: string;
	status: number;
	file_path?: string;
	error_message?: string;
	report_data: any;
	report_type: number;
	tenant_id: number;
	start_date: string;
	end_date: string;
	create_time: string;
	delete_time?: string;
}

export interface WechatOfficialAccountReportResponse {
	code: number;
	message: string;
	data: WechatOfficialAccountReport;
}

export interface WechatOfficialAccountReportListParams {
	page?: number;
	limit?: number;
	status?: number;
	title?: string;
	report_type?: number;
	platform_type?: string;
	start_date?: string;
	end_date?: string;
}

export interface WechatOfficialAccountReportListResponse {
	code: number;
	message: string;
	data: WechatOfficialAccountReport[];
	page: number;
	limit: number;
	total: number;
	is_next: boolean;
	is_previous: boolean;
}

export const apiPrefix = '/api/operation-report/reports/';
// export const apiPrefix = '/api/wechat_official_account/reports/';

// 获取运营报表列表
export async function getList(query: WechatOfficialAccountReportListParams): Promise<WechatOfficialAccountReportListResponse> {
	return request({
		url: apiPrefix,
		method: 'get',
		params: {
			...query,
			platform_type: query.platform_type ? query.platform_type : 'wechat',
		},
	});
}

// 获取运营报表详情
export async function getReportDetail(id: string): Promise<WechatOfficialAccountReportResponse> {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

// 生成微信公众号运营报表
export async function generateReport(params: { start_date: string; end_date: string; report_type: number }) {
	return request({
		url: apiPrefix + 'generate/',
		method: 'post',
		data: params,
	});
}

// 导出微信公众号运营报表
export async function exportReport(id: string) {
	return request({
		url: apiPrefix + id + '/export/',
		method: 'get',
		responseType: 'blob',
		timeout: 60000,
	});
}
// 重新生成微信公众号运营报表
export async function regenerateReport(id: string) {
	return request({
		url: apiPrefix + 'regenerate_report/?report_id=' + id,
		method: 'get',
	});
}

// 创建AI报告 /api/report/ai-reports/create_report/
export async function createAiReport(params: {
	tenant_id: number;
	start_date: string;
	end_date: string;
	operation_report_id: number;
	account_level: string;
}) {
	return request({
		url: '/api/report/ai-reports/create_report/',
		method: 'post',
		data: params,
	});
}

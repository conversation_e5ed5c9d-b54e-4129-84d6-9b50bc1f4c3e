<template>
	<el-dialog v-model="dialogVisible" title="人工校对" width="500px">
		<div class="judge-dialog-content">
			<div class="error-info">
				<p><strong>错误句子：</strong>{{ errorInfo.sentence }}</p>
				<p><strong>错误类型：</strong>{{ errorInfo.error_type }}</p>
				<p><strong>错误词：</strong>{{ errorInfo.wrong_word }}</p>
				<p><strong>建议修正词：</strong>{{ errorInfo.correct_word }}</p>
			</div>
			<el-divider />
			<el-form :model="judgeForm" label-width="100px">
				<el-form-item label="人工判定：">
					<el-select v-model="judgeForm.human_judge" placeholder="请选择" clearable style="width: 220px">
						<el-option v-for="item in $getEnumDatas($ENUM.JUDGE_STATUS)" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="closeDialog">取消</el-button>
				<el-button type="primary" @click="submitJudge">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ErrorSentenceInfo } from '../correction';

defineOptions({
	name: 'JudgeDialog',
});

interface Props {
	visible: boolean;
	errorInfo: ErrorSentenceInfo;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = ref(false);
const judgeForm = ref({
	human_judge: 2,
});

watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val;
		if (val) {
			// 初始化表单
			judgeForm.value.human_judge = props.errorInfo.human_judge || 2;
		}
	}
);

watch(
	() => dialogVisible.value,
	(val) => {
		if (!val) {
			emit('update:visible', false);
		}
	}
);

const closeDialog = () => {
	dialogVisible.value = false;
};

const submitJudge = () => {
	emit('submit', {
		id: props.errorInfo.id,
		human_judge: judgeForm.value.human_judge,
	});
	dialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.judge-dialog-content {
	padding: 0 20px;

	.error-info {
		margin-bottom: 20px;

		p {
			margin: 8px 0;
		}
	}
}
</style> 
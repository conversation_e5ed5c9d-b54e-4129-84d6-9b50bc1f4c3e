# 租户管理模块

## 核心功能
- 租户信息管理：维护租户基础信息，包括企业名称、联系方式和服务状态
- 套餐配置：管理租户订购的服务套餐，支持权限和功能模块配置
- 账号绑定：处理租户与媒体账号的绑定关系，支持多平台账号关联
- 客户阶段管理：跟踪客户生命周期阶段，支持阶段标记和状态展示
- Logo展示：支持租户Logo上传和预览功能，提供企业形象展示
- 多租户隔离：确保不同租户数据的安全隔离和权限控制

## 技术实现

### API接口层 (api.ts)
- getTenants(): 获取租户列表数据，支持阶段筛选和搜索功能
- createTenant(): 创建新租户，处理基础信息录入和初始化配置
- updateTenant(): 更新租户信息，支持状态变更和配置修改
- bindMediaAccount(): 绑定媒体账号，处理账号关联和权限配置
- getPackageConfig(): 获取套餐配置信息，支持权限模块展示

### 组件架构 (Fast-CRUD)
- crud.tsx: Fast-CRUD配置，定义租户管理的表格列和操作功能
- dict-tag: 客户阶段标签组件，使用$ENUM.CUSTOMER_STAGE枚举数据
- el-image: Logo预览组件，getBaseURL()处理图片URL和预览功能
- AccountBindDialog: 账号绑定弹窗，处理媒体账号的关联配置

### 主页面控制 (index.vue)
- 数据管理: useFs()集成Fast-CRUD，实现租户数据的增删改查
- 模板定制: cell_stage和cell_logo自定义单元格渲染
- 图片处理: preview-teleported和crossorigin配置，支持安全的图片预览
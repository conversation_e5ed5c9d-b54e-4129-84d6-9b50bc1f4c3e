---
description: 
globs: 
alwaysApply: false
---
# 结果：如何使用枚举以及dict-tag标签组件
## 在新增一个新的枚举类型时，需要在在 `src\stores\constants\enum.ts` 中：添加枚举常量和工具方法注册到全局
```ts
export const ENUM_TYPE = {
  // 任务相关
  TASK_TYPE: 'regulate.TaskType',
  TASK_STATUS: 'regulate.TaskStatus',
  MANUAL_DECISION: 'regulate.ManualDecision',
  // 可以继续添加其他枚举类型...
}
```
## Vue文件使用示例：

```vue
<template>
  <div>
    <!-- 使用枚举标签 -->
    <span :class="$getEnumColor(taskType)">
      {{ $getEnumLabel($ENUM.TASK_TYPE, taskType) }}
    </span>
    
    <!-- 使用枚举数据列表 -->
    <el-select v-model="taskType">
      <el-option
        v-for="item in $getEnumDatas($ENUM.TASK_TYPE)"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>
<el-option v-for="item in $getEnumDatas($ENUM.TASK_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
<script setup lang="ts">
import { ref } from 'vue'

const taskType = ref(1)
</script>
```
crud的index.vue文件使用参考
```vue
<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding" >
			<!-- 自定义功能角色显示 -->
			<template #cell_function_type="scope">
				<dict-tag :options="$getEnumDatas($ENUM.FUNCTION_TYPE)" :value="scope.row.function_type" color-type="primary" />
			</template>
		</fs-crud>
	</fs-page>
</template>
```
## Vue文件中使用dict-tag组件实例：
### 使用字典数据

<!-- 使用字典数据 -->
<dict-tag
  :options="DictionaryDatas"
  :value="someValue"
  color-type="success"
/>


### 使用枚举数据

<!-- 使用枚举数据 -->
<dict-tag
	:options="$getEnumDatas($ENUM.OPERATION_REPORT_STATUS)"
	:value="scope.row.status"
	:color-type="['info', 'primary', 'success', 'warning']"
/>

### 使用自定义数据

<!-- 使用自定义数据 -->
<dict-tag
  :options="[
    { label: '正常', value: 1, cssClass: 'text-success' },
    { label: '禁用', value: 0, cssClass: 'text-danger' }
  ]"
  :value="status"
  :color-type="['success', 'danger']"
/>

### fast-crud框架中使用：crud.tsx文件中

```ts
// 首先导入依赖
import { dict } from '@fast-crud/fast-crud' // 注意，这里这里可能还有其他依赖已经引入了，只需要把 dict 添加到其引入中即可
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';
// 使用：REPORT_STATUS这个值是定义的枚举类型  定义的文件在：src\stores\constants\enum.ts
  status: {
      title: '状态',
      type: 'dict-select',
      dict: dict({
          data: getEnumDatas(ENUM_TYPE.REPORT_STATUS)
      }),
      search: { show: true },
  },

```
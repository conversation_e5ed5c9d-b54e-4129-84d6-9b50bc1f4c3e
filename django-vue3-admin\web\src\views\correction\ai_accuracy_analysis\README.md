# AI校对效果分析模块

## 核心功能
- 多维度筛选：支持租户、时间范围、判定人员、错误类型、判断状态筛选
- 统计指标展示：AI准确率、一致判断数、误报漏报数等关键指标
- 数据可视化：准确率分布饼图、错误类型对比柱状图、时间趋势图
- 详细记录查询：分页展示校对记录，支持图表联动筛选

## 技术实现

### API接口层 (api.ts)
- getStats(): 获取统计数据接口
- getErrorTypeAnalysis(): 错误类型分析接口
- getTrendAnalysis(): 时间趋势分析接口
- getDetailedRecords(): 详细记录分页查询接口
- getTenantOptions()/getJudgeUserOptions(): 筛选选项数据接口

### 组件架构 (components/)
- FilterForm: 筛选表单组件，支持快速时间选择和自定义日期范围
- StatisticsCards: 统计卡片组件，6个关键指标的可视化展示
- ChartsContainer: 图表容器组件，基于ECharts实现三类数据图表
- RecordsTable: 详细记录表格组件，支持分页和状态标签展示

### 主页面控制 (index.vue)
- 数据流管理：并行获取统计、图表、详细记录数据
- 图表联动：handleChartClick()实现点击图表元素筛选详细记录
- 响应式布局：适配移动端和桌面端显示
- 状态管理：统一的加载状态和错误处理机制

<template>
  <fs-page class="PageWebsiteSiteManage">
    <fs-crud ref="crudRef" v-bind="crudBinding">
    </fs-crud>
  </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
  name: 'WebsiteSiteManage',
  setup() {
    // 初始化CRUD
    const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

    // 生命周期
    onMounted(() => {
      console.log('WebsiteSiteManage组件已挂载');
      // 页面加载后自动获取列表数据
      crudExpose.doRefresh();
    });

    return {
      crudBinding,
      crudRef,
      crudExpose,
    };
  },
});
</script>

<style lang="scss" scoped>
.PageWebsiteSiteManage {
  padding: 20px;
}
</style>
import { request } from '/@/utils/service';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';

// 平台类型枚举
export enum PlatformType {
	WECHAT = 'wechat', // 微信
	DOUYIN = 'douyin', // 抖音
	WEIBO = 'weibo', // 微博
}

// 排行榜周期枚举
export enum RankingPeriod {
	DAILY = 1, // 日榜
	WEEKLY = 2, // 周榜
	MONTHLY = 3, // 月榜
}

// 排行榜类型枚举
export enum RankingType {
	ACCOUNT_WCI = 1, // 账号WCI排行榜
	ACCOUNT_BCI = 2, // 账号BCI排行榜
	ACCOUNT_DCI = 3, // 账号DCI排行榜
}

// 日期范围请求参数
export interface DateRangesParams {
	platform_type?: string; // 平台类型: 1-微信
	ranking_period: number; // 排行榜周期: 1-日榜, 2-周榜, 3-月榜
	ranking_type: number; // 排行榜类型: 1-账号WCI排行榜 2-账号BCI排行榜 3-账号DCI排行榜
}

// 日期范围响应结果
export interface DateRangesResponse {
	code: number;
	message: string;
	data: string[];
}

// 排行榜列表请求参数
export interface RankingListParams {
	platform_type: string; // 平台类型: 1-微信
	ranking_period: number; // 排行榜周期: 1-日榜, 2-周榜, 3-月榜
	ranking_type: number; // 排行榜类型: 1-账号WCI排行榜
	start_date: string; // 开始日期
	end_date: string; // 结束日期
	page: number; // 页码
	limit: number; // 每页数量
	custom_ranking_id?: string | number; // 自定义榜单ID（可选）
}

// 排行榜项目数据结构
export interface RankingItem {
	id: number;
	name: string;
	avatar?: string;
	publish_count: number;
	read_count: number;
	view_count: number;
	like_count: number;
	forward_count: number;
	wci: number;
}

// 排行榜列表响应结果
export interface RankingListResponse {
	code: number;
	message: string;
	data: {
		list: RankingItem[];
		total: number;
		page_size: number;
		current_page: number;
	};
}

// 自定义榜单数据结构
export interface CustomRankingItem {
	id: number;
	name: string;
	platform_type: string;
	ranking_type: number;
	account_ids: string;
	create_time: string;
	update_time: string;
}

// 自定义榜单创建请求参数
export interface CustomRankingCreateParams {
	id?: number;
	account_ids: string; // 账号ID列表
	name: string; // 榜单名称
	platform_type: string; // 平台类型: 1-微信
	ranking_type: number; // 排行榜类型: 1-账号WCI排行榜
}

// 自定义榜单创建响应结果
export interface CustomRankingCreateResponse {
	code: number;
	message: string;
	data: CustomRankingItem;
}

// 自定义榜单更新响应结果
export interface CustomRankingUpdateResponse {
	code: number;
	message: string;
	data: CustomRankingItem;
}

// 自定义榜单删除响应结果
export interface CustomRankingDeleteResponse {
	code: number;
	message: string;
	data: any;
}

// 自定义榜单列表响应结果
export interface CustomRankingListResponse {
	code: number;
	message: string;
	data: CustomRankingItem[];
}

const apiPrefix = '/api/ranking/ranking/'; // 排行榜接口前缀
const customApiPrefix = '/api/ranking/ranking-custom/'; // 自定义榜单接口前缀
/**
 * 获取日期范围列表
 * @param params 请求参数
 * @returns 日期范围列表
 */
export async function getDateRanges(params: DateRangesParams): Promise<DateRangesResponse> {
	return request({
		url: `${apiPrefix}get_date_ranges/`,
		method: 'get',
		params,
	});
}

/**
 * 获取排行榜列表
 * @param params 请求参数
 * @returns 排行榜列表数据
 */
export async function getRankingList(params: RankingListParams): Promise<RankingListResponse> {
	return request({
		url: `${apiPrefix}list_rankings/`,
		method: 'get',
		params,
	});
}

/**
 * 创建自定义榜单
 * @param data 请求数据
 * @returns 创建结果
 */
export async function createCustomRank(data: CustomRankingCreateParams): Promise<CustomRankingCreateResponse> {
	return request({
		url: `${customApiPrefix}`,
		method: 'post',
		data,
	});
}

/**
 * 获取自定义榜单列表 /api/ranking/ranking-custom/select_list/
 * @param platform_type 平台类型（可选）
 * @returns 自定义榜单列表
 */
export async function getCustomRankList(platform_type?: string): Promise<CustomRankingListResponse> {
	const params = platform_type ? { platform_type } : {};
	return request({
		url: `${customApiPrefix}select_list/`,
		method: 'get',
		params,
	});
}

/**
 * 更新自定义榜单
 * @param data 请求数据
 * @returns 更新结果
 */
export async function updateCustomRank(data: CustomRankingCreateParams): Promise<CustomRankingUpdateResponse> {
	return request({
		url: `${customApiPrefix}${data.id}/`,
		method: 'put',
		data,
	});
}

/**
 * 删除自定义榜单
 * @param id 自定义榜单ID
 * @returns 删除结果
 */
export async function deleteCustomRank(id: number): Promise<CustomRankingDeleteResponse> {
	return request({
		url: `${customApiPrefix}${id}/`,
		method: 'delete',
	});
}

/**
 * 获取自定义榜单详情
 * @param id 自定义榜单ID
 * @returns 自定义榜单详情
 */
export async function getCustomDetail(id: number | string) {
	return request({
		url: `${customApiPrefix}${id}/`,
		method: 'get',
	});
}

/**
 * 导出榜单数据
 * @param params 请求参数
 * @returns 导出结果
 */
export async function exportData(params: RankingListParams) {
	return request({
		url: `${apiPrefix}export_list/`,
		method: 'get',
		params,
		responseType: 'blob',
	});
}

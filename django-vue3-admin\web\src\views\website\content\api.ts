import { request } from '/@/utils/service';

// 内容状态枚举
export enum ContentStatus {
  PENDING = 'pending',
  EXTRACTED = 'extracted',
  FAILED = 'failed',
  MANUAL = 'manual',
  NO_RULE_MATCH = 'no_rule_match',
  RULE_GENERATION_FAILED = 'rule_generation_failed'
}

// 内容数据类型
export interface ContentType {
  id: number;
  sitemap_unique_id: string;
  site_id: number;
  column_id?: number;
  title: string;
  content?: string;
  content_summary?: string;
  publish_time?: string;
  publish_time_display?: string;
  author?: string;
  source?: string;
  status: ContentStatus;
  status_display: string;
  content_extraction_rule_id?: number;
  create_time: string;
  create_time_display: string;
  update_time: string;
  update_time_display: string;
  
  // 关联字段
  site_name?: string;
  site_domain?: string;
  column_name?: string;
  rule_name?: string;
  content_url?: string;
}

// 内容详情类型
export interface ContentDetailType extends ContentType {
  site_info?: {
    id: number;
    name: string;
    domain: string;
    description?: string;
    status: string;
    status_display: string;
  };
  column_info?: {
    id: number;
    name: string;
    description?: string;
    status: string;
    status_display: string;
    has_rule: boolean;
    content_count: number;
  };
  rule_info?: {
    id: number;
    name: string;
    rule_config: any;
    success_count: number;
    created_at: string;
    updated_at: string;
  };
  sitemap_info?: {
    id: number;
    unique_id: string;
    url: string;
    title: string;
    content_type: string;
    content_type_display: string;
    last_crawl_time?: string;
    created_at: string;
  };
  status_description: string;
  is_rule_issue: boolean;
  needs_manual_intervention: boolean;
  is_success: boolean;
  is_pending: boolean;
}

// 查询参数类型
export interface ContentQuery {
  page?: number;
  page_size?: number;
  site_id?: number;
  column_id?: number;
  status?: ContentStatus | string;
  content_extraction_rule_id?: number;
  has_rule?: boolean | string;
  date_from?: string;
  date_to?: string;
  publish_date_from?: string;
  publish_date_to?: string;
  search?: string;
  ordering?: string;
}

// 创建内容数据类型
export interface ContentCreateData {
  sitemap_unique_id: string;
  site_id: number;
  column_id?: number;
  title: string;
  content?: string;
  publish_time?: string;
  author?: string;
  source?: string;
  content_extraction_rule_id?: number;
  status?: ContentStatus;
}

// 统一API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 内容统计类型
export interface ContentStatistics {
  total_contents: number;
  pending_contents: number;
  extracted_contents: number;
  failed_contents: number;
  manual_contents: number;
  no_rule_match_contents: number;
  rule_generation_failed_contents: number;
  extraction_success_rate: number;
  rule_match_rate: number;
  contents_by_site: Record<string, number>;
  contents_by_status: Record<string, number>;
  contents_by_date: Record<string, number>;
  recent_extracted: number;
  recent_failed: number;
}

// 批量操作结果类型
export interface BatchOperationResult {
  total_count: number;
  success_count: number;
  failed_count: number;
  results: Array<{
    content_id: number;
    content_title: string;
    success: boolean;
    old_status?: string;
    new_status?: string;
    error?: string;
  }>;
}

// ========== 基础CRUD操作 ==========

// 获取内容列表
export function getContentList(params?: ContentQuery) {
  return request<ApiResponse<PaginatedResponse<ContentType>>>({
    url: '/api/website/contents/',
    method: 'get',
    params,
  });
}

// 创建内容
export function createContent(data: ContentCreateData) {
  return request<ApiResponse<ContentType>>({
    url: '/api/website/contents/',
    method: 'post',
    data,
  });
}

// 更新内容
export function updateContent(id: number, data: Partial<ContentCreateData>) {
  return request<ApiResponse<ContentType>>({
    url: `/api/website/contents/${id}/`,
    method: 'put',
    data,
  });
}

// 部分更新内容
export function patchContent(id: number, data: Partial<ContentCreateData>) {
  return request<ApiResponse<ContentType>>({
    url: `/api/website/contents/${id}/`,
    method: 'patch',
    data,
  });
}

// 删除内容
export function deleteContent(id: number) {
  return request<ApiResponse<{ deleted_content_id: number; deleted_content_title: string }>>({
    url: `/api/website/contents/${id}/`,
    method: 'delete',
  });
}

// 获取单个内容详情
export function getContentDetail(id: number) {
  return request<ApiResponse<ContentDetailType>>({
    url: `/api/website/contents/${id}/`,
    method: 'get',
  });
}

// ========== 统计信息 ==========

// 获取内容统计信息
export function getContentStatistics(params?: ContentQuery) {
  return request<ApiResponse<ContentStatistics>>({
    url: '/api/website/contents/statistics/',
    method: 'get',
    params,
  });
}

// ========== 状态管理 ==========

// 更新内容状态
export function updateContentStatus(id: number, action: string) {
  return request<ApiResponse<{
    content_id: number;
    content_title: string;
    old_status: string;
    new_status: string;
    action: string;
  }>>({
    url: `/api/website/contents/${id}/status/`,
    method: 'patch',
    data: { action },
  });
}

// 批量状态管理
export function batchUpdateContentStatus(content_ids: number[], action: string) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/contents/batch_status/',
    method: 'post',
    data: { content_ids, action },
  });
}

// ========== 内容操作 ==========

// 重新提取内容
export function extractContent(id: number) {
  return request<ApiResponse<{
    content_id: number;
    content_title: string;
    status: string;
    message: string;
  }>>({
    url: `/api/website/contents/${id}/extract_content/`,
    method: 'post',
  });
}

// 批量重新提取内容
export function batchExtractContent(content_ids: number[]) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/contents/batch_extract/',
    method: 'post',
    data: { content_ids },
  });
}

// 导出内容数据
export function exportContentData(params?: ContentQuery & { format?: string }) {
  return request<ApiResponse<{
    count: number;
    contents: ContentType[];
  }>>({
    url: '/api/website/contents/export_data/',
    method: 'get',
    params,
  });
}

// ========== 快速操作函数 ==========

// 标记为人工处理
export function markContentManual(id: number) {
  return updateContentStatus(id, 'mark_manual');
}

// 重置为待处理
export function markContentPending(id: number) {
  return updateContentStatus(id, 'mark_pending');
}

// 标记为失败
export function markContentFailed(id: number) {
  return updateContentStatus(id, 'mark_failed');
}

// 重置状态
export function resetContentStatus(id: number) {
  return updateContentStatus(id, 'reset_status');
}

// 重新提取内容
export function reExtractContent(id: number) {
  return updateContentStatus(id, 'extract');
}
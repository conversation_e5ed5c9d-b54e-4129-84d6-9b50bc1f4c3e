<template>
	<el-dialog v-model="dialogVisible" title="查看报告详情" width="80%" :close-on-click-modal="false">
		<div class="report-content">
			<MarkdownEditor v-model="reportContent" :height="'500px'" :preview="true" :disabled="true" />
		</div>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogVisible = false">关闭</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineAsyncComponent } from 'vue';
import { ElDialog, ElButton, ElMessage } from 'element-plus';
import * as api from '../api';

// 动态导入MarkdownEditor组件
const MarkdownEditor = defineAsyncComponent(() => import('/@/components/markdownEditor/index.vue'));

// 响应式数据
const dialogVisible = ref(false);
const reportContent = ref('');

// 打开对话框
const open = async (row: any) => {
	try {
		// 获取报告内容
		const res = await api.getReportContent(row.id);
		reportContent.value = res.data.content;
		dialogVisible.value = true;
	} catch (error: any) {
		ElMessage.error(error.message || '获取报告内容失败');
	}
};

defineExpose({
	open,
});
</script>

<style lang="scss" scoped>
.report-content {
	min-height: 500px;
	width: 100%;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
}

:deep(.el-dialog__body) {
	padding: 20px;
}

:deep(.markdown-editor-container) {
	width: 100%;
}
</style> 
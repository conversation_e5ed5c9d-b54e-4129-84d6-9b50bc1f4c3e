.loading-next {
	width: 100%;
	height: 100%;
}
.loading-next .loading-next-box {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.loading-next .loading-next-box-warp {
	width: 80px;
	height: 80px;
}
.loading-next .loading-next-box-warp .loading-next-box-item {
	width: 33.333333%;
	height: 33.333333%;
	background: var(--el-color-primary);
	float: left;
	animation: loading-next-animation 1.2s infinite ease;
	border-radius: 1px;
}
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(7) {
	animation-delay: 0s;
}
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(4),
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(8) {
	animation-delay: 0.1s;
}
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(1),
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(5),
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(9) {
	animation-delay: 0.2s;
}
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(2),
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(6) {
	animation-delay: 0.3s;
}
.loading-next .loading-next-box-warp .loading-next-box-item:nth-child(3) {
	animation-delay: 0.4s;
}
@keyframes loading-next-animation {
	0%,
	70%,
	100% {
		transform: scale3D(1, 1, 1);
	}
	35% {
		transform: scale3D(0, 0, 1);
	}
}

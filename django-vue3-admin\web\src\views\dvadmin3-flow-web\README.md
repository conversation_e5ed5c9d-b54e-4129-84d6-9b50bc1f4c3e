# dvadmin3-flow-web

## 组件介绍

`dvadmin3-flow-web` 是基于 Django-Vue3-Admin 框架开发的工作流管理组件，提供完整的企业级流程审批和工作流管理解决方案。该组件支持可视化流程设计、流程实例管理、任务处理等核心功能。

## 主要功能

### 🔧 核心功能

- **可视化流程设计器**：支持拖拽式流程设计，包含开始节点、审批节点、条件节点等
- **流程模板管理**：创建、编辑、删除流程模板
- **流程实例管理**：跟踪和管理流程执行状态
- **任务处理**：待办任务处理、已办任务查询
- **组织架构集成**：与系统用户、角色、部门信息集成

### 🎯 应用场景

- 企业审批流程（请假、报销、采购等）
- 业务流程管理
- 文档审核流程
- 项目审批流程

## 页面结构

### 主要页面及路径

| 页面名称       | 路径              | 主要功能                         |
| -------------- | ----------------- | -------------------------------- |
| **流程管理**   | `/flowManagement` | 流程模板的创建、编辑、查看和管理 |
| **待办事项**   | `/flowTodo`       | 查看和处理当前用户的待办任务     |
| **已提交流程** | `/flowSubmitted`  | 查看用户已提交的流程实例         |
| **抄送我的**   | `/flowCC`         | 查看抄送给当前用户的流程信息     |
| **流程数据**   | `/flowData`       | 流程实例数据的查询和管理         |
| **我发起的**   | `/flowIdo`        | 查看用户发起的流程实例           |

### 核心模块

#### 1. 流程管理模块 (`/flowManagement`)

- **功能**：流程模板的全生命周期管理
- **特性**：
  - 新建流程模板
  - 可视化流程设计器
  - 流程模板编辑和查看
  - 流程图标和样式配置

#### 2. 待办事项模块 (`/flowTodo`)

- **功能**：处理分配给当前用户的待办任务
- **特性**：
  - 待办任务列表展示
  - 任务处理操作
  - 任务状态跟踪

#### 3. 已提交流程模块 (`/flowSubmitted`)

- **功能**：管理用户提交的流程实例
- **特性**：
  - 已提交流程查询
  - 流程状态监控
  - 流程撤回（如支持）

#### 4. 抄送管理模块 (`/flowCC`)

- **功能**：处理抄送相关的流程信息
- **特性**：
  - 抄送消息查看
  - 流程知会信息

#### 5. 流程数据模块 (`/flowData`)

- **功能**：流程实例数据的统计和管理
- **特性**：
  - 流程数据查询
  - 流程统计分析

#### 6. 我发起的模块 (`/flowIdo`)

- **功能**：管理用户主动发起的流程
- **特性**：
  - 发起流程查询
  - 流程进度跟踪

## 技术架构

### 前端技术栈

- **Vue 3** + **TypeScript**
- **Element Plus** UI 组件库
- **Fast-Crud** 快速 CRUD 框架
- **Axios** HTTP 客户端

### 核心组件

- **流程设计器** (`/wflow/design`)：可视化流程设计工具
- **表单设计器** (`/wflow/form`)：动态表单构建
- **流程对话框** (`/components/flowDialog`)：任务处理界面

### API 接口

- **组织架构接口** (`/api/org.js`)：用户、部门、角色查询
- **流程管理接口**：流程 CRUD 操作
- **任务处理接口**：待办任务处理

## 安装使用

该组件作为 Django-Vue3-Admin 的插件使用，需要：

1. 确保后端已安装 `dvadmin3-flow` 插件
2. 将本组件放置在 `src/plugin/` 目录下
3. 在主应用中注册相关路由和组件

## 数据流

```
用户操作 → 前端组件 → API接口 → 后端服务 → 数据库
                ↓
            实时更新 ← 响应数据 ← 业务逻辑 ← 数据处理
```

## 权限控制

组件集成了系统的权限管理，支持：

- 基于角色的访问控制
- 流程节点权限配置
- 操作权限验证

---

> **注意**：使用前请确保后端 `dvadmin3-flow` 服务正常运行，并正确配置相关 API 接口。

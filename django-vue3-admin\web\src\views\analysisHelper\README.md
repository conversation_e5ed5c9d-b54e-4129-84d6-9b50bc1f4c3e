# 分析助手模块

## 核心功能
- 运营报告：生成多平台运营数据报告，支持微信、抖音、微博等平台分析
- AI智能报告：基于AI技术生成智能分析报告，提供数据洞察和建议
- 校对分析：对校对结果进行深度分析，提供质量评估和改进建议
- 运营详情：展示各平台的详细运营数据，支持月度和季度数据展示
- 模块选择：提供统一的模块选择器，支持跨平台数据分析切换
- 多维度分析：支持账号级别、时间维度、平台维度的多重数据分析

## 技术实现

### API接口层 (api.ts)
- getOperateReports(): 获取运营报告列表，支持平台筛选和状态查询
- generateAIReport(): 触发AI报告生成，处理账号级别和分析参数
- getCorrectionAnalysis(): 获取校对分析数据，提供质量评估指标
- getOperateDetails(): 获取运营详情数据，支持时间范围和平台筛选
- exportReport(): 导出分析报告，支持多种格式和自定义配置

### 组件架构 (components/)
- common-tabs: 平台切换组件，currentTab控制当前平台，handleTabChange()处理切换逻辑
- ModuleSelector: 模块选择器，提供统一的功能模块选择界面
- ReportEditDialog: 报告编辑弹窗，支持报告内容的编辑和配置
- ReportViewDialog: 报告查看弹窗，提供报告内容的只读展示
- AccountLevelDialog: 账号级别选择弹窗，selectedAccountLevel管理选择状态

### 主页面控制 (index.vue)
- 平台管理: platformItems定义支持的平台类型，支持多平台数据切换
- 状态展示: dict-tag组件展示报告状态，使用$ENUM.OPERATION_REPORT_STATUS枚举
- 弹窗控制: showAccountLevelDialog控制AI报告生成的参数选择
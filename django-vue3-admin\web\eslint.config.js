import js from '@eslint/js';
import vue from 'eslint-plugin-vue';
import typescript from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import vueParser from 'vue-eslint-parser';

export default [
  js.configs.recommended,
  ...vue.configs['flat/recommended'],
  
  {
    files: ['**/*.{js,jsx,ts,tsx,vue}'],
    languageOptions: {
      ecmaVersion: 2021,
      sourceType: 'module',
      parser: vueParser,
      parserOptions: {
        parser: typescriptParser,
        ecmaVersion: 12,
        sourceType: 'module',
      },
      globals: {
        browser: true,
        es2021: true,
        node: true,
      },
    },
    plugins: {
      vue,
      '@typescript-eslint': typescript,
    },
    rules: {
      // TypeScript ESLint rules
      '@typescript-eslint/ban-ts-ignore': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-use-before-define': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/ban-types': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-redeclare': 'error',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      
      // Vue rules
      'vue/custom-event-name-casing': 'off',
      'vue/attributes-order': 'off',
      'vue/one-component-per-file': 'off',
      'vue/html-closing-bracket-newline': 'off',
      'vue/max-attributes-per-line': 'off',
      'vue/multiline-html-element-content-newline': 'off',
      'vue/singleline-html-element-content-newline': 'off',
      'vue/attribute-hyphenation': 'off',
      'vue/html-self-closing': 'off',
      'vue/no-multiple-template-root': 'off',
      'vue/require-default-prop': 'off',
      'vue/no-v-model-argument': 'off',
      'vue/no-arrow-functions-in-watch': 'off',
      'vue/no-template-key': 'off',
      'vue/no-v-html': 'off',
      'vue/comment-directive': 'off',
      'vue/no-parsing-error': 'off',
      'vue/no-deprecated-v-on-native-modifier': 'off',
      'vue/multi-word-component-names': 'off',
      'vue/no-unused-vars': 'off',
      
      // General ESLint rules
      'no-useless-escape': 'off',
      'no-sparse-arrays': 'off',
      'no-prototype-builtins': 'off',
      'no-constant-condition': 'off',
      'no-use-before-define': 'off',
      'no-restricted-globals': 'off',
      'no-restricted-syntax': 'off',
      'generator-star-spacing': 'off',
      'no-unreachable': 'off',
      'no-unused-vars': 'warn',
      'no-case-declarations': 'off',
      'no-console': 'error',
      'no-redeclare': 'off',
    },
  },
  
  // TypeScript specific overrides
  {
    files: ['**/*.{ts,tsx,vue}'],
    rules: {
      'no-undef': 'off',
    },
  },
  
  // Ignore patterns (replacing .eslintignore)
  {
    ignores: [
      '*.sh',
      'node_modules/**',
      'lib/**',
      '*.md',
      '*.scss',
      '*.woff',
      '*.ttf',
      '.vscode/**',
      '.idea/**',
      'dist/**',
      'mock/**',
      'public/**',
      'bin/**',
      'build/**',
      'config/**',
      'index.html',
      'src/assets/**',
    ],
  },
];
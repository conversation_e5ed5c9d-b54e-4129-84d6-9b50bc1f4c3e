<template>
	<div class="app-container">
		<div v-loading="pageLoading">
			<!-- 本级公众号季报 -->
			<my-wx-operate-quarter-detail
				ref="myWxOperateRef"
				style="margin-bottom: 40px"
				:date="{
					start_date: reportData.start_date,
					end_date: reportData.end_date,
				}"
				:title="reportData.title"
				:self-report-data="reportData.report_data?.my_account"
				:tenant-name="reportData.tenant_name"
				:account-counts="accountCounts"
				v-if="reportData.report_data?.my_account"
			/>

			<!-- 下级公众号季报 -->
			<operate-report-quarter-detail
				ref="operateReportRef"
				:date="{
					start_date: reportData.start_date,
					end_date: reportData.end_date,
				}"
				:title="reportData.title"
				:sub-report-data="reportData.report_data?.sub_account"
				:same-account="reportData.report_data?.same_account"
				:no-update-account="reportData.report_data?.no_update_account"
				:create-time="reportData?.update_time"
				v-if="reportData.report_data?.sub_account || reportData?.account_publish_info || reportData.report_data?.same_account"
			/>
		</div>

		<!-- 模块选择器 -->
		<ModuleSelector v-model="showModuleSelector" :modules="allModules" @confirm="handleModuleSelectionConfirm" />
	</div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import OperateReportQuarterDetail from './operateReportQuarterDetail.vue';
import MyWxOperateQuarterDetail from './myWxOperateQuarterDetail.vue';
import ModuleSelector from '/@/views/analysisHelper/component/ModuleSelector.vue';
import * as api from '/@/views/analysisHelper/operateReport/api';
import { exportToPDF } from '/@/utils/pdfExportHelper';
import { ElNotification } from 'element-plus';

const route = useRoute();
const reportId = ref('');
const pageLoading = ref(false);
const exporting = ref(false);
const reportData = ref({});
const myWxOperateRef = ref();
const operateReportRef = ref();
const showModuleSelector = ref(false);
const allModules = ref([]);

// 计算账号数量
const accountCounts = computed(() => {
	const myAccountCount = reportData.value.report_data?.my_account?.account_data?.length || 0;
	const subAccountCount = reportData.value.report_data?.sub_account?.wci_rank?.length || 0;
	const sameAccountCount = reportData.value.report_data?.same_account?.wci_rank?.length || 0;

	return {
		myAccount: myAccountCount,
		subAccount: subAccountCount,
		sameAccount: sameAccountCount,
	};
});

// 获取报告数据
const getReportData = async () => {
	pageLoading.value = true;
	try {
		// 获取微信季报数据
		const res = await api.getReportDetail(reportId.value);
		if (res.code === 2000) {
			reportData.value = res.data;
			console.log('季报数据:', reportData.value);
		}
	} catch (error) {
		console.error('获取季报数据失败:', error);
	} finally {
		pageLoading.value = false;
	}
};

// 修改导出PDF方法
const handleExportPDF = async () => {
	try {
		// 等待下一个渲染周期，确保组件已渲染
		await nextTick();

		// 获取本级和下级报告的模块列表
		const myModuleIds = myWxOperateRef.value?.moduleIds || [];
		const subModuleIds = operateReportRef.value?.moduleIds || [];

		// 合并所有模块
		allModules.value = [...myModuleIds, ...subModuleIds];

		if (allModules.value.length === 0) {
			ElMessage.warning('没有找到可导出的内容');
			return;
		}

		console.log('可用模块:', allModules.value);

		// 显示模块选择器
		showModuleSelector.value = true;
	} catch (error) {
		console.error('准备导出时出错:', error);
		ElMessage.error('准备导出时出错，请重试');
	}
};

// 处理模块选择确认
const handleModuleSelectionConfirm = async (selectedModules) => {
	try {
		exporting.value = true;

		ElNotification.info({
			title: 'PDF正在导出，请耐心等待',
			duration: 1500,
		});

		console.log('选中的模块:', selectedModules);

		if (selectedModules.length === 0) {
			ElMessage.warning('请至少选择一个模块');
			return;
		}

		// 验证所有模块是否存在
		const missingModules = selectedModules.filter((module) => {
			const elementId = module.moduleId || module.id;
			return !document.getElementById(elementId);
		});

		if (missingModules.length > 0) {
			console.warn('以下模块未找到:', missingModules);
		}

		// 导出PDF
		const reportTitle = reportData.value.title || '微信公众号季度运营报告';
		await exportToPDF(selectedModules, `${reportTitle}.pdf`, {
			orientation: 'landscape',
			format: 'a4',
			quality: 0.3,
			scale: 2.435,
		});

		ElMessage.success('PDF导出完成');
	} catch (error) {
		console.error('导出过程出错:', error);
		ElMessage.error('导出过程出错，请重试');
	} finally {
		exporting.value = false;
	}
};

onMounted(() => {
	const { id } = route.query;
	if (id) {
		reportId.value = String(id);
		console.log('reportId:', reportId.value);
		getReportData();
	}
});
</script>

<style scoped>
.operate-report-container {
	background-color: #fff;
	padding: 20px;
	min-height: calc(100vh - 84px);
}

.export-buttons {
	position: fixed;
	top: 80px;
	right: 20px;
	z-index: 999;
}
</style> 
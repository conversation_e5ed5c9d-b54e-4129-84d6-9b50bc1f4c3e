import jsPD<PERSON> from 'jspdf';
import html2canvas from 'html2canvas';

// 动态导入markdown-it和highlight.js
let MarkdownIt: any = null;
let hljs: any = null;

// 异步初始化markdown-it
async function initMarkdownIt() {
	if (!MarkdownIt) {
		const markdownItModule = await import('markdown-it');
		MarkdownIt = markdownItModule.default;

		try {
			const hljsModule = await import('highlight.js');
			hljs = hljsModule.default;
		} catch (e) {
			console.warn('highlight.js not available, code highlighting disabled');
		}
	}

	return new MarkdownIt({
		html: true,
		linkify: true,
		typographer: true,
		highlight: hljs
			? function (str: string, lang: string) {
					if (lang && hljs.getLanguage(lang)) {
						try {
							return '<pre class="hljs"><code>' + hljs.highlight(str, { language: lang, ignoreIllegals: true }).value + '</code></pre>';
						} catch (__) {}
					}
					return '<pre class="hljs"><code>' + str + '</code></pre>';
			  }
			: undefined,
	});
}

interface MarkdownExportOptions {
	orientation?: 'landscape' | 'portrait' | 'p' | 'l';
	unit?: 'px' | 'pt' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc';
	format?: string;
	compress?: boolean;
	quality?: number;
	scale?: number;
}

export class MarkdownPDFExporter {
	private pdf: jsPDF;
	private options: MarkdownExportOptions;
	private pageWidth: number;
	private pageHeight: number;

	constructor(options: MarkdownExportOptions = {}) {
		this.options = {
			orientation: 'landscape',
			unit: 'px',
			format: 'a4',
			compress: true,
			quality: 0.85,
			scale: 3.5,
			...options,
		};

		this.pdf = new jsPDF({
			orientation: this.options.orientation || 'landscape',
			unit: this.options.unit || 'px',
			format: this.options.format || 'a4',
			compress: this.options.compress,
		});

		// 设置页面尺寸
		const pageSize = this.pdf.internal.pageSize;
		this.pageWidth = pageSize.width;
		this.pageHeight = pageSize.height;
	}

	// 使用markdown-it进行Markdown转HTML转换
	private async markdownToHTML(markdown: string): Promise<string> {
		if (!markdown) return '';

		try {
			const md = await initMarkdownIt();
			return md.render(markdown);
		} catch (error) {
			console.error('Markdown转换失败，使用简单转换:', error);
			// 回退到简单转换
			return this.simpleMarkdownToHTML(markdown);
		}
	}

	// 简单的Markdown转HTML转换（备用方案）
	private simpleMarkdownToHTML(markdown: string): string {
		if (!markdown) return '';

		let html = markdown
			// 转换标题
			.replace(/^### (.*$)/gm, '<h3>$1</h3>')
			.replace(/^## (.*$)/gm, '<h2>$1</h2>')
			.replace(/^# (.*$)/gm, '<h1>$1</h1>')
			// 转换粗体
			.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
			// 转换段落
			.replace(/\n\n/g, '</p><p>')
			// 转换列表
			.replace(/^\* (.*$)/gm, '<li>$1</li>')
			.replace(/^- (.*$)/gm, '<li>$1</li>')
			// 转换换行
			.replace(/\n/g, '<br>');

		// 包装在段落中
		html = '<p>' + html + '</p>';

		// 处理列表
		html = html.replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');

		return html;
	}

	// 从原始Markdown内容创建临时DOM并导出
	public async exportFromMarkdownContent(markdownContent: string): Promise<HTMLCanvasElement[]> {
		try {
			console.log('=== 开始处理Markdown内容 ===');
			console.log('输入参数:', {
				contentLength: markdownContent?.length || 0,
				contentPreview: markdownContent?.substring(0, 100) || 'undefined',
			});

			if (!markdownContent || markdownContent.trim() === '') {
				console.warn('Markdown内容为空');
				return [];
			}

			// 使用HTML转换方案
			return await this.exportFromMarkdownContentHTML(markdownContent);
		} catch (error: any) {
			console.error('从Markdown内容导出失败:', error);
			throw error;
		}
	}

	// HTML转换方法
	private async exportFromMarkdownContentHTML(markdownContent: string): Promise<HTMLCanvasElement[]> {
		if (!markdownContent || markdownContent.trim() === '') {
			console.warn('Markdown内容为空');
			return [];
		}

		// 创建临时容器
		const tempContainer = document.createElement('div');
		tempContainer.style.position = 'absolute';
		tempContainer.style.left = '0px'; // 修改：使用可见位置
		tempContainer.style.top = '0px'; // 修改：使用可见位置
		tempContainer.style.width = this.pageWidth + 'px'; // 使用完整页面宽度
		tempContainer.style.backgroundColor = '#ffffff';
		tempContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		tempContainer.style.fontSize = '11px'; // 从13px进一步调整为11px，减小字体大小
		tempContainer.style.lineHeight = '1.3'; // 从1.5进一步调整为1.3，减小行高
		tempContainer.style.padding = '0 30px'; // 左右15px内边距，上下0内边距
		tempContainer.style.color = '#333';
		tempContainer.style.boxSizing = 'border-box';
		tempContainer.style.zIndex = '-1000'; // 添加：使用z-index隐藏而不是位置隐藏

		// 转换Markdown为HTML并插入容器
		const htmlContent = await this.markdownToHTML(markdownContent);
		console.log('HTML内容:', htmlContent ? `长度: ${htmlContent.length}` : '转换结果为空');

		if (!htmlContent || htmlContent.trim() === '') {
			console.error('Markdown转HTML失败，内容为空');
			return [];
		}

		tempContainer.innerHTML = htmlContent;

		// 应用样式
		this.applyMarkdownStyles(tempContainer);

		// 添加到DOM
		document.body.appendChild(tempContainer);

		// 等待内容渲染
		await new Promise((resolve) => setTimeout(resolve, 800)); // 增加渲染等待时间

		console.log('临时容器尺寸:', {
			width: tempContainer.scrollWidth,
			height: tempContainer.scrollHeight,
			content: tempContainer.textContent?.substring(0, 100),
			hasValidContent: !!tempContainer.textContent?.trim(),
		});

		// 内容有效性检查
		if (tempContainer.scrollHeight === 0 || !tempContainer.textContent?.trim()) {
			console.error('临时容器渲染失败，内容为空或高度为0');
			document.body.removeChild(tempContainer);
			return [];
		}

		// 捕获内容 - 使用智能分页控制
		const canvases = await this.captureMarkdownModuleWithIntelligentPaging(tempContainer);
		console.log('生成的画布数量:', canvases.length);

		// 清理临时DOM
		document.body.removeChild(tempContainer);

		return canvases;
	}

	// 智能分页的捕获方法 - 基于多级分割策略（HR → H3 → 高度）
	private async captureMarkdownModuleWithIntelligentPaging(moduleElement: HTMLElement): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];
		const currentScale = 4; // 使用当前的scale设置

		// A4横版的有效高度（使用完整页面高度）
		const maxPageHeight = this.pageHeight * 0.85; // 使用85%的页面高度，留少量边距避免截断
		const pageWidth = this.pageWidth;

		const moduleHeight = moduleElement.scrollHeight;
		const moduleWidth = moduleElement.scrollWidth;

		console.log('智能多级分页控制参数:', {
			moduleHeight,
			maxPageHeight,
			pageWidth,
			scale: currentScale,
			heightRatio: '85%',
			moduleContent: moduleElement.textContent?.trim().substring(0, 100) || '无内容',
			moduleHTML: moduleElement.innerHTML.substring(0, 200) || '无HTML',
		});

		// 检查模块是否有内容
		if (!moduleElement.textContent?.trim() && !moduleElement.innerHTML?.trim()) {
			console.error('模块内容完全为空，无法生成PDF');
			return [];
		}

		if (moduleHeight <= maxPageHeight) {
			// 不需要分页
			console.log('内容高度小于页面高度，无需分页');
			const canvas = await html2canvas(moduleElement, {
				scale: currentScale,
				useCORS: true,
				allowTaint: true,
				logging: false,
				backgroundColor: '#ffffff',
				width: moduleWidth,
				height: moduleHeight,
			});
			return [canvas];
		}

		// 需要分页 - 使用多级分割策略（HR → H3 → 高度）+ 元素级精确分页
		console.log('内容需要分页处理，开始多级分割策略分析');

		// 使用多级分割策略获取内容块
		const contentBlocks = await this.splitContentByMultipleStrategies(moduleElement);
		console.log('多级分割结果:', contentBlocks.length, '个逻辑块');

		// 检查是否成功分割出内容
		if (contentBlocks.length === 0) {
			console.error('分割策略未能产生任何内容块，回退到元素级分页');
			// 回退策略：使用元素级精确分页
			return await this.splitContentByElementLevel(moduleElement, maxPageHeight, pageWidth);
		}

		// 智能合并和分页 - 在HR/H3分块基础上进行元素级精确分页
		let currentPageElements: HTMLElement[] = [];
		let currentPageHeight = 0;

		for (let blockIndex = 0; blockIndex < contentBlocks.length; blockIndex++) {
			const block = contentBlocks[blockIndex];

			// 检查块是否有有效内容
			if (!block.elements || block.elements.length === 0) {
				console.warn(`逻辑块 ${blockIndex + 1} 为空，跳过`);
				continue;
			}

			const blockActualHeight = await this.calculateBlockHeight(block.elements);

			console.log(`处理逻辑块 ${blockIndex + 1}:`, {
				hasBreakSeparator: block.hasBreakSeparator,
				splitStrategy: block.splitStrategy,
				elementsCount: block.elements.length,
				blockHeight: blockActualHeight,
				currentPageHeight: currentPageHeight,
				blockContent: block.elements.map((el) => el.textContent?.substring(0, 50)).join(', '),
			});

			// 检查单个块是否超过页面高度，需要进一步分割
			if (blockActualHeight > maxPageHeight) {
				console.log(`逻辑块 ${blockIndex + 1} 超过页面高度，需要进一步分割`);

				// 如果当前页已有内容，先生成当前页
				if (currentPageElements.length > 0) {
					const actualPageHeight = await this.calculateActualPageHeight(currentPageElements);
					const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, actualPageHeight);
					canvases.push(pageCanvas);
					currentPageElements = [];
					currentPageHeight = 0;
				}

				// 对超大块进行元素级精确分页
				const blockCanvases = await this.splitContentByElementLevel(this.createTempContainer(block.elements), maxPageHeight, pageWidth);
				canvases.push(...blockCanvases);

				// 重置当前页状态
				currentPageElements = [];
				currentPageHeight = 0;
				continue;
			}

			// 检查是否需要分页
			const wouldExceedHeight = currentPageHeight + blockActualHeight > maxPageHeight;
			const shouldForceBreak = block.hasBreakSeparator && currentPageElements.length > 0;

			if ((wouldExceedHeight || shouldForceBreak) && currentPageElements.length > 0) {
				// 生成当前页
				console.log(
					`生成页面，当前页高度: ${currentPageHeight}，包含 ${currentPageElements.length} 个元素，原因: ${
						shouldForceBreak ? `${block.splitStrategy}强制分页` : '高度超限'
					}`
				);
				const actualPageHeight = await this.calculateActualPageHeight(currentPageElements);
				const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, actualPageHeight);
				canvases.push(pageCanvas);

				// 重置下一页
				currentPageElements = [...block.elements];
				currentPageHeight = blockActualHeight;
			} else {
				// 添加到当前页
				currentPageElements.push(...block.elements);
				currentPageHeight += blockActualHeight;
			}
		}

		// 生成最后一页
		if (currentPageElements.length > 0) {
			console.log(`生成最后一页，高度: ${currentPageHeight}，包含 ${currentPageElements.length} 个元素`);
			const actualPageHeight = await this.calculateActualPageHeight(currentPageElements);
			const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, actualPageHeight);
			canvases.push(pageCanvas);
		}

		console.log('智能多级分页完成，总计页数:', canvases.length);

		// 最终检查是否生成了任何内容
		if (canvases.length === 0) {
			console.error('分页过程完成但未生成任何画布，这表明存在严重问题');
			throw new Error('PDF生成失败：未能生成任何页面内容');
		}

		return canvases;
	}

	// 新增：创建临时容器（用于包装元素数组）
	private createTempContainer(elements: HTMLElement[]): HTMLElement {
		const container = document.createElement('div');
		elements.forEach((element) => {
			const clonedElement = element.cloneNode(true) as HTMLElement;
			container.appendChild(clonedElement);
		});
		return container;
	}

	// 新增：元素级精确分页 - 确保不截断文字
	private async splitContentByElementLevel(moduleElement: HTMLElement, maxPageHeight: number, pageWidth: number): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];
		const allElements = Array.from(moduleElement.children) as HTMLElement[];

		if (allElements.length === 0) {
			console.warn('没有找到任何子元素');
			return [];
		}

		let currentPageElements: HTMLElement[] = [];
		let currentPageHeight = 0;

		console.log(`开始元素级分页，共 ${allElements.length} 个元素`);

		for (let i = 0; i < allElements.length; i++) {
			const element = allElements[i];

			// 跳过HR标签（用于分页控制但不显示）
			if (element.tagName.toLowerCase() === 'hr') {
				// HR标签强制分页
				if (currentPageElements.length > 0) {
					console.log(`遇到HR标签，强制分页，当前页包含 ${currentPageElements.length} 个元素`);
					const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, currentPageHeight);
					canvases.push(pageCanvas);
					currentPageElements = [];
					currentPageHeight = 0;
				}
				continue;
			}

			// 计算当前元素的高度
			const elementHeight = await this.calculateSingleElementHeight(element, pageWidth);

			console.log(`元素 ${i + 1}/${allElements.length} (${element.tagName}):`, {
				elementHeight,
				currentPageHeight,
				wouldExceed: currentPageHeight + elementHeight > maxPageHeight,
				content: element.textContent?.substring(0, 50) || '无内容',
			});

			// 检查单个元素是否超过页面高度
			if (elementHeight > maxPageHeight) {
				console.log(`元素 ${i + 1} 高度超过页面限制，需要拆分`);

				// 先生成当前页（如果有内容）
				if (currentPageElements.length > 0) {
					const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, currentPageHeight);
					canvases.push(pageCanvas);
					currentPageElements = [];
					currentPageHeight = 0;
				}

				// 对超大元素进行内部拆分
				const elementCanvases = await this.splitLargeElement(element, maxPageHeight, pageWidth);
				canvases.push(...elementCanvases);
				continue;
			}

			// 检查添加当前元素是否会超出页面高度
			if (currentPageHeight + elementHeight > maxPageHeight && currentPageElements.length > 0) {
				// 当前页已满，生成页面
				console.log(`页面已满，生成页面，包含 ${currentPageElements.length} 个元素，高度: ${currentPageHeight}`);
				const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, currentPageHeight);
				canvases.push(pageCanvas);

				// 开始新页面
				currentPageElements = [element];
				currentPageHeight = elementHeight;
			} else {
				// 添加到当前页面
				currentPageElements.push(element);
				currentPageHeight += elementHeight;
			}
		}

		// 生成最后一页
		if (currentPageElements.length > 0) {
			console.log(`生成最后一页，包含 ${currentPageElements.length} 个元素，高度: ${currentPageHeight}`);
			const pageCanvas = await this.generatePageFromElements(currentPageElements, pageWidth, currentPageHeight);
			canvases.push(pageCanvas);
		}

		console.log(`元素级分页完成，总计 ${canvases.length} 页`);
		return canvases;
	}

	// 新增：计算单个元素的精确高度
	private async calculateSingleElementHeight(element: HTMLElement, containerWidth: number): Promise<number> {
		// 创建临时容器来测量单个元素高度
		const tempContainer = document.createElement('div');
		tempContainer.style.position = 'absolute';
		tempContainer.style.left = '0px';
		tempContainer.style.top = '0px';
		tempContainer.style.width = containerWidth + 'px';
		tempContainer.style.backgroundColor = '#ffffff';
		tempContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		tempContainer.style.fontSize = '11px';
		tempContainer.style.lineHeight = '1.3';
		tempContainer.style.padding = '0 30px';
		tempContainer.style.boxSizing = 'border-box';
		tempContainer.style.zIndex = '-1000';

		// 克隆元素
		const clonedElement = element.cloneNode(true) as HTMLElement;
		tempContainer.appendChild(clonedElement);

		// 应用样式
		this.applyMarkdownStyles(tempContainer);

		// 添加到DOM并测量
		document.body.appendChild(tempContainer);
		await new Promise((resolve) => setTimeout(resolve, 100));

		const elementHeight = tempContainer.scrollHeight;

		// 清理
		document.body.removeChild(tempContainer);

		return elementHeight;
	}

	// 新增：拆分超大元素（如长表格、长代码块等）
	private async splitLargeElement(element: HTMLElement, maxPageHeight: number, pageWidth: number): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];
		const tagName = element.tagName.toLowerCase();

		console.log(`拆分超大元素: ${tagName}`);

		// 对于表格，按行拆分
		if (tagName === 'table') {
			return await this.splitLargeTable(element, maxPageHeight, pageWidth);
		}

		// 对于列表，按列表项拆分
		if (tagName === 'ul' || tagName === 'ol') {
			return await this.splitLargeList(element, maxPageHeight, pageWidth);
		}

		// 对于其他元素，使用固定高度分割（保持原有逻辑作为后备）
		return await this.splitElementByFixedHeight(element, maxPageHeight, pageWidth);
	}

	// 新增：拆分大表格
	private async splitLargeTable(tableElement: HTMLElement, maxPageHeight: number, pageWidth: number): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];
		const table = tableElement as HTMLTableElement;
		const tbody = table.querySelector('tbody');

		if (!tbody) {
			// 如果没有tbody，使用固定高度分割
			return await this.splitElementByFixedHeight(tableElement, maxPageHeight, pageWidth);
		}

		const rows = Array.from(tbody.querySelectorAll('tr')) as HTMLTableRowElement[];
		const thead = table.querySelector('thead');

		let currentRows: HTMLTableRowElement[] = [];
		let currentHeight = 0;

		// 计算表头高度
		let headerHeight = 0;
		if (thead) {
			headerHeight = await this.calculateSingleElementHeight(thead as HTMLElement, pageWidth);
		}

		for (let i = 0; i < rows.length; i++) {
			const row = rows[i];
			const rowHeight = await this.calculateSingleElementHeight(row, pageWidth);

			// 检查添加当前行是否会超出页面高度（包括表头）
			if (currentHeight + rowHeight + headerHeight > maxPageHeight && currentRows.length > 0) {
				// 生成当前页的表格
				const pageTable = await this.createTablePage(table, thead, currentRows, pageWidth);
				canvases.push(pageTable);

				// 开始新页
				currentRows = [row];
				currentHeight = rowHeight;
			} else {
				currentRows.push(row);
				currentHeight += rowHeight;
			}
		}

		// 生成最后一页
		if (currentRows.length > 0) {
			const pageTable = await this.createTablePage(table, thead, currentRows, pageWidth);
			canvases.push(pageTable);
		}

		return canvases;
	}

	// 新增：创建表格页面
	private async createTablePage(
		originalTable: HTMLTableElement,
		thead: Element | null,
		rows: HTMLTableRowElement[],
		pageWidth: number
	): Promise<HTMLCanvasElement> {
		// 创建新的表格容器
		const container = document.createElement('div');
		container.style.position = 'absolute';
		container.style.left = '0px';
		container.style.top = '0px';
		container.style.width = pageWidth + 'px';
		container.style.backgroundColor = '#ffffff';
		container.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		container.style.fontSize = '11px';
		container.style.lineHeight = '1.3';
		container.style.padding = '0 30px';
		container.style.boxSizing = 'border-box';
		container.style.zIndex = '-1000';

		// 克隆表格结构
		const newTable = originalTable.cloneNode(false) as HTMLTableElement;

		// 添加表头（如果存在）
		if (thead) {
			const clonedThead = thead.cloneNode(true) as HTMLElement;
			newTable.appendChild(clonedThead);
		}

		// 创建tbody并添加行
		const newTbody = document.createElement('tbody');
		rows.forEach((row) => {
			const clonedRow = row.cloneNode(true) as HTMLTableRowElement;
			newTbody.appendChild(clonedRow);
		});
		newTable.appendChild(newTbody);

		container.appendChild(newTable);

		// 应用样式
		this.applyMarkdownStyles(container);

		// 添加到DOM
		document.body.appendChild(container);
		await new Promise((resolve) => setTimeout(resolve, 300));

		// 生成画布
		const canvas = await html2canvas(container, {
			scale: 4,
			useCORS: true,
			allowTaint: true,
			logging: false,
			backgroundColor: '#ffffff',
			width: pageWidth,
			height: container.scrollHeight,
		});

		// 清理
		document.body.removeChild(container);

		return canvas;
	}

	// 新增：拆分大列表
	private async splitLargeList(listElement: HTMLElement, maxPageHeight: number, pageWidth: number): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];
		const listItems = Array.from(listElement.querySelectorAll('li')) as HTMLElement[];

		let currentItems: HTMLElement[] = [];
		let currentHeight = 0;

		for (let i = 0; i < listItems.length; i++) {
			const item = listItems[i];
			const itemHeight = await this.calculateSingleElementHeight(item, pageWidth);

			if (currentHeight + itemHeight > maxPageHeight && currentItems.length > 0) {
				// 生成当前页的列表
				const pageList = await this.createListPage(listElement, currentItems, pageWidth);
				canvases.push(pageList);

				// 开始新页
				currentItems = [item];
				currentHeight = itemHeight;
			} else {
				currentItems.push(item);
				currentHeight += itemHeight;
			}
		}

		// 生成最后一页
		if (currentItems.length > 0) {
			const pageList = await this.createListPage(listElement, currentItems, pageWidth);
			canvases.push(pageList);
		}

		return canvases;
	}

	// 新增：创建列表页面
	private async createListPage(originalList: HTMLElement, items: HTMLElement[], pageWidth: number): Promise<HTMLCanvasElement> {
		// 创建容器
		const container = document.createElement('div');
		container.style.position = 'absolute';
		container.style.left = '0px';
		container.style.top = '0px';
		container.style.width = pageWidth + 'px';
		container.style.backgroundColor = '#ffffff';
		container.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		container.style.fontSize = '11px';
		container.style.lineHeight = '1.3';
		container.style.padding = '0 30px';
		container.style.boxSizing = 'border-box';
		container.style.zIndex = '-1000';

		// 克隆列表结构
		const newList = originalList.cloneNode(false) as HTMLElement;

		// 添加列表项
		items.forEach((item) => {
			const clonedItem = item.cloneNode(true) as HTMLElement;
			newList.appendChild(clonedItem);
		});

		container.appendChild(newList);

		// 应用样式
		this.applyMarkdownStyles(container);

		// 添加到DOM
		document.body.appendChild(container);
		await new Promise((resolve) => setTimeout(resolve, 300));

		// 生成画布
		const canvas = await html2canvas(container, {
			scale: 4,
			useCORS: true,
			allowTaint: true,
			logging: false,
			backgroundColor: '#ffffff',
			width: pageWidth,
			height: container.scrollHeight,
		});

		// 清理
		document.body.removeChild(container);

		return canvas;
	}

	// 新增：按固定高度拆分元素（后备方案）
	private async splitElementByFixedHeight(element: HTMLElement, maxPageHeight: number, pageWidth: number): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];

		// 创建包含元素的临时容器
		const fullContainer = document.createElement('div');
		fullContainer.style.position = 'absolute';
		fullContainer.style.left = '0px';
		fullContainer.style.top = '0px';
		fullContainer.style.width = pageWidth + 'px';
		fullContainer.style.backgroundColor = '#ffffff';
		fullContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		fullContainer.style.fontSize = '11px';
		fullContainer.style.lineHeight = '1.3';
		fullContainer.style.padding = '0 30px';
		fullContainer.style.boxSizing = 'border-box';

		const clonedElement = element.cloneNode(true) as HTMLElement;
		fullContainer.appendChild(clonedElement);

		this.applyMarkdownStyles(fullContainer);

		document.body.appendChild(fullContainer);
		await new Promise((resolve) => setTimeout(resolve, 200));

		const totalHeight = fullContainer.scrollHeight;
		const pageCount = Math.ceil(totalHeight / maxPageHeight);

		console.log(`固定高度分割: 总高度=${totalHeight}, 页面高度=${maxPageHeight}, 需要分页=${pageCount}`);

		// 按照页面高度分割生成画布
		for (let i = 0; i < pageCount; i++) {
			const currentPageHeight = Math.min(maxPageHeight, totalHeight - i * maxPageHeight);

			// 创建当前页的容器
			const pageContainer = document.createElement('div');
			pageContainer.style.position = 'absolute';
			pageContainer.style.left = '0px';
			pageContainer.style.top = '0px';
			pageContainer.style.width = pageWidth + 'px';
			pageContainer.style.height = maxPageHeight + 'px';
			pageContainer.style.overflow = 'hidden';
			pageContainer.style.backgroundColor = '#ffffff';
			pageContainer.style.zIndex = '-1000';

			// 创建内容容器
			const contentContainer = document.createElement('div');
			contentContainer.style.position = 'relative';
			contentContainer.style.width = pageWidth + 'px';
			contentContainer.style.backgroundColor = '#ffffff';
			contentContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
			contentContainer.style.fontSize = '11px';
			contentContainer.style.lineHeight = '1.3';
			contentContainer.style.padding = '0 30px';
			contentContainer.style.boxSizing = 'border-box';
			contentContainer.style.top = -i * maxPageHeight + 'px';

			const pageElement = element.cloneNode(true) as HTMLElement;
			contentContainer.appendChild(pageElement);

			this.applyMarkdownStyles(contentContainer);

			pageContainer.appendChild(contentContainer);
			document.body.appendChild(pageContainer);

			await new Promise((resolve) => setTimeout(resolve, 300));

			const canvas = await html2canvas(pageContainer, {
				scale: 4,
				useCORS: true,
				allowTaint: true,
				logging: false,
				backgroundColor: '#ffffff',
				width: pageWidth,
				height: maxPageHeight,
			});

			canvases.push(canvas);
			document.body.removeChild(pageContainer);
		}

		// 清理
		document.body.removeChild(fullContainer);

		return canvases;
	}

	// 旧的分页逻辑保留（暂时不删除，以防需要回退）

	// 新增：多级分割策略 - HR → H3 → 高度
	private async splitContentByMultipleStrategies(moduleElement: HTMLElement): Promise<
		Array<{
			elements: HTMLElement[];
			hasBreakSeparator: boolean;
			splitStrategy: string;
		}>
	> {
		const allElements = Array.from(moduleElement.children) as HTMLElement[];

		console.log('多级分割策略输入分析:', {
			totalElements: allElements.length,
			elementTypes: allElements.map((el) => el.tagName.toLowerCase()),
			hasContent: moduleElement.textContent?.trim().length || 0,
			moduleHTML: moduleElement.innerHTML.substring(0, 200),
		});

		// 检查是否有内容
		if (allElements.length === 0) {
			console.warn('没有找到任何子元素，尝试创建包装元素');
			if (moduleElement.textContent?.trim()) {
				// 如果有文本内容但没有子元素，创建一个div包装
				const wrapperDiv = document.createElement('div');
				wrapperDiv.innerHTML = moduleElement.innerHTML;
				return [
					{
						elements: [wrapperDiv],
						hasBreakSeparator: false,
						splitStrategy: '包装元素',
					},
				];
			} else {
				console.error('模块内容完全为空');
				return [];
			}
		}

		// 策略1：优先检查HR标签
		const hrElements = allElements.filter((el) => el.tagName.toLowerCase() === 'hr');
		if (hrElements.length > 0) {
			console.log('检测到HR标签，使用HR分割策略');
			return this.splitContentByHrTags(moduleElement);
		}

		// 策略2：检查H3标签
		const h3Elements = allElements.filter((el) => el.tagName.toLowerCase() === 'h3');
		if (h3Elements.length > 0) {
			console.log('未检测到HR标签，但检测到H3标签，使用H3分割策略');
			return this.splitContentByH3Tags(moduleElement);
		}

		// 策略3：按内容高度智能分割
		console.log('未检测到HR和H3标签，使用智能高度分割策略');
		return await this.splitContentByHeight(moduleElement);
	}

	// 修改：根据HR标签分割内容为逻辑块
	private splitContentByHrTags(moduleElement: HTMLElement): Array<{
		elements: HTMLElement[];
		hasBreakSeparator: boolean;
		splitStrategy: string;
	}> {
		const allElements = Array.from(moduleElement.children) as HTMLElement[];
		const blocks: Array<{ elements: HTMLElement[]; hasBreakSeparator: boolean; splitStrategy: string }> = [];
		let currentBlock: HTMLElement[] = [];

		for (let i = 0; i < allElements.length; i++) {
			const element = allElements[i];

			if (element.tagName.toLowerCase() === 'hr') {
				// 遇到HR标签，结束当前块
				if (currentBlock.length > 0) {
					blocks.push({
						elements: [...currentBlock],
						hasBreakSeparator: true,
						splitStrategy: 'HR标签',
					});
					currentBlock = [];
				}
				// HR标签本身不加入任何块（实现隐藏效果）
				console.log('检测到HR分页标识，进行分块');
			} else {
				// 普通元素添加到当前块
				currentBlock.push(element);
			}
		}

		// 处理最后一个块
		if (currentBlock.length > 0) {
			blocks.push({
				elements: currentBlock,
				hasBreakSeparator: false,
				splitStrategy: 'HR标签',
			});
		}

		// 如果没有HR标签，则将所有元素作为一个块
		if (blocks.length === 0 && allElements.length > 0) {
			blocks.push({
				elements: allElements.filter((el) => el.tagName.toLowerCase() !== 'hr'),
				hasBreakSeparator: false,
				splitStrategy: 'HR标签',
			});
		}

		console.log(
			'HR标签分块结果:',
			blocks.map((block, index) => ({
				blockIndex: index + 1,
				elementsCount: block.elements.length,
				hasBreakSeparator: block.hasBreakSeparator,
				splitStrategy: block.splitStrategy,
			}))
		);

		return blocks;
	}

	// 新增：根据H3标签分割内容为逻辑块
	private splitContentByH3Tags(moduleElement: HTMLElement): Array<{
		elements: HTMLElement[];
		hasBreakSeparator: boolean;
		splitStrategy: string;
	}> {
		const allElements = Array.from(moduleElement.children) as HTMLElement[];
		const blocks: Array<{ elements: HTMLElement[]; hasBreakSeparator: boolean; splitStrategy: string }> = [];
		let currentBlock: HTMLElement[] = [];

		for (let i = 0; i < allElements.length; i++) {
			const element = allElements[i];

			if (element.tagName.toLowerCase() === 'h3') {
				// 遇到H3标签，如果当前块有内容则结束当前块
				if (currentBlock.length > 0) {
					blocks.push({
						elements: [...currentBlock],
						hasBreakSeparator: true,
						splitStrategy: 'H3标签',
					});
					currentBlock = [];
				}
				// H3标签作为新块的开始
				currentBlock.push(element);
				console.log('检测到H3标签分页标识，进行分块');
			} else {
				// 普通元素添加到当前块
				currentBlock.push(element);
			}
		}

		// 处理最后一个块
		if (currentBlock.length > 0) {
			blocks.push({
				elements: currentBlock,
				hasBreakSeparator: false,
				splitStrategy: 'H3标签',
			});
		}

		// 如果没有找到有效的分块，将所有元素作为一个块
		if (blocks.length === 0 && allElements.length > 0) {
			blocks.push({
				elements: allElements,
				hasBreakSeparator: false,
				splitStrategy: 'H3标签',
			});
		}

		console.log(
			'H3标签分块结果:',
			blocks.map((block, index) => ({
				blockIndex: index + 1,
				elementsCount: block.elements.length,
				hasBreakSeparator: block.hasBreakSeparator,
				splitStrategy: block.splitStrategy,
			}))
		);

		return blocks;
	}

	// 新增：根据内容高度智能分割
	private async splitContentByHeight(moduleElement: HTMLElement): Promise<
		Array<{
			elements: HTMLElement[];
			hasBreakSeparator: boolean;
			splitStrategy: string;
		}>
	> {
		const allElements = Array.from(moduleElement.children) as HTMLElement[];
		const blocks: Array<{ elements: HTMLElement[]; hasBreakSeparator: boolean; splitStrategy: string }> = [];
		const maxPageHeight = this.pageHeight * 0.85;

		let currentBlock: HTMLElement[] = [];
		let currentBlockHeight = 0;

		for (let i = 0; i < allElements.length; i++) {
			const element = allElements[i];
			const elementHeight = await this.calculateBlockHeight([element]);

			// 检查添加当前元素是否会超出页面高度
			if (currentBlockHeight + elementHeight > maxPageHeight && currentBlock.length > 0) {
				// 当前块已满，开始新块
				blocks.push({
					elements: [...currentBlock],
					hasBreakSeparator: true,
					splitStrategy: '智能高度',
				});
				currentBlock = [element];
				currentBlockHeight = elementHeight;
			} else {
				// 添加到当前块
				currentBlock.push(element);
				currentBlockHeight += elementHeight;
			}
		}

		// 处理最后一个块
		if (currentBlock.length > 0) {
			blocks.push({
				elements: currentBlock,
				hasBreakSeparator: false,
				splitStrategy: '智能高度',
			});
		}

		console.log(
			'智能高度分块结果:',
			blocks.map((block, index) => ({
				blockIndex: index + 1,
				elementsCount: block.elements.length,
				hasBreakSeparator: block.hasBreakSeparator,
				splitStrategy: block.splitStrategy,
				estimatedHeight: '待计算',
			}))
		);

		return blocks;
	}

	// 新增：对超大模块进行精确的剩余内容分页
	private async splitModuleByRemainingContent(elements: HTMLElement[], maxPageHeight: number, pageWidth: number): Promise<HTMLCanvasElement[]> {
		const canvases: HTMLCanvasElement[] = [];

		// 创建包含所有元素的临时容器来测量总高度
		const fullContainer = document.createElement('div');
		fullContainer.style.position = 'absolute';
		fullContainer.style.left = '-9999px';
		fullContainer.style.top = '-9999px';
		fullContainer.style.width = pageWidth + 'px';
		fullContainer.style.backgroundColor = '#ffffff';
		fullContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		fullContainer.style.fontSize = '11px'; // 与主容器保持一致
		fullContainer.style.lineHeight = '1.3'; // 与主容器保持一致
		fullContainer.style.padding = '0 30px'; // 左右30px内边距，上下0内边距
		fullContainer.style.boxSizing = 'border-box';

		// 克隆所有元素
		elements.forEach((element) => {
			const clonedElement = element.cloneNode(true) as HTMLElement;
			fullContainer.appendChild(clonedElement);
		});

		// 应用样式
		this.applyMarkdownStyles(fullContainer);

		document.body.appendChild(fullContainer);
		await new Promise((resolve) => setTimeout(resolve, 200));

		const totalHeight = fullContainer.scrollHeight;
		const totalWidth = fullContainer.scrollWidth;
		const pageCount = Math.ceil(totalHeight / maxPageHeight);

		console.log(`超大模块分页信息: 总高度=${totalHeight}, 页面高度=${maxPageHeight}, 需要分页=${pageCount}`);

		// 确保内容有效性检查
		if (totalHeight === 0 || !fullContainer.textContent?.trim()) {
			console.error('超大模块内容为空，无法生成分页');
			document.body.removeChild(fullContainer);
			return [];
		}

		// 按照页面高度分割生成画布
		for (let i = 0; i < pageCount; i++) {
			const isLastPage = i === pageCount - 1;
			const currentPageHeight = isLastPage ? totalHeight - i * maxPageHeight : maxPageHeight;

			// 创建当前页的容器 - 使用可见定位
			const pageContainer = document.createElement('div');
			pageContainer.style.position = 'absolute';
			pageContainer.style.left = '0px'; // 修改：使用可见位置而不是-9999px
			pageContainer.style.top = '0px'; // 修改：使用可见位置而不是-9999px
			pageContainer.style.width = pageWidth + 'px';
			pageContainer.style.height = maxPageHeight + 'px';
			pageContainer.style.overflow = 'hidden';
			pageContainer.style.backgroundColor = '#ffffff';
			pageContainer.style.zIndex = '-1000'; // 添加：使用z-index隐藏而不是位置隐藏

			// 创建内容容器
			const contentContainer = document.createElement('div');
			contentContainer.style.position = 'relative';
			contentContainer.style.width = pageWidth + 'px';
			contentContainer.style.backgroundColor = '#ffffff';
			contentContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
			contentContainer.style.fontSize = '11px';
			contentContainer.style.lineHeight = '1.3';
			contentContainer.style.padding = '0 30px';
			contentContainer.style.boxSizing = 'border-box';
			contentContainer.style.top = -i * maxPageHeight + 'px'; // 偏移显示当前页内容

			// 克隆元素到内容容器
			elements.forEach((element) => {
				const clonedElement = element.cloneNode(true) as HTMLElement;
				contentContainer.appendChild(clonedElement);
			});

			// 应用样式
			this.applyMarkdownStyles(contentContainer);

			pageContainer.appendChild(contentContainer);
			document.body.appendChild(pageContainer);

			// 等待渲染
			await new Promise((resolve) => setTimeout(resolve, 300));

			console.log(`第 ${i + 1}/${pageCount} 页内容检查:`, {
				pageHeight: currentPageHeight,
				containerText: contentContainer.textContent?.substring(0, 100) || '无内容',
				containerHeight: contentContainer.scrollHeight,
				containerWidth: contentContainer.scrollWidth,
			});

			// 生成当前页画布 - 修正参数
			const canvas = await html2canvas(pageContainer, {
				scale: 4,
				useCORS: true,
				allowTaint: true,
				logging: true, // 开启日志查看问题
				backgroundColor: '#ffffff',
				width: pageWidth,
				height: maxPageHeight,
				x: 0, // 添加：明确指定截取起始位置
				y: 0, // 添加：明确指定截取起始位置
				scrollX: 0, // 添加：重置滚动位置
				scrollY: 0, // 添加：重置滚动位置
			});

			canvases.push(canvas);

			// 清理
			document.body.removeChild(pageContainer);

			console.log(`生成超大模块第 ${i + 1}/${pageCount} 页，高度: ${currentPageHeight}, 画布尺寸: ${canvas.width}x${canvas.height}`);
		}

		// 清理完整容器
		document.body.removeChild(fullContainer);

		return canvases;
	}

	// 新增：计算逻辑块的实际高度
	private async calculateBlockHeight(elements: HTMLElement[]): Promise<number> {
		if (elements.length === 0) return 0;

		// 创建临时容器来测量块高度
		const tempContainer = document.createElement('div');
		tempContainer.style.position = 'absolute';
		tempContainer.style.left = '0px'; // 修改：使用可见位置
		tempContainer.style.top = '0px'; // 修改：使用可见位置
		tempContainer.style.width = this.pageWidth + 'px';
		tempContainer.style.backgroundColor = '#ffffff';
		tempContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		tempContainer.style.fontSize = '11px'; // 保持与主容器一致
		tempContainer.style.lineHeight = '1.3'; // 保持与主容器一致
		tempContainer.style.padding = '0 30px'; // 左右15px内边距，上下0内边距
		tempContainer.style.boxSizing = 'border-box';
		tempContainer.style.zIndex = '-1000'; // 使用z-index隐藏

		// 检查元素有效性
		if (!elements || elements.length === 0) {
			return 0;
		}

		// 克隆元素到临时容器
		elements.forEach((element) => {
			if (element && element.cloneNode) {
				const clonedElement = element.cloneNode(true) as HTMLElement;
				tempContainer.appendChild(clonedElement);
			}
		});

		// 应用样式
		this.applyMarkdownStyles(tempContainer);

		// 添加到DOM并测量
		document.body.appendChild(tempContainer);
		await new Promise((resolve) => setTimeout(resolve, 200));

		const blockHeight = tempContainer.scrollHeight;

		console.log('calculateBlockHeight:', {
			elementsCount: elements.length,
			blockHeight,
			hasContent: !!tempContainer.textContent?.trim(),
		});

		// 清理
		document.body.removeChild(tempContainer);

		return blockHeight;
	}

	// 新增：计算页面实际渲染高度
	private async calculateActualPageHeight(elements: HTMLElement[]): Promise<number> {
		// 创建临时容器来测量实际高度
		const tempContainer = document.createElement('div');
		tempContainer.style.position = 'absolute';
		tempContainer.style.left = '0px'; // 修改：使用可见位置
		tempContainer.style.top = '0px'; // 修改：使用可见位置
		tempContainer.style.width = this.pageWidth + 'px'; // 使用完整页面宽度
		tempContainer.style.backgroundColor = '#ffffff';
		tempContainer.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		tempContainer.style.fontSize = '11px'; // 与主容器保持一致
		tempContainer.style.lineHeight = '1.3'; // 与主容器保持一致
		tempContainer.style.padding = '0 30px'; // 左右15px内边距，上下0内边距
		tempContainer.style.boxSizing = 'border-box';
		tempContainer.style.zIndex = '-1000'; // 使用z-index隐藏

		// 检查元素有效性
		if (!elements || elements.length === 0) {
			return 0;
		}

		// 克隆元素到临时容器
		elements.forEach((element) => {
			if (element && element.cloneNode) {
				const clonedElement = element.cloneNode(true) as HTMLElement;
				tempContainer.appendChild(clonedElement);
			}
		});

		// 应用样式
		this.applyMarkdownStyles(tempContainer);

		// 添加到DOM并测量
		document.body.appendChild(tempContainer);
		await new Promise((resolve) => setTimeout(resolve, 300)); // 增加渲染等待时间

		const actualHeight = tempContainer.scrollHeight;

		console.log('calculateActualPageHeight:', {
			elementsCount: elements.length,
			actualHeight,
			hasContent: !!tempContainer.textContent?.trim(),
		});

		// 清理
		document.body.removeChild(tempContainer);

		return actualHeight;
	}

	// 从元素数组生成页面 (优化版 - 使用实际高度，减少空白)
	private async generatePageFromElements(elements: HTMLElement[], width: number, contentHeight: number): Promise<HTMLCanvasElement> {
		// 创建临时容器
		const container = document.createElement('div');
		container.style.position = 'absolute';
		container.style.left = '0px'; // 修改：使用可见位置
		container.style.top = '0px'; // 修改：使用可见位置
		container.style.width = this.pageWidth + 'px'; // 使用完整页面宽度
		container.style.backgroundColor = '#ffffff';
		container.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		container.style.fontSize = '11px'; // 与主容器保持一致
		container.style.lineHeight = '1.3'; // 与主容器保持一致
		container.style.overflow = 'visible'; // 改为visible以确保内容不被截断
		container.style.padding = '0 30px'; // 左右15px内边距，上下0内边距
		container.style.boxSizing = 'border-box';
		container.style.zIndex = '-1000'; // 添加：使用z-index隐藏而不是位置隐藏

		// 检查元素有效性
		if (!elements || elements.length === 0) {
			console.error('generatePageFromElements: 传入的元素数组为空');
			return document.createElement('canvas'); // 返回空画布避免崩溃
		}

		// 克隆并添加所有元素
		elements.forEach((element) => {
			if (element && element.cloneNode) {
				const clonedElement = element.cloneNode(true) as HTMLElement;
				clonedElement.style.position = 'relative';
				clonedElement.style.width = '100%';
				clonedElement.style.wordWrap = 'break-word';
				clonedElement.style.wordBreak = 'break-word';
				container.appendChild(clonedElement);
			} else {
				console.warn('generatePageFromElements: 发现无效元素，跳过', element);
			}
		});

		// 应用样式
		this.applyMarkdownStyles(container);

		// 添加到DOM
		document.body.appendChild(container);

		// 等待渲染
		await new Promise((resolve) => setTimeout(resolve, 500)); // 增加渲染等待时间

		// 获取实际渲染后的高度
		const actualHeight = container.scrollHeight;
		const actualWidth = container.scrollWidth;

		console.log('生成页面元素详情:', {
			elementsCount: elements.length,
			containerWidth: actualWidth,
			containerHeight: actualHeight,
			contentHeight: contentHeight,
			containerText: container.textContent?.substring(0, 100) || '无内容',
			hasValidContent: !!container.textContent?.trim(),
		});

		// 内容有效性检查
		if (actualHeight === 0 || !container.textContent?.trim()) {
			console.error('generatePageFromElements: 容器内容为空或高度为0');
			document.body.removeChild(container);
			return document.createElement('canvas'); // 返回空画布避免崩溃
		}

		try {
			// 截图 - 使用实际高度和修正的参数
			const canvas = await html2canvas(container, {
				scale: 4,
				useCORS: true,
				allowTaint: true,
				logging: true, // 开启日志以便调试
				backgroundColor: '#ffffff',
				width: this.pageWidth, // 使用页面宽度
				height: actualHeight, // 使用实际高度而非预估高度
				x: 0, // 明确指定截取起始位置
				y: 0, // 明确指定截取起始位置
				scrollX: 0, // 重置滚动位置
				scrollY: 0, // 重置滚动位置
			});

			console.log('generatePageFromElements: 画布生成成功', {
				canvasWidth: canvas.width,
				canvasHeight: canvas.height,
			});

			return canvas;
		} catch (error) {
			console.error('generatePageFromElements: html2canvas截图失败', error);
			return document.createElement('canvas'); // 返回空画布避免崩溃
		} finally {
			// 确保清理DOM
			if (document.body.contains(container)) {
				document.body.removeChild(container);
			}
		}
	}

	// 应用Markdown样式
	private applyMarkdownStyles(container: HTMLElement): void {
		// 标题样式 - 进一步缩小字体和间距
		const h1s = container.querySelectorAll('h1');
		h1s.forEach((h1) => {
			(h1 as HTMLElement).style.fontSize = '16px'; // 从18px调整为16px
			(h1 as HTMLElement).style.fontWeight = 'bold';
			(h1 as HTMLElement).style.marginTop = '5px'; // 从15px调整为12px
			(h1 as HTMLElement).style.marginBottom = '8px'; // 从10px调整为8px
			(h1 as HTMLElement).style.color = '#008ccc';
			(h1 as HTMLElement).style.paddingBottom = '5px'; // 进一步减少标题底部内边距
		});

		const h2s = container.querySelectorAll('h2');
		h2s.forEach((h2) => {
			(h2 as HTMLElement).style.fontSize = '14px'; // 从15px调整为14px
			(h2 as HTMLElement).style.fontWeight = 'bold';
			(h2 as HTMLElement).style.marginTop = '10px'; // 从12px调整为10px
			(h2 as HTMLElement).style.marginBottom = '6px'; // 从8px调整为6px
			(h2 as HTMLElement).style.color = '#008ccc';
		});

		const h3s = container.querySelectorAll('h3');
		h3s.forEach((h3) => {
			(h3 as HTMLElement).style.fontSize = '12px'; // 从13px调整为12px
			(h3 as HTMLElement).style.fontWeight = 'bold';
			(h3 as HTMLElement).style.marginBottom = '8px'; // 从6px调整为4px
			(h3 as HTMLElement).style.color = '#333';
		});
		const h4s = container.querySelectorAll('h4');
		h4s.forEach((h4) => {
			(h4 as HTMLElement).style.fontSize = '12px'; // 从13px调整为12px
			(h4 as HTMLElement).style.fontWeight = 'bold';
			(h4 as HTMLElement).style.marginTop = '4px'; // 从10px调整为8px
			(h4 as HTMLElement).style.marginBottom = '8px'; // 从6px调整为4px
			(h4 as HTMLElement).style.color = '#333';
		});
		const h5s = container.querySelectorAll('h5');
		h5s.forEach((h5) => {
			(h5 as HTMLElement).style.fontSize = '10px'; // 从13px调整为12px
			(h5 as HTMLElement).style.fontWeight = 'bold';
			(h5 as HTMLElement).style.marginTop = '4px'; // 从10px调整为8px
			(h5 as HTMLElement).style.marginBottom = '8px'; // 从6px调整为4px
			(h5 as HTMLElement).style.color = '#333';
		});
		// 段落样式 - 进一步缩小字体和间距
		const paragraphs = container.querySelectorAll('p');
		paragraphs.forEach((p) => {
			(p as HTMLElement).style.marginBottom = '6px'; // 从8px调整为6px
			(p as HTMLElement).style.lineHeight = '1.5'; // 从1.3调整为1.2
			(p as HTMLElement).style.textAlign = 'justify';
			// (p as HTMLElement).style.textIndent = '2em'; //
			(p as HTMLElement).style.fontSize = '8px'; // 从11px调整为9px
		});

		// 列表样式 - 隐藏圆点但保持缩进效果
		const lists = container.querySelectorAll('ul, ol');
		lists.forEach((ul) => {
			(ul as HTMLElement).style.marginBottom = '4px'; // 从8px调整为6px
			(ul as HTMLElement).style.paddingLeft = '16px'; // 增加左侧内边距
			(ul as HTMLElement).style.marginLeft = '0px'; // 重置外边距，使用paddingLeft控制
			(ul as HTMLElement).style.listStyleType = 'none'; // 隐藏圆点
			(ul as HTMLElement).style.marginTop = '4px'; // 与上方文字产生缩进效果
		});

		// 处理嵌套列表 - 每层再缩进12px
		const nestedLists = container.querySelectorAll('ul ul, ol ol, ul ol, ol ul');
		nestedLists.forEach((nestedUl) => {
			(nestedUl as HTMLElement).style.paddingLeft = '12px'; // 嵌套列表额外缩进12px
			(nestedUl as HTMLElement).style.marginTop = '2px';
			(nestedUl as HTMLElement).style.marginBottom = '2px';
		});

		const listItems = container.querySelectorAll('li');
		listItems.forEach((li) => {
			(li as HTMLElement).style.marginBottom = '2px'; // 从3px调整为2px
			(li as HTMLElement).style.marginTop = '0px'; // 重置上边距，通过ul控制
			(li as HTMLElement).style.lineHeight = '1.4'; // 从1.5调整为1.4，提高可读性
			(li as HTMLElement).style.fontSize = '8px'; // 从11px调整为9px
			(li as HTMLElement).style.paddingLeft = '0px'; // 不需要padding，通过margin控制缩进
			(li as HTMLElement).style.marginLeft = '0px'; // 重置margin
			(li as HTMLElement).style.textIndent = '0px'; // 确保列表项不缩进
		});

		// 粗体样式
		const strongs = container.querySelectorAll('strong');
		strongs.forEach((strong) => {
			(strong as HTMLElement).style.fontWeight = 'bold';
			(strong as HTMLElement).style.color = '#000';
		});

		// 表格样式 - 进一步缩小字体和间距
		const tables = container.querySelectorAll('table');
		tables.forEach((table) => {
			(table as HTMLElement).style.width = '100%';
			(table as HTMLElement).style.borderCollapse = 'collapse';
			// (table as HTMLElement).style.marginBottom = '8px'; // 从10px调整为8px
			(table as HTMLElement).style.border = '1px solid #ddd';
			(table as HTMLElement).style.fontSize = '8px'; // 从10px调整为8px

			const cells = table.querySelectorAll('th, td');
			cells.forEach((cell) => {
				(cell as HTMLElement).style.border = '1px solid #ddd';
				(cell as HTMLElement).style.padding = '0px 5px 12px'; // 从6px 4px调整为4px 3px
				(cell as HTMLElement).style.textAlign = 'left';
				(cell as HTMLElement).style.fontSize = '8px'; // 从10px调整为8px
				(cell as HTMLElement).style.lineHeight = '1.5'; // 从1.2调整为1.1
			});

			const headers = table.querySelectorAll('th');
			headers.forEach((th) => {
				(th as HTMLElement).style.backgroundColor = '#f8f8f9';
				(th as HTMLElement).style.fontWeight = 'bold';
				(th as HTMLElement).style.color = '#515a6e';
			});
		});

		// 隐藏<hr>标签，用于分页控制但不显示在PDF中
		const hrs = container.querySelectorAll('hr');
		hrs.forEach((hr) => {
			(hr as HTMLElement).style.display = 'none'; // 完全隐藏HR标签
		});

		// 代码块样式 - 进一步缩小字体和间距
		const codeBlocks = container.querySelectorAll('pre');
		codeBlocks.forEach((pre) => {
			(pre as HTMLElement).style.backgroundColor = '#f6f8fa';
			(pre as HTMLElement).style.padding = '3px'; // 进一步减少代码块内边距
			(pre as HTMLElement).style.borderRadius = '4px'; // 调整圆角
			(pre as HTMLElement).style.overflow = 'auto';
			(pre as HTMLElement).style.margin = '3px 0'; // 进一步减少代码块外边距
			(pre as HTMLElement).style.fontSize = '7px'; // 从9px调整为7px
			(pre as HTMLElement).style.lineHeight = '1.1'; // 从1.2调整为1.1
		});

		const inlineCodes = container.querySelectorAll('code');
		inlineCodes.forEach((code) => {
			if (!code.parentElement?.tagName.toLowerCase().includes('pre')) {
				(code as HTMLElement).style.backgroundColor = '#f4f4f4';
				(code as HTMLElement).style.padding = '1px 2px'; // 进一步减少行内代码内边距
				(code as HTMLElement).style.borderRadius = '3px';
				(code as HTMLElement).style.fontFamily = 'Courier New, monospace';
				(code as HTMLElement).style.fontSize = '7px'; // 从9px调整为7px
			}
		});
	}

	public async captureMarkdownModule(moduleElement: HTMLElement): Promise<HTMLCanvasElement[]> {
		// 优化Markdown内容样式
		const originalStyle = moduleElement.style.cssText;
		moduleElement.style.width = '100%';
		moduleElement.style.margin = '0';
		moduleElement.style.padding = '0 30px'; // 左右15px内边距，上下0内边距
		moduleElement.style.backgroundColor = '#ffffff';
		moduleElement.style.fontFamily = 'Microsoft YaHei, Arial, sans-serif';
		moduleElement.style.fontSize = '8px'; // 使用用户手动设置的字体大小
		moduleElement.style.lineHeight = '1.3'; // 从1.5进一步调整为1.3

		// 处理Markdown渲染的内容
		const markdownContent = moduleElement.querySelector('.markdown-content, .wx-content-wrapper');
		if (markdownContent) {
			// 设置Markdown内容样式 - 与外部容器样式保持一致
			(markdownContent as HTMLElement).style.padding = '0'; // 内容区域不再需要padding，由外部容器控制
			(markdownContent as HTMLElement).style.lineHeight = '1.3'; // 从1.5进一步调整为1.3
			(markdownContent as HTMLElement).style.fontSize = '8px'; // 保持与外部容器一致

			// 处理标题 - 进一步缩小字体和间距
			const headings = markdownContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
			headings.forEach((heading) => {
				(heading as HTMLElement).style.marginTop = '10px'; // 从15px调整为10px
				(heading as HTMLElement).style.marginBottom = '16px'; // 从8px调整为6px
				(heading as HTMLElement).style.fontWeight = 'bold';
				(heading as HTMLElement).style.color = '#333';

				// 根据标题级别设置不同字体大小
				const tagName = heading.tagName.toLowerCase();
				if (tagName === 'h1') {
					(heading as HTMLElement).style.fontSize = '16px'; // 从20px调整为16px
				} else if (tagName === 'h2') {
					(heading as HTMLElement).style.fontSize = '14px'; // 从18px调整为14px
				} else if (tagName === 'h3') {
					(heading as HTMLElement).style.fontSize = '12px'; // 从16px调整为12px
				} else {
					(heading as HTMLElement).style.fontSize = '10px'; // 从14px调整为10px
				}
			});

			// 处理段落
			const paragraphs = markdownContent.querySelectorAll('p');
			paragraphs.forEach((p) => {
				(p as HTMLElement).style.marginBottom = '6px'; // 从10px调整为6px
				(p as HTMLElement).style.textAlign = 'justify';
				(p as HTMLElement).style.textIndent = '2em';
				(p as HTMLElement).style.fontSize = '9px'; // 从13px调整为9px
				(p as HTMLElement).style.lineHeight = '1.2'; // 从1.5调整为1.2
			});

			// 处理列表 - 隐藏圆点但保持缩进效果
			const lists = markdownContent.querySelectorAll('ul, ol');
			lists.forEach((list) => {
				(list as HTMLElement).style.marginBottom = '6px'; // 从10px调整为6px
				(list as HTMLElement).style.paddingLeft = '16px'; // 增加左侧内边距
				(list as HTMLElement).style.marginLeft = '0px'; // 重置外边距，使用paddingLeft控制
				(list as HTMLElement).style.listStyleType = 'none'; // 隐藏圆点
				(list as HTMLElement).style.marginTop = '4px'; // 与上方文字产生缩进效果
			});

			// 处理嵌套列表 - 每层再缩进12px
			const nestedLists = markdownContent.querySelectorAll('ul ul, ol ol, ul ol, ol ul');
			nestedLists.forEach((nestedUl) => {
				(nestedUl as HTMLElement).style.paddingLeft = '12px'; // 嵌套列表额外缩进12px
				(nestedUl as HTMLElement).style.marginTop = '2px';
				(nestedUl as HTMLElement).style.marginBottom = '2px';
			});

			// 处理列表项 - 隐藏圆点的缩进样式
			const listItems = markdownContent.querySelectorAll('li');
			listItems.forEach((li) => {
				(li as HTMLElement).style.marginBottom = '2px'; // 从3px调整为2px
				(li as HTMLElement).style.marginTop = '0px'; // 重置上边距，通过ul控制
				(li as HTMLElement).style.lineHeight = '1.3'; // 从1.1调整为1.3，提高可读性
				(li as HTMLElement).style.fontSize = '9px'; // 从13px调整为9px
				(li as HTMLElement).style.paddingLeft = '0px'; // 不需要padding，通过margin控制缩进
				(li as HTMLElement).style.marginLeft = '0px'; // 重置margin
				(li as HTMLElement).style.textIndent = '0px'; // 确保列表项不缩进
			});

			// 处理代码块
			const codeBlocks = markdownContent.querySelectorAll('pre, code');
			codeBlocks.forEach((code) => {
				(code as HTMLElement).style.backgroundColor = '#f5f5f5';
				(code as HTMLElement).style.padding = '2px'; // 进一步减少代码块内边距
				(code as HTMLElement).style.borderRadius = '4px';
				(code as HTMLElement).style.fontFamily = 'Consolas, Monaco, monospace';
				(code as HTMLElement).style.fontSize = '7px'; // 从11px调整为7px
				(code as HTMLElement).style.lineHeight = '1.2'; // 从1.3调整为1.2
			});

			// 处理表格
			const tables = markdownContent.querySelectorAll('table');
			tables.forEach((table) => {
				(table as HTMLElement).style.width = '100%';
				(table as HTMLElement).style.borderCollapse = 'collapse';
				(table as HTMLElement).style.marginBottom = '10px'; // 从15px调整为10px
				(table as HTMLElement).style.fontSize = '8px'; // 保持与基础字体一致

				const cells = table.querySelectorAll('th, td');
				cells.forEach((cell) => {
					(cell as HTMLElement).style.border = '1px solid #ddd';
					(cell as HTMLElement).style.padding = '2px 3px'; // 进一步减少表格单元格内边距
					(cell as HTMLElement).style.textAlign = 'left';
					(cell as HTMLElement).style.fontSize = '8px'; // 保持与基础字体一致
					(cell as HTMLElement).style.lineHeight = '1.2'; // 从1.3调整为1.2
				});

				const headers = table.querySelectorAll('th');
				headers.forEach((th) => {
					(th as HTMLElement).style.backgroundColor = '#f8f9fa';
					(th as HTMLElement).style.fontWeight = 'bold';
				});
			});
		}

		try {
			// 获取模块实际高度
			const moduleHeight = moduleElement.scrollHeight;
			const moduleWidth = moduleElement.scrollWidth;
			const currentScale = 4;
			// A4横版的有效高度（统一设置为85%）
			const maxPageHeight = this.pageHeight * 0.85; // 统一使用85%的页面高度

			// 如果模块高度超过页面高度，则分页处理
			if (moduleHeight > maxPageHeight) {
				const canvases = [];
				const pageCount = Math.ceil(moduleHeight / maxPageHeight);

				for (let i = 0; i < pageCount; i++) {
					// 设置临时的裁剪区域，仅显示当前页的内容
					const tempDiv = document.createElement('div');
					tempDiv.style.position = 'absolute';
					tempDiv.style.width = moduleWidth + 'px';
					tempDiv.style.height = maxPageHeight + 'px';
					tempDiv.style.overflow = 'hidden';
					tempDiv.style.backgroundColor = '#ffffff';

					const clonedModule = moduleElement.cloneNode(true) as HTMLElement;
					clonedModule.style.position = 'relative';
					clonedModule.style.top = -i * maxPageHeight + 'px';
					clonedModule.style.width = moduleWidth + 'px';

					tempDiv.appendChild(clonedModule);
					document.body.appendChild(tempDiv);

					// 截取当前页
					const canvas = await html2canvas(tempDiv, {
						scale: currentScale,
						useCORS: true,
						allowTaint: true,
						logging: false,
						backgroundColor: '#ffffff',
						windowWidth: moduleWidth,
						windowHeight: maxPageHeight,
					});

					canvases.push(canvas);
					document.body.removeChild(tempDiv);
				}

				// 恢复原始样式
				moduleElement.style.cssText = originalStyle;
				return canvases;
			} else {
				// 如果不需要分页，按原逻辑处理
				const canvas = await html2canvas(moduleElement, {
					scale: currentScale,
					useCORS: true,
					allowTaint: true,
					logging: false,
					backgroundColor: '#ffffff',
					windowWidth: this.pageWidth * currentScale,
					windowHeight: this.pageHeight * currentScale,
				});

				// 恢复原始样式
				moduleElement.style.cssText = originalStyle;
				return [canvas];
			}
		} catch (error) {
			console.error('模块截图失败:', error);
			moduleElement.style.cssText = originalStyle;
			throw error;
		}
	}

	private addCanvasToPage(canvas: HTMLCanvasElement, isLastPage: boolean = false): void {
		const canvasWidth = canvas.width;
		const canvasHeight = canvas.height;

		// 优化缩放策略 - 优先考虑宽度适配，同时确保高度不超出
		const widthScale = this.pageWidth / canvasWidth;
		const heightScale = this.pageHeight / canvasHeight;

		// 使用较小的缩放比例确保内容完全显示，但优先使用宽度缩放
		const scale = Math.min(widthScale, heightScale);

		const scaledWidth = canvasWidth * scale;
		const scaledHeight = canvasHeight * scale;

		// 优化定位 - 内容从页面顶部开始，水平居中
		const x = (this.pageWidth - scaledWidth) / 2;
		const y = 0; // 从页面顶部开始，不留上边距

		console.log('页面添加参数:', {
			canvasSize: { width: canvasWidth, height: canvasHeight },
			pageSize: { width: this.pageWidth, height: this.pageHeight },
			scale: scale,
			scaledSize: { width: scaledWidth, height: scaledHeight },
			position: { x, y },
			utilization: ((scaledHeight / this.pageHeight) * 100).toFixed(1) + '%',
		});

		// 添加到PDF
		this.pdf.addImage(canvas.toDataURL('image/jpeg', this.options.quality), 'JPEG', x, y, scaledWidth, scaledHeight);

		// 如果不是最后一页，添加新页
		if (!isLastPage) {
			this.pdf.addPage();
		}
	}

	public async exportMarkdownToPDF(moduleElement: HTMLElement, filename: string = 'markdown-export.pdf'): Promise<void> {
		try {
			// 等待内容完全渲染
			await new Promise((resolve) => setTimeout(resolve, 500));

			// 捕获Markdown模块内容
			const canvases = await this.captureMarkdownModule(moduleElement);

			// 添加每个画布到PDF
			for (let i = 0; i < canvases.length; i++) {
				const isLastPage = i === canvases.length - 1;
				this.addCanvasToPage(canvases[i], isLastPage);
			}

			// 保存PDF
			this.pdf.save(filename);
		} catch (error) {
			console.error('Markdown PDF导出失败:', error);
			throw error;
		}
	}

	public async exportMarkdownContentToPDF(markdownContent: string, filename: string = 'markdown-export.pdf'): Promise<void> {
		try {
			// 使用新的导出方法
			const canvases = await this.exportFromMarkdownContent(markdownContent);

			// 添加每个画布到PDF
			for (let i = 0; i < canvases.length; i++) {
				const isLastPage = i === canvases.length - 1;
				this.addCanvasToPage(canvases[i], isLastPage);
			}

			// 保存PDF
			this.pdf.save(filename);
		} catch (error) {
			console.error('Markdown内容PDF导出失败:', error);
			throw error;
		}
	}

	public getPDF(): jsPDF {
		return this.pdf;
	}
}

// 导出便捷方法
export const exportMarkdownToPDF = async (moduleElement: HTMLElement, filename?: string, options?: MarkdownExportOptions): Promise<void> => {
	const exporter = new MarkdownPDFExporter(options);
	await exporter.exportMarkdownToPDF(moduleElement, filename);
};

export const exportMarkdownContentToPDF = async (markdownContent: string, filename?: string, options?: MarkdownExportOptions): Promise<void> => {
	const exporter = new MarkdownPDFExporter(options);
	await exporter.exportMarkdownContentToPDF(markdownContent, filename);
};

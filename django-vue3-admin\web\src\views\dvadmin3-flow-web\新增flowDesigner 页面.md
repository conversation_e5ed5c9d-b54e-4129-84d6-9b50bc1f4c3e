# flowDesigner 页面菜单配置

## 📋 新增页面说明

我已经创建了独立的 `/flowDesigner` 页面：`src/views/dvadmin3-flow-web/src/flowDesigner/index.vue`

这个页面用于全屏显示流程设计器，解决了原代码中 `window.open('/flowDesigner')` 404 的问题。

## 🔧 数据库菜单配置

### 新增 flowDesigner 页面菜单

```sql
-- 添加 flowDesigner 页面菜单（假设工作流管理的父菜单ID为 @parent_id）
INSERT INTO `dvadmin_system_menu` (
    `parent_id`, `title`, `icon`, `web_path`, `component`, `component_name`,
    `cache`, `visible`, `is_catalog`, `is_frame`, `creator`, `modifier`,
    `dept_belong_id`, `update_datetime`, `create_datetime`
) VALUES (
    @parent_id, '流程设计器', 'iconfont icon-design', '/flowDesigner',
    '/views/dvadmin3-flow-web/src/flowDesigner/index', 'FlowDesignerIndex',
    1, 0, 0, 0, 1, 1, 1, NOW(), NOW()
);
```

### 字段说明

- `title`: 流程设计器
- `web_path`: `/flowDesigner`
- `component`: `/views/dvadmin3-flow-web/src/flowDesigner/index`
- `component_name`: `FlowDesignerIndex`
- `visible`: `0` (隐藏，因为这是通过点击按钮打开的页面，不需要在菜单中显示)
- `cache`: `1` (缓存页面状态)

## ⚠️ 重要说明

1. **隐藏菜单项**：`visible` 设为 `0`，因为这个页面是通过"新建流程"按钮打开的，不需要在左侧菜单中显示

2. **组件路径**：使用 `/views/dvadmin3-flow-web/src/flowDesigner/index` 路径

3. **功能说明**：
   - 全屏流程设计器
   - 支持通过 URL 参数传递流程 ID
   - 包含完整的流程设计功能

## 🔄 配置完成后

1. 执行 SQL 添加菜单
2. 重启前后端服务
3. 给管理员角色分配权限
4. 测试"新建流程"功能

## 📝 测试方法

1. 登录系统
2. 进入"工作流管理 → 流程管理"
3. 点击"新建流程"按钮
4. 应该能正常打开流程设计器页面

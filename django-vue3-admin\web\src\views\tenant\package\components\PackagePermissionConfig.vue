<template>
	<div class="package-permission-config">
		<!-- 固定头部操作区 -->
		<div class="permission-header">
			<h4></h4>
			<div class="permission-actions">
				<el-button size="small" @click="expandAll">展开全部</el-button>
				<el-button size="small" @click="collapseAll">收起全部</el-button>
				<el-button size="small" type="primary" @click="checkAll">全选</el-button>
				<el-button size="small" @click="uncheckAll">取消全选</el-button>
			</div>
		</div>

		<!-- 可滚动权限内容区 -->
		<div class="permission-content" v-loading="isLoading" :style="{ maxHeight: contentMaxHeight }">
			<el-tree
				ref="menuTreeRef"
				:data="menuTreeData"
				:props="treeProps"
				:default-checked-keys="defaultCheckedKeys"
				@check="handleNodeCheck"
				node-key="id"
				show-checkbox
			>
			</el-tree>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue';
import { ElTree } from 'element-plus';
import { getPackageMenuPermissions, MenuPermissionType } from '../api';

interface Props {
	packageId?: string | number;
	modelValue?: string; // 改为字符串，逗号分隔的菜单ID
}

interface Emits {
	(e: 'update:modelValue', value: string): void; // 改为字符串
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: () => '',
});

const emit = defineEmits<Emits>();

const menuTreeRef = ref<InstanceType<typeof ElTree>>();
const menuTreeData = ref<MenuPermissionType[]>([]);
const defaultCheckedKeys = ref<number[]>([]);
const isLoading = ref(false);

const treeProps = {
	children: 'children',
	label: 'name',
	value: 'id',
};

// 计算权限内容区的最大高度
const contentMaxHeight = computed(() => {
	// 弹窗高度为屏幕高度的80%
	const dialogHeight = window.innerHeight * 0.8;
	// 头部区域高度 (padding + border + content)
	const headerHeight = 60;
	// 底部区域高度 (由父组件控制的按钮区域)
	const footerHeight = 60;
	// 计算内容区可用高度
	const availableHeight = dialogHeight - headerHeight - footerHeight;
	return `${Math.max(availableHeight, 300)}px`; // 最小高度300px
});

// 加载菜单权限数据
const loadMenuPermissions = async () => {
	if (isLoading.value) return;

	try {
		isLoading.value = true;
		const response = await getPackageMenuPermissions(props.packageId);
		if (response.code === 2000) {
			menuTreeData.value = buildTreeStructure(response.data);
			setDefaultCheckedNodes();
		}
	} catch (error) {
		console.error('加载菜单权限失败:', error);
	} finally {
		isLoading.value = false;
	}
};

// 构建树形结构
const buildTreeStructure = (flatData: any[]): MenuPermissionType[] => {
	const nodeMap = new Map();
	const rootNodes: MenuPermissionType[] = [];

	// 创建节点映射
	flatData.forEach((item) => {
		const node: MenuPermissionType = {
			...item,
			children: [],
		};
		nodeMap.set(item.id, node);
	});

	// 构建父子关系
	flatData.forEach((item) => {
		const node = nodeMap.get(item.id);
		if (item.parent && item.parent !== null && nodeMap.has(item.parent)) {
			const parentNode = nodeMap.get(item.parent);
			if (!parentNode.children) {
				parentNode.children = [];
			}
			parentNode.children.push(node);
		} else {
			rootNodes.push(node);
		}
	});

	return rootNodes;
};

// 计算正确的选中状态
const calculateCorrectCheckedState = (nodes: MenuPermissionType[]): void => {
	nodes.forEach((node) => {
		if (node.children && node.children.length > 0) {
			// 先递归处理子节点
			calculateCorrectCheckedState(node.children);

			// 检查子节点的选中状态
			const checkedChildren = node.children.filter((child) => (child as any).isCheck);
			const allChecked = checkedChildren.length === node.children.length;

			// 父节点的选中状态应该基于子节点：
			// - 如果所有子节点都选中，父节点为选中
			// - 如果部分子节点选中，父节点为半选（在Element Tree中会自动处理）
			// - 如果没有子节点选中，父节点为未选中
			(node as any).isCheck = allChecked;
		}
	});
};

// 设置默认选中的节点
const setDefaultCheckedNodes = () => {
	// 首先计算正确的选中状态
	calculateCorrectCheckedState(menuTreeData.value);

	const checkedKeys: number[] = [];
	const collectCheckedNodes = (nodes: MenuPermissionType[]) => {
		nodes.forEach((node) => {
			if ((node as any).isCheck) {
				checkedKeys.push(node.id as number);
			}
			if (node.children) {
				collectCheckedNodes(node.children);
			}
		});
	};
	collectCheckedNodes(menuTreeData.value);
	defaultCheckedKeys.value = checkedKeys;
};

// 处理节点选中状态变化
const handleNodeCheck = () => {
	emitPermissionData();
};

// 发送权限数据给父组件
const emitPermissionData = () => {
	if (!menuTreeRef.value) return;

	// 获取完全选中的节点（包括叶子节点和完全选中的父节点）
	const checkedNodes = menuTreeRef.value.getCheckedNodes();

	// 获取半选状态的父节点
	const halfCheckedNodes = menuTreeRef.value.getHalfCheckedNodes();

	// 收集所有选中的节点（包括完全选中的父节点）和半选状态的父节点
	const menuIds = [...checkedNodes, ...halfCheckedNodes].map((node: any) => node.id as number);

	// 去重并排序
	const uniqueMenuIds = [...new Set(menuIds)].sort((a, b) => a - b);

	// 将菜单ID数组转换为逗号分隔的字符串
	const menuIdsString = uniqueMenuIds.join(',');

	emit('update:modelValue', menuIdsString);
};

// 展开全部
const expandAll = () => {
	const allKeys = getAllNodeKeys(menuTreeData.value);
	allKeys.forEach((key) => {
		const node = menuTreeRef.value?.getNode(key);
		if (node) {
			node.expanded = true;
		}
	});
};

// 收起全部
const collapseAll = () => {
	const allKeys = getAllNodeKeys(menuTreeData.value);
	allKeys.forEach((key) => {
		const node = menuTreeRef.value?.getNode(key);
		if (node) {
			node.expanded = false;
		}
	});
};

// 全选
const checkAll = () => {
	const allKeys = getAllNodeKeys(menuTreeData.value);
	menuTreeRef.value?.setCheckedKeys(allKeys);
	emitPermissionData();
};

// 取消全选
const uncheckAll = () => {
	menuTreeRef.value?.setCheckedKeys([]);
	emitPermissionData();
};

// 获取所有节点的key
const getAllNodeKeys = (nodes: MenuPermissionType[]): number[] => {
	const keys: number[] = [];
	const collectKeys = (nodeList: MenuPermissionType[]) => {
		nodeList.forEach((node) => {
			keys.push(node.id as number);
			if (node.children) {
				collectKeys(node.children);
			}
		});
	};
	collectKeys(nodes);
	return keys;
};

// 监听packageId变化
watch(
	() => props.packageId,
	(newPackageId, oldPackageId) => {
		if (newPackageId !== oldPackageId) {
			loadMenuPermissions();
		}
	},
	{ immediate: false }
);

onMounted(() => {
	nextTick(() => {
		loadMenuPermissions();
	});
});
</script>

<style lang="scss" scoped>
.package-permission-config {
	display: flex;
	flex-direction: column;
	height: 100%;

	.permission-header {
		flex-shrink: 0; // 固定高度，不参与滚动
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16px;
		border-bottom: 1px solid #ebeef5;
		background: #fff;

		h4 {
			margin: 0;
			color: #303133;
		}

		.permission-actions {
			display: flex;
			gap: 8px;
		}
	}

	.permission-content {
		overflow-y: auto; // 启用垂直滚动
		padding: 16px;
		// max-height 通过内联样式动态设置

		// 自定义滚动条样式
		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}
	}
}
</style> 
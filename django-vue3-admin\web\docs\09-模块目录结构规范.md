# 模块目录结构规范

## 📂 标准模块结构

### 1. 目录命名规范
```
src/views/[module]/           # 模块目录（小写+下划线）
├── [Feature]/               # 功能目录（PascalCase）
│   ├── api.ts              # API接口定义
│   ├── crud.tsx            # Fast-CRUD配置
│   ├── index.vue           # 主页面组件
│   └── components/         # 功能专用组件（可选）
│       ├── [Feature]Form.vue
│       ├── [Feature]Dialog.vue
│       └── [Feature]Table.vue
└── components/             # 模块共享组件（可选）
    └── SharedComponent.vue
```

### 2. 实际案例对比

#### ✅ 正确结构 - wechat_official_account模块
```
wechat_official_account/
├── TenantMediaAccounts/     # 租户媒体账号管理
│   ├── api.ts
│   ├── crud.tsx
│   └── index.vue
├── WechatOfficialAccount/   # 微信公众号管理
│   ├── api.ts
│   ├── crud.tsx
│   ├── index.vue
│   └── ImportResultDialog.vue
└── components/              # 共享组件
    └── PlatformTabs.vue
```

#### ✅ 正确结构 - website模块（已重构）
```
website/
├── Site/                   # 站点管理功能
│   ├── api.ts             # 站点API接口
│   ├── crud.tsx           # 站点CRUD配置
│   └── index.vue          # 站点管理主页
└── components/            # 共享组件（未来扩展）
```

### 3. 文件命名约定

#### 目录命名
- **模块目录**: 小写+下划线 (如: `wechat_official_account`)
- **功能目录**: PascalCase (如: `TenantMediaAccounts`, `Site`)
- **组件目录**: 小写 (固定名称: `components`)

#### 文件命名
- **主页面**: `index.vue`
- **API接口**: `api.ts`
- **CRUD配置**: `crud.tsx`
- **组件文件**: PascalCase (如: `ImportResultDialog.vue`)

### 4. API接口结构规范

```typescript
// api.ts 标准结构
import { request } from '/@/utils/service';

// 1. 类型定义
export interface FeatureData {
  id: number;
  // ... 其他字段
}

export interface FeatureQuery {
  page?: number;
  // ... 查询参数
}

// 2. 基础CRUD操作
export function getFeatureList(params?: FeatureQuery) {
  return request({
    url: '/api/module/features/',
    method: 'get',
    params,
  });
}

export function createFeature(data: Partial<FeatureData>) {
  return request({
    url: '/api/module/features/',
    method: 'post',
    data,
  });
}

// ... 其他操作
```

### 5. CRUD配置规范

```typescript
// crud.tsx 标准结构
import * as api from './api';  // ✅ 使用相对路径导入同目录api
import { CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';

export default function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      request: {
        pageRequest: async (query: any) => {
          const res = await api.getFeatureList(query);
          return {
            records: res.results,
            total: res.count,
          };
        },
        // ... 其他请求配置
      },
      columns: {
        // ... 列配置
      },
    },
  };
}
```

### 6. 主页面组件规范

```vue
<!-- index.vue 标准结构 -->
<template>
  <fs-page class="Page[Module][Feature]">
    <fs-crud ref="crudRef" v-bind="crudBinding" />
  </fs-page>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';  // ✅ 同目录导入

export default defineComponent({
  name: '[Module][Feature]Manage',
  setup() {
    const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
    
    return {
      crudBinding,
      crudRef,
      crudExpose,
    };
  },
});
</script>

<style lang="scss" scoped>
.Page[Module][Feature] {
  padding: 20px;
}
</style>
```

## 🔧 模块开发检查清单

### 创建新模块时
- [ ] 模块目录使用小写+下划线命名
- [ ] 功能目录使用PascalCase命名
- [ ] 每个功能包含：api.ts、crud.tsx、index.vue
- [ ] API导入使用相对路径 `import * as api from './api'`
- [ ] 组件名称与功能对应
- [ ] 样式类名与组件名对应

### 重构现有模块时
- [ ] 检查目录结构是否符合规范
- [ ] API文件是否在正确位置
- [ ] 导入路径是否使用相对路径
- [ ] 文件命名是否一致

## 📝 规范说明

### 为什么要这样组织？
1. **一致性**: 所有模块遵循相同结构，便于维护
2. **独立性**: 每个功能包含完整的api、配置、页面
3. **可读性**: 清晰的目录结构便于理解和导航
4. **扩展性**: 规范化的结构便于添加新功能

### 与其他框架对比
- **相对路径导入**: 比绝对路径更灵活，重构时不易出错
- **功能聚合**: 每个功能的相关文件集中在一起
- **模块化**: 清晰的模块边界，便于团队协作
import { request } from '/@/utils/service';

// 校对任务信息接口
export interface CorrectionTaskInfo {
	id: number;
	name: string;
	type: number; // 任务类型
	status: number; // 任务状态
	remark: string; // 备注
	creator: number; // 任务创建人ID
	create_time: string; // 任务创建时间
	update_time: string; // 更新时间
	tenant_id: number; // 租户ID
	error_summary: string; // 错误概述
	report_id: number; // 月报ID
}

// 校对目标信息接口
export interface CorrectionTargetInfo {
	id: number;
	task_id: number;
	media_account_id: string;
	start_time: string;
	end_time: string;
	priority: number; // 执行优先级
	status: number; // 目标状态
	remark: string; // 备注
	wechat_name: string; // 公众号名称
	total_count: number; // 总文章数
	crawled_count: number; // 抓取文章数
	corrected_count: number; // 纠错文章数
	creator: number; // 创建人ID
	create_time: string;
	update_time: string;
	tenant_id: number;
	type: number; // 任务类型
	task: number;
}

// 账号信息接口
export interface AccountInfo {
	id: string;
	name: string;
}

// 查询参数接口
export interface QueryParams {
	id?: number; // 任务ID
	page?: number;
	limit?: number;
	name?: string; // 任务名称
	type?: number; // 任务类型
	status?: number; // 任务状态
	tenant_id?: number; // 租户ID
	create_time_start?: string;
	create_time_end?: string;
}

// 目标查询参数接口
export interface TargetQueryParams extends QueryParams {
	task_id?: number; // 任务ID
	media_account_id?: string; // 媒体账号ID
	priority?: number; // 优先级
	wechat_name?: string; // 校对账号名称
}

// 响应接口
export interface ApiResponse<T> {
	code: number;
	message: string;
	data: T;
	page?: number;
	limit?: number;
	total?: number;
	is_next?: boolean;
	is_previous?: boolean;
}
// 创建校对任务
export interface CreateTaskParams {
	tenant_id: number;
	name: string;
	type: number;
	remark?: string;
	targets?: {
		media_account_id: string;
		start_time: string;
		end_time: string;
	}[];
}

export interface TaskTargetCreateResult {
	task: CorrectionTaskInfo;
	targets: {
		success_count: number;
		fail_count: number;
		error_messages: string[];
	};
}
// 创建校对目标
export interface CreateTargetParams {
	items: {
		task_id: number;
		media_account_id: string;
		start_time: string;
		end_time: string;
	}[];
}

export interface BatchCreateResult {
	success_count: number;
	fail_count: number;
	error_messages: string[];
}

// API前缀
const taskPrefix = '/api/regulate/tasks/';
const targetPrefix = '/api/regulate/targets/';

// 获取校对任务列表
export async function getTaskList(query: QueryParams): Promise<ApiResponse<CorrectionTaskInfo[]>> {
	return request({
		url: taskPrefix,
		method: 'get',
		params: query,
	});
}
// 获取不分页的全部校对任务列表
export async function getTaskAll() {
	return request({
		url: taskPrefix + 'select_list/',
		method: 'get',
	});
}

export async function createTask(data: CreateTaskParams): Promise<ApiResponse<TaskTargetCreateResult>> {
	return request({
		url: taskPrefix,
		method: 'post',
		data,
	});
}

// 删除校对任务
export async function deleteTask(id: number): Promise<ApiResponse<null>> {
	return request({
		url: taskPrefix + id + '/',
		method: 'delete',
	});
}

// 获取校对任务详情
export async function getTaskDetail(id: number): Promise<ApiResponse<CorrectionTaskInfo>> {
	return request({
		url: taskPrefix + id + '/',
		method: 'get',
	});
}

// 获取校对目标列表
export async function getTargetList(query: TargetQueryParams): Promise<ApiResponse<CorrectionTargetInfo[]>> {
	return request({
		url: targetPrefix,
		method: 'get',
		params: query,
	});
}

export async function createTargets(data: CreateTargetParams): Promise<ApiResponse<BatchCreateResult>> {
	return request({
		url: targetPrefix,
		method: 'post',
		data,
	});
}

// 获取可用账号列表
export async function getAvailableAccounts() {
	return request({
		url: '/api/accounts/available/',
		method: 'get',
	});
}

// 检查任务是否可删除
// export async function checkTaskDeletable(id: number): Promise<ApiResponse<boolean>> {
//     return request({
//         url: taskPrefix + id + '/check_deletable/',
//         method: 'get'
//     })
// }

// 启动所有目标
export async function startAllTargets(id: number): Promise<ApiResponse<null>> {
	return request({
		url: targetPrefix + 'start_all_targets/',
		method: 'get',
		params: {
			task_id: id,
		},
	});
}

// 更新任务统计信息
export async function updateTaskStats(id: number): Promise<ApiResponse<null>> {
	return request({
		url: taskPrefix + id + '/update_task_stats/',
		method: 'get',
	});
}
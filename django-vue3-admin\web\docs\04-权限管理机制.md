# 权限管理机制

## 🔐 权限层级体系
```typescript
// 权限层级：模块:资源:操作
const permissionCodes = {
  // 站点管理权限
  'website:site:view',     // 查看站点
  'website:site:create',   // 创建站点
  'website:site:update',   // 编辑站点
  'website:site:delete',   // 删除站点
  'website:site:status',   // 状态管理
  'website:site:export',   // 导出站点
  'website:site:import',   // 导入站点
  
  // 列级权限
  'website:site:field:domain',    // 域名字段
  'website:site:field:status',    // 状态字段
};
```

## 权限使用方式

### 1. 模板指令权限
```vue
<template>
  <!-- 按钮权限 -->
  <el-button v-auth="'website:site:create'">新增</el-button>
  
  <!-- 多权限(满足其一) -->
  <el-button v-auths="['website:site:create', 'website:site:import']">操作</el-button>
  
  <!-- 多权限(全部满足) -->
  <el-button v-auth-all="['website:site:create', 'admin:all']">高级操作</el-button>
</template>
```

### 2. JavaScript函数权限
```typescript
import { auth, auths, authAll } from '/@/utils/authFunction';

// 单个权限判断
if (auth('website:site:create')) {
  // 有创建权限
}

// 多个权限判断(或关系)
if (auths(['website:site:create', 'website:site:update'])) {
  // 有创建或编辑权限
}

// 多个权限判断(且关系)  
if (authAll(['website:site:create', 'admin:all'])) {
  // 同时具有创建权限和管理员权限
}
```

### 3. CRUD配置中的权限
```typescript
// 在CRUD配置中集成权限
columns: {
  domain: {
    title: '域名',
    column: {
      show: auth('website:site:field:domain'), // 列级权限
    },
    form: {
      show: auth('website:site:update'), // 表单权限
    }
  }
},
rowHandle: {
  buttons: {
    edit: {
      show: auth('website:site:update'), // 按钮权限
    }
  }
}
```

## 权限配置流程

### 1. 后端权限配置
在Django Admin或系统管理界面配置：

```python
# 菜单权限配置
menu_permissions = [
    {
        "name": "站点管理",
        "code": "website:site",
        "type": "menu",
        "children": [
            {"name": "查看站点", "code": "website:site:view", "type": "button"},
            {"name": "创建站点", "code": "website:site:create", "type": "button"},
            {"name": "编辑站点", "code": "website:site:update", "type": "button"},
            {"name": "删除站点", "code": "website:site:delete", "type": "button"},
            {"name": "状态管理", "code": "website:site:status", "type": "button"},
        ]
    }
]
```

### 2. 角色权限分配
```typescript
// 不同角色的权限示例
const rolePermissions = {
  'admin': ['*'], // 超级管理员 - 所有权限
  
  'website_admin': [
    'website:*', // 网站模块所有权限
  ],
  
  'website_operator': [
    'website:site:view',
    'website:site:update', 
    'website:site:status',
  ],
  
  'website_viewer': [
    'website:site:view',
  ],
};
```

## 权限最佳实践
1. **最小权限原则**: 用户只获得完成工作所需的最少权限
2. **权限分层**: 按模块、资源、操作进行分层管理
3. **角色基础**: 基于角色而不是用户分配权限
4. **定期审查**: 定期审查和更新权限配置
5. **权限日志**: 记录权限变更和使用日志
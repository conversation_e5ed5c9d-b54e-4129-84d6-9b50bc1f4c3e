import { CrudOptions, AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud'
import * as api from './api'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag, ElMessageBox, ElMessage } from 'element-plus'
import { ENUM_TYPE } from '/@/stores/constants/enum'
import { getEnumDatas } from '/@/stores/enum'

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    // 定义请求方法
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateData(form)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createData(form)
    }

    return {
        crudOptions: {
            // 请求配置
            request: {
                pageRequest,
                addRequest,
                editRequest
            },
            // 操作栏配置
            actionbar: {
                buttons: {
                    add: {
                        show: auth('errorWord:Create'),
                    },
                },

            },
            toolbar: {
                show: false,
            },
            // 行操作配置
            rowHandle: {
                width: 260,
                buttons: {
                    edit: {
                        show: auth('errorWord:Update'),
                    },
                    remove: {
                        show: auth('errorWord:Delete'),
                        click: ({ row }) => {
                            ElMessageBox.confirm(`确认删除词条 "${row.word}" 吗？`, '删除确认', {
                                confirmButtonText: '确认',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(async () => {
                                return await api.deleteData(row.id).then(() => {
                                    ElMessage.success('删除成功')
                                    crudExpose.doRefresh()
                                }).catch((err: any) => {
                                    ElMessage.error(err.msg)
                                })
                            }).catch(() => {
                                return Promise.reject('取消删除')
                            })
                        }
                    },
                }
            },
            // 列配置
            columns: {
                id: {
                    title: 'ID',
                    type: 'number',
                    column: {
                        width: 80
                    },
                    form: {
                        show: false
                    },
                    search: { show: true }
                },
                word: {
                    title: '错误词条',
                    type: 'text',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '错误词条为必填项' }]
                    }
                },
                type: {
                    title: '词条类型',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.DICT_WORD_TYPE)
                    }),
                    column: {
                        width: 100,
                        component: {
                            name: 'fs-dict-select'
                        }
                    },
                    form: {
                        value: 2,  // 默认为错误词条
                        rules: [{ required: true, message: '请选择词条类型' }]
                    }
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.ENABLE_STATUS)
                    }),
                    search: { show: true },
                    column: {
                        width: 80,
                        component: {
                            name: 'fs-dict-select'
                        }
                    },
                    form: {
                        value: 0,
                        rules: [{ required: true, message: '请选择状态' }]
                    }
                },

                correct_word: {
                    title: '正确词条',
                    type: 'text',
                    form: {
                        rules: [{ required: false, message: '请输入正确词条' }]
                    }
                },
                hint: {
                    title: '错误提示信息',
                    type: 'text',
                    form: {
                        rules: [{ required: false, message: '请输入错误提示信息' }]
                    }
                },
                recommend: {
                    title: '推荐修改',
                    type: 'text',
                    form: {
                        rules: [{ required: false, message: '请输入推荐修改' }]
                    }
                },
                word_explain: {
                    title: '错误解释',
                    type: 'textarea',
                    column: {
                        show: false
                    },
                    form: {
                        rules: [{ required: false, message: '请解释为什么这个词是错误的' }]
                    }
                },
                word_example: {
                    title: '错误示例',
                    type: 'textarea',
                    column: {
                        show: false
                    },
                    form: {
                        rules: [{ required: false, message: '请提供错误示例' }]
                    }
                },
                account_id: {
                    title: '账号ID',
                    type: 'text',
                    search: { show: true },
                    form: {
                        value: '0',
                        helper: '默认值为0表示通用词条，其他为指定账号的词条'
                    }
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: 100
                    },
                    form: {
                        show: false
                    }
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime',
                    column: {
                        width: 100
                    },
                    form: {
                        show: false
                    }
                }
            }
        }
    }
} 
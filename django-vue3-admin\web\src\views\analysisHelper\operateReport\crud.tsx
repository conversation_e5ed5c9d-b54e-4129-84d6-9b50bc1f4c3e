import { CrudOptions, EditReq, useCompute, CrudExpose, UserPageQuery, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud'
import * as api from '/@/views/analysisHelper/operateReport/api'
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';
import { auth } from "/@/utils/authFunction"
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage, ElSelect, ElOption } from 'element-plus';
import { computed, ref, Ref } from 'vue'
import { h } from 'vue'

export default function ({ crudExpose, context }: { crudExpose: CrudExpose, context?: any }): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getList(query)
    }
    const router = useRouter();
    const { compute } = useCompute();

    // 从外部传入或使用默认值
    const currentPlatformType = context?.currentPlatformType || ref('wechat');

    // 控制AI报告生成弹窗的显示
    const showAccountLevelDialog = ref(false);
    // 存储当前行的report数据
    const currentRowData = ref<any>(null);
    // 存储选择的账号级别
    const selectedAccountLevel = ref('');

    // 打开AI报告生成弹窗
    const openAccountLevelDialog = (row: any) => {
        currentRowData.value = row;
        selectedAccountLevel.value = ''; // 打开弹窗时清空之前的选择
        showAccountLevelDialog.value = true;
    };

    // 处理弹窗确定事件
    const handleAccountLevelConfirm = async () => {
        if (!selectedAccountLevel.value) {
            ElMessage.warning('请选择账号级别');
            return;
        }

        // 构建调用 createReport 函数所需的参数对象
        const { tenant_id, start_date, end_date, id: operation_report_id } = currentRowData.value;
        const params = {
            tenant_id,
            start_date,
            end_date,
            operation_report_id,
            account_level: selectedAccountLevel.value
        };

        try {
            await api.createAiReport(params);
            ElMessage.success('AI 分析报告生成中，请稍后查看');
            showAccountLevelDialog.value = false; // 关闭弹窗
            // 可选：生成成功后刷新列表
            // crudExpose.doRefresh();
        } catch (error: any) {
            console.error('生成 AI 分析报告失败', error);
            showAccountLevelDialog.value = false; // 关闭弹窗
            ElMessage.error(error.response.data.message || '生成 AI 分析报告失败');
        }
    };

    // 处理弹窗取消事件
    const handleAccountLevelCancel = () => {
        showAccountLevelDialog.value = false; // 关闭弹窗
        selectedAccountLevel.value = ''; // 清空选择
        currentRowData.value = null; // 清空行数据
    };

    // 账号级别选项数据
    const accountLevelOptions = getEnumDatas(ENUM_TYPE.OPERATION_REPORT_ACCOUNT_LEVEL);

    return {
        crudOptions: {
            request: {
                pageRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    }
                }
            },
            toolbar: {
                show: false,
            },
            rowHandle: {
                width: 180,
                buttons: {
                    edit: {
                        text: '查看报表详情',
                        link: true,
                        show: true,
                        click: ({ row }) => {
                            // 使用行数据中的report_type
                            const reportType = row.report_type; // 使用行数据中的实际报告类型
                            const platformType = currentPlatformType.value;

                            console.log('跳转报表详情，报告类型:', reportType, '平台类型:', platformType);

                            if (reportType === 3) {
                                // 月报逻辑
                                if (platformType === 'wechat') {
                                    router.push({
                                        path: '/analysisHelper/operateDetail',
                                        query: { id: row.id }
                                    });
                                } else if (platformType === 'douyin') {
                                    router.push({
                                        path: '/analysisHelper/operateDyDetail',
                                        query: { id: row.id }
                                    });
                                } else if (platformType === 'weibo') {
                                    router.push({
                                        path: '/analysisHelper/operateWbDetail',
                                        query: { id: row.id }
                                    });
                                } else {
                                    ElMessage.warning('暂未支持该平台月报');
                                }
                            } else if (reportType === 4) {
                                // 季报逻辑
                                if (platformType === 'wechat') {
                                    router.push({
                                        path: '/analysisHelper/operateWxQuarterDetail',
                                        query: { id: row.id }
                                    });
                                } else if (platformType === 'douyin') {
                                    router.push({
                                        path: '/analysisHelper/operateDyQuarterDetail',
                                        query: { id: row.id }
                                    });
                                } else if (platformType === 'weibo') {
                                    router.push({
                                        path: '/analysisHelper/operateWbQuarterDetail',
                                        query: { id: row.id }
                                    });
                                } else {
                                    ElMessage.warning('暂未支持该平台季报');
                                }
                            } else {
                                ElMessage.warning('未知的报告类型');
                            }
                        }
                    },
                    remove: {
                        show: false
                    },
                    view: {
                        show: false
                    },

                    regenerate: {
                        text: '重新生成报表',
                        show: computed(() => {
                            if (currentPlatformType.value === 'wechat') {
                                return auth('OperateReport:RegenerateReportWx')
                            } else if (currentPlatformType.value === 'douyin') {
                                return auth('OperateReport:RegenerateReportDy')
                            } else if (currentPlatformType.value === 'weibo') {
                                return auth('OperateReport:RegenerateReportWb')
                            } else {
                                return false
                            }
                        }),
                        link: true,
                        type: 'primary',
                        click: ({ row }) => {
                            ElMessageBox.confirm('确认重新生成报表吗？', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(async () => {
                                await api.regenerateReport(row.id);
                                // 重新生成成功后，刷新当前页面
                                ElMessage.success('重新生成成功');
                                crudExpose.doRefresh();
                            }).catch(() => {
                                ElMessage.info('已取消重新生成');
                            });
                        }
                    },
                    // 生成 AI 分析报告
                    generateAiReport: {
                        text: '生成 AI 分析报告',
                        show: computed(() => {
                            return auth('OperateReport:GenerateAiReport')
                        }),
                        link: true,
                        type: 'primary',
                        click: ({ row }) => {
                            openAccountLevelDialog(row); // 调用新函数打开弹窗
                        }
                    },
                    // 查看 AI 生成报告按钮
                    viewAiReport: {
                        text: '查看 AI 生成报告',
                        show: compute(({ row }) => {
                            return row.ai_report_ids != null && row.ai_report_ids.length > 0 && auth('OperateReport:ViewAiReport');
                        }),
                        link: true,
                        type: 'primary',
                        click: ({ row }) => {
                            // 跳转到 AI 报告列表页面，并传递 ai_report_ids 参数
                            router.push({
                                path: '/analysisHelper/aiReport',
                                query: { ids: row.ai_report_ids, operation_report_id: row.id }
                            });
                        }
                    }
                }
            },
            columns: {
                id: {
                    title: 'ID',
                    type: 'text',
                    form: { show: false },
                    search: { show: true }
                },
                title: {
                    title: '标题',
                    type: 'text',
                    search: { show: true },
                    form: { show: true }
                },
                report_type: {
                    title: '报告类型',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.OPERATION_REPORT_TYPE).filter((item) =>
                            item.label.includes('月报') || item.label.includes('季报')
                        )
                    }),
                    search: {
                        show: true,
                        value: 3 // 默认为月报
                    },
                    form: { show: false },
                    column: { show: false },
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.OPERATION_REPORT_STATUS)
                    }),
                    search: { show: true },
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'daterange',
                                valueFormat: 'YYYY-MM-DD'
                            }
                        }
                    },
                    form: { show: false }
                }
            }
        },
        // 返回弹窗相关的状态和方法，供父组件使用
        showAccountLevelDialog,
        selectedAccountLevel,
        handleAccountLevelConfirm,
        handleAccountLevelCancel,
        accountLevelOptions,
        h, // 将h函数也返回，方便父组件渲染
        ElSelect, // 返回ElSelect和ElOption，方便父组件渲染
        ElOption
    }
} 
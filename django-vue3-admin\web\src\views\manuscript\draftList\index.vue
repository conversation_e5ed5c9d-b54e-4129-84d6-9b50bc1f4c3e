<template>
	<div class="manuscript-list">
		<SearchForm @search="handleSearch" @reset="handleReset" />

		<div class="table-header">
			<el-button v-if="hasPermission('draftList:Create')" type="primary" @click="handleAdd"> 新增 </el-button>
		</div>

		<div class="table-container">
			<ManuscriptTable
				:loading="loading"
				:table-data="tableData"
				:total="total"
				@update:page="handlePageChange"
				@edit="handleEdit"
				@delete="handleDelete"
				@audit="handleAudit"
				@contribute="handleContribute"
				page-type="draft"
			/>
		</div>

		<ManuscriptDialog
			ref="manuscriptDialogRef"
			v-model:visible="manuscriptDialogVisible"
			:edit-data="currentEditData"
			@submit="handleManuscriptSubmit"
		/>

		<AuditDialog
			ref="auditDialogRef"
			v-model:visible="auditDialogVisible"
			:loading="auditLoading"
			:flow-list="flowList"
			@submit="handleAuditSubmit"
		/>

		<ContributionDialog
			ref="contributionDialogRef"
			v-model:visible="contributionDialogVisible"
			:loading="contributionLoading"
			:manuscript-data="currentContributionData"
			@submit="handleContributionSubmit"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { auth } from '/@/utils/authFunction';
import * as api from '../manuscriptList/api';
import SearchForm from '../manuscriptList/components/SearchForm.vue';
import ManuscriptTable from '../manuscriptList/components/ManuscriptTable.vue';
import ManuscriptDialog from '../manuscriptList/components/ManuscriptDialog.vue';
import AuditDialog from '../manuscriptList/components/AuditDialog.vue';
import ContributionDialog from '../manuscriptList/components/ContributionDialog.vue';
import { QueryParams } from '../manuscriptList/api';

// 权限判断
const hasPermission = auth;

// 数据加载状态
const loading = ref(false);

// 表格数据
const tableData = ref([]);
const total = ref(0);

// 查询参数
const queryParams = ref({
	page: 1,
	limit: 20,
	title: '',
	creator_name: '',
	query_type: 'draft', // 新增查询类型
});

// 弹窗控制
const manuscriptDialogVisible = ref(false);
const auditDialogVisible = ref(false);
const contributionDialogVisible = ref(false);
const auditLoading = ref(false);
const contributionLoading = ref(false);
const currentEditData = ref();
const currentAuditData = ref();
const currentContributionData = ref();

// 弹窗Refs
const manuscriptDialogRef = ref<InstanceType<typeof ManuscriptDialog>>();
const auditDialogRef = ref<InstanceType<typeof AuditDialog>>();
const contributionDialogRef = ref<InstanceType<typeof ContributionDialog>>();

// 流程列表
const flowList = ref([]);

// 获取流程列表
const getFlowList = async () => {
	try {
		const { data } = await api.getFlowList();
		flowList.value = data;
	} catch (error) {
		console.error('获取流程列表失败', error);
	}
};

// 加载表格数据
const loadTableData = async () => {
	loading.value = true;
	try {
		const res = await api.getList(queryParams.value as QueryParams);
		// 检查返回数据结构
		console.log('API返回数据:', res);
		if (res.data) {
			// 根据实际返回数据结构调整
			tableData.value = Array.isArray(res.data) ? res.data : res.data.results || [];
			total.value = res.total || 0;
		} else {
			tableData.value = [];
			total.value = 0;
		}
	} catch (error) {
		console.error('加载数据失败', error);
		ElMessage.error('加载数据失败');
	} finally {
		loading.value = false;
	}
};

// 搜索处理
const handleSearch = (formData: any) => {
	queryParams.value = {
		...queryParams.value,
		page: 1,
		...formData,
	};
	loadTableData();
};

// 重置处理
const handleReset = () => {
	queryParams.value = {
		page: 1,
		limit: 20,
		title: '',
		creator_name: '',
		query_type: 'draft', // 新增查询类型
	};
	loadTableData();
};

// 分页处理
const handlePageChange = ({ page, limit }: { page: number; limit: number }) => {
	queryParams.value.page = page;
	queryParams.value.limit = limit;
	loadTableData();
};

// 新增处理
const handleAdd = () => {
	currentEditData.value = undefined;
	manuscriptDialogVisible.value = true;
};

// 编辑处理
const handleEdit = (row: any) => {
	currentEditData.value = row;
	manuscriptDialogVisible.value = true;
};

// 删除处理
const handleDelete = async (row: any) => {
	try {
		await ElMessageBox.confirm('确认删除该稿件吗？', '提示', {
			type: 'warning',
		});

		await api.deleteData(row.id);
		ElMessage.success('删除成功');
		loadTableData();
	} catch (error) {
		if (error !== 'cancel') {
			console.error('删除失败', error);
			ElMessage.error('删除失败');
		}
	}
};

// 提交审核处理
const handleAudit = (row: any) => {
	currentAuditData.value = row;
	auditDialogVisible.value = true;
	auditLoading.value = false;
};

// 投稿处理
const handleContribute = (row: any) => {
	currentContributionData.value = row;
	contributionDialogVisible.value = true;
	contributionLoading.value = false;
};

// 稿件表单提交
const handleManuscriptSubmit = async (formData: any) => {
	try {
		if (formData.id) {
			await api.updateData(formData);
			ElMessage.success('更新成功');
		} else {
			await api.createData(formData);
			ElMessage.success('创建成功');
		}
		manuscriptDialogVisible.value = false;
		manuscriptDialogRef.value?.resetForm();
		loadTableData();
	} catch (error) {
		console.error('保存失败', error);
		ElMessage.error('保存失败');
	}
};

// 审核提交
const handleAuditSubmit = async (formData: any) => {
	try {
		auditLoading.value = true;
		await api.submitToFlow(currentAuditData.value.id, formData);
		ElMessage.success('提交审核成功');
		auditLoading.value = false;
		auditDialogVisible.value = false;
		auditDialogRef.value?.resetForm();
		loadTableData();
	} catch (error) {
		console.error('提交审核失败', error);
		ElMessage.error('提交审核失败');
	}
};

// 投稿提交
const handleContributionSubmit = async (formData: any) => {
	try {
		contributionLoading.value = true;
		await api.submitContribution(formData.manuscript_id, formData.dept_id);
		ElMessage.success('投稿成功');
		contributionLoading.value = false;
		contributionDialogVisible.value = false;
		contributionDialogRef.value?.resetForm();
		loadTableData();
	} catch (error) {
		console.error('投稿失败', error);
		ElMessage.error('投稿失败');
	} finally {
		contributionLoading.value = false;
	}
};

// 初始化
onMounted(() => {
	loadTableData();
	getFlowList();
});
</script>

<style scoped>
.manuscript-list {
	padding: 16px;
}

.table-header {
	margin-bottom: 16px;
	display: flex;
	justify-content: flex-start;
}

.table-container {
	height: 70vh;
}
</style> 
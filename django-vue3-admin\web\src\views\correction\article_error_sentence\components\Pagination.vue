<template>
	<div class="pagination-container">
		<el-pagination
			v-model:current-page="currentPage"
			v-model:page-size="pageSize"
			:page-sizes="[10, 20, 50, 100]"
			:total="total"
			layout="total, sizes, prev, pager, next, jumper"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { QueryParams } from '../correction';

defineOptions({
	name: 'Pagination',
});

interface Props {
	modelValue: QueryParams;
	total: number;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'size-change', 'current-change']);

const currentPage = computed({
	get: () => props.modelValue.page || 1,
	set: (val) => {
		emit('update:modelValue', { ...props.modelValue, page: val });
	},
});

const pageSize = computed({
	get: () => props.modelValue.page_size || 20,
	set: (val) => {
		emit('update:modelValue', { ...props.modelValue, page_size: val });
	},
});

const handleSizeChange = (val: number) => {
	emit('size-change', val);
};

const handleCurrentChange = (val: number) => {
	emit('current-change', val);
};
</script>

<style lang="scss" scoped>
.pagination-container {
	margin: 15px 20px;
}
</style> 
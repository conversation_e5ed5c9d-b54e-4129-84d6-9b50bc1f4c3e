<template>
	<div>
		<!-- 添加导出内容包裹层 -->
		<div>
			<!-- 1.1.1 标题部分 -->
			<div>
				<div class="top">
					<div class="top-title">{{ title.split('月')[0] }}月本级抖音运营报告</div>
					<!-- <div style="font-size: 16px; margin-top: 20px">{{ dateRange }}</div>
          <div style="font-size: 14px; float: right; color: #4678ab">
            汇远轻媒出品
          </div> -->
				</div>
			</div>
			<div v-if="!hasAccounts">
				<div style="width: 100%; margin: 10px auto; font-size: 26px; text-align: center">无绑定账号</div>
			</div>
			<div v-else>
				<!-- 1.1.2 账号传播力指数报告 -->
				<!-- 1.1.4 作品传播报告 -->
				<div id="dy-myoperate-1">
					<!-- 账号统计信息 -->
					<div v-if="props.tenantName" class="account-stats-container">
						<div class="account-stats-content">
							<span class="tenant-name">{{ props.tenantName }}</span>
							<span class="stats-item"
								>本级抖音账号数量为<span class="count-number">{{ props.accountCounts.myAccount }}</span></span
							>
							<span class="stats-item"
								>下级抖音账号数量为<span class="count-number">{{ props.accountCounts.subAccount }}</span></span
							>
						</div>
					</div>
					<div class="title-wrapper">
						<div class="title1">账号传播分析</div>
					</div>

					<!-- 运营数据详情表格 -->
					<div v-if="accountDataList && accountDataList.length > 0">
						<div v-for="(account, index) in accountDataList" :key="account.account_name" :id="`dy-account-detail-${index + 1}`">
							<div class="title-wrapper">
								<div class="title2">{{ account.account_name }}运营数据详情表</div>
							</div>
							<div class="table-wrapper">
								<el-table
									:data="formatAccountTableData(account)"
									border
									:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
									:cell-style="{ height: '45px' }"
								>
									<el-table-column label="指标" align="center" prop="type" min-width="80px" />
									<el-table-column label="本月值" align="center" prop="total" min-width="80px" />
									<el-table-column label="较上月" align="center" prop="compareChange" min-width="80px" />
									<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="100px" />
									<el-table-column label="均篇值" align="center" prop="articleAvg" min-width="100px" />
									<el-table-column label="峰值" align="center" prop="peak" min-width="100px" />
									<el-table-column label="峰值作品" align="center" prop="peakCase" min-width="350px" />
								</el-table>
							</div>
						</div>
					</div>
				</div>
				<!-- 1.1.3 作品发布报告 -->
				<div v-for="(account, index) in worksIssue" :key="index" style="margin-bottom: 15px">
					<div :id="`dy-my-${index + 1}`">
						<div class="title-wrapper" v-if="index === 0">
							<div class="title1">作品发布分析</div>
						</div>
						<!-- <div class="content-wrapper">
							<p class="content">
								<span class="text-bold text-blue">{{ account.accountName }}</span
								>，月度发布作品总量： <span class="text-bold">{{ tranNum(account.count) }}个</span>， 比上月<span class="text-bold"
									>{{ account.worksStatus
									}}<span v-if="account.worksStatus !== '持平'">
										<span class="text-bold">{{ tranNum(account.diff) }}</span
										>篇， <span class="text-bold">{{ account.rate }}</span></span
									></span
								>
							</p>
						</div> -->
						<div class="table-wrapper">
							<el-table
								:data="[account]"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="发布作品量" align="center" prop="count" min-width="120px" />
								<el-table-column label="较上月" align="center" prop="diff" min-width="100px">
									<template #default="scope">
										{{ scope.row.worksStatus === '持平' ? '持平' : scope.row.worksStatus === '增加' ? '' : '-'
										}}{{ scope.row.worksStatus === '持平' ? '' : tranNum(Math.abs(scope.row.diff)) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="120px">
									<template #default="scope">
										{{ scope.row.worksStatus === '持平' ? '持平' : scope.row.worksStatus === '增加' ? '' : '-'
										}}{{ scope.row.worksStatus === '持平' ? '' : scope.row.rate }}
									</template>
								</el-table-column>
							</el-table>
						</div>
						<!-- 视频、图文类型分布饼图 -->
						<div class="sub-title">{{ account.accountName }}内容类型分布</div>
						<div class="chart-container">
							<pie :data="account.contentTypePie.data" :total="account.contentTypePie.total" style="width: 100%; height: 300px" />
						</div>

						<!-- 发布时间区间分布图 -->
						<div class="sub-title">{{ account.accountName }}发布时间分布</div>
						<div class="chart-container">
							<line-chart
								:data="account.timeDistribution"
								style="width: 100%; height: 300px"
								:axis-font-size="16"
								:legend-font-size="16"
								:axis-name-font-size="16"
							/>
						</div>
					</div>
				</div>


				<div id="dy-myoperate-2">
					<!-- 作品分析 -->
					<div class="title-wrapper">
						<div class="title1">作品分析</div>
					</div>

					<!-- 作品分析表格 -->
					<div class="title-wrapper">
						<div class="title2">本月点赞量前三作品及最少作品分析</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatWorksAnalysisTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" prop="rank" min-width="80px" />
							<el-table-column label="作品标题" align="center" prop="title" min-width="350px" />
							<el-table-column label="点赞量" align="center" prop="like" min-width="80px" />
							<el-table-column label="评论量" align="center" prop="comment" min-width="80px" />
							<el-table-column label="收藏量" align="center" prop="collect" min-width="80px" />
							<el-table-column label="转发量" align="center" prop="forward" min-width="80px" />
							<el-table-column label="来源账号" align="center" prop="account" min-width="120px" />
						</el-table>
					</div>
				</div>

				<div id="dy-myoperate-3">
					<!-- 1.1.5 账号粉丝报告 -->
					<div class="title-wrapper">
						<div class="title1">账号粉丝</div>
					</div>

					<!-- 粉丝数据详情表格 -->
					<div class="title-wrapper">
						<div class="title2">本级账号粉丝总数汇总</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatAllFansTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="序号" align="center" prop="index" min-width="80px" />
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="200px" />
							<el-table-column label="粉丝总数" align="center" prop="totalFans" min-width="120px" />
						</el-table>
					</div>

					<!-- 总关注量占比图 -->
					<div class="chart-container" v-if="fansPieData.length > 1">
						<div class="title3">总粉丝量占比关系</div>
						<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 350px" :label-font-size="18" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineExpose, watch, computed } from 'vue';
import pie from '/@/components/Echarts/pie.vue';
import lineChart from '/@/components/Echarts/line.vue';
import { tranNumber } from '/@/utils/tranNum';

const props = defineProps({
	selfReportData: {
		type: Object,
		default: () => ({}),
	},
	title: {
		type: String,
		default: '',
	},
	date: {
		type: Object,
		default: () => ({}),
	},
	tenantName: {
		type: String,
		default: '',
	},
	accountCounts: {
		type: Object,
		default: () => ({
			myAccount: 0,
			subAccount: 0,
		}),
	},
});

// 数据定义
const pageLoading = ref(true);
const hasAccounts = ref(true);
const reportYear = ref(null);
const month = ref(null);
const reportDay = ref(null);
const dateRange = ref(null);
// 传播力指数报告
const dci = ref([]);

// 作品发布报告
const worksIssue = ref([]);
const contentTypePieData = ref([]);
const contentTypePieTotal = ref({});
const timeDistributionData = ref([]);

// 作品传播报告
const worksLike = ref([]);
const worksComment = ref([]);
const worksForward = ref([]);
const worksCollect = ref([]);
const worksTopLike = ref([]);
const minVideo = ref(null);

// 粉丝报告
const totalFans = ref([]);
const fansPieData = ref([]);
const fansPieTotal = ref({});

// 新增：账号数据列表，用于表格展示
const accountDataList = ref([]);

// 数字转换方法
const tranNum = (num) => {
	if (num !== undefined && num !== null && num !== '') {
		return tranNumber(num);
	}
	return '';
};

// 格式化账号表格数据
const formatAccountTableData = (account) => {
	const tableData = [];

	// DCI指数数据
	if (account.dci) {
		const item = account.dci;
		tableData.push({
			type: '传播力指数',
			total: item.dci || 0,
			compareChange: `${item.compare_trend > 0 ? '' : item.compare_trend < 0 ? '-' : '持平'}${item.compare_trend === 0 ? '' : Math.abs(item.diff)}`,
			compareRate: `${item.compare_trend > 0 ? '' : item.compare_trend < 0 ? '-' : '持平'}${item.compare_rate || 0}%`,
			articleAvg: '-',
			peak: '-',
			peakCase: '-',
		});
	}

	// 点赞量数据
	if (account.like_count_stats) {
		const item = account.like_count_stats;
		tableData.push({
			type: '点赞量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg || 0),
			peak: item.top_article ? item.top_article.value : '-',
			peakCase: item.top_article ? item.top_article.title : '-',
		});
	}

	// 评论量数据
	if (account.comment_count_stats) {
		const item = account.comment_count_stats;
		tableData.push({
			type: '评论量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg || 0),
			peak: item.top_article ? item.top_article.value : 0,
			peakCase: item.top_article ? item.top_article.title : '-',
		});
	}

	// 转发量数据
	if (account.share_count_stats) {
		const item = account.share_count_stats;
		tableData.push({
			type: '转发量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg || 0),
			peak: item.top_article ? item.top_article.value : 0,
			peakCase: item.top_article ? item.top_article.title : '-',
		});
	}

	// 收藏量数据
	if (account.collect_count_stats) {
		const item = account.collect_count_stats;
		tableData.push({
			type: '收藏量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg || 0),
			peak: item.top_article ? item.top_article.value : 0,
			peakCase: item.top_article ? item.top_article.title : '-',
		});
	}

	return tableData;
};

// 格式化作品分析表格数据
const formatWorksAnalysisTableData = () => {
	const tableData = [];

	// 添加前三名作品
	worksTopLike.value.forEach((work, index) => {
		tableData.push({
			rank: index + 1,
			title: work.title,
			like: tranNum(work.like),
			comment: tranNum(work.comment),
			collect: tranNum(work.collect),
			forward: tranNum(work.forward),
			account: work.account,
		});
	});

	// 添加最少点赞作品
	if (minVideo.value) {
		tableData.push({
			rank: '最少',
			title: minVideo.value.title,
			like: tranNum(minVideo.value.like),
			comment: tranNum(minVideo.value.comment),
			collect: tranNum(minVideo.value.collect),
			forward: tranNum(minVideo.value.forward),
			account: minVideo.value.account,
		});
	}

	return tableData;
};

// 格式化所有账号粉丝表格数据
const formatAllFansTableData = () => {
	const tableData = [];

	// 从totalFans.data获取所有账号的粉丝数据
	if (totalFans.value && totalFans.value.data) {
		totalFans.value.data.forEach((account, index) => {
			tableData.push({
				index: index + 1,
				accountName: account.name,
				totalFans: tranNum(account.count),
			});
		});
	}

	return tableData;
};

// 处理数据
const processData = (data) => {
	// 基础信息处理
	hasAccounts.value = data.dci && data.dci.length > 0; // 根据账号数量是否大于零，判断当前是否绑定了我运营的账号
	month.value = data.month;
	dateRange.value = data.yearMonth;

	// 传播力指数报告
	dci.value = data.dci || [];

	// 作品发布报告
	worksIssue.value = data.worksIssue || [];

	// 点赞量
	worksLike.value = data.worksLike || [];

	// 评论量
	worksComment.value = data.worksComment || [];

	// 转发量
	worksForward.value = data.worksForward || [];

	// 收藏量
	worksCollect.value = data.worksCollect || [];

	// 作品分析
	worksTopLike.value = data.worksTopLike || [];

	// 点赞最少的视频
	minVideo.value = data.minVideo;

	// 粉丝报告
	totalFans.value = data.totalFans || {};

	// 粉丝占比饼图数据
	fansPieData.value = data.totalFans?.data || [];
	fansPieTotal.value = data.totalFans?.total || {};

	// 新增：账号数据列表，用于表格展示
	accountDataList.value = data.accountDataList || [];

	pageLoading.value = false;
};

// 监听selfReportData变化，处理数据
watch(
	() => props.selfReportData,
	(newVal) => {
		if (newVal) {
			processData(newVal);
		}
	},
	{ immediate: true, deep: true }
);

// 定义导出的模块ID列表，供父组件使用
const moduleIds = computed(() => {
	const ids = [];
	// 先添加传播分析模块ID
	ids.push('dy-myoperate-1');
	// 然后动态生成作品发布模块的ID
	worksIssue.value.forEach((_, index) => {
		ids.push(`dy-my-${index + 1}`);
	});
	// 最后添加其他固定模块ID：作品分析、粉丝
	ids.push('dy-myoperate-2', 'dy-myoperate-3');
	return ids;
});

defineExpose({
	moduleIds,
});
</script>

<style scoped>
.top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	overflow: hidden;
	height: 200px;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.title-wrapper {
	width: 100%;
}

.title1 {
	font-size: 30px;
	width: 25%;
	margin: 0 auto 15px;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 15px;
	line-height: 1.5;
}

.title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	margin: 0px auto 10px;
	text-align: center;
	padding-bottom: 10px;
	line-height: 1.5;
}
:deep(.el-table .cell) {
	font-size: 18px;
	color: #000;
}
:deep(.el-table--border) {
	border-top: 1px solid #fff;
}

.content-wrapper {
	position: relative;
	margin: 0 30px 20px;
	padding: 7px 20px 20px 20px;
	border: #9d9c9a 1px solid;
	overflow: visible;
}

.content {
	font-size: 18px;
	line-height: 2;
	/* padding: 10px 0; */
	white-space: inherit !important;
}

.sub-title {
	font-size: 16px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}

.chart-container {
	margin: 0 30px;
}

.text-bold {
	font-weight: bold;
}
:deep(.el-table th) {
	font-weight: bold;
	color: #000;
	background-color: #f8f8f9;
	padding: 15px 4px !important;
}
.text-blue {
	color: #00abf9;
}

:deep(.el-loading-spinner) {
	top: 300px;
}
.title3 {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}
.table-wrapper {
	margin: 20px 30px;
}

/* 添加按钮样式 */
.btn-wrapper {
	position: absolute;
	right: 50px;
	top: 95px;
	z-index: 1000;
}
* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

/* 账号统计信息样式 */
.account-stats-container {
	width: 100%;
	background-color: #fff;
	border-bottom: 2px solid #008ccc;
	padding: 20px 0;
	margin-bottom: 20px;
}

.account-stats-content {
	text-align: center;
	font-size: 20px;
	line-height: 1.8;
	color: #333;
}

.tenant-name {
	font-weight: bold;
	color: #008ccc;
	font-size: 20px;
	margin-right: 8px;
}

.stats-item {
	margin: 0 15px;
	color: #555;
}

.count-number {
	font-weight: bold;
	color: #e74c3c;
	font-size: 20px;
}
</style>

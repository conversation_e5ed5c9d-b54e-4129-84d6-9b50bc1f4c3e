<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell_task_kwargs="scope">
				<el-popover
					placement="bottom"
					:width="600"
					trigger="click"
					content="this is content, this is content, this is content"
					@hide="taskKwargsShow = false"
				>
					<template #reference>
						<el-button class="m-2" type="primary" @click="handleTaskKwargsClick(scope.row.task_kwargs)">查看</el-button>
					</template>
					<div style="height: 260px">
						<JsonEditorVue class="editor" v-if="taskKwargsShow" style="height: 250px" v-model="taskKwargsValue" />
					</div>
				</el-popover>
			</template>
			<template #cell_result="scope">
				<el-popover
					placement="bottom"
					:width="600"
					trigger="click"
					content="this is content, this is content, this is content"
					@hide="resultShow = false"
				>
					<template #reference>
						<el-button class="m-2" type="success" @click="handleResultClick(scope.row.result)">查看</el-button>
					</template>
					<div style="height: 260px">
						<JsonEditorVue class="editor" v-if="resultShow" style="height: 250px" v-model="resultValue" />
					</div>
				</el-popover>
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts" setup name="taskLog">
import { ref, onMounted, watch } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import JsonEditorVue from 'json-editor-vue3';
import 'jsoneditor';
const taskKwargsValue = ref({});
const taskKwargsShow = ref(false);
const resultValue = ref({});
const resultShow = ref(false);
const props = defineProps<{
	taskItem: object;
}>();

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions, context: { taskItem: props.taskItem } });
// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});

const parseValue = (value: string) => {
	try {
		if (typeof value === 'string') {
			// 移除外层引号
			let processedValue = value.replace(/^"(.*)"$/, '$1');

			// 处理 Unicode 转义序列
			try {
				// 尝试直接解码 Unicode 字符串
				processedValue = decodeURIComponent(JSON.parse(`"${processedValue}"`));
			} catch (e) {
				// 如果解码失败，尝试第二种方式
				processedValue = processedValue.replace(/\\u([\da-f]{4})/gi, (_, hex) => {
					return String.fromCodePoint(parseInt(hex, 16));
				});
			}

			// 尝试解析为 JSON 对象（如果是 JSON 字符串的话）
			try {
				return JSON.parse(processedValue);
			} catch {
				return processedValue;
			}
		}
		return value;
	} catch (e) {
		return value;
	}
};

const handleTaskKwargsClick = (value: string) => {
	taskKwargsValue.value = parseValue(value);
	taskKwargsShow.value = true;
};

const handleResultClick = (value: string) => {
	resultValue.value = parseValue(value);
	resultShow.value = true;
};
</script>

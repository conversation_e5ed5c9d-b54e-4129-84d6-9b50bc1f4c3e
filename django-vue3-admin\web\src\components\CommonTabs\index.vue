<template>
	<div class="common-tabs" :class="customClass" v-show="visibleItems.length > 0">
		<el-radio-group v-model="currentTab" @change="handleTabChange" :size="size">
			<el-radio-button v-for="item in visibleItems" :key="item.value" :value="item.value" :disabled="item.disabled">
				<template v-if="item.icon">
					<component :is="item.icon" v-if="typeof item.icon === 'object'" class="tab-icon" />
					<SvgIcon :name="item.icon" v-else class="tab-icon" />
				</template>
				{{ item.label }}
			</el-radio-button>
		</el-radio-group>
	</div>
</template>

<script setup lang="ts" name="CommonTabs">
import { ref, computed, watch, PropType, defineAsyncComponent } from 'vue';
import { auth } from '/@/utils/authFunction';

// 动态导入SvgIcon组件
const SvgIcon = defineAsyncComponent(() => import('/@/components/svgIcon/index.vue'));

// Tab项目接口定义
export interface TabItem {
	label: string;
	value: string | number;
	icon?: string | object;
	disabled?: boolean;
	permission?: string; // 新增权限字段
}

// 定义组件属性
const props = defineProps({
	// 初始选中的值
	modelValue: {
		type: [String, Number],
		default: '',
	},
	// Tab项列表
	items: {
		type: Array as PropType<TabItem[]>,
		required: true,
	},
	// 大小：可选值 large / default / small
	size: {
		type: String,
		default: 'default',
		validator: (value: string) => ['large', 'default', 'small'].includes(value),
	},
	// 自定义样式类
	customClass: {
		type: String,
		default: '',
	},
	// 按钮最小宽度
	minWidth: {
		type: Number,
		default: 120,
	},
	// 按钮内边距
	padding: {
		type: String,
		default: '12px 20px',
	},
	// 字体大小
	fontSize: {
		type: Number,
		default: 14,
	},
	// 活跃状态文字颜色
	activeColor: {
		type: String,
		default: '#409eff',
	},
	// 活跃状态边框颜色
	activeBorderColor: {
		type: String,
		default: '#409eff',
	},
	// 背景色
	bgColor: {
		type: String,
		default: '#ffffff',
	},
});

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'change']);

// Tab项列表
const tabItems = computed(() => props.items);

// 当前选中的Tab
const currentTab = ref(props.modelValue || (tabItems.value.length > 0 ? tabItems.value[0].value : ''));

// 过滤有权限的Tab项
const visibleItems = computed(() => {
	return props.items.filter((item) => {
		// 如果没有设置权限字段，则默认显示
		if (!item.permission) {
			return true;
		}
		// 使用权限函数检查权限
		return auth(item.permission);
	});
});

// 监听外部值变化
watch(
	() => props.modelValue,
	(newVal) => {
		if (newVal !== undefined && newVal !== currentTab.value) {
			currentTab.value = newVal;
		}
	}
);

// 监听items变化，确保权限过滤正确应用
watch(
	() => props.items,
	() => {
		// 当items变化时，visibleItems会自动重新计算
		// 这里不需要额外处理，因为visibleItems的watch会处理选中项的更新
	}
);

// 监听可见项变化，自动选择第一个有权限的项
watch(
	() => visibleItems.value,
	(newItems) => {
		if (newItems.length > 0) {
			// 检查当前选中项是否还在可见列表中
			const currentItemExists = newItems.some((item) => item.value === currentTab.value);
			if (!currentItemExists) {
				// 如果当前选中项不可见，则选择第一个可见项
				currentTab.value = newItems[0].value;
				emit('update:modelValue', currentTab.value);
				emit('change', currentTab.value);
			}
		}
	},
	{ immediate: true }
);

// Tab变更处理函数
const handleTabChange = (value: string | number) => {
	// 触发更新和变更事件
	emit('update:modelValue', value);
	emit('change', value);
};
</script>

<style lang="scss" scoped>
.common-tabs {
	display: inline-block;
	margin-right: 16px;

	.tab-icon {
		margin-right: 5px;
	}

	:deep(.el-radio-button__inner) {
		min-width: v-bind('minWidth + "px"');
		padding: v-bind('padding');
		font-size: v-bind('fontSize + "px"');
		background-color: v-bind('bgColor');
		color: v-bind('activeColor');
		border-color: #dcdfe6;
		height: auto;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
		background-color: v-bind('bgColor');
		color: v-bind('activeColor');
		border-color: v-bind('activeBorderColor');
		box-shadow: -1px 0 0 0 v-bind('activeBorderColor');
	}

	:deep(.el-radio-group) {
		display: flex;
	}
}
</style> 
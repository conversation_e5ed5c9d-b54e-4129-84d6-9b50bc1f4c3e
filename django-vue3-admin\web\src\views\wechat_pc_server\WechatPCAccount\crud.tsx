import { CrudOptions, AddReq, DelReq, EditReq, CrudExpose, UserPageQuery, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { request } from '/@/utils/service'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag } from 'element-plus'

interface BasicColumn {
    title: string;
    dataIndex: string;
    width?: number;
    align?: string;
    customRender?: (options: { record: any }) => any;
}

interface FormSchema {
    field: string;
    label: string;
    component: string;
    required?: boolean;
    colProps?: any;
    componentProps?: any;
    defaultValue?: any;
}

export const columns: BasicColumn[] = [
  {
    title: '微信号',
    dataIndex: 'wechat_id',
    width: 150,
    align: 'center'
  },
  {
    title: '昵称',
    dataIndex: 'nick_name',
    width: 150,
    align: 'center'
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    width: 150,
    align: 'center'
  },
  {
    title: '端口',
    dataIndex: 'port',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    align: 'center',
    customRender: ({ record }) => {
      const status = record.status
      const enable = ~~status === 1
      const color = enable ? 'green' : 'red'
      const text = enable ? '在线' : '离线'
      return h(ElTag, { color: color }, () => text)
    }
  },
  {
    title: '最后活跃时间',
    dataIndex: 'last_active_time',
    width: 180,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    width: 180,
    align: 'center'
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    width: 180,
    align: 'center'
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'wechat_id',
    label: '微信号',
    component: 'Input',
    colProps: { span: 6 }
  },
  {
    field: 'nick_name',
    label: '昵称',
    component: 'Input',
    colProps: { span: 6 }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '离线', value: 0 },
        { label: '在线', value: 1 }
      ]
    },
    colProps: { span: 6 }
  }
]

export const formSchema: FormSchema[] = [
  {
    field: 'wechat_id',
    label: '微信号',
    component: 'Input',
    required: true
  },
  {
    field: 'nick_name',
    label: '昵称',
    component: 'Input',
    required: true
  },
  {
    field: 'ip',
    label: 'IP地址',
    component: 'Input',
    required: true
  },
  {
    field: 'port',
    label: '端口',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 65535
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'RadioButtonGroup',
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '离线', value: 0 },
        { label: '在线', value: 1 }
      ]
    }
  }
]

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    const pageRequest = async (query: any) => {
        return await api.getWechatPCAccountList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateWechatPCAccount(form)
    }
    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteWechatPCAccount(row.id)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createWechatPCAccount(form)
    }

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('WechatPCAccount:Create')
                    }
                }
            },
            rowHandle: {
                width: 260,
                buttons: {
                    edit: {
                        show: auth('WechatPCAccount:Update')
                    },
                    remove: {
                        show: auth('WechatPCAccount:Delete')
                    }
                }
            },
            columns: {
                wechat_id: {
                    title: '微信ID',
                    type: 'text',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '微信ID必填' }]
                    }
                },
                nick_name: {
                    title: '昵称',
                    type: 'text',
                    search: { show: true }
                },
                ip: {
                    title: 'IP地址',
                    type: 'text',
                    form: {
                        rules: [{ required: true, message: 'IP地址必填' }]
                    }
                },
                port: {
                    title: '端口',
                    type: 'number',
                    form: {
                        rules: [{ required: true, message: '端口必填' }]
                    }
                },
                status: {
                    title: '状态',
                    type: 'select',
                    search: { show: true },
                    form: {
                        component: {
                            options: [
                                { value: 0, label: '离线' },
                                { value: 1, label: '在线' }
                            ]
                        }
                    }
                },
                last_active_time: {
                    title: '最后活跃时间',
                    type: 'datetime',
                    form: { show: false }
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    form: { show: false }
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime',
                    form: { show: false }
                }
            }
        }
    }
}

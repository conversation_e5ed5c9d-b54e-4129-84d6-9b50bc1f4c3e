import { App } from 'vue';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas, getEnumData, getEnumLabel, getEnumColor } from '/@/stores/enum';

// 定义枚举数据的接口
export interface EnumData {
  value: string | number;
  label: string;
  [key: string]: any;
}

// 声明模块扩充全局属性
declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    $ENUM: typeof ENUM_TYPE;
    $getEnumDatas: (type: string) => EnumData[];
    $getEnumData: (type: string, value: string | number) => EnumData | undefined;
    $getEnumLabel: (type: string, value: string | number) => string;
    $getEnumColor: (type: string, value: string | number) => string;
  }
}

export const enumPlugin = {
  install: (app: App) => {
    // 注册全局属性
    app.config.globalProperties.$ENUM = ENUM_TYPE;
    
    // 注册全局方法
    app.config.globalProperties.$getEnumDatas = getEnumDatas;
    app.config.globalProperties.$getEnumData = getEnumData;
    app.config.globalProperties.$getEnumLabel = getEnumLabel;
    app.config.globalProperties.$getEnumColor = getEnumColor;
  }
};

export default enumPlugin; 
<template>
	<div class="markdown-editor-container">
		<MdEditor
			v-model="content"
			:height="height"
			:preview="preview"
			:toolbars="disabled ? [] : toolbars"
			:theme="theme"
			:readOnly="disabled"
			:disabled="disabled"
			@save="handleSave"
			@change="handleChange"
		/>
	</div>
</template>

<script setup lang="ts" name="MarkdownEditor">
import { ref, watch, computed } from 'vue';
import { MdEditor } from 'md-editor-v3';
import type { ToolbarNames, Themes } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';

// 定义组件属性
const props = defineProps({
	// 双向绑定的内容
	modelValue: {
		type: String,
		default: '',
	},
	// 编辑器高度
	height: {
		type: String,
		default: '400px',
	},
	// 是否显示预览
	preview: {
		type: Boolean,
		default: true,
	},
	// 主题
	theme: {
		type: String as () => Themes,
		default: 'light' as Themes,
	},
	// 是否禁用
	disabled: {
		type: Boolean,
		default: false,
	},
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'save', 'change']);

// 内容状态
const content = ref(props.modelValue);

// 工具栏配置
const toolbars = computed((): ToolbarNames[] => [
	'bold',
	'underline',
	'italic',
	'-',
	'title',
	'strikeThrough',
	'sub',
	'sup',
	'quote',
	'unorderedList',
	'orderedList',
	'task',
	'-',
	'codeRow',
	'code',
	'link',
	'image',
	'table',
	'mermaid',
	'katex',
	'-',
	'revoke',
	'next',
	'save',
	'=',
	'pageFullscreen',
	'fullscreen',
	'preview',
	'htmlPreview',
	'catalog',
]);

// 监听外部值变化
watch(
	() => props.modelValue,
	(newVal) => {
		content.value = newVal;
	},
	{ immediate: true }
);

// 监听内容变化
watch(content, (newVal) => {
	emit('update:modelValue', newVal);
});

// 处理保存事件
const handleSave = (value: string) => {
	emit('save', { value });
};

// 处理内容变化事件
const handleChange = (value: string) => {
	emit('change', { value });
};
</script>

<style scoped>
.markdown-editor-container {
	width: 100%;
	min-height: 300px;
}

/* 自定义样式 */
:deep(.md-editor) {
	border: 1px solid var(--el-border-color-light);
	border-radius: 4px;
	width: 100%;
}

:deep(.md-editor-dark) {
	border-color: var(--el-border-color-darker);
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
	.markdown-editor-container {
		min-height: 250px;
	}

	:deep(.md-editor) {
		font-size: 14px;
	}
}

/* 弹窗内的编辑器样式优化 */
:deep(.md-editor-toolbar) {
	flex-wrap: wrap;
}

:deep(.md-editor-toolbar-item) {
	margin: 2px;
}
</style> 
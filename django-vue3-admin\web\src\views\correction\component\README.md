# 校对管理通用组件

## 核心功能
- 导出配置：提供统一的数据导出配置弹窗，支持任务和目标两种导出模式
- 多选筛选：支持错误类型、判定状态等多维度条件选择
- 格式支持：支持Excel、CSV等多种导出格式配置
- 参数验证：表单验证确保导出配置的完整性和正确性

## 技术实现

### 导出配置组件 (ExportConfigDialog.vue)
- 模式切换：target_type控制任务/目标导出模式，动态显示对应选项
- 数据联动：taskOptions和targetOptions提供选择数据源，支持搜索筛选
- 条件配置：error_type多选支持，handleSelectAllErrorTypes()实现全选功能
- 表单验证：formRef和rules实现导出参数的完整性校验
- 提交处理：handleSubmit()处理导出请求，emit('success')通知父组件

### 通用工具方法
- 选项数据获取：异步加载任务和目标选项数据
- 状态管理：dialogVisible控制弹窗显示状态  
- 参数构建：构建符合后端要求的导出参数对象

### 使用集成方式
- 跨模块复用：可被article_error_sentence、proofread_task等模块引用
- 配置灵活：支持预设任务ID、目标ID等参数，适应不同使用场景
- 事件通信：通过props和emit实现与父组件的数据交互
# 下拉选择器组件使用规范

## 基于字典的下拉选择器

### 标准字典列配置模板

```typescript
{
    title: '状态',          // 列标题
    type: 'dict-select',   // 字段类型：dict-select 用于字典选择
    dict: dict({           // 字典配置
        data: dictionary('button_status_bool')  // 字典数据源
    }),
    search: { show: true },  // 是否在搜索表单中显示
    form: {                  // 表单配置
        component: {         // 表单组件配置
            span: 12         // 表单项宽度
        }
    },
    column: {               // 列配置
        width: 100,         // 列宽
        component: {        // 列组件配置
            name: 'dict-select',  // 组件名称
            props: {         // 组件属性
                dict: dict({
                    data: dictionary('button_status_bool')
                })
            }
        }
    }
}
```

### 完整示例

```ts
// 1. 引入必要的依赖
import { dict } from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';

// 2. 列配置
export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
	return {
		crudOptions: {
			columns: {
				status: {
					title: '状态',
					type: 'dict-select',
					dict: dict({
						data: dictionary('button_status_bool'),
					}),
					search: { show: true },
					form: {
						component: {
							span: 12,
						},
					},
					column: {
						width: 100,
						component: {
							name: 'dict-select',
							props: {
								dict: dict({
									data: dictionary('button_status_bool'),
								}),
							},
						},
					},
				},
			},
		},
	};
}
```

## 树形下拉选择器

```typescript
fieldName: {
    title: '字段名称',
    type: 'dict-tree',
    dict: dict({
        isTree: true,
        url: '/api/system/endpoint/',
        value: 'id',
        label: 'name',
    }),
    column: {
        minWidth: 200,
        formatter({ value, row, index }) {
            return row.field_name_all // 显示完整路径
        }
    },
    form: {
        rules: [{ required: true, message: '必填项' }],
        component: {
            filterable: true,
            placeholder: '请选择',
            props: {
                props: {
                    value: 'id',
                    label: 'name',
                },
            },
        },
    },
}
```

## 可搜索，且数据来源于接口的下拉选择器

```ts
tenant: {
    title: '租户',
    type: 'dict-select',
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
                filterable: true,
                multiple: true,
                options: asyncCompute({
                    asyncFn: async () => {
                        const res = await tenantApi.getList({page: 1, limit: 500})
                        return res.data.map((item: any) => ({
                            label: item.name,
                            value: item.id
                        }))
                    }
                })
            }
        }
    },
    column: {
        minWidth: 120,
        show: false
    },
    form: { show: false }
},
```

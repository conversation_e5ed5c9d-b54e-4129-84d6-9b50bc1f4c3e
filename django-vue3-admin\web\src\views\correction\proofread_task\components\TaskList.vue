<template>
	<div class="task-list">
		<el-table v-loading="loading" :data="list" border style="width: 100%" :height="tableHeight" :row-key="getRowKey" :key="tableKey">
			<el-table-column prop="id" label="ID" width="50" />
			<el-table-column prop="name" label="任务名称" min-width="120" />
			<!-- <el-table-column prop="type" label="执行类型" min-width="80">
				<template #default="scope">
					{{ getTaskTypeName(scope.row.type) }}
				</template>
			</el-table-column> -->
			<el-table-column prop="status" label="任务状态" min-width="70">
				<template #default="scope">
					<dict-tag :options="$getEnumDatas($ENUM.TASK_STATUS)" :value="scope.row.status" :color-type="statusTagTypeMap" />
				</template>
			</el-table-column>
			<el-table-column prop="create_time" label="任务创建时间" min-width="70" />
			<el-table-column prop="tenant_id" label="所属客户" min-width="70" v-auth="'Tenants:List'">
				<template #default="scope">
					<span>{{ getTenantName(scope.row.tenant_id) }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="target_count" label="目标数" min-width="50" />
			<el-table-column prop="article_count" label="文章数" min-width="50" />
			<el-table-column prop="crawl_count" label="已抓取" min-width="50" />
			<el-table-column prop="error_count" label="已纠错" min-width="50" />
			<el-table-column prop="result_count" label="错误结果" min-width="50" />
			<el-table-column prop="recheck_count" label="需复检" min-width="50" />
			<el-table-column prop="ai_recheck_count" label="AI判定需复检" min-width="50" />
			<el-table-column label="操作" fixed="right" min-width="170">
				<template #default="scope">
					<el-button type="primary" link @click="handleAddTarget(scope.row)" v-auth="'CorrectionTarget:Create'">新建目标</el-button>
					<el-button type="primary" link @click="handleOpenTarget(scope.row)" v-auth="'ProofreadTask:OpenTarget'">开启所有目标</el-button>
					<el-button type="primary" link @click="handleViewTargets(scope.row)" v-auth="'ViewTargets:Button'">查看目标</el-button>
					<el-button type="success" link @click="handleViewErrors(scope.row)" v-auth="'ProofreadTask:ViewErrors'">查看校对结果</el-button>
					<el-button type="success" link @click="handleExport(scope.row)" v-auth="'ProofreadTask:Export'">导出</el-button>
					<el-button type="danger" link @click="handleDelete(scope.row)" v-auth="'CorrectionTask:Delete'"> 删除 </el-button>
					<el-button type="primary" link @click="handleGenerateReport(scope.row)" v-auth="'Generate:Report'">生成报表</el-button>
					<el-button type="primary" link v-if="scope.row.report_id" @click="handleViewReport(scope.row)">查看报表</el-button>
					<el-button type="primary" link @click="handleUpdateTaskStats(scope.row)" v-auth="'ProofreadTask:UpdateTaskStats'">更新统计信息</el-button>
				</template>
			</el-table-column>
		</el-table>

		<div class="pagination-container">
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:page-sizes="[10, 20, 30, 50]"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 导出配置弹窗 -->
		<ExportConfigDialog v-model:visible="exportDialogVisible" :taskName="exportTaskName" :taskId="exportTaskId" @success="handleExportSuccess" />
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue';
import type { CorrectionTaskInfo } from '/@/views/correction/proofread_task/api';
import type { TenantInfo } from '/@/views/tenant/api';
import { DictionaryStore } from '/@/stores/dictionary';
import ExportConfigDialog from '/@/views/correction/component/ExportConfigDialog.vue';
import { useRouter } from 'vue-router';

// 定义属性
const props = defineProps({
	list: {
		type: Array as () => CorrectionTaskInfo[],
		required: true,
	},
	loading: {
		type: Boolean,
		required: true,
	},
	total: {
		type: Number,
		required: true,
	},
	currentPage: {
		type: Number,
		required: true,
	},
	pageSize: {
		type: Number,
		required: true,
	},
	tenants: {
		type: Array as () => TenantInfo[],
		required: true,
	},
});

const getRowKey = (row: CorrectionTaskInfo) => row.id;
const tableKey = ref(0);
// 定义事件
const emit = defineEmits([
	'update:currentPage',
	'update:pageSize',
	'addTarget',
	'viewErrors',
	'viewTargets',
	'delete',
	'generateReport',
	'openTarget',
	'updateTaskStats',
]);
const tableHeight = ref(window.innerHeight - 320);
// 本地状态
const currentPage = ref(props.currentPage);
const pageSize = ref(props.pageSize);
// 路由
const router = useRouter();
// 导出相关
const exportDialogVisible = ref(false);
const exportTaskName = ref('');
const exportTaskId = ref<number | undefined>(undefined);

// 获取字典数据
const dictionaryStore = DictionaryStore();

// 处理窗口大小变化
const handleResize = () => {
	tableHeight.value = window.innerHeight - 320;
};

// 添加和移除窗口大小变化监听器
onMounted(() => {
	if (Object.keys(dictionaryStore.data).length === 0) {
		dictionaryStore.getSystemDictionarys();
	}
	window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});

// 任务类型名称映射
const taskTypeMap = computed(() => {
	if (dictionaryStore.data.task_type) {
		// 将字典数据转换为简单的键值对对象
		const typeMap: Record<number, string> = {};
		dictionaryStore.data.task_type.forEach((item: any) => {
			typeMap[item.value] = item.label;
		});
		return typeMap;
	} else {
		return {
			1: '历史巡检',
			2: '复检',
		};
	}
});
const handleUpdateTaskStats = (row: CorrectionTaskInfo) => {
	emit('updateTaskStats', row);
};
// 任务状态名称映射
const statusMap = computed(() => {
	if (dictionaryStore.data.task_status) {
		// 将字典数据转换为简单的键值对对象
		const statusMap: Record<number, string> = {};
		dictionaryStore.data.task_status.forEach((item: any) => {
			statusMap[item.value] = item.label;
		});
		return statusMap;
	} else {
		return {
			0: '未开始',
			1: '进行中',
			2: '已完成',
		};
	}
});

// 状态标签类型映射
const statusTagTypeMap = ['info', 'warning', 'primary', 'success', 'danger'];

// 获取任务类型名称
const getTaskTypeName = (type: number): string => {
	const map = taskTypeMap.value;
	return map[type] || '未知类型';
};

// 获取任务状态名称
const getStatusName = (status: number): string => {
	const map = statusMap.value;
	return map[status] || '未知状态';
};

// 获取租户名称
const getTenantName = (tenantId: number): string => {
	// 这里可以根据实际情况获取租户名称
	const tenant = props.tenants.find((t) => t.id === tenantId);
	return tenant ? tenant.name : `客户ID: ${tenantId}`;
};

// 监听属性变化
watch(
	() => props.currentPage,
	(newVal) => {
		currentPage.value = newVal;
	}
);

watch(
	() => props.pageSize,
	(newVal) => {
		pageSize.value = newVal;
	}
);

// 同步本地状态到父组件
watch(currentPage, (newVal) => {
	emit('update:currentPage', newVal);
});

watch(pageSize, (newVal) => {
	emit('update:pageSize', newVal);
});

// 分页处理
const handleSizeChange = (val: number) => {
	emit('update:pageSize', val);
};

const handleCurrentChange = (val: number) => {
	emit('update:currentPage', val);
};

// 操作处理
const handleAddTarget = (row: CorrectionTaskInfo) => {
	emit('addTarget', row);
};

const handleViewTargets = (row: CorrectionTaskInfo) => {
	emit('viewTargets', row);
};
// 查看任务下的错误

const handleViewErrors = (row: CorrectionTaskInfo) => {
	emit('viewErrors', row);
};
const handleDelete = (row: CorrectionTaskInfo) => {
	emit('delete', row);
};
const handleOpenTarget = (row: CorrectionTaskInfo) => {
	emit('openTarget', row);
};
// 导出处理
const handleExport = (row: CorrectionTaskInfo) => {
	exportTaskName.value = `${row.name}`;
	exportTaskId.value = row.id;
	exportDialogVisible.value = true;
};

// 导出成功处理
const handleExportSuccess = () => {
	// Do nothing or add additional logic if needed
};

// 生成月报
const handleGenerateReport = (row: CorrectionTaskInfo) => {
	emit('generateReport', row);
};

// 查看月报
const handleViewReport = (row: CorrectionTaskInfo) => {
	router.push({
		path: '/analysisHelper/correctionDetail',
		query: { id: row.report_id },
	});
};
</script>

<style scoped>
::v-deep(.el-table__header th) {
	color: #606266; /* 设置为你想要的颜色 */
}
.task-list {
	background-color: #fff;
	border-radius: 4px;
	padding: 20px;
}

.pagination-container {
	margin-top: 20px;
}
</style> 
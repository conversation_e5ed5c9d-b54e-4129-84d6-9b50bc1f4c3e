import { request } from '/@/utils/service';

// 定义正确词词库数据类型接口
export interface CorrectDictWordType {
	id: number;
	type: number;
	status: number;
	word: string;
	account_id: string;
	hint?: string;
	recommend?: string;
	word_explain?: string;
	word_example?: string;
	create_time?: string;
	update_time?: string;
}

// 定义查询参数接口
export interface QueryParams {
	page?: number;
	limit?: number;
	word?: string;
	account_id?: string;
	type?: number;
	status?: number;
}

// 定义API前缀
export const apiPrefix = '/api/regulate_jiaodui/correct-dict-word/';

// 获取正确词列表数据
export async function getList(query: QueryParams) {
	return await request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 创建正确词数据
export async function createData(obj: Partial<CorrectDictWordType>) {
	return await request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新正确词数据
export async function updateData(obj: Partial<CorrectDictWordType>) {
	return await request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 删除正确词数据
export async function deleteData(id: number) {
	return await request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}
// 正确词下拉列表数据获取
export async function getCorrectWordSelectList() {
	return await request({
		url: apiPrefix + 'select_list/',
		method: 'get',
	});
}

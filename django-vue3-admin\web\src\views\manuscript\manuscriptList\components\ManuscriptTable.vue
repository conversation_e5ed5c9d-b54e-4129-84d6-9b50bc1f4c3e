<template>
	<div class="manuscript-table">
		<el-table v-loading="loading" :data="tableData" border style="width: 100%; height: 100%">
			<el-table-column prop="id" label="ID" width="50" />
			<el-table-column prop="title" label="稿件标题" min-width="200" show-overflow-tooltip />
			<el-table-column prop="creator_name" label="编稿人" width="120" />
			<el-table-column prop="contribution_dept_name" label="投稿部门" width="120" />
			<el-table-column prop="status" label="投稿状态" width="100">
				<template #default="{ row }">
					<dict-tag :options="getEnumDatas(ENUM_TYPE.MANUSCRIPT_STATUS)" :value="row.status" :color-type="['info', 'success', 'primary']" />
				</template>
			</el-table-column>
			<el-table-column prop="audit_status" label="审核状态" width="100">
				<template #default="{ row }">
					<dict-tag
						:options="getEnumDatas(ENUM_TYPE.MANUSCRIPT_AUDIT_STATUS)"
						:value="row.audit_status"
						:color-type="['info', 'primary', 'success']"
					/>
				</template>
			</el-table-column>
			<el-table-column prop="audit_result" label="审核结果" width="110">
				<template #default="{ row }">
					<dict-tag
						:options="getEnumDatas(ENUM_TYPE.MANUSCRIPT_AUDIT_RESULT)"
						:value="row.audit_result"
						:color-type="['info', 'success', 'danger']"
					/>
				</template>
			</el-table-column>
			<el-table-column prop="audit_time" label="审核时间" width="160" />
			<el-table-column prop="create_datetime" label="稿件创建时间" width="160" />
			<el-table-column label="操作" width="320" fixed="right">
				<template #default="{ row }">
					<el-button v-auth="canEdit(row)" type="primary" link @click="handleEdit(row)" :disabled="row.audit_status === 'auditing'"> 编辑 </el-button>
					<el-button v-auth="canDelete(row)" type="danger" link @click="handleDelete(row)" :disabled="row.audit_status === 'auditing'"> 删除 </el-button>
					<el-button v-auth="canAudit(row)" type="success" link @click="handleAudit(row)" :disabled="row.audit_status === 'auditing'"> 提交审核 </el-button>
					<el-button v-auth="canContribute(row)" type="warning" link @click="handleContribute(row)" :disabled="row.audit_status === 'auditing'"> 投稿 </el-button>
				</template>
			</el-table-column>
		</el-table>

		<div class="pagination-container">
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:total="total"
				:page-sizes="[10, 20, 30, 50]"
				layout="total, sizes, prev, pager, next, jumper"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';

interface Props {
	loading?: boolean;
	tableData: any[];
	total: number;
	pageType: 'draft' | 'submitted' | 'my'; // 新增页面类型，用于区分草稿和已投稿和我的稿件
}

const props = withDefaults(defineProps<Props>(), {
	loading: false,
	tableData: () => [],
	total: 0,
	pageType: 'draft',
});

const emit = defineEmits(['update:page', 'edit', 'delete', 'audit', 'contribute']);

const currentPage = ref(1);
const pageSize = ref(20);

const hasPermission = auth;

const tableHeight = ref('80vh');

// 辅助函数：判断编辑按钮是否显示
const canEdit = (row: any) => {
	const permission = props.pageType === 'draft' ? 'draftList:Update' : props.pageType === 'my' ? 'myManuscriptList:Update' : 'manuscriptList:Update';
	return permission;
};

// 辅助函数：判断删除按钮是否显示
const canDelete = (row: any) => {
	const permission = props.pageType === 'draft' ? 'draftList:Delete' : props.pageType === 'my' ? 'myManuscriptList:Delete' : 'manuscriptList:Delete';
	return permission;
};

// 辅助函数：判断提交审核按钮是否显示
const canAudit = (row: any) => {
	const permission = props.pageType === 'draft' ? 'draftList:audit' : props.pageType === 'my' ? 'myManuscriptList:audit' : 'audit:submit';
	return permission;
};

// 辅助函数：判断投稿按钮是否显示 myManuscriptList:contribute
const canContribute = (row: any) => {
	const permission = props.pageType === 'draft' ? 'draftList:contribute' : props.pageType === 'my' ? 'myManuscriptList:contribute' : 'content:submit';
	return permission;
};

// 监听窗口大小变化
const handleResize = () => {
	tableHeight.value = `${window.innerHeight * 0.8}px`;
};

// 分页方法
const handleSizeChange = (val: number) => {
	pageSize.value = val;
	emit('update:page', { page: currentPage.value, limit: pageSize.value });
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	emit('update:page', { page: currentPage.value, limit: pageSize.value });
};

// 操作方法
const handleEdit = (row: any) => {
	emit('edit', row);
};

const handleDelete = (row: any) => {
	emit('delete', row);
};

const handleAudit = (row: any) => {
	emit('audit', row);
};

// 投稿方法
const handleContribute = (row: any) => {
	emit('contribute', row);
};
</script>

<style scoped>
.manuscript-table {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.pagination-container {
	margin-top: 16px;
	display: flex;
	justify-content: flex-start;
	flex-shrink: 0;
}

:deep(.el-table) {
	flex: 1;
}
</style> 
<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding" >
			<template #cell_type="scope">
				<dict-tag :options="$getEnumDatas($ENUM.CORRECT_DICT_WORD_TYPE)" :value="scope.row.type" :color-type="['primary', 'success']" />
			</template>
			<template #cell_status="scope">
				<dict-tag :options="$getEnumDatas($ENUM.ENABLE_STATUS)" :value="scope.row.status" :color-type="['success', 'warning']" />
			</template>
		</fs-crud>
	</fs-page>
</template>


<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import type { CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
	name: 'ErrorDictWordList',
	setup() {
		const { crudBinding, crudRef, crudExpose } = useFs<CreateCrudOptionsRet>({ createCrudOptions });
		// 页面打开后获取列表数据
		onMounted(() => {
			crudExpose.doRefresh();
		});
		return {
			crudBinding,
			crudRef,
		};
	},
});
</script> 
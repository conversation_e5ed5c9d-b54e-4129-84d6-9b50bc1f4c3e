---
description: 
globs: 
alwaysApply: false
---
# Fast-Crud 表单字段显示状态控制规则

## 背景说明

在使用 fast-crud 开发表单时，经常需要根据某些字段的值来控制其他字段的显示或隐藏。例如，当用户选择了某个套餐后，某些基础字段需要隐藏。如果直接使用表单值的实时变化来控制显示状态，会导致用户在编辑过程中字段突然消失，严重影响用户体验。

## 问题现象

常见的做法是使用`compute`动态监听表单值变化来控制字段显示状态：

```typescript
editForm: {
	show: compute(({ form }) => {
		return !form.package_id;
	});
}
```

这种方式会带来以下问题：

- 用户在编辑过程中修改表单值时，字段会突然消失
- 可能导致用户正在编辑的数据丢失
- 不符合用户的预期交互行为
- 影响表单的可用性和用户体验

## 推荐解决方案

使用`infoRequest`在表单打开时就确定字段的显示状态：

1. **定义状态变量**：

```typescript
// 使用ref定义状态，用于控制字段显示/隐藏
const editingHasPackage = ref(false);
```

2. **配置 infoRequest 请求**：

```typescript
crudOptions: {
	request: {
		infoRequest: async ({ mode, row }) => {
			if (mode === 'edit') {
				// 编辑模式下，保存状态
				editingHasPackage.value = !!row.package_id;
				// 获取最新数据（可选）
				return await api.getDetail(row.id);
			}
			return row;
		};
	}
}
```

3. **控制字段显示**：

```typescript
editForm: {
	show: compute(() => {
		return !editingHasPackage.value;
	});
}
```

## 方案优势

1. **状态固定**：字段的显示状态在表单打开时就确定，不会随着用户编辑而改变
2. **数据一致性**：可以在编辑时获取最新的数据
3. **用户体验**：避免字段在编辑过程中突然消失
4. **代码维护**：逻辑集中，易于维护和修改

## 适用场景

- 需要根据某个字段值控制其他字段显示/隐藏的情况
- 字段的显示状态应该在表单打开时就确定
- 不希望字段的显示状态随表单值实时变化
- 需要提供稳定的表单编辑体验

## 注意事项

1. 状态管理推荐使用`ref`而不是`reactive`
2. 在`infoRequest`中处理状态，避免在其他地方修改
3. 字段显示逻辑要使用保存的状态，而不是实时表单值
4. 如果新增模式下也需要控制字段显示，要记得处理`mode === 'add'`的情况
5. 可以根据需要在`infoRequest`中获取最新数据

## 错误示例

❌ **不推荐的做法**：

```typescript
editForm: {
	show: compute(({ form }) => {
		// 直接使用表单值控制显示状态，会导致实时变化
		return !form.someField;
	});
}
```

✅ **正确的做法**：

```typescript
// 1. 创建状态
const someState = ref(false);

// 2. 在infoRequest中设置状态
infoRequest: async ({ mode, row }) => {
	if (mode === 'edit') {
		someState.value = !!row.someField;
	}
	return row;
};

// 3. 使用状态控制显示
editForm: {
	show: compute(() => !someState.value);
}
```

## 规则维护

本规则文档由开发团队维护，如有问题或建议，请及时反馈。规则会根据实际使用情况不断更新和完善。

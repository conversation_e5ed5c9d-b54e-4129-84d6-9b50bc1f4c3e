<template>
	<fs-page class="PageWechatOfficialAccountSnapshot">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<!-- 使用fs-crud的顶部插槽 -->
			<template #actionbar-left>
				<common-tabs v-model="currentTab" :items="platformItems" @change="handleTabChange" />
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { useRoute, useRouter } from 'vue-router';
import { WECHAT_SNAPSHOT_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions, TabItem } from '/@/utils/platformTabsHelper';

// 动态导入CommonTabs组件
const CommonTabs = defineAsyncComponent(() => import('/@/components/CommonTabs/index.vue'));

// 获取路由实例
const route = useRoute();
const router = useRouter();

// 当前选中的Tab，优先使用platform_type参数，其次使用tab_type参数，如果都没有则默认为'wechat'
const currentTab = ref((route.query.platform_type as string) || (route.query.tab_type as string) || 'wechat');
// 定义当前平台类型
const currentPlatformType = ref(currentTab.value);
// 平台类型选项数据
const platformItems = ref<TabItem[]>([]);

// 获取平台类型数据
const loadPlatformTypes = () => {
	platformItems.value = generatePlatformTabsWithPermissions(WECHAT_SNAPSHOT_PERMISSIONS);
};

// crud组件的ref
const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions, context: { currentPlatformType } });

// Tab变更处理函数
const handleTabChange = (value: string) => {
	console.log(value, '切换平台类型');
	// 更新当前平台类型
	currentPlatformType.value = value;

	// 先获取表单数据，备份可能需要的值
	const originForm = crudExpose.getSearchFormData();

	// 清空所有表单数据，只保留平台类型和必要参数
	crudExpose.setSearchFormData({
		form: {
			platform_type: value,
			// 可以在这里添加其他需要保留的参数
		},
	});

	// 更新路由参数，保留现有参数
	const query = { ...route.query, tab_type: value };
	router.replace({ query });

	// 手动触发搜索，同时保证跳回第一页
	// crudExpose.doRefresh();
};

// 在组件挂载后加载数据
onMounted(() => {
	loadPlatformTypes();
	crudExpose.doRefresh();
});
</script>

<style lang="scss" scoped>
.PageWechatOfficialAccountSnapshot {
	.el-tag {
		margin: 0;
		padding: 0;
	}
}
</style> 
<template>
    <fs-page class="PageWechatPCAPICount">
        <fs-crud ref="crudRef" v-bind="crudBinding">
            <!-- 自定义插槽如果需要 -->
        </fs-crud>
    </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue'
import { useFs } from '@fast-crud/fast-crud'
import createCrudOptions from './crud'

export default defineComponent({
    name: "WechatPCAPICount",
    setup() {
        // crud组件的ref
        const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions })

        // 在组件挂载后加载数据
        onMounted(() => {
            crudExpose.doRefresh();
        });

        return {
            crudBinding,
            crudRef,
            crudExpose
        }
    }
})
</script>

<style scoped>
:deep(.fs-page) {
    padding: 0;
}
</style>
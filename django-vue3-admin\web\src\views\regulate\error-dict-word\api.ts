import { request } from '/@/utils/service';

// 定义错误词词库数据类型接口
export interface ErrorDictWordType {
	id: number;
	word: string;
	account_id: string;
	correct_word?: string;
	hint?: string;
	recommend?: string;
	word_explain?: string;
	word_example?: string;
	create_time?: string;
	update_time?: string;
}

// 定义查询参数接口
export interface QueryParams {
	page?: number;
	limit?: number;
	word?: string;
	account_id?: string;
}

// 定义API前缀
export const apiPrefix = '/api/regulate_jiaodui/error-dict-word/';

// 获取错误词列表数据
export async function getList(query: QueryParams) {
	return await request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 创建错误词数据
export async function createData(obj: Partial<ErrorDictWordType>) {
	return await request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新错误词数据
export async function updateData(obj: Partial<ErrorDictWordType>) {
	return await request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 删除错误词数据
export async function deleteData(id: number) {
	return await request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}
// 错误词下拉列表数据获取
export async function getErrorWordSelectList() {
	return await request({
		url: apiPrefix + 'select_list/',
		method: 'get',
	});
}
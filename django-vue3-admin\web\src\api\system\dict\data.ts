import { request } from '/@/utils/service'

export interface DictDataQuery {
  dictType: string
  dictLabel?: string
  status?: string
}

export interface DictData {
  dictCode: number
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass?: string
  listClass?: string
  isDefault?: string
  status: string
  colorType?: string
  remark?: string
}

// 查询字典数据列表
export function listData(query: DictDataQuery) {
  return request({
    url: '/api/enums/',
    method: 'get',
    params: query
  })
}

// 查询字典数据详细
export function getData(dictCode: number) {
  return request({
    url: '/system/dict/data/' + dictCode,
    method: 'get'
  })
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType: string) {
  return request({
    url: '/system/dict/data/type/' + dictType,
    method: 'get'
  })
}

// 新增字典数据
export function addData(data: DictData) {
  return request({
    url: '/system/dict/data',
    method: 'post',
    data: data
  })
}

// 修改字典数据
export function updateData(data: DictData) {
  return request({
    url: '/system/dict/data',
    method: 'put',
    data: data
  })
}

// 删除字典数据
export function delData(dictCode: number) {
  return request({
    url: '/system/dict/data/' + dictCode,
    method: 'delete'
  })
} 
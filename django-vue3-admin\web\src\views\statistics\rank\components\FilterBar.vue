<template>
	<div class="filter-container">
		<!-- 原有的筛选栏 -->
		<div class="filter-row">
			<div class="filter-item">
				<el-select v-model="currentPeriodValue" placeholder="选择周期" class="filter-select" @change="handlePeriodChange">
					<el-option label="月榜" value="monthly" />
					<el-option label="周榜" value="weekly" />
				</el-select>
			</div>
			<div class="filter-item">
				<el-select v-model="currentTime" placeholder="选择时间" class="filter-select" @change="handleTimeChange">
					<el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="filter-item">
				<el-select filterable v-model="currentRank" placeholder="默认榜单" class="filter-select" @change="handleRankChange">
					<el-option v-for="item in rankOptions" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="filter-item" v-if="!showEditButtons">
				<span v-auth="'tenant:rank'">
					<el-select filterable clearable v-model="currentTenantId" placeholder="选择租户" class="filter-select" @change="handleTenantIdChange">
						<el-option v-for="item in tenantList" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</span>
			</div>
			<div class="filter-item">
				<el-button type="primary" v-auth="'RankingCustom:Create'" plain @click="openCustomRank">新增自定义榜单</el-button>
				<template v-if="showEditButtons">
					<el-button type="warning" v-auth="'RankingCustom:Update'" plain @click="editCustomRank">编辑</el-button>
					<el-button type="danger" v-auth="'RankingCustom:Delete'" plain @click="deleteCustomRank">删除</el-button>
				</template>
			</div>
			<div class="filter-item right-aligned" v-auth="'Rank:Export'">
				<el-button type="primary" plain @click="exportData" :loading="exportLoading">导出Excel</el-button>
				<el-button type="success" plain @click="exportImage" :loading="imageExportLoading">导出图片</el-button>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { ElMessageBox } from 'element-plus';

const props = defineProps({
	timeOptions: {
		type: Array,
		required: true,
	},
	rankOptions: {
		type: Array,
		required: true,
	},
	tenantList: {
		type: Array,
		default: () => [],
	},
	modelValue: {
		type: Object,
		required: true,
	},
	// 新增：当前周期
	currentPeriod: {
		type: String,
		default: 'monthly',
	},
});

const emit = defineEmits([
	'update:modelValue',
	'openCustomRank',
	'export',
	'editCustomRank',
	'deleteCustomRank',
	'periodChange',
	'exportImage',
	'exportColumns',
]);

const currentTime = ref(props.modelValue.time);
const currentRank = ref(props.modelValue.rank);
const currentTenantId = ref(props.modelValue.tenant_id || '');
const currentPeriodValue = ref(props.currentPeriod);
const exportLoading = ref(false);
const imageExportLoading = ref(false);

// 计算属性：是否显示编辑按钮（只有选择了自定义榜单时才显示）
const showEditButtons = computed(() => {
	// 当选择的不是default选项时，显示编辑和删除按钮
	return currentRank.value !== 'default';
});

// 监听父组件传递的值变化
watch(
	() => props.modelValue,
	(newVal) => {
		currentTime.value = newVal.time || '';
		currentRank.value = newVal.rank || 'default';
		currentTenantId.value = newVal.tenant_id || '';
	},
	{ deep: true }
);

// 监听父组件传递的周期变化
watch(
	() => props.currentPeriod,
	(newVal) => {
		currentPeriodValue.value = newVal;
	}
);

// 监听选项变化，更新到父组件
watch([currentTime, currentRank, currentTenantId], () => {
	const selectedTimeOption = props.timeOptions.find((item) => item.value === currentTime.value);
	const selectedRankOption = props.rankOptions.find((item) => item.value === currentRank.value);
	const selectedTenantOption = props.tenantList.find((item) => item.id === currentTenantId.value);

	emit('update:modelValue', {
		...props.modelValue,
		time: currentTime.value,
		timeLabel: selectedTimeOption ? selectedTimeOption.label : '',
		rank: currentRank.value,
		rankLabel: selectedRankOption ? selectedRankOption.label : '',
		tenant_id: currentTenantId.value,
		tenantIdLabel: selectedTenantOption ? selectedTenantOption.name : '',
	});
});

const handleTimeChange = () => {
	emit('update:modelValue', {
		...props.modelValue,
		time: currentTime.value,
	});
};

const handleRankChange = () => {
	emit('update:modelValue', {
		...props.modelValue,
		rank: currentRank.value,
	});
};

const handleTenantIdChange = () => {
	emit('update:modelValue', {
		...props.modelValue,
		tenant_id: currentTenantId.value,
	});
};

// 修改：处理周期切换
const handlePeriodChange = (value) => {
	emit('periodChange', value);
};

const openCustomRank = () => {
	emit('openCustomRank');
};

const editCustomRank = () => {
	emit('editCustomRank', currentRank.value);
};

const deleteCustomRank = () => {
	emit('deleteCustomRank', currentRank.value);
};

const exportData = () => {
	emit('exportColumns');
};

const exportImage = () => {
	imageExportLoading.value = true;
	emit('exportImage', imageExportLoading);
};
</script>

<style scoped>
.filter-container {
	margin-bottom: 20px;
}

.filter-row {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.filter-item {
	margin-right: 16px;
	margin-bottom: 10px;
}

.filter-select {
	width: 200px;
}

.right-aligned {
	margin-left: auto;
	margin-right: 0;
}
</style> 
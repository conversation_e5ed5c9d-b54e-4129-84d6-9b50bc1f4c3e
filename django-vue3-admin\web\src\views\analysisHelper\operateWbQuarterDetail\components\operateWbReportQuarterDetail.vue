<template>
	<div>
		<!-- 添加导出内容包裹层 -->
		<div id="export-content">
			<!-- 1.1.1 标题部分 -->
			<div id="wb-suboperate-1">
				<div class="top">
					<div class="top-title">{{ title.split('季')[0] }}季度下级微博运营报告</div>
					<!-- <div style="font-size: 16px; margin-top: 20px">{{ yearMonth }}</div>
						<div style="font-size: 14px; float: right; color: #4678ab">
							汇远轻媒出品
						</div> -->
				</div>
			</div>

			<div v-if="!hasAccounts">
				<div style="width: 100%; margin: 10px auto; font-size: 26px; text-align: center">无绑定账号</div>
			</div>
			<div v-else>
				<!-- 1.1.2 账号传播力指数报告 -->
				<div id="wb-sub-1">
					<div class="title-wrapper">
						<div class="title1">账号传播力指数榜单</div>
					</div>
					<div class="table-wrapper" v-if="bciData.length <= 5">
						<el-table :data="bciData" border>
							<el-table-column label="排序" align="center" prop="rank" width="70" />
							<el-table-column label="账号名称" align="center" prop="accountName" />
							<el-table-column label="传播力指数" align="center" prop="bci">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.bci) }}</span>
										<!-- <div style="margin-left: 10px; display: flex; align-items: center" v-if="scope.row.diff !== 0">
											<el-icon class="green-icon" v-if="scope.row.bciStatus === '上升'"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.bciStatus === '下降'"><Bottom /></el-icon>
											<span
												style="margin-left: 5px"
												:style="{
													color: scope.row.bciStatus === '上升' ? 'green' : scope.row.bciStatus === '下降' ? 'red' : '',
												}"
												>{{ tranNum(scope.row.diff) }}</span
											>
											<span
												style="margin-left: 5px"
												:style="{
													color: scope.row.bciStatus === '上升' ? 'green' : scope.row.bciStatus === '下降' ? 'red' : '',
												}"
												>（{{ scope.row.rate }}）</span
											>
										</div> -->
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div v-else>
						<div class="table-wrapper">
							<!-- 榜单前6名 -->
							<el-table :data="bciData.slice(0, 8)" border>
								<el-table-column label="排序" align="center" prop="rank" width="70" />
								<el-table-column label="账号名称" align="center" prop="accountName" />
								<el-table-column label="传播力指数" align="center" prop="bci">
									<template #default="scope">
										<div style="display: flex; justify-content: center">
											<span>{{ tranNum(scope.row.bci) }}</span>
											<!-- <div style="margin-left: 10px; display: flex; align-items: center" v-if="scope.row.diff !== 0">
												<el-icon class="green-icon" v-if="scope.row.bciStatus === '上升'"><Top /></el-icon>
												<el-icon class="red-icon" v-else-if="scope.row.bciStatus === '下降'"><Bottom /></el-icon>
												<span
													style="margin-left: 5px"
													:style="{
														color: scope.row.bciStatus === '上升' ? 'green' : scope.row.bciStatus === '下降' ? 'red' : '',
													}"
													>{{ tranNum(scope.row.diff) }}</span
												>
												<span
													style="margin-left: 5px"
													:style="{
														color: scope.row.bciStatus === '上升' ? 'green' : scope.row.bciStatus === '下降' ? 'red' : '',
													}"
													>（{{ scope.row.rate }}）</span
												>
											</div> -->
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
					<div v-if="bciData.length <= 5">
						<!-- 1.1.3 作品发布报告 -->
						<div class="title-wrapper">
							<div class="title1">作品发布</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="formatPublishTableData()"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="发布作品量" align="center" prop="publishCount" min-width="120px" />
								<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
								<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							</el-table>
						</div>
					</div>

					<!-- 视频、非视频类型分布饼图 -->
				</div>
				<div v-if="bciData.length > 5" id="wb-sub1-plus">
					<div class="table-wrapper">
						<!-- 榜单6名以外 -->
						<el-table :data="bciData.slice(8)" border>
							<!-- 排序应该接着数组的长度进行排序，而不是重新进行排序 比如上一个表格的数组是八，那么应该这个排序的第一项应该为九-->
							<el-table-column label="排序" align="center" prop="rank" width="70" />
							<el-table-column label="账号名称" align="center" prop="accountName" />
							<el-table-column label="传播力指数" align="center" prop="bci">
								<template #default="scope">
									<div style="display: flex; justify-content: center">
										<span>{{ tranNum(scope.row.bci) }}</span>
										<!-- <div style="margin-left: 10px; display: flex; align-items: center" v-if="scope.row.diff !== 0">
											<el-icon class="green-icon" v-if="scope.row.bciStatus === '上升'"><Top /></el-icon>
											<el-icon class="red-icon" v-else-if="scope.row.bciStatus === '下降'"><Bottom /></el-icon>
											<span
												style="margin-left: 5px"
												:style="{
													color: scope.row.bciStatus === '上升' ? 'green' : scope.row.bciStatus === '下降' ? 'red' : '',
												}"
												>{{ tranNum(scope.row.diff) }}</span
											>
											<span
												style="margin-left: 5px"
												:style="{
													color: scope.row.bciStatus === '上升' ? 'green' : scope.row.bciStatus === '下降' ? 'red' : '',
												}"
												>（{{ scope.row.rate }}）</span
											>
										</div> -->
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<!-- 1.1.3 作品发布报告 -->
					<div class="title-wrapper">
						<div class="title1">作品发布</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatPublishTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="发布作品量" align="center" prop="publishCount" min-width="120px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
						</el-table>
					</div>
				</div>
				<div id="wb-sub-2">
					<div class="flex">
						<div style="width: 50%">
							<div class="sub-title">下级账号原创类型分布</div>
							<div class="chart-container">
								<pie :data="origPie.data" :total="origPie.total" style="width: 100%; height: 400px" :label-font-size="18" />
							</div>
						</div>
						<div style="width: 50%">
							<div class="sub-title">下级账号内容类型分布</div>
							<div class="chart-container">
								<pie :data="issuePie.data" :total="issuePie.total" style="width: 100%; height: 400px" :label-font-size="18" />
							</div>
						</div>
					</div>

					<!-- 发布时间区间分布图 -->
					<div class="sub-title">下级账号发布时间分布</div>
					<div class="chart-container">
						<line-chart
							:data="formatLineChartData(timeDistribution)"
							style="width: 100%; height: 350px"
							:axis-font-size="16"
							:legend-font-size="16"
							:axis-name-font-size="16"
						/>
					</div>
				</div>
				<div id="wb-sub-3">
					<!-- 1.1.4 作品传播报告 -->
					<div class="title-wrapper">
						<div class="title1">作品传播</div>
					</div>

					<!-- 转发量表格 - 所有情况都显示 -->
					<div class="title-wrapper">
						<div class="title2">转发量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatForwardTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>

					<!-- 1个账号时：所有表格都在wb-sub-3中 -->
					<template v-if="bciData.length <= 5">
						<!-- 点赞量表格 -->
						<div class="title-wrapper">
							<div class="title2">点赞量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="formatLikeTableData()"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
								<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
								<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
								<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
							</el-table>
						</div>
					</template>
					<template v-if="bciData.length <= 2">
						<!-- 评论量表格 -->
						<div class="title-wrapper">
							<div class="title2">评论量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="formatCommentTableData()"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
								<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
								<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
								<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
							</el-table>
						</div>
					</template>
					<template v-if="bciData.length === 1">
						<!-- 播放量表格 -->
						<div class="title-wrapper">
							<div class="title2">播放量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="formatPlayTableData()"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
								<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
								<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
								<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
							</el-table>
						</div>
					</template>
				</div>

				<!-- 2个账号时：播放量单独为1个ID -->
				<div id="wb-sub3-play" v-if="bciData.length === 2">
					<div class="title-wrapper">
						<div class="title2">播放量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatPlayTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>
				</div>

				<!-- 3-5个账号时：评论量+播放量为第二个ID -->
				<div id="wb-sub3-comment-play" v-if="bciData.length >= 3 && bciData.length <= 5">
					<!-- 评论量表格 -->
					<div class="title-wrapper">
						<div class="title2">评论量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatCommentTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>

					<!-- 播放量表格 -->
					<div class="title-wrapper">
						<div class="title2">播放量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatPlayTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>
				</div>

				<!-- 大于5个账号时：每个表格独立ID -->
				<div id="wb-sub3-like" v-if="bciData.length > 5">
					<div class="title-wrapper">
						<div class="title2">点赞量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatLikeTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>
				</div>

				<div id="wb-sub3-comment" v-if="bciData.length > 5">
					<div class="title-wrapper">
						<div class="title2">评论量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatCommentTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>
				</div>

				<div id="wb-sub3-play" v-if="bciData.length > 5">
					<div class="title-wrapper">
						<div class="title2">播放量</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatPlayTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
							<el-table-column label="本季值" align="center" prop="currentValue" min-width="100px" />
							<el-table-column label="较上季" align="center" prop="compareChange" min-width="100px" />
							<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
						</el-table>
					</div>
				</div>

				<!-- 转发量Top 10 - 支持分页 -->
				<div
					v-for="(chunk, chunkIndex) in paginatedForwardTop"
					:key="`forward-${chunkIndex}`"
					:id="`wb-sub-4${chunkIndex > 0 ? '-' + (chunkIndex + 1) : ''}`"
				>
					<div class="title-wrapper" v-if="chunkIndex === 0">
						<div class="title1">作品分析</div>
					</div>
					<div class="title2" v-if="chunkIndex === 0">转发量Top 10</div>
					<div class="table-wrapper">
						<el-table :data="chunk" border>
							<el-table-column label="排序" align="center" width="70">
								<template #default="scope">
									{{ chunkIndex * getPageSize('forward') + scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="微博内容" align="center" prop="title" min-width="500" />
							<el-table-column label="转发量" align="center" prop="data" width="90">
								<template #default="scope">
									{{ tranNum(scope.row.data) }}
								</template>
							</el-table-column>
							<el-table-column label="发布时间" align="center" prop="issueDate" width="140" />
							<el-table-column label="来源账号" align="center" prop="account" width="100" v-if="bciData.length > 1" />
						</el-table>
					</div>
				</div>
				<!-- 评论量Top 10 - 支持分页 -->
				<div
					v-for="(chunk, chunkIndex) in paginatedCommentTop"
					:key="`comment-${chunkIndex}`"
					:id="`wb-sub-5${chunkIndex > 0 ? '-' + (chunkIndex + 1) : ''}`"
				>
					<div class="title2" v-if="chunkIndex === 0">评论量Top 10</div>
					<div class="table-wrapper">
						<el-table :data="chunk" border>
							<el-table-column label="排序" align="center" width="70">
								<template #default="scope">
									{{ chunkIndex * getPageSize('comment') + scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="微博内容" align="center" prop="title" min-width="500" />
							<el-table-column label="评论量" align="center" prop="data" width="90">
								<template #default="scope">
									{{ tranNum(scope.row.data) }}
								</template>
							</el-table-column>
							<el-table-column label="发布时间" align="center" prop="issueDate" width="140" />
							<el-table-column label="来源账号" align="center" prop="account" width="100" v-if="bciData.length > 1" />
						</el-table>
					</div>
				</div>
				<!-- 点赞量Top 10 - 支持分页 -->
				<div
					v-for="(chunk, chunkIndex) in paginatedLikeTop"
					:key="`like-${chunkIndex}`"
					:id="`wb-sub-6${chunkIndex > 0 ? '-' + (chunkIndex + 1) : ''}`"
				>
					<div class="title2" v-if="chunkIndex === 0">点赞量Top 10</div>
					<div class="table-wrapper">
						<el-table :data="chunk" border>
							<el-table-column label="排序" align="center" width="70">
								<template #default="scope">
									{{ chunkIndex * getPageSize('like') + scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="微博内容" align="center" prop="title" min-width="500" />
							<el-table-column label="点赞量" align="center" prop="data" width="90">
								<template #default="scope">
									{{ tranNum(scope.row.data) }}
								</template>
							</el-table-column>
							<el-table-column label="发布时间" align="center" prop="issueDate" width="140" />
							<el-table-column label="来源账号" align="center" prop="account" width="100" v-if="bciData.length > 1" />
						</el-table>
					</div>
				</div>
				<div id="wb-sub-7">
					<!-- 1.1.5 账号粉丝报告 -->
					<div class="title-wrapper">
						<div class="title1">账号粉丝</div>
					</div>

					<!-- 1.1.5.1 总粉丝量 -->
					<div class="title-wrapper">
						<div class="title2">总粉丝量</div>
					</div>
					<div class="content text-bold" style="margin-left: 40px">截至{{ endTime }}，粉丝总量情况如下：</div>
					<div class="table-wrapper">
						<el-table :data="totalFans.slice(0, 8)" border>
							<el-table-column label="排序" align="center" prop="rank" width="70" />
							<el-table-column label="来源账号" align="center" prop="name" />
							<el-table-column label="总粉丝量" align="center" prop="count">
								<template #default="scope">
									{{ tranNum(scope.row.count) }}
								</template>
							</el-table-column>
						</el-table>
					</div>
					<!-- 总关注量占比图 -->
					<div v-if="fansPieData.length > 1 && bciData.length <= 6">
						<div class="title3">总关注量占比关系</div>
						<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 400px" />
					</div>
				</div>
				<div id="wb-sub-8" v-if="bciData.length > 8">
					<div class="table-wrapper">
						<el-table :data="totalFans.slice(8)" border>
							<el-table-column label="排序" align="center" prop="rank" width="70" />
							<el-table-column label="来源账号" align="center" prop="name" />
							<el-table-column label="总粉丝量" align="center" prop="count">
								<template #default="scope">
									{{ tranNum(scope.row.count) }}
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div v-if="fansPieData.length > 6">
						<div class="title3">总关注量占比关系</div>
						<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 400px" />
					</div>
					<!-- 1.1.5.2 新增粉丝 -->
				</div>
			</div>

			<div v-if="noUpdateWbAccounts.length > 0">
				<div>
					<div class="top">
						<div class="top-title">超一个月未更新微博号</div>
					</div>
				</div>
				<div id="wb-sub-9">
					<!-- 超一个月未更新微博号 -->
					<div class="title-wrapper">
						<div class="title1" style="width: 40%">超一个月及更久未更新微博号列表</div>
					</div>

					<!-- 表格 -->
					<div class="table-wrapper">
						<el-table :data="noUpdateWbAccounts" border>
							<el-table-column label="排序" align="center" type="index" width="70" />
							<el-table-column label="账号名称" align="center" prop="accountName" />
							<el-table-column label="不更新天数" align="center" prop="noUpdateDays" />
							<el-table-column label="最后发文时间" align="center" prop="lastIssueTime" />
						</el-table>
					</div>
					<div style="text-align: right; font-size: 16px; color: #000; margin: 20px 30px; padding-bottom: 20px">
						<p>注：不更新天数计算，截止日期为{{ endTime }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineExpose, watch, computed } from 'vue';
import pie from '/@/components/Echarts/pie.vue';
import lineChart from '/@/components/Echarts/line.vue';
import { tranNumber } from '/@/utils/tranNum';

const props = defineProps({
	id: {
		type: [String, Number],
		default: '',
	},
	subReportData: {
		type: Object,
		default: () => ({}),
	},
	sameAccount: {
		type: Array,
		default: () => [],
	},
	noUpdateAccount: {
		type: Array,
		default: () => [],
	},
	title: {
		type: String,
		default: '',
	},
	date: {
		type: Object,
		default: () => ({}),
	},
	createTime: {
		type: String,
		default: '',
	},
});

// 数据定义
const pageLoading = ref(false);
const hasAccounts = ref(true);
const month = ref(null);
const yearMonth = ref(null);
const endTime = ref(null);

// 传播力指数报告
const bciData = ref([]);

// 作品发布报告
const worksIssue = ref([]);

// 作品传播报告
const worksLike = ref([]); // 点赞数据
const worksComment = ref([]); // 评论数据
const worksForward = ref([]); // 转发数据
const worksPlay = ref([]); // 播放数据
const worksTop = ref({
	like: [], // 点赞top10
	comment: [], // 评论top10
	forward: [], // 转发top10
	play: [], // 播放top10
});

const fansPieData = ref([]);
const fansPieTotal = ref({});
const totalFans = ref([]);
const newFans = ref([]); // 新增粉丝
const netFans = ref([]); // 净增粉丝
const issuePie = ref({}); // 发文类型统计饼图
const origPie = ref({}); // 原创类型统计饼图
const timeDistribution = ref({}); // 账号发文时间统计折线图数据
const noUpdateWbAccounts = ref([]); // 未更新微博账号数据

onMounted(() => {
	// 不再从API获取数据，只处理props中的数据
	if (props.subReportData) {
		handleReportData(props.subReportData);
	}
});

// 监听props的变化
watch(
	() => props.subReportData,
	(newVal) => {
		if (newVal) {
			handleReportData(newVal);
		}
	},
	{ deep: true }
);

// 数字转换工具方法
const tranNum = (num) => {
	return tranNumber(num);
};

// 格式化转发量表格数据
const formatForwardTableData = () => {
	return worksForward.value.map((account) => ({
		accountName: account.accountName,
		currentValue: tranNum(account.data),
		compareChange: `${account.trend === 0 ? '持平' : account.trend === -1 ? '-' : ''}${account.trend === 0 ? '' : tranNum(Math.abs(account.diff))}`,
		compareRate: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${account.rate}`,
		avgValue: account.avg,
	}));
};

// 格式化点赞量表格数据
const formatLikeTableData = () => {
	return worksLike.value.map((account) => ({
		accountName: account.accountName,
		currentValue: tranNum(account.data),
		compareChange: `${account.trend === 0 ? '持平' : account.trend === -1 ? '-' : ''}${account.trend === 0 ? '' : tranNum(Math.abs(account.diff))}`,
		compareRate: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${account.rate}`,
		avgValue: account.avg,
	}));
};

// 格式化评论量表格数据
const formatCommentTableData = () => {
	return worksComment.value.map((account) => ({
		accountName: account.accountName,
		currentValue: tranNum(account.data),
		compareChange: `${account.trend === 0 ? '持平' : account.trend === -1 ? '-' : ''}${account.trend === 0 ? '' : tranNum(Math.abs(account.diff))}`,
		compareRate: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${account.rate}`,
		avgValue: account.avg,
	}));
};

// 格式化播放量表格数据
const formatPlayTableData = () => {
	return worksPlay.value
		.filter((account) => account.data > 0) // 只显示播放量大于0的账号
		.map((account) => ({
			accountName: account.accountName,
			currentValue: tranNum(account.data),
			compareChange: `${account.trend === 0 ? '持平' : account.trend === -1 ? '-' : ''}${account.trend === 0 ? '' : tranNum(Math.abs(account.diff))}`,
			compareRate: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${account.rate}`,
			avgValue: account.avg,
		}));
};

// 格式化作品发布表格数据
const formatPublishTableData = () => {
	return worksIssue.value.map((account) => ({
		accountName: account.accountName,
		publishCount: tranNum(account.issue),
		compareChange: `${account.trend === 0 ? '持平' : account.trend === -1 ? '-' : ''}${account.trend === 0 ? '' : tranNum(Math.abs(account.diff))}`,
		compareRate: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${account.rate}`,
	}));
};

// 新增：格式化折线图数据的函数
const formatLineChartData = (data) => {
	// 如果已经是新格式，直接返回
	if (data && data.row && data.dates) {
		return data;
	}

	// 如果是旧格式，进行转换
	if (data && data.xAxis && data.series) {
		return {
			title: '发布时间区间分布情况',
			row: data.series.map((item) => ({
				name: item.name,
				counts: item.data,
				type: 'line',
			})),
			dates: data.xAxis,
		};
	}

	// 默认返回空数据结构
	return {
		title: '发布时间区间分布情况',
		row: [],
		dates: [],
	};
};

// 检查标题长度是否超过150字
const checkTitleLength = (title) => {
	return title && title.length > 150;
};

// 计算每页显示的条数
const getPageSize = (type) => {
	const data = worksTop.value[type] || [];
	const longTitleCount = data.filter((item) => checkTitleLength(item.title)).length;

	// 如果超过2个标题大于150字，每页显示5条，否则显示10条
	return longTitleCount > 2 ? 5 : 10;
};

// 分页处理函数
const paginateData = (data, type) => {
	if (!data || data.length === 0) return [[]];

	const pageSize = getPageSize(type);
	const chunks = [];

	for (let i = 0; i < data.length; i += pageSize) {
		chunks.push(data.slice(i, i + pageSize));
	}

	return chunks.length > 0 ? chunks : [[]];
};

// 转发量Top10分页数据
const paginatedForwardTop = computed(() => {
	return paginateData(worksTop.value.forward, 'forward');
});

// 评论量Top10分页数据
const paginatedCommentTop = computed(() => {
	return paginateData(worksTop.value.comment, 'comment');
});

// 点赞量Top10分页数据
const paginatedLikeTop = computed(() => {
	return paginateData(worksTop.value.like, 'like');
});

// 计算需要导出的模块ID
const moduleIds = computed(() => {
	const ids = [];

	// 下级账号模块
	ids.push('wb-sub-1', 'wb-sub-2', 'wb-sub-3');

	// 根据账号数量动态添加作品传播相关的ID
	if (bciData.value.length === 2) {
		// 2个账号：转发量+点赞量+评论量 | 播放量
		ids.push('wb-sub3-play');
	} else if (bciData.value.length >= 3 && bciData.value.length <= 5) {
		// 3-5个账号：转发量+点赞量 | 评论量+播放量
		ids.push('wb-sub3-comment-play');
	} else if (bciData.value.length > 5) {
		// 大于5个账号：每个表格独立ID
		ids.push('wb-sub3-like', 'wb-sub3-comment', 'wb-sub3-play');
	}
	// 1个账号时：所有表格都在wb-sub-3中，不需要额外ID

	// 添加分页的Top10模块ID
	const forwardPages = paginatedForwardTop.value.length;
	const commentPages = paginatedCommentTop.value.length;
	const likePages = paginatedLikeTop.value.length;

	console.log('季报下级分页信息 - 转发:', forwardPages, '评论:', commentPages, '点赞:', likePages);

	// 转发量Top10的所有页面
	for (let i = 0; i < forwardPages; i++) {
		ids.push(i === 0 ? 'wb-sub-4' : `wb-sub-4-${i + 1}`);
	}

	// 评论量Top10的所有页面
	for (let i = 0; i < commentPages; i++) {
		ids.push(i === 0 ? 'wb-sub-5' : `wb-sub-5-${i + 1}`);
	}

	// 点赞量Top10的所有页面
	for (let i = 0; i < likePages; i++) {
		ids.push(i === 0 ? 'wb-sub-6' : `wb-sub-6-${i + 1}`);
	}

	// 粉丝相关
	ids.push('wb-sub-7');
	if (fansPieData.value && fansPieData.value.length > 1) {
		ids.push('wb-sub-8');
	}

	// 传播力指数相关
	if (bciData.value.length > 5) {
		const insertIndex = ids.findIndex((id) => id === 'wb-sub-2');
		if (insertIndex !== -1) {
			ids.splice(insertIndex, 0, 'wb-sub1-plus');
		}
	}

	if (bciData.value.length > 8) {
		ids.push('wb-sub8-plus');
	}

	if (noUpdateWbAccounts.value && noUpdateWbAccounts.value.length > 0) {
		ids.push('wb-sub-9');
	}

	console.log('季报下级所有模块ID:', ids);
	return ids;
});

// 导出组件内部方法给父组件使用
defineExpose({
	moduleIds,
});

// 处理报告数据
const handleReportData = (data) => {
	try {
		pageLoading.value = true;
		console.log('开始处理报告数据');

		// 基础信息处理
		hasAccounts.value = data.bci && data.bci.length > 0; // 是否有绑定账号

		// 标题可能来自props或数据中的title
		if (data.title && !props.title) {
			title.value = data.title; // 标题
		}

		endTime.value = props.date?.end_date || data.endTime; // 结束时间
		month.value = new Date(endTime.value).getMonth() + 1 + '月';
		const year = new Date(endTime.value).getFullYear();
		yearMonth.value = `${year}年${month.value}`;

		// 各模块数据处理
		// 传播力指数报告
		bciData.value = (data.bci || []).map((item) => ({
			rank: item.rank,
			accountName: item.account_name,
			bci: item.bci,
			diff: item.diff,
			bciStatus: item.compare_trend === 1 ? '上升' : item.compare_trend === -1 ? '下降' : '持平',
			rate: `${item.compare_rate}%`,
		}));

		// 作品发布报告
		worksIssue.value = (data.publish || []).map((item) => {
			return {
				accountName: item.account_name,
				issue: item.total_count,
				diff: item.total_count_compare?.diff || 0,
				trend: item.total_count_compare?.trend || 0,
				status: item.total_count_compare?.trend === 1 ? '增加' : item.total_count_compare?.trend === -1 ? '减少' : '持平',
				rate: `${item.total_count_compare?.rate || 0}%`,
				totalIssue: item.total_count || 0,
			};
		});

		// 点赞量
		worksLike.value = (data.like_count_stats || []).map((item) => {
			return {
				accountName: item.account_name,
				data: item.total,
				diff: item.compare?.diff || 0,
				trend: item.compare?.trend || 0,
				status: item.compare?.trend === 1 ? '增加' : item.compare?.trend === -1 ? '减少' : '持平',
				rate: `${item.compare?.rate || 0}%`,
				avg: item.total_avg || 0,
			};
		});

		// 评论量
		worksComment.value = (data.comment_count_stats || []).map((item) => {
			return {
				accountName: item.account_name,
				data: item.total,
				diff: item.compare?.diff || 0,
				trend: item.compare?.trend || 0,
				status: item.compare?.trend === 1 ? '增加' : item.compare?.trend === -1 ? '减少' : '持平',
				rate: `${item.compare?.rate || 0}%`,
				avg: item.total_avg || 0,
			};
		});

		// 转发量
		worksForward.value = (data.share_count_stats || []).map((item) => {
			return {
				accountName: item.account_name,
				data: item.total,
				diff: item.compare?.diff || 0,
				trend: item.compare?.trend || 0,
				status: item.compare?.trend === 1 ? '增加' : item.compare?.trend === -1 ? '减少' : '持平',
				rate: `${item.compare?.rate || 0}%`,
				avg: item.total_avg || 0,
			};
		});

		// 播放量
		worksPlay.value = (data.play_count_stats || []).map((item) => {
			return {
				accountName: item.account_name,
				data: item.total,
				diff: item.compare?.diff || 0,
				trend: item.compare?.trend || 0,
				status: item.compare?.trend === 1 ? '增加' : item.compare?.trend === -1 ? '减少' : '持平',
				rate: `${item.compare?.rate || 0}%`,
				avg: item.total_avg || 0,
			};
		});

		// 处理作品Top数据
		if (data.work_analysis) {
			worksTop.value = {
				like: (data.work_analysis.likeTop || []).map((item) => ({
					title: item.title,
					data: item.total,
					issueDate: item.issue_time?.split(' ')[0] || '',
					isVideo: !!item.video,
					isOrig: item.type === 1,
					account: item.account_name,
				})),
				comment: (data.work_analysis.commentTop || []).map((item) => ({
					title: item.title,
					data: item.total,
					issueDate: item.issue_time?.split(' ')[0] || '',
					isVideo: !!item.video,
					isOrig: item.type === 1,
					account: item.account_name,
				})),
				forward: (data.work_analysis.shareTop || []).map((item) => ({
					title: item.title,
					data: item.total,
					issueDate: item.issue_time?.split(' ')[0] || '',
					isVideo: !!item.video,
					isOrig: item.type === 1,
					account: item.account_name,
				})),
				play: [], // 暂无此数据
			};
		}

		// 处理内容类型统计饼图
		issuePie.value = {
			data: [
				{ name: '视频', value: data.video_count || 0 },
				{ name: '非视频', value: data.txt_count || 0 },
			],
			total: { name: '总数', value: data.total_count || 0 },
		};

		// 处理原创类型统计饼图
		origPie.value = {
			data: [
				{ name: '原创', value: data.orig_count || 0 },
				{ name: '非原创', value: data.no_orig_count || 0 },
			],
			total: { name: '总数', value: data.total_count || 0 },
		};

		// 账号发文时间统计
		timeDistribution.value = {
			title: '发布时间区间分布情况',
			row: [
				{
					name: '发布数量',
					counts: (data.hour_distribution || []).map((item) => item.count),
					type: 'line',
				},
			],
			dates: (data.hour_distribution || []).map((item) => item.hour),
		};

		// 总粉丝量
		totalFans.value = (data.account_fans_data?.total_fans || []).map((item) => ({
			rank: item.rank,
			name: item.account_name,
			count: item.total,
		}));

		// 处理总粉丝饼图
		fansPieData.value = (data.account_fans_data?.total_fans || []).map((item) => ({
			name: item.account_name,
			value: item.total,
		}));

		fansPieTotal.value = {
			name: '总数',
			value: (data.account_fans_data?.total_fans || []).reduce((sum, item) => sum + (parseInt(item.total) || 0), 0),
		};

		// 新增粉丝
		newFans.value = [];

		// 净增粉丝 - 使用新增粉丝数据作为替代
		netFans.value = (data.account_fans_data?.new_fans || []).map((item) => ({
			accountName: item.account_name,
			data: item.total,
			diff: item.compare?.diff || 0,
			status: item.compare?.trend === 1 ? '增加' : item.compare?.trend === -1 ? '减少' : '持平',
			rate: item.compare?.rate || 0,
		}));

		// 处理未更新账号数据
		// 假设 props.noUpdateAccount 是一个包含 { accountName, noUpdateDays, lastIssueTime } 对象的数组
		noUpdateWbAccounts.value = (props.noUpdateAccount || []).map((item) => ({
			accountName: item.accountName || item.account_name, // 适应可能的字段名差异
			noUpdateDays: item.noUpdateDays || item.no_update_days, // 适应可能的字段名差异
			lastIssueTime: item.lastIssueTime || item.last_update_date, // 适应可能的字段名差异
		}));

		console.log('数据处理完成');
	} catch (error) {
		console.error('处理报告数据失败:', error);
	} finally {
		pageLoading.value = false;
	}
};
</script>

<style scoped>
.top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	overflow: hidden;
	height: 200px;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}
.title-wrapper {
	width: 100%;
	/* padding: 20px; */
}
.flex {
	display: flex;
	justify-content: space-between;
}
.title1 {
	font-size: 30px;
	width: 25%;
	margin: 0 auto 15px;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 15px;
	line-height: 1.5;
}

.title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	text-align: center;
	padding-bottom: 5px;
	line-height: 1.5;
}

.content-wrapper {
	position: relative;
	margin: 0 30px 20px;
	padding: 7px 20px 20px 20px;
	border: #9d9c9a 1px solid;
	overflow: visible;
}

.content {
	font-size: 18px;
	line-height: 2;
	/* padding: 10px 0; */
	white-space: inherit !important;
}

.sub-title {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}

.chart-container {
	margin: 10px 0 0 40px;
}

.text-bold {
	font-weight: bold;
}

.text-blue {
	color: #00abf9;
}

:deep(.el-loading-spinner) {
	top: 300px;
}
:deep(.el-table .cell) {
	font-size: 18px;
	color: #000;
}
:deep(.el-table--border) {
	border-top: 1px solid #fff;
}
:deep(.el-table th) {
	font-weight: bold;
	color: #000;
	background-color: #f8f8f9;
	padding: 15px 4px !important;
}

:deep(.el-table td) {
	padding: 15px 4px !important;
}
.green-icon {
	padding-top: 12px;
	color: green;
}
.red-icon {
	padding-top: 12px;
	color: red;
}
.title3 {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}
.table-wrapper {
	margin: 10px 30px;
}
/* 添加按钮样式 */
.btn-wrapper {
	position: absolute;
	right: 50px;
	top: 95px;
	z-index: 1000;
}
* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}
</style> 
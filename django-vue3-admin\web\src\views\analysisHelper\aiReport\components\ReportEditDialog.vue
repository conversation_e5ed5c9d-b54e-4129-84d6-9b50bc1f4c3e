<template>
	<el-dialog
		v-model="dialogVisible"
		title="编辑报告内容"
		width="80%"
		:before-close="handleClose"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
	>
		<el-form ref="formRef" :model="formData" label-width="0">
			<el-form-item>
				<MarkdownEditor v-model="formData.content" :height="'500px'" :preview="true" />
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button type="warning" @click="handleRestore" :loading="restoreLoading"> 还原最初报告 </el-button>
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, defineEmits, defineExpose, defineAsyncComponent } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElButton, ElMessageBox, ElMessage } from 'element-plus';
import * as api from '../api';

// 动态导入MarkdownEditor组件
const MarkdownEditor = defineAsyncComponent(() => import('/@/components/markdownEditor/index.vue'));

interface FormData {
	id?: number;
	content: string;
	title?: string;
}

const emit = defineEmits<{
	saved: [];
	closed: [];
}>();

// 响应式数据
const dialogVisible = ref(false);
const formRef = ref();
const formData = ref<FormData>({
	content: '',
});
const saveLoading = ref(false);
const restoreLoading = ref(false);
const originalContent = ref('');

// 打开对话框
const open = async (row: any) => {
	if (!row?.id) {
		ElMessage.error('缺少报告ID');
		return;
	}

	try {
		// 获取报告内容
		const res = await api.getReportContent(row.id);
		formData.value = {
			id: row.id,
			content: res.data.content,
			title: row.title,
		};

		// 获取原始内容用于还原功能
		const originalRes = await api.getOriginalContent(row.id);
		originalContent.value = originalRes.data.content;

		dialogVisible.value = true;
	} catch (error: any) {
		ElMessage.error(error.message || '获取报告内容失败');
	}
};

// 关闭对话框
const handleClose = async () => {
	try {
		await ElMessageBox.confirm('确定要关闭表单吗？未保存的内容将会丢失。', '关闭确认', {
			confirmButtonText: '确定关闭',
			cancelButtonText: '继续编辑',
			type: 'warning',
		});

		dialogVisible.value = false;
		emit('closed');
	} catch {
		// 用户取消关闭，不执行任何操作
	}
};

// 保存报告
const handleSave = async () => {
	if (!formData.value.content.trim()) {
		ElMessage.warning('请输入报告内容');
		return;
	}

	if (!formData.value.id) {
		ElMessage.error('缺少报告ID');
		return;
	}

	saveLoading.value = true;
	try {
		// 编辑报告内容
		await api.editReportContent(formData.value.id.toString(), {
			edited_report: formData.value.content,
		});
		ElMessage.success('报告内容保存成功');

		dialogVisible.value = false;
		emit('saved');
	} catch (error: any) {
		ElMessage.error(error.message || '保存失败');
	} finally {
		saveLoading.value = false;
	}
};

// 还原最初报告
const handleRestore = async () => {
	if (!originalContent.value) {
		ElMessage.warning('无法获取原始报告内容');
		return;
	}

	try {
		await ElMessageBox.confirm('确定要还原为最初报告内容吗？当前编辑的内容将会丢失。', '还原确认', {
			confirmButtonText: '确定还原',
			cancelButtonText: '取消',
			type: 'warning',
		});

		formData.value.content = originalContent.value;
		ElMessage.success('已还原为最初报告内容');
	} catch {
		// 用户取消还原
	}
};

// 监听对话框关闭
watch(dialogVisible, (newVal) => {
	if (!newVal) {
		// 重置表单数据
		formData.value = {
			content: '',
		};
		originalContent.value = '';
	}
});

defineExpose({
	open,
});
</script>

<style lang="scss" scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

:deep(.el-dialog__body) {
	padding: 20px;
}

:deep(.el-form-item) {
	margin-bottom: 0;
}

:deep(.el-form-item__content) {
	width: 100%;
}

:deep(.markdown-editor-container) {
	width: 100%;
}
</style> 
<template>
	<div class="rank-container">
		<!-- 平台切换tab -->
		<common-tabs v-model="currentPlatform" :items="platformTabs" @change="handlePlatformChange" class="platform-tabs"></common-tabs>

		<!-- 筛选栏组件 -->
		<filter-bar
			:time-options="timeOptions"
			:rank-options="rankOptions"
			:tenant-list="tenantList"
			:current-period="currentPeriodString"
			v-model="filterParams"
			@open-custom-rank="openCustomRankDialog"
			@edit-custom-rank="editCustomRank"
			@delete-custom-rank="deleteCustomRank"
			@period-change="handlePeriodChange"
			@export="exportRankData"
			@export-image="exportRankImage"
			@export-columns="openExportColumnsDialog"
		></filter-bar>

		<!-- 排行榜表格组件 -->
		<rank-table
			:rank-data="rankData"
			:loading="loading"
			:has-more-data="hasMoreData"
			:table-height="rankTableHeight"
			:platform="currentPlatform"
			@load-more="loadMoreData"
		></rank-table>

		<!-- 自定义榜单对话框组件 -->
		<custom-rank-dialog
			ref="customRankDialogRef"
			v-model="dialogVisible"
			:account-list="accountList"
			:edit-mode="editMode"
			:edit-data="editData"
			@account-change="handleAccountChange"
			@save="saveCustomRank"
		></custom-rank-dialog>

		<!-- 图片导出对话框 -->
		<image-export-dialog
			ref="imageExportDialogRef"
			v-model="imageExportDialogVisible"
			:default-title="getDefaultExportTitle()"
			:total-count="totalItems"
			@confirm="handleImageExportConfirm"
		></image-export-dialog>

		<!-- 导出字段配置对话框 -->
		<export-columns-dialog
			ref="exportColumnsDialogRef"
			v-model="exportColumnsDialogVisible"
			:platform="currentPlatform"
			@confirm="handleExportColumnsConfirm"
		></export-columns-dialog>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getAllTenants } from '/@/views/tenant/api.ts';
import { getAccountList, getDouyinAccountList, getWeiboAccountList } from '/@/views/wechat_official_account/WechatOfficialAccount/api';
import {
	getDateRanges,
	getRankingList,
	createCustomRank,
	getCustomRankList,
	updateCustomRank,
	deleteCustomRank as deleteRank,
	PlatformType,
	RankingPeriod,
	RankingType,
	getCustomDetail,
	exportData as exportApiData,
} from './api';
import FilterBar from './components/FilterBar.vue';
import RankTable from './components/RankTable.vue';
import CustomRankDialog from './components/CustomRankDialog.vue';
import ImageExportDialog from './components/ImageExportDialog.vue';
import ExportColumnsDialog from './components/ExportColumnsDialog.vue';
import download from '/@/utils/download';
import CommonTabs from '/@/components/CommonTabs/index.vue';
import { auth } from '/@/utils/authFunction';

// 平台选项
const platformTabs = ref([
	{ label: '微信', value: 'wechat' },
	{ label: '抖音', value: 'douyin' },
	{ label: '微博', value: 'weibo' },
]);

// 当前选中的平台
const currentPlatform = ref('wechat');

// 当前榜单周期（默认月榜）
const currentPeriod = ref(RankingPeriod.MONTHLY);

// 当前周期字符串（用于UI显示）
const currentPeriodString = ref('monthly');

// 周期字符串到枚举值的映射
const periodMap = {
	monthly: RankingPeriod.MONTHLY,
	weekly: RankingPeriod.WEEKLY,
};

// 筛选参数
const filterParams = ref({
	time: '',
	rank: 'default',
	tenant_id: '',
	rankLabel: '',
	tenantIdLabel: '',
});

// 时间选项
const timeOptions = ref([]);

// 租户选项
const rankOptions = ref([{ label: '默认榜单', value: 'default' }]);

// 排行榜数据
const rankData = ref([]);
const loading = ref(false);
const page = ref(1);
const pageSize = ref(60);
const hasMoreData = ref(true);
const totalItems = ref(0);
const rankTableHeight = ref(window.innerHeight - 200);

// 计算基础API请求参数
const baseApiParams = computed(() => {
	const { start_date, end_date } = getCurrentDateRange();

	const params = {
		platform_type: currentPlatform.value, // 当前选中的平台
		ranking_period: currentPeriod.value, // 当前选中的周期
		ranking_type:
			currentPlatform.value === 'wechat'
				? RankingType.ACCOUNT_WCI
				: currentPlatform.value === 'weibo'
				? RankingType.ACCOUNT_BCI
				: RankingType.ACCOUNT_DCI, // 排行榜类型根据平台调整
		start_date: start_date,
		end_date: end_date,
	};

	// 如果是自定义榜单，添加自定义榜单ID
	if (filterParams.value.rank !== 'default') {
		params.custom_ranking_id = filterParams.value.rank;
	}

	// 如果选择了租户ID，添加租户ID参数
	if (filterParams.value.tenant_id) {
		params.tenant_id = filterParams.value.tenant_id;
	}

	return params;
});

// 获取日期范围
const fetchDateRanges = async () => {
	try {
		// 根据当前平台和周期设置参数
		const params = {
			platform_type: currentPlatform.value, // 当前选中的平台
			ranking_period: currentPeriod.value, // 当前选中的周期
			ranking_type:
				currentPlatform.value === 'wechat'
					? RankingType.ACCOUNT_WCI
					: currentPlatform.value === 'weibo'
					? RankingType.ACCOUNT_BCI
					: RankingType.ACCOUNT_DCI, // 排行榜类型根据平台调整
		};

		const res = await getDateRanges(params);

		if (res && res.code === 200 && res.data) {
			// 后端返回的是嵌套数组格式: [["2025-04-01", "2025-04-30"]]
			if (Array.isArray(res.data) && res.data.length > 0) {
				// 将每个日期范围对转换为选项
				timeOptions.value = res.data
					.map((dateRange) => {
						if (Array.isArray(dateRange) && dateRange.length === 2) {
							const startDate = dateRange[0];
							const endDate = dateRange[1];
							const dateRangeStr = `${startDate}~${endDate}`;
							return {
								label: dateRangeStr,
								value: dateRangeStr,
								start_date: startDate,
								end_date: endDate,
							};
						}
						return null;
					})
					.filter((item) => item !== null);

				// 设置当前选中的日期范围（默认第一个）
				if (timeOptions.value.length > 0) {
					filterParams.value.time = timeOptions.value[0].value;
				}
			} else {
				// 如果返回的数据格式不符合预期，则显示警告
				console.log('获取日期范围格式不正确');
				// ElMessage.warning('获取日期范围格式不正确');
			}
		}
	} catch (error) {
		console.error('获取日期范围失败', error);
		// ElMessage.error('获取日期范围失败');
	}
};

// 获取当前时间选项的开始和结束日期
const getCurrentDateRange = () => {
	if (!filterParams.value.time) {
		return { start_date: '', end_date: '' };
	}

	// 先尝试从timeOptions中查找匹配的选项
	const selectedOption = timeOptions.value.find((option) => option.value === filterParams.value.time);

	// 如果找到了选项，直接返回其start_date和end_date
	if (selectedOption && selectedOption.start_date && selectedOption.end_date) {
		return {
			start_date: selectedOption.start_date,
			end_date: selectedOption.end_date,
		};
	}

	// 否则尝试从时间字符串中解析
	return parseDateRange(filterParams.value.time);
};

// 解析日期范围字符串（用作备选方案）
const parseDateRange = (dateRangeStr) => {
	if (!dateRangeStr) return { start_date: '', end_date: '' };

	const [start_date, end_date] = dateRangeStr.split('~');
	return { start_date, end_date };
};

// 标记是否发生过API错误
const hasApiError = ref(false);

// 重置错误状态并重新加载数据
const resetAndFetchRankData = () => {
	page.value = 1;
	hasMoreData.value = true;
	rankData.value = [];
	hasApiError.value = false; // 重置错误状态
	fetchRankData(true);
};

// 加载排行榜数据
const fetchRankData = async (isReset = false) => {
	// 避免重复请求
	if (loading.value || !filterParams.value.time) return;

	// 如果非重置模式，且已经有API错误，则不再发送请求
	if (!isReset && hasApiError.value) {
		return;
	}

	loading.value = true;

	// 使用基础参数并添加分页参数
	const params = {
		...baseApiParams.value,
		page: page.value,
		limit: pageSize.value,
	};

	try {
		const res = await getRankingList(params);

		if (res && res.code === 2000 && res.data) {
			// 请求成功，重置错误状态
			hasApiError.value = false;

			// 如果返回的数据中没有分页信息，则使用默认值
			const total = res.total || 0;
			const list = res.data || [];

			totalItems.value = total;

			// 处理排行榜数据
			const formattedList = list.map((item) => {
				const baseData = {
					id: item.account_id,
					name: item.account_name, // 公众号/账号名称
					avatar: item.account_logo_url, // 头像
					publishCount: item.publish_count || 0, // 发布数量
					rankIndex: item.wci || 0, // 传播指数
				};

				// 根据平台添加特定字段
				if (currentPlatform.value === 'wechat') {
					// 微信平台字段
					return {
						...baseData,
						readCount: item.read_count || 0, // 阅读量
						likeCount: item.like_count || 0, // 点赞数
						viewCount: item.view_count || 0, // 在看量
						forwardCount: item.forward_count || 0, // 分享量
					};
				} else if (currentPlatform.value === 'douyin') {
					// 抖音平台字段
					return {
						...baseData,
						fansCount: item.fans_count || 0, // 粉丝量
						playCount: item.play_count || 0, // 播放量
						likeCount: item.like_count || 0, // 点赞量
						commentCount: item.comment_count || 0, // 评论量
						shareCount: item.share_count || 0, // 分享量
						collectCount: item.collect_count || 0, // 收藏量
						dci: item.dci || 0, // 传播指数
					};
				} else if (currentPlatform.value === 'weibo') {
					// 微博平台字段
					return {
						...baseData,
						avatar: item.account_logo_base64 || '', // 头像
						readCount: item.read_count || 0, // 阅读量
						likeCount: item.like_count || 0, // 点赞量
						commentCount: item.comment_count || 0, // 评论量
						forwardCount: item.share_count || 0, // 转发量
						collectCount: item.collect_count || 0, // 收藏量
						originalCount: item.original_count || 0, // 原创量
						bci: item.bci || 0, // 传播指数
					};
				}

				return baseData;
			});

			// 如果是重置数据，则清空之前的数据
			if (isReset) {
				rankData.value = formattedList;
			} else {
				rankData.value = [...rankData.value, ...formattedList];
			}

			// 判断是否还有更多数据
			hasMoreData.value = rankData.value.length < total;
		} else {
			console.log('获取排行榜数据失败', res);
			// 请求失败，设置错误状态
			hasApiError.value = true;
			// 请求失败时，设置无更多数据，避免重复请求
			hasMoreData.value = false;
		}
	} catch (error) {
		console.error('获取排行榜数据失败', error);
		// 发生错误，设置错误状态
		hasApiError.value = true;
		// 发生错误时，设置无更多数据，避免重复请求
		hasMoreData.value = false;
	} finally {
		loading.value = false;
	}
};

// 加载更多数据
const loadMoreData = () => {
	if (!loading.value && hasMoreData.value) {
		page.value++;
		fetchRankData();
	}
};

// 导出数据
const exportRankData = async (loadingRef) => {
	try {
		// 使用基础参数
		const params = {
			...baseApiParams.value,
			// 导出不需要分页参数
		};

		// 调用导出接口
		const res = await exportApiData(params);

		// 导出Excel
		const fileName = `${filterParams.value.rankLabel && filterParams.value.rank !== 'default' ? filterParams.value.rankLabel + '_' : ''}${
			filterParams.value.tenantIdLabel ? filterParams.value.tenantIdLabel + '_' : ''
		}排行榜数据_${params.start_date}_${params.end_date}.xlsx`;
		const result = await download.excel(res.data, fileName);

		if (result.success) {
			ElMessage.success('导出成功');
		} else {
			ElMessage.error(result.message || '导出失败');
		}
	} catch (error) {
		console.error('导出数据失败', error);
		// ElMessage.error('导出失败');
	} finally {
		// 关闭loading状态
		if (loadingRef) {
			loadingRef.value = false;
		}
	}
};

// 自定义榜单弹窗
const dialogVisible = ref(false);
const tenantList = ref([]);
const selectedAccounts = ref([]);
const customRankDialogRef = ref(null);
const editMode = ref(false);
const editData = ref({});

// 图片导出相关
const imageExportDialogVisible = ref(false);
const imageExportDialogRef = ref(null);

// 导出字段配置相关
const exportColumnsDialogVisible = ref(false);
const exportColumnsDialogRef = ref(null);

// 获取租户列表
const fetchTenantList = async () => {
	if (auth('tenant:rank')) {
		try {
			const res = await getAllTenants();
			if (res && res.code === 2000 && res.data) {
				tenantList.value = JSON.parse(JSON.stringify(res.data));
			} else {
				console.log('获取租户列表失败', res);
				// ElMessage.warning('获取自定义榜单列表失败');
			}
		} catch (error) {
			console.error('获取租户列表失败', error);
			// ElMessage.error('获取租户列表失败');
		}
	}
};
const accountList = ref([]);
// 获取公众号列表
const fetchAccountList = async () => {
	try {
		let res;

		// 根据平台调用不同的账号接口
		if (currentPlatform.value === 'wechat') {
			// 微信公众号接口
			res = await getAccountList();
		} else if (currentPlatform.value === 'douyin') {
			// 抖音账号接口
			res = await getDouyinAccountList();
		} else if (currentPlatform.value === 'weibo') {
			// 微博账号接口
			res = await getWeiboAccountList();
		}

		if (res && res.code === 2000 && res.data) {
			accountList.value = JSON.parse(JSON.stringify(res.data));
		} else {
			console.log('获取账号列表失败', res);
		}
	} catch (error) {
		console.error('获取账号列表失败', error);
	}
};

// 获取自定义榜单详情
const getCustomRankDetail = async (id) => {
	try {
		const rankOption = rankOptions.value.find((item) => item.value === id);
		if (!rankOption) return null;

		const res = await getCustomDetail(id);
		return res.data;
	} catch (error) {
		console.error('获取自定义榜单详情失败', error);
		return null;
	}
};

// 获取自定义榜单列表
const fetchCustomRankList = async () => {
	try {
		const res = await getCustomRankList(currentPlatform.value);
		if (res && res.code === 2000 && res.data) {
			// 保留默认榜单选项
			const defaultOptions = rankOptions.value.filter((item) => item.value === 'default');

			// 添加自定义榜单选项
			const customOptions = res.data.map((item) => ({
				label: item.name,
				value: String(item.id),
			}));

			rankOptions.value = [...defaultOptions, ...customOptions];
		} else {
			console.log('获取自定义榜单列表失败', res);
		}
	} catch (error) {
		console.error('获取自定义榜单列表失败', error);
	}
};

// 打开自定义榜单弹窗（新增模式）
const openCustomRankDialog = async () => {
	editMode.value = false;
	editData.value = {};
	dialogVisible.value = true;

	// 获取公众号列表
	if (accountList.value.length === 0) {
		await fetchAccountList();
	}
};

// 编辑自定义榜单
const editCustomRank = async (id) => {
	if (!id || id === 'default') {
		ElMessage.warning('请选择要编辑的自定义榜单');
		return;
	}

	// 获取榜单详情
	const detail = await getCustomRankDetail(id);
	if (!detail) {
		ElMessage.error('获取榜单详情失败');
		return;
	}

	// 设置编辑模式和数据
	editMode.value = true;
	editData.value = detail;

	dialogVisible.value = true;

	// 获取公众号列表
	if (accountList.value.length === 0) {
		await fetchAccountList();
	}
};

// 删除自定义榜单
const deleteCustomRank = async (id) => {
	if (!id || id === 'default') {
		ElMessage.warning('请选择要删除的自定义榜单');
		return;
	}

	// 确认删除
	ElMessageBox.confirm('确定要删除该自定义榜单吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				const res = await deleteRank(Number(id));

				if (res && res.code === 2000) {
					ElMessage.success('删除成功');

					// 如果当前选中的就是被删除的榜单，则切换到默认榜单
					if (filterParams.value.rank === id) {
						filterParams.value.rank = 'default';
					}

					// 重新获取榜单列表
					await fetchCustomRankList();

					// 重新加载数据
					resetAndFetchRankData();
				} else {
					ElMessage.error(res.message || '删除失败');
				}
			} catch (error) {
				console.error('删除自定义榜单失败', error);
				ElMessage.error('删除失败');
			}
		})
		.catch(() => {});
};

// 处理账号变更
const handleAccountChange = (value) => {
	selectedAccounts.value = value;
};

// 处理周期变化
const handlePeriodChange = async (period) => {
	// 更新当前周期
	currentPeriodString.value = period;
	currentPeriod.value = periodMap[period];

	// 重置筛选条件
	filterParams.value.rank = 'default';
	filterParams.value.tenant_id = '';
	filterParams.value.rankLabel = '';
	filterParams.value.tenantIdLabel = '';

	// 清空当前数据
	rankData.value = [];
	timeOptions.value = [];
	filterParams.value.time = '';

	// 重新获取周期相关数据
	await fetchDateRanges(); // 不同周期有不同的时间范围
	await fetchCustomRankList(); // 重新获取自定义榜单

	// 如果获取到了时间选项，则加载排行榜数据
	if (filterParams.value.time) {
		resetAndFetchRankData();
	}
};

// 保存自定义榜单
const saveCustomRank = async (formData) => {
	if (!customRankDialogRef.value) return;

	// 设置loading状态
	customRankDialogRef.value.setLoading(true);

	try {
		// 构建请求参数
		const params = {
			name: formData.name,
			account_ids: formData.account_ids, // 使用已经处理好的逗号分隔字符串
			platform_type: currentPlatform.value, // 当前选中的平台
			ranking_type:
				currentPlatform.value === 'wechat'
					? RankingType.ACCOUNT_WCI
					: currentPlatform.value === 'weibo'
					? RankingType.ACCOUNT_BCI
					: RankingType.ACCOUNT_DCI, // 排行榜类型根据平台调整
		};

		let res;

		// 根据是否是编辑模式决定调用创建还是更新API
		if (editMode.value && formData.id) {
			// 编辑模式
			params.id = Number(formData.id);
			res = await updateCustomRank(params);
		} else {
			// 新增模式
			res = await createCustomRank(params);
		}

		if (res && res.code === 2000) {
			const actionText = editMode.value ? '编辑' : '创建';
			ElMessage.success(`自定义榜单${actionText}成功`);

			// 清除loading状态
			customRankDialogRef.value.setLoading(false);

			// 关闭弹窗
			dialogVisible.value = false;

			// 重新获取自定义榜单列表
			await fetchCustomRankList();

			// 如果是编辑模式，且当前选中的就是被编辑的榜单，则重新加载数据
			if (editMode.value && filterParams.value.rank === String(formData.id)) {
				resetAndFetchRankData();
			} else if (!editMode.value && res.data && res.data.id) {
				// 如果是新增模式，切换到新创建的榜单
				setTimeout(() => {
					filterParams.value.rank = String(res.data.id);
					// 重新加载数据
					resetAndFetchRankData();
				}, 1000);
			}
		} else {
			console.log(`${editMode.value ? '编辑' : '创建'}自定义榜单失败`, res);
			ElMessage.error(res.message || `${editMode.value ? '编辑' : '创建'}自定义榜单失败`);
			// 清除loading状态
			customRankDialogRef.value.setLoading(false);
		}
	} catch (error) {
		console.error(`${editMode.value ? '编辑' : '创建'}自定义榜单失败`, error);
		ElMessage.error(`${editMode.value ? '编辑' : '创建'}自定义榜单失败`);
		// 清除loading状态
		customRankDialogRef.value.setLoading(false);
	}
};

// 处理窗口大小变化
const handleResize = () => {
	rankTableHeight.value = window.innerHeight - 200;
};

// 跟踪数据是否正在加载或者初始化中
const isInitializing = ref(true);

// 监听筛选条件变化，重新加载数据
watch(
	() => filterParams.value,
	(newVal, oldVal) => {
		// 初始化完成后才响应筛选条件变化
		if (isInitializing.value) {
			isInitializing.value = false;
			return;
		}

		// 当时间、榜单或租户ID发生变化时才重新加载数据
		if ((newVal.time && newVal.time !== oldVal.time) || (newVal.rank && newVal.rank !== oldVal.rank) || newVal.tenant_id !== oldVal.tenant_id) {
			resetAndFetchRankData();
		}
	},
	{ deep: true }
);

// 处理重试加载
const handleRetry = () => {
	resetAndFetchRankData();
};

// 获取默认导出标题
const getDefaultExportTitle = () => {
	const rankLabel = filterParams.value.rankLabel && filterParams.value.rank !== 'default' ? filterParams.value.rankLabel : '默认排行榜';
	const tenantLabel = filterParams.value.tenantIdLabel ? `${filterParams.value.tenantIdLabel}` : '';

	return `${tenantLabel}${rankLabel}`;
};

// 获取默认导出文件名
const getDefaultExportFilename = () => {
	const { start_date, end_date } = getCurrentDateRange();
	const rankLabel = filterParams.value.rankLabel && filterParams.value.rank !== 'default' ? filterParams.value.rankLabel : '默认排行榜';
	const tenantLabel = filterParams.value.tenantIdLabel ? `${filterParams.value.tenantIdLabel}_` : '';
	const platformLabel = currentPlatform.value === 'wechat' ? '微信' : currentPlatform.value === 'douyin' ? '抖音' : '微博';

	return `${tenantLabel}${platformLabel}_${rankLabel}_${start_date}_${end_date}`;
};

// 验证排行榜数据完整性
const validateRankData = (data) => {
	if (!data || !Array.isArray(data) || data.length === 0) {
		return { valid: false, message: '暂无数据可导出' };
	}

	// 检查必要字段
	const requiredFields = ['id', 'name', 'publishCount'];
	const missingFields = [];

	// 检查第一项数据
	const firstItem = data[0];
	for (const field of requiredFields) {
		if (firstItem[field] === undefined) {
			missingFields.push(field);
		}
	}

	if (missingFields.length > 0) {
		return {
			valid: false,
			message: `数据缺少必要字段: ${missingFields.join(', ')}`,
		};
	}

	// 检查平台特定字段
	let platformSpecificFields = [];
	if (currentPlatform.value === 'wechat') {
		platformSpecificFields = ['readCount', 'likeCount'];
	} else if (currentPlatform.value === 'douyin') {
		platformSpecificFields = ['likeCount', 'commentCount'];
	} else if (currentPlatform.value === 'weibo') {
		platformSpecificFields = ['likeCount', 'commentCount'];
	}

	// 检查是否至少有一个平台特定字段
	const hasAnyPlatformField = platformSpecificFields.some((field) => firstItem[field] !== undefined);

	if (!hasAnyPlatformField) {
		return {
			valid: true, // 仍然有效，但发出警告
			warning: `数据可能缺少平台特定字段，导出结果可能不完整`,
		};
	}

	return { valid: true };
};

// 导出排行榜图片
const exportRankImage = async (loadingRef) => {
	try {
		// 检查数据完整性
		const validation = validateRankData(rankData.value);

		if (!validation.valid) {
			ElMessage.warning(validation.message);
			return;
		}

		// 如果有警告但仍然有效
		if (validation.warning) {
			ElMessage.warning(validation.warning);
		}

		// 显示导出选项弹窗
		imageExportDialogVisible.value = true;
	} catch (error) {
		console.error('导出图片失败', error);
		ElMessage.error('导出失败');
	} finally {
		if (loadingRef) {
			loadingRef.value = false;
		}
	}
};

// 处理图片导出确认
const handleImageExportConfirm = async (formData, loadingRef) => {
	try {
		// 再次验证数据完整性
		const validation = validateRankData(rankData.value);
		if (!validation.valid) {
			ElMessage.error(validation.message);
			if (loadingRef) loadingRef.value = false;
			return;
		}

		// 获取当前时间范围用于显示
		const { start_date, end_date } = getCurrentDateRange();
		if (!start_date || !end_date) {
			ElMessage.warning('无法获取有效的时间范围');
			if (loadingRef) loadingRef.value = false;
			return;
		}

		const timePeriod = `${start_date}~${end_date}`;

		// 验证导出数量
		let exportCount = formData.exportCount;
		if (exportCount === 'custom') {
			// 如果是自定义数量，使用自定义数量值
			exportCount = String(formData.customCount);
		} else if (exportCount !== 'all') {
			const count = parseInt(exportCount);
			if (isNaN(count) || count <= 0) {
				exportCount = '10'; // 默认值
			}
		}

		// 使用默认标题
		let title = getDefaultExportTitle();

		// 构建导出数据，优化大数据量情况
		let dataToExport = rankData.value;

		// 如果导出全部且数据量很大，可能会导致性能问题
		if (exportCount === 'all' && dataToExport.length > 100) {
			// 显示警告
			ElMessage.warning(`数据量较大(${dataToExport.length}条)，可能会影响导出性能`);
		}

		// 构建最终导出数据
		const exportData = {
			rankData: dataToExport,
			platform: currentPlatform.value,
			exportCount: exportCount,
			title: title,
			timePeriod: timePeriod,
			period: currentPeriodString.value, // 添加周期信息
			titleLine1: formData.titleLine1 || '',
			titleLine2: formData.titleLine2 || '',
			titleLine3: formData.titleLine3 || '',
		};

		// 生成唯一ID
		const exportId = `rank_export_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

		try {
			// 将数据存储到 localStorage
			localStorage.setItem(exportId, JSON.stringify(exportData));
		} catch (storageError) {
			// 处理存储错误（可能是数据太大）
			console.error('存储数据失败', storageError);
			ElMessage.error('数据量过大，无法导出。请尝试减少导出数量。');
			if (loadingRef) loadingRef.value = false;
			return;
		}

		// 在新标签页中打开导出页面，只传递ID
		// 使用完整URL，避免路由问题
		const baseUrl = window.location.origin;
		const exportUrl = `${baseUrl}/rank-image-export.html?id=${exportId}`;
		const newWindow = window.open(exportUrl, '_blank');

		// 检查新窗口是否成功打开
		if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
			ElMessage.error('无法打开新标签页，请检查浏览器是否阻止了弹出窗口');
			localStorage.removeItem(exportId);
			if (loadingRef) loadingRef.value = false;
			return;
		}

		// 关闭弹窗
		imageExportDialogVisible.value = false;

		ElMessage.success('已在新标签页中打开导出页面');

		// 设置自动清理定时器（10分钟后自动清理数据）
		setTimeout(() => {
			localStorage.removeItem(exportId);
		}, 10 * 60 * 1000);
	} catch (error) {
		console.error('导出图片失败', error);
		ElMessage.error(`导出失败: ${error.message || '未知错误'}`);
	} finally {
		if (loadingRef) {
			loadingRef.value = false;
		}
	}
};

// 打开导出字段配置弹窗
const openExportColumnsDialog = () => {
	exportColumnsDialogVisible.value = true;
};

// 处理导出字段配置确认
const handleExportColumnsConfirm = async (exportParams) => {
	try {
		// 设置loading状态
		if (exportColumnsDialogRef.value) {
			exportColumnsDialogRef.value.setLoading(true);
		}

		// 使用基础参数
		const params = {
			...baseApiParams.value,
		};

		// 如果不导出作品，将字段设为空字符串
		if (!exportParams.export_works) {
			params.export_columns = '';
		} else {
			params.export_columns = exportParams.export_columns.join(',');
		}

		// 调用导出接口
		const res = await exportApiData(params);

		// 生成文件名
		const fileName = `${getDefaultExportFilename()}.xlsx`;
		const result = await download.excel(res.data, fileName);

		if (result.success) {
			ElMessage.success('导出成功');
			// 关闭弹窗
			if (exportColumnsDialogRef.value) {
				exportColumnsDialogRef.value.closeDialog();
			}
		} else {
			ElMessage.error(result.message || '导出失败');
		}
	} catch (error) {
		console.error('导出数据失败', error);
		ElMessage.error('导出失败');
	} finally {
		// 关闭loading状态
		if (exportColumnsDialogRef.value) {
			exportColumnsDialogRef.value.setLoading(false);
		}
	}
};

// 处理平台切换
const handlePlatformChange = async (platform) => {
	currentPlatform.value = platform;
	// 重置筛选条件
	filterParams.value.rank = 'default';
	filterParams.value.tenant_id = '';
	filterParams.value.rankLabel = '';
	filterParams.value.tenantIdLabel = '';

	// 清空当前数据
	rankData.value = [];
	timeOptions.value = [];
	filterParams.value.time = '';

	// 重新获取平台相关数据
	await fetchDateRanges(); // 不同平台可能有不同的时间范围
	await fetchCustomRankList(); // 不同平台有不同的自定义榜单
	await fetchAccountList(); // 不同平台有不同的账号列表

	// 如果获取到了时间选项，则加载排行榜数据
	if (filterParams.value.time) {
		resetAndFetchRankData();
	}
};

// 初始化
onMounted(async () => {
	window.addEventListener('resize', handleResize);

	// 获取日期范围
	await fetchDateRanges();

	// 获取自定义榜单列表
	await fetchCustomRankList();

	// 只有当日期范围获取成功时才加载数据
	if (filterParams.value.time) {
		resetAndFetchRankData();
	}
	await fetchTenantList();
	await fetchAccountList();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.rank-container {
	padding: 20px;
}

.platform-tabs {
	margin-bottom: 20px;
}
</style>


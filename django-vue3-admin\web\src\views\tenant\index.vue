<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell_stage="scope">
				<dict-tag
					:options="$getEnumDatas($ENUM.CUSTOMER_STAGE)"
					:value="scope.row.stage"
					:color-type="['info', 'primary', 'warning', 'success', 'danger']"
				/>
			</template>
			<template #cell_logo="scope">
				<!-- <el-image
					style="display: block; margin: 0 auto"
					:src="scope.row.logo"
					:preview-src-list="[scope.row.logo]"
					:z-index="99999"
					fit="contain"
					preview-teleported
					:crossorigin="null"
					referrerpolicy="no-referrer"
				/> -->
				<el-image
					style="width: 100%; aspect-ratio: 1 /1"
					:src="getBaseURL(scope.row.logo)"
					:preview-src-list="[getBaseURL(scope.row.logo)]"
					:preview-teleported="true"
				/>
			</template>
		</fs-crud>

		<!-- 账号绑定弹窗组件 -->
		<AccountBindDialog />
	</fs-page>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import type { CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { getBaseURL } from '/@/utils/baseUrl';
import AccountBindDialog from './components/AccountBindDialog.vue';

const { crudBinding, crudRef, crudExpose } = useFs<CreateCrudOptionsRet>({ createCrudOptions });
// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script> 
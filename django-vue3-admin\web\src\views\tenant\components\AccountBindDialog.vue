<template>
	<el-dialog v-model="visible" :title="`绑定监管账号 - ${tenantName}`" width="70%" :before-close="handleClose" append-to-body>
		<!-- 平台选择标签页 -->
		<el-tabs v-model="currentPlatform" @tab-click="handlePlatformChange">
			<el-tab-pane v-for="platform in platformOptions" :key="platform.value" :label="platform.label" :name="platform.value"> </el-tab-pane>
		</el-tabs>

		<!-- 账号绑定穿梭框 -->
		<div v-loading="loading" class="transfer-container">
			<Transfer
				v-if="accountList.length > 0"
				:current-bound-account="boundAccountIds"
				:data="accountList"
				:titles="['未绑定账号', '已绑定账号']"
				@data-change="handleAccountChange"
			/>
			<el-empty v-else description="暂无可绑定的账号" />
		</div>

		<!-- 弹窗底部按钮 -->
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleConfirm" :loading="saving"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Transfer from '/@/components/transfer/transfer.vue';
import * as api from '../api';

// ==================== 组件接口定义 ====================

interface PlatformOption {
	label: string;
	value: string;
}

interface AccountItem {
	key: number;
	label: string;
	account_id: number;
	// 保留原始数据用于调试
	_originalData?: any;
}

// ==================== 响应式数据 ====================

// 弹窗显示状态
const visible = ref(false);
// 加载状态
const loading = ref(false);
// 保存状态
const saving = ref(false);
// 当前租户信息
const tenantId = ref<number>(0);
const tenantName = ref<string>('');
// 当前选中的平台
const currentPlatform = ref<string>('wechat');
// 账号列表数据
const accountList = ref<AccountItem[]>([]);
// 已绑定的账号ID列表
const boundAccountIds = ref<number[]>([]);
// 当前选中的账号ID列表（用于提交）
const selectedAccountIds = ref<number[]>([]);

// 平台选项配置
const platformOptions: PlatformOption[] = [
	{ label: '微信公众号', value: 'wechat' },
	{ label: '抖音', value: 'douyin' },
	{ label: '微博', value: 'weibo' },
];

// ==================== 组件方法 ====================

/**
 * 打开弹窗
 * @param params 租户信息参数
 */
const open = (params: { tenantId: number; tenantName: string }) => {
	console.log('🔄 打开账号绑定弹窗', {
		tenantId: params.tenantId,
		tenantName: params.tenantName,
	});

	tenantId.value = params.tenantId;
	tenantName.value = params.tenantName;
	visible.value = true;
	currentPlatform.value = 'wechat'; // 默认选中微信平台

	// 清空之前的数据
	accountList.value = [];
	boundAccountIds.value = [];
	selectedAccountIds.value = [];

	// 立即加载账号列表
	loadAccountList();
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
	visible.value = false;
	// 重置数据
	tenantId.value = 0;
	tenantName.value = '';
	currentPlatform.value = 'wechat';
	accountList.value = [];
	boundAccountIds.value = [];
	selectedAccountIds.value = [];
};

/**
 * 处理平台切换
 * @param tab 标签页对象
 */
const handlePlatformChange = (tab: any) => {
	console.log('平台切换', tab.props.name, currentPlatform.value);
	// 强制更新当前平台 否则会出现当前页面tab切换后调用接口会 调用上一次的tab值
	currentPlatform.value = tab.props.name; 
	// 清空当前数据
	accountList.value = [];
	boundAccountIds.value = [];
	selectedAccountIds.value = [];

	// 只有在有租户ID的情况下才重新加载账号列表
	if (tenantId.value) {
		console.log('🔄 开始重新加载账号列表');
		loadAccountList();
	} else {
		console.log('❌ 没有租户ID，跳过加载账号列表');
	}
};

/**
 * 加载账号列表
 */
const loadAccountList = async () => {
	if (!tenantId.value || !currentPlatform.value) return;

	loading.value = true;
	try {
		console.log('🔄 加载账号列表', {
			tenantId: tenantId.value,
			platform: currentPlatform.value,
		});

		const params: api.QueryMediaAccountsParams = {
			tenant_id: tenantId.value,
			platform_type: currentPlatform.value,
		};
		const response = await api.queryMediaAccountsByTenantAndPlatform(params);

		console.log('📥 API响应结果', {
			code: response.code,
			message: response.message,
			dataType: typeof response.data,
			dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
			hasSelected: response.data && typeof response.data === 'object' && 'selected' in response.data,
			response: response,
		});

		// 修正：后端返回的code是2000，不是200
		if (response.code === 2000) {
			// 处理账号列表数据
			let allAccounts: any[] = [];
			let boundAccounts: any[] = [];

			if (response.data && typeof response.data === 'object') {
				// 检查是否包含selected和all字段的结构
				if ('selected' in response.data && 'all' in response.data) {
					// 新的数据结构：{ selected: [], all: [] }
					boundAccounts = (response.data as any).selected || [];
					allAccounts = (response.data as any).all || [];
				} else if (Array.isArray(response.data)) {
					// 如果data本身就是数组，说明是所有账号，需要从中筛选已绑定的
					allAccounts = response.data;
					// 从账号列表中筛选出已绑定的账号（is_selected为true）
					boundAccounts = allAccounts.filter((account) => account.is_selected === true);
				}
			} else if (Array.isArray(response.data)) {
				// 如果data直接是数组
				allAccounts = response.data;
				boundAccounts = allAccounts.filter((account) => account.is_selected === true);
			}

			console.log('📊 数据处理结果', {
				allAccountsCount: allAccounts.length,
				boundAccountsCount: boundAccounts.length,
				allAccounts: allAccounts.slice(0, 3), // 只显示前3个用于调试
				boundAccounts: boundAccounts,
			});

			// 转换数据格式为穿梭框需要的格式
			accountList.value = allAccounts.map((account: any) => ({
				key: account.media_account_id || account.id, // 使用media_account_id或id作为key
				label: account.account_name || account.nickname || account.nick_name || account.wechat_id || `账号${account.media_account_id || account.id}`,
				account_id: account.media_account_id || account.id,
				// 保留原始数据用于调试
				_originalData: account,
			}));

			// 提取已绑定的账号ID
			// 注意：这里需要确保boundAccountIds中的ID与accountList中的key一致
			boundAccountIds.value = boundAccounts.map((bound: any) => bound.media_account_id || bound.id);

			// 初始化选中的账号ID
			selectedAccountIds.value = [...boundAccountIds.value];
		} else {
			console.log('❌ 接口调用失败', {
				code: response.code,
				message: response.message,
				expectedCode: 2000,
			});
			ElMessage.error(response.message || '获取账号列表失败');
		}
	} catch (error) {
		console.error('💥 加载账号列表异常', {
			error: error,
			errorMessage: error instanceof Error ? error.message : '未知错误',
			tenantId: tenantId.value,
			platform: currentPlatform.value,
		});
		ElMessage.error('加载账号列表失败');
	} finally {
		loading.value = false;
		console.log('🔚 账号列表加载流程结束');
	}
};

/**
 * 处理账号选择变化 - 实时绑定
 * @param value 选中的账号ID列表
 */
const handleAccountChange = async (value: number[]) => {
	console.log('🔄 账号选择变化', {
		platform: currentPlatform.value,
		originalSelected: selectedAccountIds.value,
		newSelected: value,
		changeType: value.length > selectedAccountIds.value.length ? '增加' : '减少',
		changeCount: Math.abs(value.length - selectedAccountIds.value.length),
	});

	// 更新选中的账号ID列表
	selectedAccountIds.value = value;
	boundAccountIds.value = value;

	// 实时执行绑定操作
	await performAccountBinding(value);
};

/**
 * 执行账号绑定操作
 * @param accountIds 要绑定的账号ID列表
 */
const performAccountBinding = async (accountIds: number[]) => {
	if (!tenantId.value || !currentPlatform.value) return;

	try {
		console.log('🔄 实时绑定账号', {
			platform: currentPlatform.value,
			tenant_id: tenantId.value,
			accountIds: accountIds,
		});

		// 调用批量绑定接口
		await api.batchToggleAccountSelection({
			media_account_ids: accountIds,
			tenant_id: tenantId.value,
			platform_type: currentPlatform.value,
			is_selected: true,
		});

		console.log('✅ 账号实时绑定成功');
		ElMessage.success('账号绑定更新成功');
	} catch (error) {
		console.error('💥 账号实时绑定失败:', error);
		ElMessage.error('账号绑定失败');

		// 绑定失败时回滚数据
		loadAccountList();
	}
};

/**
 * 确认绑定操作（保留原有逻辑，但现在主要用于关闭弹窗）
 */
const handleConfirm = async () => {
	console.log('✅ 确认绑定操作，账号已实时绑定');
	handleClose();
};

// ==================== 事件监听 ====================

/**
 * 监听全局事件，用于打开弹窗
 */
const handleGlobalEvent = (event: CustomEvent) => {
	const { tenantId: id, tenantName: name } = event.detail;
	open({ tenantId: id, tenantName: name });
};

// 组件挂载时添加事件监听
onMounted(() => {
	window.addEventListener('openAccountBindDialog', handleGlobalEvent as EventListener);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
	window.removeEventListener('openAccountBindDialog', handleGlobalEvent as EventListener);
});

// ==================== 组件暴露 ====================

defineExpose({
	open,
	handleClose,
});
</script>

<style scoped>
.transfer-container {
	margin: 20px 0;
	min-height: 400px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
	.transfer-container {
		min-height: 300px;
	}
}

@media screen and (min-width: 1920px) {
	.transfer-container {
		min-height: 500px;
	}
}
</style> 
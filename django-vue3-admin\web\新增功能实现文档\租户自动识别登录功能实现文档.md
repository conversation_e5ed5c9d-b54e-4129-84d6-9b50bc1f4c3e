# 租户自动识别登录功能实现文档

## 项目概述

在 Django-Vue3-Admin 系统中实现租户自动识别功能，支持通过 URL 路由参数自动匹配租户，隐藏租户选择输入框，并在登录页面显示租户名称。

## 需求分析

### 核心需求

1. **URL 路由自动匹配租户**：通过 URL 参数 `?tenant=租户名称` 自动识别租户
2. **智能隐藏租户输入框**：当检测到租户参数时，自动隐藏租户输入框
3. **登录标题增强显示**：在"欢迎登录"前添加租户名称显示
4. **API 集成转换**：将租户名称转换为租户 ID 传递给后端
5. **测试模式支持**：本地开发时支持模拟数据

### 用户体验需求

1. **租户名称显示优化**：

   - 短租户名称（≤8 个字符）：一行显示
   - 长租户名称（>8 个字符）：换行显示
   - 租户名称用蓝色高亮显示，不使用括号和阴影

2. **实时租户匹配**：
   - 在租户输入框失去焦点时立即调用 API
   - 提供即时的匹配成功/失败反馈
   - 只有匹配成功才允许登录

## 技术实现

### 1. 文件修改清单

#### 1.1 `src/views/system/login/component/account.vue`

**主要修改内容：**

- **添加租户自动识别逻辑**
- **增加路由参数监听**
- **实现租户名称到 ID 的转换**
- **添加实时租户匹配功能**

**关键代码结构：**

```javascript
// 表单数据结构
ruleForm: {
    tenantname: '',
    tenant_id: null,  // 新增：后端需要的租户ID
    username: '',
    password: '',
    // ...其他字段
}

// 自动租户状态管理
const autoTenant = reactive({
    isAutoMode: false,    // 是否为自动租户模式
    tenantName: '',       // 租户名称
    tenantId: null,       // 租户ID
    loading: false,       // 加载状态
});
```

**核心功能函数：**

1. **`getTenantIdByName()`**：通过租户名称获取租户 ID
2. **`handleAutoTenant()`**：处理 URL 参数自动租户逻辑
3. **`onTenantNameBlur()`**：处理手动输入租户的实时匹配
4. **路由监听器**：监听 URL 参数变化

#### 1.2 `src/views/system/login/index.vue`

**主要修改内容：**

- **登录标题动态显示逻辑**
- **租户名称样式优化**
- **响应式布局适配**

**关键样式实现：**

```scss
.title-with-tenant {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
	flex-wrap: nowrap;

	&.long-tenant-layout {
		flex-direction: column; // 长名称换行显示
		gap: 8px;
	}
}

.tenant-name {
	color: var(--el-color-primary); // 蓝色高亮
	font-weight: 700;
	font-size: 32px;
	text-align: center;
}
```

### 2. 功能实现详解

#### 2.1 自动租户识别流程

```mermaid
graph TD
    A[页面加载] --> B{检查URL参数}
    B -->|有tenant参数| C[进入自动模式]
    B -->|无tenant参数| D[正常模式]

    C --> E[调用API获取租户ID]
    E -->|成功| F[隐藏租户输入框]
    E -->|失败| G[显示警告，退出自动模式]

    F --> H[显示租户名称标题]
    G --> D
    D --> I[显示租户输入框]
```

#### 2.2 手动输入租户流程

```mermaid
graph TD
    A[用户输入租户名称] --> B[输入框失去焦点]
    B --> C[调用API查询租户]
    C -->|找到租户| D[显示成功提示]
    C -->|未找到| E[显示警告提示]
    C -->|API错误| F[显示错误提示]

    D --> G[设置tenant_id]
    E --> H[清空tenant_id]
    F --> H

    G --> I[允许登录]
    H --> J[阻止登录]
```

#### 2.3 登录数据传递

**发送给后端的数据结构：**

```javascript
{
    tenantname: "租户名称",      // 字符串，用于显示
    tenant_id: 123,            // 数字，后端业务逻辑使用
    username: "用户名",
    password: "加密后的密码",
    captcha: "验证码",
    captchaKey: "验证码KEY"
}
```

### 3. 用户界面展示

#### 3.1 标题显示效果

**正常模式：**

```
欢迎登录
```

**短租户名称（≤8 字符）：**

```
测试租户 欢迎登录    ← 蓝色租户名 + 黑色欢迎登录，同行显示
```

**长租户名称（>8 字符）：**

```
        内蒙古交通集团巴彦淖尔分公司        ← 第一行，32px蓝色
               欢迎登录                    ← 第二行，32px黑色
```

#### 3.2 输入框状态

- **自动模式**：租户输入框隐藏
- **手动模式**：租户输入框显示，支持实时匹配反馈

### 4. 测试用例

#### 4.1 URL 测试链接

```bash
# 正常登录模式
http://localhost:端口号/#/login

# 自动租户模式 - 测试租户
http://localhost:端口号/#/login?tenant=测试租户

# 自动租户模式 - 长名称租户
http://localhost:端口号/#/login?tenant=内蒙古交通集团巴彦淖尔分公司

# 自动租户模式 - 不存在的租户
http://localhost:端口号/#/login?tenant=不存在的租户
```

#### 4.2 功能测试场景

| 测试场景             | 预期结果                         |
| -------------------- | -------------------------------- |
| URL 带有效租户参数   | 隐藏租户输入框，显示租户名称标题 |
| URL 带无效租户参数   | 显示警告，回到正常模式           |
| 手动输入存在的租户   | 失去焦点时显示成功提示           |
| 手动输入不存在的租户 | 失去焦点时显示警告提示           |
| 租户未匹配时登录     | 阻止登录，提示确保租户匹配       |
| 租户匹配成功后登录   | 正常登录流程                     |

### 5. 错误处理机制

#### 5.1 API 调用容错

- **开发环境**：API 失败时使用测试数据
- **生产环境**：显示错误提示，要求用户重试
- **网络异常**：友好的错误提示信息

#### 5.2 用户反馈机制

- **成功提示**：绿色消息 "租户匹配成功: [租户名称]"
- **警告提示**：黄色消息 "未找到租户: [租户名称]"
- **错误提示**：红色消息 "查询租户失败，请重试"

### 6. 技术特点

#### 6.1 性能优化

- **API 缓存**：避免重复调用相同租户的 API
- **防抖处理**：输入框失去焦点时才触发查询
- **异步处理**：不阻塞用户界面

#### 6.2 用户体验优化

- **即时反馈**：实时显示租户匹配状态
- **智能布局**：根据租户名称长度自适应显示
- **无缝切换**：自动模式和手动模式平滑过渡

#### 6.3 开发友好

- **测试模式**：开发环境支持模拟数据
- **详细日志**：控制台输出调试信息
- **类型安全**：TypeScript 类型定义完整

### 7. 退出登录租户上下文保持

#### 7.1 功能说明

当用户通过租户链接登录后，退出登录时系统会自动保持租户上下文，确保用户重新登录时不需要再次选择租户。

#### 7.2 实现机制

**登录时保存租户信息：**

```javascript
// 登录成功后保存租户信息到Session
if (autoTenant.isAutoMode || state.ruleForm.tenant_id) {
	const tenantInfo = {
		tenantName: autoTenant.tenantName || state.ruleForm.tenantname,
		tenantId: autoTenant.tenantId || state.ruleForm.tenant_id,
	};
	Session.set('loginTenantInfo', tenantInfo);
}
```

**退出登录时构建租户 URL：**

```javascript
// 获取保存的租户信息
const loginTenantInfo = Session.get('loginTenantInfo');

// 构建包含租户参数的登录URL
if (loginTenantInfo && loginTenantInfo.tenantName) {
	const tenantParam = encodeURIComponent(loginTenantInfo.tenantName);
	window.location.href = `${window.location.origin}${window.location.pathname}#/login?tenant=${tenantParam}`;
}
```

#### 7.3 适用场景

- **手动退出登录**：用户点击退出按钮
- **Token 过期**：401/4001 错误自动跳转
- **会话超时**：其他服务端错误导致的重新登录

#### 7.4 URL 编码处理

系统会自动处理租户名称中的特殊字符：

- 中文字符：`内蒙古交通集团` → `%E5%86%85%E8%92%99%E5%8F%A4%E4%BA%A4%E9%80%9A%E9%9B%86%E5%9B%A2`
- 特殊符号：确保 URL 参数的安全传递

## 总结

本次功能实现完成了租户自动识别登录的全部需求，包括：

✅ **核心功能**：URL 参数自动识别、租户输入框智能隐藏、API 集成转换
✅ **用户体验**：智能布局、实时反馈、友好提示、租户上下文保持
✅ **技术实现**：响应式设计、错误处理、性能优化、安全的 URL 编码
✅ **测试支持**：开发模式、多场景测试用例
✅ **会话管理**：退出登录时保持租户上下文，无缝重新登录体验

该功能提升了系统的用户体验，特别适合多租户 SaaS 场景下的快速登录需求，确保租户用户在整个会话生命周期中都能保持一致的登录体验。

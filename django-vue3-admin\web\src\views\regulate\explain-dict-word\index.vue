<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<!-- 状态列自定义渲染 -->
			<template #cell_status="scope">
				<dict-tag :options="$getEnumDatas($ENUM.ENABLE_STATUS)" :value="scope.row.status" :color-type="['success', 'warning']" />
			</template>

			<!-- 词语解释列自定义渲染，支持tooltip显示完整内容 -->
			<template #cell_explanation="scope">
				<el-tooltip :content="scope.row.explanation" placement="top" :show-after="200" popper-class="explanation-tooltip-popper">
					<div class="truncate-text">{{ formatText(scope.row.explanation) }}</div>
				</el-tooltip>
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import type { CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
	name: 'ExplainDictWordList',
	setup() {
		const { crudBinding, crudRef, crudExpose } = useFs<CreateCrudOptionsRet>({ createCrudOptions });

		// 文本截取方法，超过50个字符显示省略号
		const formatText = (text: string) => {
			if (!text) return '';
			return text.length > 50 ? text.substring(0, 50) + '...' : text;
		};

		// 页面打开后获取列表数据
		onMounted(() => {
			crudExpose.doRefresh();
		});

		return {
			crudBinding,
			crudRef,
			formatText,
		};
	},
});
</script>

<style scoped>
.truncate-text {
	display: inline-block;
	width: 100%;
}
</style>

<style>
.explanation-tooltip-popper {
	width: 400px !important;
	max-width: 400px;
	white-space: normal;
	word-break: break-all;
	word-wrap: break-word;
	line-height: 1.5;
}
</style> 
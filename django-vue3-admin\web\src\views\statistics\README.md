# 统计分析模块

## 核心功能
- 排行榜统计：展示多平台账号的排行榜数据，支持自定义排名指标
- 平台切换：支持微信、抖音、微博等多平台数据展示和对比分析
- 时间维度：提供灵活的时间周期选择，支持日、周、月、季度、年度统计
- 自定义排名：支持用户自定义排名规则和权重配置
- 数据导出：支持排行榜数据的Excel导出和图片导出功能
- 租户筛选：支持按租户维度进行数据筛选和权限隔离
- 滚动加载：支持大数据量的分页加载和性能优化

## 技术实现

### API接口层 (api.ts)
- getRankData(): 获取排行榜数据，支持平台、时间、租户等多维度筛选
- createCustomRank(): 创建自定义排名规则，处理权重配置和指标选择
- exportRankData(): 导出排行榜数据，支持Excel和图片格式
- getTenantList(): 获取租户列表，提供筛选选项数据
- updateRankConfig(): 更新排名配置，支持实时调整排名算法

### 组件架构 (components/)
- FilterBar: 筛选组件，handlePeriodChange()处理时间切换，exportRankData()处理导出
- RankTable: 排行榜表格，loadMoreData()实现滚动加载，支持大数据量展示
- CustomRankDialog: 自定义排名弹窗，openCustomRankDialog()处理排名规则配置
- ExportColumnsDialog: 导出列选择弹窗，openExportColumnsDialog()处理导出字段配置
- ImageExportDialog: 图片导出弹窗，处理图片格式和质量配置

### 主页面控制 (index.vue)
- 平台管理: platformTabs定义支持平台，handlePlatformChange()处理平台切换
- 数据管理: rankData管理排行数据，hasMoreData控制加载更多按钮状态
- 筛选控制: filterParams统一管理筛选条件，currentPeriodString展示当前时间周期
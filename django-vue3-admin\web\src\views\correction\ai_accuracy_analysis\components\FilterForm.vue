<template>
	<div class="filter-container">
		<el-form :inline="true" :model="filterForm" class="search-form">
			<el-form-item label="租户筛选">
				<el-select v-model="filterForm.tenant_id" placeholder="请选择租户" clearable filterable style="width: 220px">
					<el-option v-for="tenant in tenantOptions" :key="tenant.value" :label="tenant.label" :value="tenant.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="时间范围">
				<el-select v-model="quickTimeRange" placeholder="快速选择" clearable style="width: 120px" @change="handleQuickTimeChange">
					<el-option v-for="time in timeRangeOptions" :key="time.value" :label="time.label" :value="time.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="自定义时间">
				<el-date-picker
					v-model="dateRange"
					type="daterange"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					format="YYYY-MM-DD"
					value-format="YYYY-MM-DD"
					style="width: 240px"
					@change="handleDateRangeChange"
				/>
			</el-form-item>
			<el-form-item label="判定人员">
				<el-select v-model="filterForm.judge_user_id" placeholder="请选择判定人员" clearable filterable style="width: 220px">
					<el-option v-for="user in judgeUserOptions" :key="user.value" :label="user.label" :value="user.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="错误类型">
				<el-select v-model="filterForm.error_type" placeholder="请选择错误类型" clearable style="width: 180px">
					<el-option v-for="error in errorTypeOptions" :key="error.value" :label="error.label" :value="error.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="判断状态">
				<el-select v-model="filterForm.judge_status" placeholder="请选择判断状态" clearable style="width: 180px">
					<el-option v-for="status in judgeStatusOptions" :key="status.value" :label="status.label" :value="status.value" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
				<el-button icon="Refresh" @click="handleReset">重置</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
	FilterParams, 
	OptionData,
	getTenantOptions, 
	getJudgeUserOptions,
	ERROR_TYPE_OPTIONS,
	JUDGE_STATUS_OPTIONS,
	TIME_RANGE_OPTIONS
} from '../api';

defineOptions({
	name: 'FilterForm',
});

interface Props {
	modelValue: FilterParams;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'search', 'reset']);

// 响应式数据
const tenantOptions = ref<OptionData[]>([]);
const judgeUserOptions = ref<OptionData[]>([]);
const errorTypeOptions = ERROR_TYPE_OPTIONS;
const judgeStatusOptions = JUDGE_STATUS_OPTIONS;
const timeRangeOptions = TIME_RANGE_OPTIONS;

// 快速时间选择
const quickTimeRange = ref<number>();
// 日期范围选择
const dateRange = ref<[string, string]>();

// 双向绑定的表单数据
const filterForm = computed({
	get: () => props.modelValue,
	set: (val) => emit('update:modelValue', val),
});

// 处理快速时间范围选择
const handleQuickTimeChange = (days: number | undefined) => {
	if (days) {
		// 清空自定义日期范围
		dateRange.value = undefined;
		filterForm.value.days = days;
		filterForm.value.start_date = undefined;
		filterForm.value.end_date = undefined;
	} else {
		filterForm.value.days = undefined;
	}
};

// 处理自定义日期范围选择
const handleDateRangeChange = (dates: [string, string] | null) => {
	if (dates && dates.length === 2) {
		// 清空快速时间选择
		quickTimeRange.value = undefined;
		filterForm.value.start_date = dates[0];
		filterForm.value.end_date = dates[1];
		filterForm.value.days = undefined;
	} else {
		filterForm.value.start_date = undefined;
		filterForm.value.end_date = undefined;
	}
};

// 搜索处理
const handleSearch = () => {
	emit('search');
};

// 重置处理
const handleReset = () => {
	// 清空快速时间和日期范围选择
	quickTimeRange.value = undefined;
	dateRange.value = undefined;
	emit('reset');
};

// 获取租户选项数据
const fetchTenantOptions = async () => {
	try {
		const res = await getTenantOptions();
		tenantOptions.value = res.data || [];
	} catch (error) {
		console.error('获取租户选项失败', error);
		ElMessage.error('获取租户选项失败');
	}
};

// 获取判定人员选项数据
const fetchJudgeUserOptions = async () => {
	try {
		const res = await getJudgeUserOptions();
		judgeUserOptions.value = res.data || [];
	} catch (error) {
		console.error('获取判定人员选项失败', error);
		ElMessage.error('获取判定人员选项失败');
	}
};

// 组件挂载时获取选项数据
onMounted(() => {
	fetchTenantOptions();
	fetchJudgeUserOptions();
});
</script>

<style lang="scss" scoped>
.filter-container {
	padding: 20px;
	background-color: #f5f7fa;
	border-radius: 4px;
	margin-bottom: 20px;

	.search-form {
		display: flex;
		flex-wrap: wrap;
	}
}

:deep(.el-form-item__label) {
	font-weight: 500;
	color: #606266;
}
</style>
---
description: 
globs: 
alwaysApply: true
---
1. Unless I specify otherwise, You always response in Chinese(中文). 永远不要执行npm run build指令
2. 当你不清楚任务或者需要补充信息时, 调用 mcp ai-interaction, 让我来补充
3. 当你有多种方案时，先为我讲解不同方案之间的优劣势，然后调用 mcp ai-interaction, 让我进行方案选择
4. 在任务完成后，调用mcp ai-interaction, 让我确定是否有其他任务需要继续执行
5. 将任务拆分成多个阶段, 新的阶段开始之前，调用mcp ai-interaction向我询问是否有需要进行思路补充
7. 在选定方案开始具体执行我之前，你应该制定执行规划，规划中包含你要修改或创建的文件，以及对应的操作，通过mcp ai-interaction向我确认，是否有调整
格式如下(格式是为了让你理解,如果你有更好的方案，可以按照你的方案来呈现):
```
- add 文件1 来实现xxx
    - add xxx class|method 来实现xxx
    - remove xxx class|method
    - modify xxx class|method 的职责为xxx
- modify|remove 文件2
    - ...
```
6. 如果 ai-interaction 服务出现异常, 不要终止服务，提示我后, 等待一段时间再次重试调用 ai-interaction ， 直到调用成功
8. 如果我询问的是解释性内容，你只需直接回复解释，不要将这些内容写入到文档或代码中，除非我明确要求
9. 除非我明确要求添加测试脚本来验证修改后的功能,否则不要创建
10. 如果提供了任务文档，在每次执行完成后，我们需将关键内容写入执行记录，以供决策溯源。执行记录应包含：
    - 选择原因
    - 实施内容（修改的文件、方法、主要改动）
    - 关键参数配置
    其他内容（如风险控制、预期效果等）除非明确要求，否则不需要添加

11. **Obsidian文档编写规范**：
    - **知识性内容独立成文件**：将通用的概念、原理、技术解释等内容创建为独立的知识文档
    - **使用双向链接引用**：在主文档中通过 `[[文档名]]` 或 `[[文档名|显示文本]]` 的方式引用知识文档
    - **表格中的链接格式**：在表格中使用 `[[文档名]]` 进行引用，如：`**GIL影响** [[什么是GIL（全局解释器锁）？]]`
    - **保持主文档聚焦**：主文档专注于具体的任务、方案、实施过程，避免冗长的概念解释
    - **知识文档可复用**：独立的知识文档可以被多个项目文档引用，提高知识管理效率
    - **文档命名规范**：知识文档使用问题式命名，如"什么是GIL（全局解释器锁）？"，便于搜索和理解

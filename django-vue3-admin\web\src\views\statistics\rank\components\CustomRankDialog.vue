<template>
	<el-dialog v-model="visible" :title="dialogTitle" width="70%" :before-close="handleClose">
		<div class="custom-rank-form">
			<div class="form-item">
				<div class="form-label required">榜单名称</div>
				<el-input v-model="rankName" placeholder="请输入榜单名称" style="width: 300px" />
			</div>
			<div class="form-item">
				<div class="form-label required">选择账号</div>
				<div class="account-selector">
					<transfer :current-bound-account="selectedAccountKeys" :data="formattedAccountList" @dataChange="handleAccountChange" />
				</div>
			</div>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="cancelDialog">取消</el-button>
				<el-button type="primary" :loading="loading" @click="saveRank">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import transfer from '/@/components/transfer/transfer.vue';

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	accountList: {
		type: Array,
		default: () => [],
	},
	editMode: {
		type: Boolean,
		default: false,
	},
	editData: {
		type: Object,
		default: () => ({}),
	},
});

const emit = defineEmits(['update:modelValue', 'save', 'accountChange']);

const visible = ref(props.modelValue);
const rankName = ref('');
const selectedAccounts = ref([]); // 用于存储实际的账号对象或ID
const selectedAccountKeys = ref([]); // 用于传递给穿梭框组件的key数组
const loading = ref(false);

// 对话框标题
const dialogTitle = computed(() => {
	return props.editMode ? '编辑自定义榜单' : '新增自定义榜单';
});

// 格式化账号列表，确保每个项目都有key和label属性
const formattedAccountList = computed(() => {
	return props.accountList.map((item) => {
		// 确保每个项目都有key和label属性
		return {
			key: String(item.id || item.account_id || item.key || ''),
			label: item.nick_name || item.nickname || item.label || '',
			...item, // 保留原始属性
		};
	});
});

// 处理account_ids字符串，转换为key数组
const processAccountIds = (accountIds) => {
	if (!accountIds) return [];

	// 分割字符串为数组，并确保每个元素都是字符串类型
	return accountIds.split(',').map((id) => String(id.trim()));
};

watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
		if (newVal) {
			// 如果是编辑模式，设置初始值
			if (props.editMode && props.editData) {
				rankName.value = props.editData.name || '';

				// 处理账号数据
				if (props.editData.account_ids) {
					selectedAccountKeys.value = processAccountIds(props.editData.account_ids);
				} else {
					selectedAccountKeys.value = [];
				}
			} else {
				// 重置表单
				rankName.value = '';
				selectedAccountKeys.value = [];
			}
		}
	}
);

watch(
	() => visible.value,
	(newVal) => {
		emit('update:modelValue', newVal);
	}
);

watch(
	() => props.editMode,
	(newVal) => {
		if (newVal && props.editData) {
			rankName.value = props.editData.name || '';

			// 处理账号数据
			if (props.editData.account_ids) {
				selectedAccountKeys.value = processAccountIds(props.editData.account_ids);
			} else {
				selectedAccountKeys.value = [];
			}
		}
	}
);

watch(
	() => props.editData,
	(newVal) => {
		if (props.editMode && newVal) {
			rankName.value = newVal.name || '';

			// 处理账号数据
			if (newVal.account_ids) {
				selectedAccountKeys.value = processAccountIds(newVal.account_ids);
			} else {
				selectedAccountKeys.value = [];
			}
		}
	},
	{ deep: true }
);

const handleAccountChange = (value) => {
	selectedAccountKeys.value = value;
	emit('accountChange', value);
};

const handleClose = (done) => {
	if (rankName.value || selectedAccountKeys.value.length > 0) {
		ElMessageBox.confirm('有未保存的更改，确定关闭吗？', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				done();
				resetForm();
			})
			.catch(() => {});
	} else {
		done();
		resetForm();
	}
};

const cancelDialog = () => {
	visible.value = false;
	resetForm();
};

const resetForm = () => {
	rankName.value = '';
	selectedAccountKeys.value = [];
};

const saveRank = () => {
	if (!rankName.value) {
		ElMessage.warning('请输入榜单名称');
		return;
	}

	if (selectedAccountKeys.value.length === 0) {
		ElMessage.warning('请至少选择一个账号');
		return;
	}

	// 设置loading状态
	loading.value = true;

	// 已选账号数组
	const accountIds = selectedAccountKeys.value;

	emit('save', {
		name: rankName.value,
		accounts: accountIds,
		account_ids: accountIds.join(','),
		id: props.editMode && props.editData ? props.editData.id : undefined,
	});

	// 保存后重置表单会在父组件中关闭对话框时进行
	// 不在这里重置表单，避免保存成功前表单被清空
};

// 暴露方法给父组件，用于设置loading状态
const setLoading = (status) => {
	loading.value = status;
};

// 暴露方法给父组件，用于关闭弹窗
const closeDialog = () => {
	visible.value = false;
	resetForm();
};

// 暴露方法给父组件
defineExpose({
	setLoading,
	closeDialog,
});
</script>

<style scoped>
/* 自定义榜单弹窗样式 */
.custom-rank-form {
	padding: 20px 0;
}

.form-item {
	margin-bottom: 20px;
}

.form-label {
	margin-bottom: 8px;
	font-weight: 500;
}

.required:before {
	content: '* ';
	color: #f56c6c;
}

.account-selector {
	margin-top: 10px;
	height: 500px;
}
</style> 
<template>
	<div class="proofread-task-container">
		<!-- 顶部筛选框 -->
		<TaskFilter
			v-model:id="queryParams.id"
			v-model:name="queryParams.name"
			v-model:status="queryParams.status"
			v-model:tenant="queryParams.tenant_id"
			:tenants="tenantList"
			@search="handleSearch"
			@reset="handleReset"
		/>
		<div class="operation-bar">
			<el-button type="primary" v-auth="'CorrectionTask:Create'" icon="Plus" @click="handleAddTask">新增任务</el-button>
		</div>
		<!-- 中间内容区 -->
		<div class="content-container">
			<!-- 任务列表 -->
			<TaskList
				:list="taskList"
				:loading="loading"
				:total="total"
				:tenants="tenantList"
				@update:currentPage="handlePageChange"
				@update:pageSize="handleSizeChange"
				v-model:current-page="queryParams.page"
				v-model:page-size="queryParams.limit"
				@add-target="handleAddTarget"
				@view-targets="handleViewTargets"
				@view-errors="handleViewErrors"
				@delete="handleDelete"
				@generate-report="handleGenerateReport"
				@open-target="handleOpenTarget"
				@update-task-stats="handleUpdateTaskStats"
			/>
		</div>

		<!-- 新增任务弹窗 -->
		<TaskTargetDialog
			v-model:visible="dialogVisible"
			:dialog-mode="dialogMode"
			:tenants="tenantList"
			:task="selectedTask"
			@submit="handleDialogSubmit"
		/>

		<!-- 删除确认弹窗 -->
		<DeleteDialog v-model:visible="deleteDialog.visible" :task-name="deleteDialog.taskName" @confirm="confirmDelete" />
	</div>
</template>

<script lang="ts" setup>
import { auth } from "/@/utils/authFunction"
import { Local } from '/@/utils/storage';
import { ref, onMounted, shallowReactive } from 'vue';
import { useRouter } from 'vue-router';
import { successMessage, errorMessage, warningMessage } from '/@/utils/message';
import {
	getTaskList,
	createTask,
	deleteTask,
	createTargets,
	type CorrectionTaskInfo,
	type QueryParams,
	type CreateTaskParams,
	type CreateTargetParams,
	startAllTargets,
	updateTaskStats,
} from './api';
import { getAllTenants, type TenantInfo } from '/@/views/tenant/api';
import { generateCorrectionReport } from '/@/views/analysisHelper/correctionAnalysis/api';
import { ElMessageBox } from 'element-plus';

// 导入组件
import TaskFilter from './components/TaskFilter.vue';
import TaskList from './components/TaskList.vue';
import TaskTargetDialog from './components/TaskTargetDialog.vue';
import DeleteDialog from './components/DeleteDialog.vue';

const router = useRouter();

// 查询参数
const queryParams = shallowReactive({
	id: undefined as number | undefined,
	page: 1,
	limit: 10,
	name: '',
	type: undefined as number | undefined,
	status: undefined as number | undefined,
	tenant_id: undefined as number | undefined,
	create_time_start: undefined as string | undefined,
	create_time_end: undefined as string | undefined,
});

// 数据列表
const taskList = ref<CorrectionTaskInfo[]>([]);
const total = ref(0);
const loading = ref(false);

// 租户列表
const tenantList = ref<TenantInfo[]>([]);

// 显示任务对话框
const showTaskDialog = () => {
	dialogMode.value = 'task';
	dialogVisible.value = true;
};

// 显示目标对话框
const showTargetDialog = (task: CorrectionTaskInfo) => {
	dialogMode.value = 'target';
	selectedTask.value = task;
	dialogVisible.value = true;
};

// 统一的对话框控制变量
const dialogVisible = ref(false);
const dialogMode = ref<'task' | 'target'>('task'); // 对话框模式：task - 任务，target - 目标
const selectedTask = ref<CorrectionTaskInfo | null>(null);

// 删除对话框
const deleteDialog = shallowReactive({
	visible: false,
	taskId: 0,
	taskName: '',
});

// 获取任务列表
const fetchTaskList = async () => {
	loading.value = true;
	try {
		const res = await getTaskList(queryParams);
		taskList.value = res.data;
		total.value = res.total || 0;

		// 为每个任务添加默认的deletable属性
		taskList.value.forEach((task) => {
			(task as any).deletable = true;
		});

		// 注释掉检查任务是否可删除的代码
		// 检查每个任务是否可删除
		// for (const task of taskList.value) {
		// 	const checkRes = await checkTaskDeletable(task.id);
		// 	(task as any).deletable = checkRes.data;
		// }
	} catch (error) {
		console.error('获取任务列表失败', error);
		errorMessage('获取任务列表失败');
	} finally {
		loading.value = false;
	}
};

// 获取租户列表
const fetchTenantList = async () => {
	if (auth('Tenants:List')) {
		// 有获取所有租户权限，则获取所有租户
		try {
			const res = await getAllTenants();
			tenantList.value = res.data;
		} catch (error) {
			console.error('获取租户列表失败', error);
			errorMessage('获取租户列表失败');
		}
	} else {
		// 没有获取所有租户权限，则获取当前登录租户
		tenantList.value = [];
		const loginTenantInfo = Local.get('loginTenantInfo') as any;
		tenantList.value.push({
			id: loginTenantInfo.tenantId || 0,
			name: loginTenantInfo.tenantName || '',
			status: loginTenantInfo.status || 0,
		});
	}
};

// 搜索
const handleSearch = () => {
	queryParams.page = 1;
	fetchTaskList();
};

// 重置
const handleReset = () => {
	queryParams.id = undefined;
	queryParams.name = '';
	queryParams.status = undefined;
	queryParams.tenant_id = undefined;
	handleSearch();
};

// 新增任务
const handleAddTask = () => {
	showTaskDialog();
};

// 处理页码变化
const handlePageChange = (page: number) => {
	queryParams.page = page;
	fetchTaskList();
};

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
	queryParams.limit = size;
	fetchTaskList();
};
// 处理新建任务提交
const handleTaskSubmit = (data: CreateTaskParams) => {
	createTask(data)
		.then((res) => {
			if (res.code === 2000) {
				successMessage('创建任务成功');
				fetchTaskList();
			} else {
				errorMessage(res.message || '创建任务失败');
			}
		})
		.catch((err) => {
			console.error('创建任务失败', err);
			errorMessage('创建任务失败');
		});
};

// 新增目标
const handleAddTarget = (row: CorrectionTaskInfo) => {
	showTargetDialog(row);
};

// 处理新增目标提交
const handleTargetSubmit = (data: CreateTargetParams) => {
	createTargets(data)
		.then((res) => {
			if (res.code === 2000) {
				successMessage('创建目标成功');
				fetchTaskList();
			} else {
				errorMessage(res.message || '创建目标失败');
			}
		})
		.catch((err) => {
			console.error('创建目标失败', err);
			errorMessage('创建目标失败');
		});
};

// 统一处理对话框提交
const handleDialogSubmit = (data: any) => {
	if (dialogMode.value === 'task') {
		handleTaskSubmit(data);
	} else {
		handleTargetSubmit(data);
	}
};

// 查看目标
const handleViewTargets = (row: CorrectionTaskInfo) => {
	router.push({
		path: '/correcttion/proofread_target',
		query: { task_id: row.id },
	});
};

const handleViewErrors = (row: CorrectionTaskInfo) => {
	router.push({
		path: '/correction/article_error',
		query: { task_id: row.id },
	});
};

// 删除任务
const handleDelete = (row: CorrectionTaskInfo) => {
	deleteDialog.taskId = row.id;
	deleteDialog.taskName = row.name;
	deleteDialog.visible = true;
};

// 确认删除
const confirmDelete = async () => {
	try {
		await deleteTask(deleteDialog.taskId);
		successMessage('删除成功');
		deleteDialog.visible = false;
		fetchTaskList();
	} catch (error) {
		console.error('删除失败', error);
		errorMessage('删除失败');
	}
};
// 开启任务下的所有目标
const handleOpenTarget = (row: CorrectionTaskInfo) => {
	ElMessageBox.confirm('您确定要开启所有校对目标吗？', '确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		try {
			await startAllTargets(row.id).then(() => {
				successMessage('校对目标开启成功');
			});
		} catch (error: any) {
			console.error('生成月报失败', error);
		}
	});
};
// 生成月报
const handleGenerateReport = async (row: CorrectionTaskInfo) => {
	ElMessageBox.confirm('您确定要生成月报吗？', '确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		try {
			await generateCorrectionReport(row.id);
			successMessage('报表生成任务已提交');
		} catch (error: any) {
			console.error('生成月报失败', error);
			warningMessage(error.message || '生成月报失败');
		}
	});
};
// 更新任务统计信息
const handleUpdateTaskStats = (row: CorrectionTaskInfo) => {
	ElMessageBox.confirm('您确定要更新任务统计信息吗？', '确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		try {
			await updateTaskStats(row.id);
			successMessage('任务统计信息更新成功');
			fetchTaskList();
		} catch (error: any) {
			console.error('更新任务统计信息失败', error);
			warningMessage(error.message || '更新任务统计信息失败');
			fetchTaskList();
		}
	});
};
// 页面加载时获取数据
onMounted(() => {
	fetchTaskList();
	fetchTenantList();
});
</script>

<style scoped>
.proofread-task-container {
	padding: 20px;
}

.content-container {
	margin-top: 20px;
}

.operation-bar {
	margin-bottom: 10px;
}
</style>
<template>
	<div class="platform-example">
		<h3>原PlatformTabs组件</h3>
		<PlatformTabs v-model="currentPlatform" @change="handlePlatformChange" />
		<div class="content-box">当前选中平台: {{ currentPlatform }}</div>

		<h3>使用CommonTabs实现</h3>
		<CommonTabs v-model="currentPlatform2" :items="platformItems" @change="handlePlatformChange" />
		<div class="content-box">当前选中平台: {{ currentPlatform2 }}</div>
	</div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';
import type { TabItem } from './index.vue';

// 定义平台类型接口
interface PlatformType {
	label: string;
	value: string;
	[key: string]: any;
}

// 动态导入组件
const PlatformTabs = defineAsyncComponent(() => import('/@/views/wechat_official_account/components/PlatformTabs.vue'));
const CommonTabs = defineAsyncComponent(() => import('./index.vue'));

// 当前选中的平台类型
const currentPlatform = ref('wechat');
const currentPlatform2 = ref('wechat');

// 平台类型列表
const platformTypes = ref<PlatformType[]>([]);
const platformItems = ref<TabItem[]>([]);

// 获取平台类型数据
onMounted(() => {
	// 获取枚举数据
	platformTypes.value = getEnumDatas(ENUM_TYPE.PLATFORM_TYPE);

	// 转换为CommonTabs需要的格式
	platformItems.value = platformTypes.value.map((item) => ({
		label: item.label,
		value: item.value,
	}));
});

// 变更处理函数
const handlePlatformChange = (value: string) => {
	console.log('平台变更:', value);
};
</script>

<style lang="scss" scoped>
.platform-example {
	padding: 20px;

	h3 {
		margin: 30px 0 15px 0;
		font-size: 18px;
		color: #333;
	}

	.content-box {
		margin-top: 20px;
		padding: 15px;
		background-color: #f5f7fa;
		border-radius: 4px;
		color: #666;
	}
}
</style> 
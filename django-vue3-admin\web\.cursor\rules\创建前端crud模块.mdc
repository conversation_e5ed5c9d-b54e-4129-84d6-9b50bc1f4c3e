---
description: 
globs: 
alwaysApply: false
---
# 前端模块开发指南

本文档将指导您如何在 django-vue3-admin 项目中创建一个新的前端模块。

## 1. 模块结构

一个完整的前端模块通常包含以下文件结构：

```
web/src/views/模块名称/
  ├── 组件名称/
  │   ├── api.ts         # API接口定义
  │   ├── crud.tsx       # CRUD配置
  │   └── index.vue      # 页面组件
  └── 其他组件...
```

## 2. 开发步骤

### 2.1 创建目录结构

首先，按照文件结构创建模块和组件的目录结构

```bash
mkdir -p src/views/模块名称/组件名称
```

### 2.2 创建API接口文件

在 `api.ts` 文件中定义与后端交互的接口：

```typescript
import { request } from '/@/utils/service'

// 定义数据类型接口
export interface DataType {
  id: number
  // 其他字段...
}

// 定义查询参数接口
export interface QueryParams {
  page?: number
  limit?: number
  // 其他查询参数...
}

// 定义API前缀
export const apiPrefix = '/api/模块路径/'

// 获取列表数据
export async function getList(query: QueryParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
}

// 创建数据
export async function createData(obj: Partial<DataType>) {
  return await request({
    url: apiPrefix,
    method: 'post',
    data: obj
  })
}

// 更新数据
export async function updateData(obj: Partial<DataType>) {
  return await request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj
  })
}

// 删除数据
export async function deleteData(id: number) {
  return await request({
    url: apiPrefix + id + '/',
    method: 'delete'
  })
}
```

### 2.3 创建CRUD配置文件

在 `crud.tsx` 文件中配置表格和表单：

```typescript
import { CrudOptions, AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag } from 'element-plus'

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    // 定义请求方法
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateData(form)
    }
    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteData(row.id)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createData(form)
    }

    return {
        crudOptions: {
            // 请求配置
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest
            },
            // 操作栏配置
            actionbar: {
                buttons: {
                    add: {
                        show: auth('权限值:Create')
                    }
                }
            },
            // 隐藏工具栏
            toolbar:{
                show: false,
            },
            // 行操作配置
            rowHandle: {
                width: 260,
                buttons: {
                    edit: {
                        show: auth('权限值:Update')
                    },
                    remove: {
                        show: auth('权限值:Delete')
                    }
                }
            },
            // 列配置
            columns: {
                // 字段配置
                fieldName: {
                    title: '字段标题',
                    type: 'text',
                    search: { 
                        show: true ,
                        autoSearchTrigger: false, //关闭自动筛选
                    },
                    form: {
                        rules: [{ required: true, message: '必填项' }]
                    }
                },
                // 更多字段...
            }
        }
    }
}
```

### 2.4 创建页面组件

在 `index.vue` 文件中创建页面组件：

```vue
<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding" />
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import type { CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
	name: 'TenantList',
	setup() {
		const { crudBinding, crudRef, crudExpose } = useFs<CreateCrudOptionsRet>({ createCrudOptions });
		// 页面打开后获取列表数据
		onMounted(() => {
			crudExpose.doRefresh();
		});
		return {
			crudBinding,
			crudRef,
		};
	},
});
</script> 
```




## 3. 只读模式配置

如果只需要展示数据，不需要编辑功能，可以按以下方式配置：

### 3.1 API文件

只保留获取列表数据的方法：

```typescript
export async function getList(query: QueryParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
}
```

### 3.2 CRUD配置

禁用添加、编辑和删除功能：

```typescript
return {
    crudOptions: {
        request: {
            pageRequest
            // 不提供其他请求方法
        },
        actionbar: {
            buttons: {
                add: {
                    show: false // 隐藏添加按钮
                }
            }
        },
        rowHandle: {
            show: false // 隐藏行操作按钮
        },
        // 列配置...
    }
}
```

## 4. 常见问题

### 4.1 自定义渲染

对于需要自定义渲染的列，可以在 `index.vue` 中使用插槽：

```vue
<template #cell_fieldName="scope">
    <!-- 自定义渲染内容 -->
</template>
```

### 4.2 数据加载

确保在组件挂载后调用 `crudExpose.doRefresh()` 方法加载数据。

## 5. 最佳实践

1. **命名规范**：使用驼峰命名法命名组件，使用小写加下划线命名API路径。
2. **类型定义**：为所有数据和参数定义TypeScript接口，提高代码可维护性。
3. **权限控制**：使用 `auth` 函数控制按钮的显示，确保权限值与后端一致。
4. **组件复用**：将通用逻辑抽取为独立组件或hooks，提高代码复用性。
5. **错误处理**：在API调用中添加适当的错误处理逻辑。

按照本指南的步骤，您可以快速创建一个功能完整的前端模块，并与后端API无缝集成。 

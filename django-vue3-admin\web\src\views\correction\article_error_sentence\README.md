# 人工校对结果模块

## 核心功能
- 错误句子展示：显示AI校对发现的错误句子和详细信息
- 人工判定：支持人工确认或否定AI判定结果，记录判定原因
- 多维度筛选：按错误类型、租户、时间范围、判定人员等条件筛选数据
- 敏感程度标记：设置错误的敏感程度等级（一般/严重）
- 数据导出：支持筛选结果的批量导出功能

## 技术实现

### API接口层 (correction.ts)
- getArticleErrorSentences(): 获取错误句子列表数据，支持分页和筛选
- updateJudge(): 更新人工判定结果，记录human_judge字段
- updateJudgeReason(): 更新判定原因，提供判定依据
- setWarning(): 设置敏感程度等级，用于优先级管理
- getErrorTypes(): 获取错误类型选项数据

### 组件架构 (components/)
- SearchForm: 搜索筛选组件，handleFilter()和resetSearch()处理筛选逻辑
- ErrorTable: 错误数据表格，handleUpdateJudge()处理人工判定操作
- JudgeDialog: 人工校对弹窗，submitJudge()提交判定结果
- Pagination: 分页组件，handleSizeChange()和handleCurrentChange()处理分页逻辑
- WordExplainDialog: 词语解释弹窗，提供错误词汇的详细说明

### 主页面控制 (index.vue)
- 数据管理：getList()实现列表数据获取，支持筛选条件和分页参数
- 状态处理：listLoading控制加载状态，error handling统一错误处理
- 交互逻辑：handleErrorPreview()实现错误句子预览，handleExportSuccess()处理导出结果
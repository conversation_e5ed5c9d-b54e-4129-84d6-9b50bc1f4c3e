<template>
	<div class="el-transfer-panel">
		<p class="el-transfer-panel__header">
			<el-checkbox v-model="allChecked" @change="handleAllCheckedChange" :indeterminate="isIndeterminate">
				{{ title }}
				<span>{{ checkedSummary }}</span>
			</el-checkbox>
		</p>

		<div :class="['el-transfer-panel__body', hasFooter && !hideFooter ? 'is-with-footer' : '']">
			<el-input
				class="el-transfer-panel__filter"
				v-model="query"
				size="small"
				:placeholder="placeholder"
				@mouseenter="inputHover = true"
				@mouseleave="inputHover = false"
				v-if="filterable"
			>
				<template #prefix>
					<i :class="['el-input__icon', 'el-icon-' + inputIcon]" @click="clearQuery"></i>
				</template>
			</el-input>
			<el-checkbox-group
				v-infinite-scroll="load"
				:infinite-scroll-distance="10"
				v-model="checked"
				v-show="!hasNoMatch && data.length > 0"
				:class="{ 'is-filterable': filterable }"
				class="el-transfer-panel__list"
			>
				<el-checkbox
					class="el-transfer-panel__item"
					:value="item[keyProp]"
					:disabled="item[disabledProp]"
					:key="item[keyProp]"
					v-for="item in filteredData.slice(0, count)"
				>
					<option-content :option="item"></option-content>
				</el-checkbox>
			</el-checkbox-group>

			<p class="el-transfer-panel__empty" v-show="hasNoMatch">
				{{ t('el.transfer.noMatch') }}
			</p>
			<p class="el-transfer-panel__empty" v-show="data.length === 0 && !hasNoMatch">
				{{ t('el.transfer.noData') }}
			</p>
		</div>
		<p class="el-transfer-panel__footer" v-if="hasFooter && !hideFooter" :style="hideFooter ? 'display: none;' : ''">
			<slot></slot>
		</p>
	</div>
</template>

<script setup>
import { ref, computed, watch, h, getCurrentInstance, defineProps, defineEmits, useSlots } from 'vue';
import { useLocale } from 'element-plus';
import { Search, CircleClose } from '@element-plus/icons-vue';

// 选项内容组件
const OptionContent = {
	name: 'OptionContent',
	props: {
		option: Object,
	},
	setup(props) {
		const getParent = (vm) => {
			// 安全地检查组件类型
			try {
				if (vm && vm.$ && vm.$.type && vm.$.type.name === 'ElTransferPanel') {
					return vm;
				} else if (vm && vm.$ && vm.$.parent) {
					return getParent(vm.$.parent);
				}
			} catch (e) {
				console.warn('Error in getParent:', e);
			}
			// 找不到ElTransferPanel时返回null
			return null;
		};

		return () => {
			try {
				const instance = getCurrentInstance();
				if (!instance) return h('span', '');

				const panel = getParent(instance);
				// 如果找不到panel，使用简单渲染
				if (!panel) {
					return h('span', props.option.label || props.option.key || '');
				}

				// 使用安全的方式访问渲染内容
				if (panel.renderContent) {
					return panel.renderContent(h, props.option);
				}

				// 尝试访问父组件的默认插槽
				const transfer = panel.$ && panel.$.parent ? panel.$.parent : panel;
				if (transfer && transfer.$slots && transfer.$slots.default) {
					return transfer.$slots.default({ option: props.option });
				}

				// 默认渲染
				const labelProp = panel.labelProp || 'label';
				const keyProp = panel.keyProp || 'key';
				const labelValue = props.option[labelProp];
				const keyValue = props.option[keyProp];

				return h('span', labelValue || keyValue || '');
			} catch (e) {
				console.warn('Error in render function:', e);
				return h('span', props.option.label || props.option.key || '');
			}
		};
	},
};

// 组件Props定义
const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
	renderContent: Function,
	placeholder: String,
	title: String,
	filterable: Boolean,
	format: Object,
	filterMethod: Function,
	defaultChecked: Array,
	props: Object,
	hideFooter: {
		type: Boolean,
		default: false,
	},
});

// 组件Emits定义
const emit = defineEmits(['checked-change']);
const slots = useSlots();
const { t } = useLocale();

const checked = ref([]);
const allChecked = ref(false);
const query = ref('');
const inputHover = ref(false);
const checkChangeByUser = ref(true);
const count = ref(50);

const labelProp = computed(() => props.props.label || 'label');
const keyProp = computed(() => props.props.key || 'key');
const disabledProp = computed(() => props.props.disabled || 'disabled');
const hasFooter = computed(() => !!slots.default && !props.hideFooter);

// 计算源数据
const filteredData = computed(() => {
	return props.data.filter((item) => {
		if (typeof props.filterMethod === 'function') {
			return props.filterMethod(query.value, item);
		} else {
			const label = item[labelProp.value] || item[keyProp.value].toString();
			return label.toLowerCase().indexOf(query.value.toLowerCase()) > -1;
		}
	});
});

const checkableData = computed(() => {
	return filteredData.value.filter((item) => !item[disabledProp.value]);
});

const checkedSummary = computed(() => {
	const checkedLength = checked.value.length;
	const dataLength = props.data.length;
	const { noChecked, hasChecked } = props.format || {};

	if (noChecked && hasChecked) {
		return checkedLength > 0
			? hasChecked.replace(/\${checked}/g, checkedLength).replace(/\${total}/g, dataLength)
			: noChecked.replace(/\${total}/g, dataLength);
	} else {
		return `${checkedLength}/${dataLength}`;
	}
});

const isIndeterminate = computed(() => {
	const checkedLength = checked.value.length;
	return checkedLength > 0 && checkedLength < checkableData.value.length;
});

const hasNoMatch = computed(() => {
	return query.value.length > 0 && filteredData.value.length === 0;
});

const inputIcon = computed(() => {
	return query.value.length > 0 && inputHover.value ? CircleClose : Search;
});

// 监听和处理
watch(
	() => checked.value,
	(val, oldVal) => {
		updateAllChecked();

		if (checkChangeByUser.value) {
			const movedKeys = val.concat(oldVal).filter((v) => val.includes(v) || oldVal.includes(v));
			emit('checked-change', val, movedKeys);
		} else {
			emit('checked-change', val);
			checkChangeByUser.value = true;
		}
	}
);

watch(
	() => props.data,
	() => {
		const checkedValFiltered = [];
		const filteredDataKeys = filteredData.value.map((item) => item[keyProp.value]);

		checked.value.forEach((item) => {
			if (filteredDataKeys.includes(item)) {
				checkedValFiltered.push(item);
			}
		});

		checkChangeByUser.value = false;
		checked.value = checkedValFiltered;
	}
);

watch(
	() => checkableData.value,
	() => {
		updateAllChecked();
	}
);

watch(
	() => props.defaultChecked,
	(val, oldVal) => {
		if (oldVal && val.length === oldVal.length && val.every((item) => oldVal.includes(item))) return;

		const checkedValFiltered = [];
		const checkableDataKeys = checkableData.value.map((item) => item[keyProp.value]);

		val.forEach((item) => {
			if (checkableDataKeys.includes(item)) {
				checkedValFiltered.push(item);
			}
		});

		checkChangeByUser.value = false;
		checked.value = checkedValFiltered;
	},
	{ immediate: true }
);

// 方法
const updateAllChecked = () => {
	const checkableDataKeys = checkableData.value.map((item) => item[keyProp.value]);
	allChecked.value = checkableDataKeys.length > 0 && checkableDataKeys.every((item) => checked.value.includes(item));
};

const handleAllCheckedChange = (val) => {
	checkChangeByUser.value = true;
	checked.value = val ? checkableData.value.map((item) => item[keyProp.value]) : [];
};

const clearQuery = () => {
	if (inputIcon.value === CircleClose) {
		query.value = '';
	}
};

const load = () => {
	if (count.value >= filteredData.value.length) return;
	count.value += 50;
};

// 定义组件实例，供OptionContent组件访问
defineExpose({
	labelProp,
	keyProp,
	renderContent: props.renderContent,
	query,
});
</script>


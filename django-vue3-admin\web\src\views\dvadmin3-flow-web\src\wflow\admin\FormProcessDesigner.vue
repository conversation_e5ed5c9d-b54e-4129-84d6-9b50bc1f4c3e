<script setup name="FormProcessDesigner">
import { computed, onMounted, reactive, ref, nextTick } from 'vue';
import FromDesigner from '../design/form/FormDesigner.vue';
import ProcessDesigner from './ProcessDesigner.vue';
import BaseSetting from './BaseSetting.vue';
import PlusSetting from './PlusSetting.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '/@/utils/service';
import nodeType from '../design/process/ProcessNodes.js';
import { Icon as iconify } from '@iconify/vue';
const active = ref('BASE');
//几个设置页面的ref
const baseRef = ref();
const formRef = ref();
const processRef = ref();
const plusRef = ref();

const activePd = computed(() => {
	return active.value === 'PROCESS';
});

const props = defineProps({
	modelValue: Object,
});

const emit = defineEmits(['update:modelValue', 'onSaveSuccess', 'onClose']);

//设计器数据
const designDataInit = reactive({
	name: '未命名流程',
	icon: {
		name: 'file-icons:omnigraffle',
		bgc: '#4C87F3',
		color: '#FFFFFF',
	},
	groupId: null,
	formConf: {
		conf: {
			labelPosition: 'right', //标签位置
			labelWidth: 100, //标签宽度，
			size: 'default',
		},
		components: [],
	},
	//流程json
	process: [nodeType.Start.create()],
	remark: null,
});

// 设计器数据的响应式计算属性，支持双向绑定
const designData = computed({
	get() {
		return props.modelValue || designDataInit;
	},
	set(val) {
		// 当数据变化时，通知父组件更新
		emit('update:modelValue', val);
	},
});

// 确保组件在挂载后再进行初始化
onMounted(() => {
	// 延迟一帧确保所有子组件都已挂载
	nextTick(() => {
		console.log('FormProcessDesigner mounted and ready');
	});
});

function switchMenu(index) {
	active.value = index;
}

function doSave(publish = false) {
	// 获取当前设计器数据
	const currentData = designData.value;
	const isNew = currentData.isNew;
	const hasId = currentData.id && currentData.id !== null;

	console.log('doSave - isNew:', isNew, 'id:', currentData.id, 'hasId:', hasId);

	// 修复URL构建逻辑：新建或没有id时用POST，有id时用PUT
	const url = isNew || !hasId ? '/api/dvadmin3_flow/flow_info/' : `/api/dvadmin3_flow/flow_info/${currentData.id}/`;
	const method = isNew || !hasId ? 'post' : 'put';

	console.log('API call:', method, url);

	// 准备保存数据，移除临时标记避免后端处理异常
	const saveData = { ...currentData };
	if (isNew) {
		delete saveData.isNew;
	}

	return request({
		url: url,
		method: method,
		data: saveData,
	}).then((res) => {
		ElMessage.success('保存成功');

		// 如果是新建，更新数据的id和状态
		if (isNew && res.data) {
			// 创建更新后的数据副本
			const updatedData = { ...currentData };
			updatedData.id = res.data.id || res.data.flow_id;
			updatedData.isNew = false;

			// 通知父组件更新数据，确保后续操作使用正确的id
			emit('update:modelValue', updatedData);

			// 清除localStorage中的临时数据
			localStorage.removeItem('tempFlowData');
		}

		// 如果不需要发布，直接触发保存成功事件
		if (!publish) {
			emit('onSaveSuccess', {
				isNew,
				data: res.data,
				isPublish: false,
			});
			return res;
		}

		// 需要发布时，先执行发布再触发事件
		console.log('开始发布流程...');
		// 获取流程ID，优先使用响应数据中的id
		const flowId = res.data?.id || res.data?.flow_id || currentData.id;
		return request({
			url: `/api/dvadmin3_flow/flow_info/${flowId}/publish/`,
			method: 'post',
			data: {},
		}).then((publishRes) => {
			console.log('发布成功，准备触发事件');
			ElMessage.success('发布成功');
			// 发布成功后才触发关闭事件，确保时序正确
			emit('onClose');
			return publishRes;
		});
	});
}

// 发布流程：直接调用保存并发布，移除了复杂的校验步骤
function publish() {
	doSave(true).catch((error) => {
		console.error('发布失败:', error);
		ElMessage.error('发布失败，请重试');
	});
}

//关闭流程设计
const onClose = () => {
	const isNew = designData.value?.isNew;
	const confirmText = isNew ? '确定关闭流程设计？未保存的数据将丢失。' : '确定关闭流程设计？';

	ElMessageBox.confirm(confirmText, {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			// 如果是新建且未保存，清除临时数据
			if (isNew) {
				localStorage.removeItem('tempFlowData');
			}
			emit('onClose');
		})
		.catch(() => {
			ElMessage.error('已取消');
		});
};
</script>

<template>
	<div class="w-designer">
		<el-container>
			<el-header style="padding: 0">
				<el-menu :default-active="active" class="w-designer-menu" mode="horizontal" @select="switchMenu">
					<div>
						<el-button icon="ArrowLeft" circle @click="onClose"></el-button>
						<iconify :icon="designData.icon.name" class="w-process-icon" :style="{ background: designData.icon.bgc, color: designData.icon.color }" />
						<el-text>{{ designData.name }}</el-text>
					</div>
					<el-menu-item index="BASE">基础设置</el-menu-item>
					<!--          <el-menu-item index="FORM">② 表单设计</el-menu-item>-->
					<el-menu-item index="PROCESS">流程设计</el-menu-item>
					<!--          <el-menu-item index="PLUS">④ 扩展设置</el-menu-item>-->
					<div>
						<el-button icon="FolderChecked" @click="doSave()" round>保存</el-button>
						<el-button icon="Promotion" type="primary" @click="publish" round>发布</el-button>
					</div>
				</el-menu>
			</el-header>
			<el-main :class="{ 'w-designer-container': true, 'w-no-padding': active === 'FORM' }">
				<base-setting v-if="designData && active === 'BASE'" ref="baseRef" v-model="designData" />
				<from-designer v-if="designData && designData.formConf && active === 'FORM'" ref="formRef" v-model="designData.formConf" />
				<process-designer
					v-if="designData && designData.formConf && activePd"
					ref="processRef"
					:formItems="designData.formConf.components || []"
					v-model="designData.process"
					:designData="designData"
					:active="activePd"
				/>
				<plus-setting v-if="designData && active === 'PLUS'" ref="plusRef" v-model="designData" />
			</el-main>
		</el-container>
	</div>
</template>

<style lang="less" scoped>
@import '../../assets/theme';
.w-designer {
	background: #f3f3f3;

	.w-designer-menu {
		display: flex;
		align-items: center;
		background: white;
		justify-content: center;
		position: relative;

		& > div:first-child {
			position: absolute;
			left: 20px;
			display: flex;
			align-items: center;

			.w-process-icon {
				margin: 0 10px 0 20px;
			}
		}

		& > div:last-child {
			position: absolute;
			right: 20px;
		}
	}

	.w-designer-container {
		overflow: auto;
		height: calc(100vh - 60px);
	}

	.w-no-padding {
		padding: 0;
	}
}

.el-menu.el-menu--horizontal {
	border-bottom: none !important;
	width: 100% !important;
	.el-menu-item,
	.el-sub-menu__title {
		height: 50px !important;
		color: var(--el-menu-text-color);
	}
	.el-menu-item:not(.is-active):hover,
	.el-sub-menu:not(.is-active):hover .el-sub-menu__title {
		color: var(--el-menu-text-color);
	}
}
</style>

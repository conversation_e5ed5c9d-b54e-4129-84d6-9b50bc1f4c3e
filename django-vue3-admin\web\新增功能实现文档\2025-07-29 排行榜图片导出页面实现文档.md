# 排行榜图片导出页面实现文档

## 1. 页面概述与目的

`public/rank-image-export.html` 页面是用于生成和导出排行榜图片的前端页面。其主要目的是接收通过 URL 参数传递的排行榜数据 ID，从本地存储（`localStorage`）中获取相应的排行榜数据，然后根据数据动态渲染排行榜界面，并提供将该界面导出为图片的功能。这个页面独立于主应用运行，确保了图片导出过程的简洁和高效。

## 2. 核心功能

1.  **数据加载与解析**: 从 URL 参数获取 `exportId`，并使用该 ID 从 `localStorage` 中检索并解析排行榜数据。
2.  **数据清理**: 包含一个 `cleanupExpiredData` 函数，用于清理 `localStorage` 中过期的排行榜数据，避免数据堆积。
3.  **错误处理**: 如果缺少 ID 参数、找不到数据或数据格式错误，会显示友好的错误信息。
4.  **页面渲染**: 根据解析到的排行榜数据（包括排行榜列表、平台类型、导出数量、标题、时间段等），动态生成 HTML 内容并渲染到页面上。
5.  **图片导出**: 利用 `html2canvas` 库将渲染完成的排行榜区域转换为图片，并提供下载功能。它会优先加载本地的 `html2canvas.min.js`，如果失败则尝试从 CDN 加载。
6.  **平台区分与样式应用**: 根据传入的 `platform` 参数（`wechat`、`douyin`、`weibo`），应用不同的 CSS 样式、图片资源和数据展示逻辑，以适应不同平台的视觉和数据特性。

## 3. 关键代码区域说明

### 3.1 HTML 结构 (`<head>` 和 `<body>` 标签)

- **字体引入**: 通过 `@font-face` 引入 `MiSans-Medium` 字体，确保页面文本显示一致。
  ```html
  9:14:public/rank-image-export.html @font-face { font-family: 'MiSans-Medium'; src: url('/src/assets/iconfont/MiSans-Medium.ttf') format('truetype');
  font-weight: normal; font-style: normal; }
  ```
- **基本样式**: `body` 和 `#app` 定义了页面的基本布局和字体样式。
- **Loading 和 Error 样式**: 用于显示加载状态和错误信息的样式。
- **`rank-image-export` 容器样式**: 这是排行榜图片的主要容器，定义了基础的背景、字体、阴影等样式。
- **控制按钮**: 包含“下载图片”和“返回”按钮。
- **根元素**: `<div id="app">` 是 Vue 应用（尽管这里是原生 JS）的挂载点，所有动态内容都将渲染到此。

### 3.2 JavaScript 逻辑 (`<script>` 标签)

#### 3.2.1 数据加载与初始化

- **`DOMContentLoaded` 事件监听**: 确保 DOM 完全加载后再执行 JS。

  ```javascript
  178:214:public/rank-image-export.html
  document.addEventListener('DOMContentLoaded', function () {
      const app = document.getElementById('app');

      // 清理过期的localStorage数据
      cleanupExpiredData();

      // 获取URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const exportId = urlParams.get('id');

      if (!exportId) {
          showError('缺少导出数据ID参数，请返回排行榜页面重新导出');
          return;
      }

      // 从localStorage获取数据
      const storedData = localStorage.getItem(exportId);
      if (!storedData) {
          showError('找不到导出数据，可能已过期或已被清理。请返回排行榜页面重新导出。');
          return;
      }

      try {
          // 解析数据
          const exportData = JSON.parse(storedData);

          // 验证数据结构
          if (!exportData || !exportData.rankData || !Array.isArray(exportData.rankData) || exportData.rankData.length === 0) {
              throw new Error('导出数据格式无效或为空');
          }

          // 渲染页面
          renderExportPage(exportData, exportId);

      } catch (error) {
          showError('数据格式错误，无法解析: ' + error.message);
      }
  });
  ```

- **`cleanupExpiredData()`**: 遍历 `localStorage`，移除超过 24 小时的数据。

  ```javascript
  217:240:public/rank-image-export.html
  function cleanupExpiredData() {
      const now = Date.now();
      const expireTime = 24 * 60 * 60 * 1000; // 24小时过期

      // 遍历所有localStorage项
      for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key && key.startsWith('rank-export-')) {
              try {
                  const data = JSON.parse(localStorage.getItem(key));
                  // 检查是否有时间戳且已过期
                  if (data && data.timestamp && (now - data.timestamp > expireTime)) {
                      localStorage.removeItem(key);
                      console.log('已清理过期数据:', key);
                  }
              } catch (e) {
                  // 如果解析失败，也删除这个项
                  localStorage.removeItem(key);
                  console.log('已清理无效数据:', key);
              }
          }
      }
  }
  ```

- **`showError(message)`**: 显示错误信息并提供关闭页面的按钮。
  ```javascript
  242:251:public/rank-image-export.html
  function showError(message) {
      const app = document.getElementById('app');
      app.innerHTML = `
          <div class="error">
              <h2>加载失败</h2>
              <p>${message}</p>
              <button onclick="window.close()">关闭页面</button>
          </div>
      `;
  }
  ```

#### 3.2.2 页面渲染 (`renderExportPage` 和 `generateRankHTML`)

- **`renderExportPage(data, exportId)`**:
  - 创建并添加控制按钮（下载、返回）。
  - 创建排行榜容器 `<div id="rankImageExport">`。
  - 根据 `data.platform` 添加平台相关的 CSS 类（例如 `platform-wechat`）。
  - 调用 `generateRankHTML(data)` 生成排行榜的 HTML 内容。
  - 将生成的 HTML 内容插入到页面中。
  - 设置页面标题。
  - 定义 `window.downloadImage` 函数，处理图片下载逻辑。
  ```javascript
  253:378:public/rank-image-export.html
  function renderExportPage(data, exportId) {
      // ... (省略部分代码，详见文件) ...
      window.downloadImage = function () {
          // ... (图片下载逻辑，详见文件) ...
      };
  }
  ```
- **`formatTimePeriod(timePeriod)`**: 格式化时间段显示，将 `YYYY-MM-DD~YYYY-MM-DD` 转换为 `YYYY年MM月DD日~YYYY年MM月DD日`。
  ```javascript
  381:402:public/rank-image-export.html
  function formatTimePeriod(timePeriod) {
      // ... (格式化逻辑，详见文件) ...
  }
  ```
- **`generateRankHTML(data)`**: 这是核心的 HTML 生成函数。

  - 根据 `exportCount` 截取排行榜数据。
  - 根据 `platform` 获取对应的平台名称、列配置 (`columnsMap`)、账号列名称 (`accountColumnMap`)、发布列名称 (`publishColumnMap`) 和指数列名称 (`indexColumnMap`)。
  - 动态生成表格的 `<thead>` 和 `<tbody>` 内容。
  - 根据 `platform` 和 `period` 选择正确的榜单 Logo 图片（例如 `douyin-week.png`）。
  - 生成标题 HTML，支持多行自定义标题 (`titleLine1`, `titleLine2`, `titleLine3`)，并添加固定的“影响力排行榜”副标题。

  ```javascript
  404:598:public/rank-image-export.html
  function generateRankHTML(data) {
      const { rankData, platform, exportCount, title, timePeriod, titleLine1, titleLine2, titleLine3 } = data;

      // ... (平台映射、列配置、账号/发布/指数列名获取，详见文件) ...

      // 生成表头
      let tableHeader = `
          <tr class="table-header">
              <th class="rank-col">排名</th>
              <th class="account-col">${accountColumnName}</th>
              <th class="publish-col">${publishColumnName}</th>
      `;
      columns.forEach(col => {
          tableHeader += `<th class="data-col">${col.label}</th>`;
      });
      tableHeader += `<th class="index-col">${indexColumnName}</th></tr>`;

      // ... (生成表格行，详见文件) ...

      // 根据平台和周期确定使用哪个图片
      let logoSrc = '/rank/week.png'; // 默认周榜图片
      if (platform === 'wechat') {
          logoSrc = data.period === 'weekly' ? '/rank/week.png' : '/rank/month.png';
      } else if (platform === 'douyin') {
          logoSrc = data.period === 'weekly' ? '/rank/douyin-week.png' : '/rank/douyin-month.png';
      } else if (platform === 'weibo') {
          logoSrc = data.period === 'weekly' ? '/rank/weibo-week.png' : '/rank/weibo-month.png';
      }

      // ... (生成标题HTML，详见文件) ...
  }
  ```

- **`formatNumber(num)`**: 格式化数字，对大于 10 万的数字转换为“X.Xw”的格式。
  ```javascript
  601:618:public/rank-image-export.html
  function formatNumber(num) {
      // ... (格式化逻辑，详见文件) ...
  }
  ```

### 3.3 CSS 样式 (`<style>` 标签)

页面的 CSS 样式分为几个主要部分：

- **通用样式**: 针对 `rank-image-export` 容器、头部、表格、底部等的基础样式。
- **平台特定样式**: 这是实现平台区分的关键。通过在 `rank-image-export` 容器上添加 `platform-wechat`、`platform-douyin` 或 `platform-weibo` 类，来覆盖或新增特定平台的样式。

#### 3.3.1 平台区分的实现细节

页面通过在 `rank-image-export` 容器上动态添加 CSS 类（如 `platform-wechat`、`platform-douyin`、`platform-weibo`）来实现不同平台的样式区分。这些类在 CSS 中定义了各自的视觉效果：

1.  **背景和装饰图**:

    - **微信 (`platform-wechat`)**: 默认的绿色渐变背景，以及 `phone-decoration.png` 装饰图。
      ```css
      88:103:public/rank-image-export.html .rank-image-export.platform-wechat {
      	background: linear-gradient(0deg, #5ccc82, #5ccc80);
      }
      .rank-image-export.platform-wechat::after {
      	content: '';
      	position: absolute;
      	top: 21px;
      	right: 0;
      	width: 204px;
      	height: 303px;
      	background-image: url('/rank/phone-decoration.png');
      	background-size: contain;
      	background-repeat: no-repeat;
      	z-index: 0;
      }
      ```
    - **抖音 (`platform-douyin`)**: 深蓝色背景，容器宽度加宽，以及 `douyin-decoration.png` 装饰图。
      ```css
      106:122:public/rank-image-export.html .rank-image-export.platform-douyin {
      	width: 1000px;
      	background: #020c25;
      }
      .rank-image-export.platform-douyin::after {
      	content: '';
      	position: absolute;
      	top: 28px;
      	right: 0;
      	width: 241.4px;
      	height: 303px;
      	background-image: url('/rank/douyin-decoration.png');
      	background-size: contain;
      	background-repeat: no-repeat;
      	z-index: 0;
      }
      ```
    - **微博 (`platform-weibo`)**: 橙色渐变背景，以及 `weibo-decoration.png` 装饰图。
      ```css
      125:140:public/rank-image-export.html .rank-image-export.platform-weibo {
      	background: linear-gradient(0deg, #fa6222 0%, #faaa31 100%);
      }
      .rank-image-export.platform-weibo::after {
      	content: '';
      	position: absolute;
      	top: 21px;
      	right: 0;
      	width: 220.2px;
      	height: 313.3px;
      	background-image: url('/rank/weibo-decoration.png');
      	background-size: contain;
      	background-repeat: no-repeat;
      	z-index: 0;
      }
      ```

2.  **榜单 Logo 图片 (`.logo-img`)**:
    在 `generateRankHTML` 函数中，根据 `platform` 和 `period` 动态设置 `logoSrc`，从而加载不同平台的周榜或月榜图片。

    ```javascript
    526:534:public/rank-image-export.html
    let logoSrc = '/rank/week.png'; // 默认周榜图片
    if (platform === 'wechat') {
        logoSrc = data.period === 'weekly' ? '/rank/week.png' : '/rank/month.png';
    } else if (platform === 'douyin') {
        logoSrc = data.period === 'weekly' ? '/rank/douyin-week.png' : '/rank/douyin-month.png';
    } else if (platform === 'weibo') {
        logoSrc = data.period === 'weekly' ? '/rank/weibo-week.png' : '/rank/weibo-month.png';
    }
    ```

    同时，CSS 也对不同平台的 logo 图片进行了尺寸和位置的调整。

    ```css
    644:658:public/rank-image-export.html
    /* 抖音平台logo样式 */
    .platform-douyin .logo-img {
    	width: 275px;
    	height: 44px;
    	top: 81px;
    	left: 66px;
    }
    /* 微博平台logo样式 */
    .platform-weibo .logo-img {
    	width: 295px;
    	height: 45px;
    	top: 80px;
    	left: 46px;
    }
    ```

3.  **标题样式 (`.header-title h1`, `.header-title h2`)**:
    不同平台对标题的阴影效果有不同的处理。

    ```css
    685:701:public/rank-image-export.html
    /* 抖音平台标题样式（无阴影） */
    .platform-douyin .header-title h1 {
    	text-shadow: none;
    }
    .platform-douyin .header-title h2 {
    	text-shadow: none;
    }
    /* 微博平台标题样式 */
    .platform-weibo .header-title h1 {
    	text-shadow: 0px 3px 7px rgba(215, 68, 3, 0.6);
    }
    .platform-weibo .header-title h2 {
    	text-shadow: 0px 3px 7px rgba(215, 68, 3, 0.6);
    }
    ```

4.  **副标题颜色 (`.subtitle`)**:
    副标题背景颜色是半透明白色，但文本颜色根据平台有所不同。

    ```css
    716:726:public/rank-image-export.html
    /* 抖音平台副标题样式 */
    .platform-douyin .subtitle {
    	background: rgba(255, 255, 255, 0.5);
    	color: #344435;
    }
    /* 微博平台副标题样式 */
    .platform-weibo .subtitle {
    	background: rgba(255, 255, 255, 0.5);
    	color: #de6e0a;
    }
    ```

5.  **表格头背景色 (`.table-header`)**:

    ```css
    760:767:public/rank-image-export.html
    /* 抖音平台表头样式 */
    .platform-douyin .table-header {
    	background: #040e28;
    }
    /* 微博平台表头样式 */
    .platform-weibo .table-header {
    	background: #fc841b;
    }
    ```

6.  **偶数行背景色 (`.table-row:nth-child(even)`)**:

    ```css
    798:807:public/rank-image-export.html
    /* 抖音平台偶数行背景色 */
    .platform-douyin .table-row:nth-child(even) {
    	background-color: #f2f2f2;
    }
    /* 微博平台偶数行背景色 */
    .platform-weibo .table-row:nth-child(even) {
    	background-color: #ffe3ca;
    }
    ```

7.  **指数颜色 (`.rank-index`)**:
    指数数值的颜色也根据平台进行了区分。

    ```css
    889:897:public/rank-image-export.html
    /* 抖音平台DCI指数颜色 */
    .platform-douyin .rank-index {
    	color: #000;
    }
    /* 微博平台BCI指数颜色 */
    .platform-weibo .rank-index {
    	color: #df611e;
    }
    ```

8.  **JS 中的列配置和指数获取**:
    在 `generateRankHTML` 函数中，通过 `columnsMap` 和 `indexColumnMap` 根据 `platform` 选择正确的列显示和指数名称。同时，在遍历数据时，也会根据 `platform` 获取对应的指数值（`item.wci`, `item.dci`, `item.bci`）。

    ```javascript
    425:469:public/rank-image-export.html
    // 获取列配置
    const columnsMap = {
        wechat: [ /* ... */ ],
        douyin: [ /* ... */ ],
        weibo: [ /* ... */ ],
    };
    const columns = columnsMap[platform] || columnsMap.wechat;

    // 获取账号列名称
    const accountColumnMap = {
        wechat: '公众号名称',
        douyin: '抖音号名称',
        weibo: '微博号名称',
    };
    const accountColumnName = accountColumnMap[platform] || '公众号名称';

    // 获取发布列名称
    const publishColumnMap = {
        wechat: '发文数',
        douyin: '发布数',
        weibo: '发博数',
    };
    const publishColumnName = publishColumnMap[platform] || '发文数';

    // 获取指数列名称
    const indexColumnMap = {
        wechat: 'WCI',
        douyin: 'DCI',
        weibo: 'BCI',
    };
    const indexColumnName = indexColumnMap[platform] || 'WCI';

    // ...

    // 获取排行指数
    let rankIndex = 0;
    if (platform === 'wechat') {
        rankIndex = item.rankIndex || item.wci || 0;
    } else if (platform === 'douyin') {
        rankIndex = item.dci || item.rankIndex || 0;
    } else if (platform === 'weibo') {
        rankIndex = item.bci || item.rankIndex || 0;
    }
    ```

通过以上 CSS 类和 JavaScript 逻辑的结合，`public/rank-image-export.html` 实现了根据不同平台动态调整页面布局、视觉风格和数据展示的灵活功能。

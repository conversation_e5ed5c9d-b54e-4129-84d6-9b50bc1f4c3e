import { request } from '/@/utils/service';

export interface WechatOfficialAccountArticleSnapshotType {
	article: string;
	read_count: number;
	viewing_count: number;
	like_count: number;
	forward_count: number;
	reward_count: number;
	snapshot_time: string;
	deleted: boolean;
}

export interface WechatOfficialAccountArticleSnapshotListParams {
	page?: number;
	limit?: number;
	article?: string;
	deleted?: boolean;
	platform_type?: string;
}

export const apiPrefix = '/api/wechat_official_account/snapshots/';

export async function getWechatOfficialAccountArticleSnapshotList(query: WechatOfficialAccountArticleSnapshotListParams) {
	return await request({
		url: apiPrefix,
		method: 'get',
		params: { ...query, platform_type: query.platform_type || 'wechat' },
	});
}

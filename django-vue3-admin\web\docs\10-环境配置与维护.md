# 环境配置与维护指南

## 核心配置文件

### ESLint 配置 (eslint.config.js)
项目使用 ESLint 9.x 和现代化配置格式：

```javascript
// eslint.config.js - 现代化配置格式
import js from '@eslint/js';
import vue from 'eslint-plugin-vue';
import typescript from '@typescript-eslint/eslint-plugin';

export default [
  js.configs.recommended,
  ...vue.configs['flat/recommended'],
  // 配置规则
];
```

**重要约定:**
- 使用 `eslint.config.js` (不是 `.eslintrc.js`)
- 支持 TypeScript ESLint 8.x 版本
- 集成 ignores 配置，无需 `.eslintignore` 文件

### TypeScript 配置要点

**类型安全要求:**
```typescript
// ✅ 正确 - request函数使用泛型
export function getSiteList(params?: SiteQuery) {
  return request<{
    results: SiteData[];
    count: number;
  }>({
    url: '/api/website/sites/',
    method: 'get',
    params,
  });
}

// ✅ 正确 - FastCrud dict配置使用数组
status: {
  title: '状态',
  type: 'dict-select',
  dict: [
    { value: 'active', label: '正常运行' },
    { value: 'inactive', label: '暂停使用' },
  ],
}
```

## 依赖版本兼容性

### 关键依赖版本要求

| 工具 | 版本要求 | 说明 |
|------|----------|------|
| ESLint | 9.x | 主要代码检查工具 |
| @typescript-eslint/eslint-plugin | ^8.0.0 | 兼容 ESLint 9.x |
| @typescript-eslint/parser | ^8.0.0 | 兼容 ESLint 9.x |
| TypeScript | ^4.9.4 | 保持项目一致性 |

### 版本冲突处理

**常见问题:**
```bash
# 错误: ESLint 9.x 与 TypeScript ESLint 5.x 不兼容
npm error ERESOLVE could not resolve
npm error peer eslint@"^6.0.0 || ^7.0.0 || ^8.0.0" from @typescript-eslint/eslint-plugin@5.62.0
```

**解决方案:**
```bash
# 升级到兼容版本
npm install --save-dev @typescript-eslint/eslint-plugin@^8.0.0 @typescript-eslint/parser@^8.0.0
```

## 环境维护最佳实践

### 依赖升级流程

1. **检查兼容性**
   - 查阅官方文档了解兼容版本
   - 测试关键功能是否正常

2. **配置迁移**
   - ESLint 配置迁移到新格式
   - 更新类型定义适配新版本

3. **验证测试**
   ```bash
   npm run build    # 验证构建
   npm run lint-fix # 验证代码检查
   ```

### 路径规范管理

**目录命名约定:**
- 功能模块使用小写目录名 (`site` 不是 `Site`)
- 确保 import 路径与物理路径一致
- Windows 系统注意大小写敏感问题

### 类型定义规范

**API 函数类型:**
```typescript
// utils/service.ts 中的 request 函数必须支持泛型
function createRequestFunction(service: any) {
  return function <T = any>(config: any): Promise<T> {
    // 实现
  };
}
```

**FastCrud 配置类型:**
```typescript
// 字典配置使用数组格式，不是对象格式
dict: Array<{value: string, label: string}>  // ✅ 正确
dict: {data: Array<{value: string, label: string}>}  // ❌ 错误
```

## 故障排查指南

### 构建失败处理

**TypeScript 错误:**
1. 检查 request 函数泛型定义
2. 确认 FastCrud 配置格式
3. 验证导入路径大小写

**ESLint 错误:**
1. 确认使用 eslint.config.js 格式
2. 检查插件版本兼容性
3. 验证规则配置语法

### 开发环境问题

**npm install 失败:**
- 清除缓存: `rm -rf node_modules package-lock.json && npm install`
- 使用兼容版本而非最新版本

**构建超时:**
- 增加内存限制: `--max_old_space_size=4096`
- 检查是否有循环依赖

## 环境检查清单

### 新环境搭建验证
- [ ] npm install 成功执行
- [ ] npm run build 构建成功  
- [ ] npm run lint-fix 代码检查通过
- [ ] 开发服务器正常启动

### 依赖升级后验证
- [ ] 所有 TypeScript 错误解决
- [ ] ESLint 配置工作正常
- [ ] 构建产物生成正确
- [ ] 关键功能页面正常渲染

---

**维护记录:**
- 2025-08-07: 记录 ESLint 9.x 升级和 TypeScript 类型修复经验
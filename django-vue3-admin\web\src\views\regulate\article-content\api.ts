import { request } from '/@/utils/service'

// 定义数据类型接口
export interface ArticleContentAnalysisType {
  id: number
  article_content_id: string
  source: string
  create_meta_info: Record<string, any>
  create_meta_info_reference: string
  create_time: string
  update_time: string
}

// 定义查询参数接口
export interface QueryParams {
  page?: number
  // 默认10
  limit?: number
  article_content_id?: string
  source?: string
  create_time_start?: string
  create_time_end?: string
}

// 定义API前缀
export const apiPrefix = '/api/regulate/article_content_analysis/'

// 获取列表数据
export async function getList(query: QueryParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
} 
<template>
	<el-dialog v-model="dialogVisible" :title="title" width="65%" :close-on-click-modal="false" @close="handleClose">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
			<!-- 任务相关部分 - 仅在任务模式或者目标已选择时显示 -->
			<template v-if="mode === 'task'">
				<!-- 任务名称 - 仅在任务模式下显示 -->
				<el-form-item label="任务名称" prop="name">
					<el-input v-model="form.name" placeholder="请输入任务名称" />
				</el-form-item>

				<!-- 执行类型 - 仅在任务模式下显示 -->
				<!-- <el-form-item label="任务类型" prop="type">
					<el-select v-model="form.type" placeholder="请选择执行类型" style="width: 100%">
						<el-option v-for="item in $getEnumDatas($ENUM.TASK_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item> -->

				<!-- 客户选择 - 仅在任务模式下可选择 -->
				<el-form-item label="客户" prop="tenant_id">
					<el-select v-model="form.tenant_id" placeholder="请选择客户" filterable style="width: 100%" @change="handleTenantChange">
						<el-option v-for="tenant in tenants" :key="tenant.id" :label="tenant.name" :value="tenant.id" />
					</el-select>
				</el-form-item>

				<!-- 备注 - 仅在任务模式下显示 -->
				<el-form-item label="备注" prop="remark">
					<el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
				</el-form-item>
			</template>

			<!-- 目标相关部分 - 仅在目标模式下显示 -->
			<template v-if="mode === 'target'">
				<!-- 关联任务 - 仅在目标模式下显示 -->
				<el-form-item label="关联任务" prop="task_id">
					<el-select v-model="form.task_id" placeholder="请选择关联任务" style="width: 100%" @change="handleTaskChange" :disabled="!!props.task">
						<el-option v-for="task in tasks" :key="task.id" :label="task.name" :value="task.id" />
					</el-select>
				</el-form-item>

				<!-- 显示已选任务的客户 - 仅在目标模式且已选择任务时显示 -->
				<el-form-item label="关联客户" v-if="selectedTask">
					<span>{{ getSelectedTenantName() }}</span>
				</el-form-item>
				<!-- 执行类型 -->
				<!-- <el-form-item label="任务类型" prop="type" v-if="selectedTask">
					<span>{{ $getEnumLabel($ENUM.TASK_TYPE, selectedTask?.type || 0) }}</span>
				</el-form-item> -->
			</template>

			<!-- 校对账号配置 - 在有租户ID时显示 -->
			<el-form-item label="校对账号配置" v-if="form.tenant_id">
				<div class="account-config-container">
					<div class="account-header">
						<el-button type="primary" size="small" @click="handleBatchSetTime" :disabled="selectedRows.length === 0"> 批量设置时间 </el-button>
					</div>

					<el-table :data="accountList" style="width: 100%" border @selection-change="handleSelectionChange" :row-key="getRowKey" :key="tableKey">
						<el-table-column type="selection" width="55" />
						<el-table-column prop="name" label="账号名称" width="200" />
						<el-table-column label="时间设置" min-width="300">
							<template #default="scope">
								<div class="time-range-container">
									<el-date-picker
										v-model="scope.row.startTime"
										type="datetime"
										placeholder="开始时间"
										format="YYYY-MM-DD HH:mm:ss"
										value-format="YYYY-MM-DD HH:mm:ss"
										:disabled="!selectedRows.includes(scope.row)"
										:disabledDate="disabledStartDate"
										@change="validateTimeRange(scope.row)"
									/>
									<span class="time-separator">至</span>
									<el-date-picker
										v-model="scope.row.endTime"
										type="datetime"
										placeholder="结束时间"
										format="YYYY-MM-DD HH:mm:ss"
										value-format="YYYY-MM-DD HH:mm:ss"
										:disabled="!selectedRows.includes(scope.row)"
										:disabledDate="(time: Date) => disabledEndDate(time, scope.row.startTime)"
										@change="validateTimeRange(scope.row)"
									/>
								</div>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</el-form-item>
		</el-form>

		<!-- 批量设置时间弹窗 -->
		<el-dialog v-model="batchTimeDialogVisible" title="批量设置时间" width="500px" append-to-body>
			<el-form :model="batchTimeForm" label-width="100px">
				<el-form-item label="开始时间">
					<el-date-picker
						v-model="batchTimeForm.startTime"
						type="datetime"
						placeholder="开始时间"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						style="width: 100%"
						:disabledDate="disabledStartDate"
						@change="validateBatchTimeRange"
					/>
				</el-form-item>
				<el-form-item label="结束时间">
					<el-date-picker
						v-model="batchTimeForm.endTime"
						type="datetime"
						placeholder="结束时间"
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						style="width: 100%"
						:disabledDate="(time: Date) => disabledEndDate(time, batchTimeForm.startTime)"
						@change="validateBatchTimeRange"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="batchTimeDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="confirmBatchSetTime">确定</el-button>
				</span>
			</template>
		</el-dialog>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :disabled="mode === 'target' && selectedRows.length === 0">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, PropType } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getTenantBindAccount } from '/@/views/tenant/api';
import type { TenantInfo } from '/@/views/tenant/api';
import { getTaskAll } from '../api';
import type { CorrectionTaskInfo } from '../api';
import { ElMessage } from 'element-plus';

// 定义组件模式: 'task' - 新建任务, 'target' - 新建目标
const mode = ref('task');

// 定义属性
const props = defineProps({
	visible: {
		type: Boolean,
		required: true,
	},
	dialogMode: {
		type: String,
		default: 'task',
	},
	tenants: {
		type: Array as () => TenantInfo[],
		required: true,
	},
	task: {
		type: Object as () => CorrectionTaskInfo | null,
		default: null,
	},
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit']);

// 表单引用
const formRef = ref<FormInstance>();
const title = computed(() => (mode.value === 'task' ? '新建任务' : '新增目标'));
const dialogVisible = ref(props.visible);

// 任务列表
const tasks = ref<CorrectionTaskInfo[]>([]);

// 已选择的任务
const selectedTask = computed(() => {
	if (!form.task_id) return null;
	return tasks.value.find((task) => task.id === form.task_id);
});

// 监听visible属性变化
watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val;
		if (val) {
			// 设置模式
			mode.value = props.dialogMode;

			// 重置表单
			resetForm();

			// 如果是编辑目标模式且有任务信息
			if (mode.value === 'target') {
				// 加载任务列表
				loadTasks();

				if (props.task) {
					// 如果是从任务列表点击新建目标进入
					form.task_id = props.task.id;
					// 直接加载该任务的账号列表
					if (props.task.tenant_id) {
						form.tenant_id = props.task.tenant_id;
						loadAccounts(props.task.tenant_id);
					}
				}
			}
		}
	}
);

// 监听dialogVisible变化，同步回父组件
watch(dialogVisible, (val) => {
	emit('update:visible', val);
});

// 表单数据
const form = reactive({
	name: '',
	type: 1,
	tenant_id: undefined as number | undefined,
	remark: '',
	task_id: undefined as number | undefined,
});

// 表单验证规则
const rules = reactive<FormRules>({
	name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
	type: [{ required: true, message: '请选择执行类型', trigger: 'change' }],
	tenant_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
	task_id: [{ required: true, message: '请选择关联任务', trigger: 'change' }],
});

// 账号列表
interface AccountItem {
	media_account_id: string;
	name: string;
	startTime: string;
	endTime: string;
}

const accountList = ref<AccountItem[]>([]);
const selectedRows = ref<AccountItem[]>([]);

// 批量设置时间相关
const batchTimeDialogVisible = ref(false);
const batchTimeForm = reactive({
	startTime: '',
	endTime: '',
});

// 加载任务列表
const loadTasks = async () => {
	try {
		const res = await getTaskAll();
		tasks.value = res.data;
	} catch (error) {
		console.error('获取全部任务列表失败', error);
	}
};

// 获取已选任务对应的租户名称
const getSelectedTenantName = (): string => {
	if (!selectedTask.value || !selectedTask.value.tenant_id) return '未知客户';

	const tenant = props.tenants.find((t) => t.id === selectedTask.value?.tenant_id);
	return tenant ? tenant.name : '未知客户';
};

// 处理任务变更，根据当前选择的任务数据，填写目标表单
const handleTaskChange = async (taskId: number) => {
	if (!taskId) {
		form.tenant_id = undefined;
		accountList.value = [];
		return;
	}

	// 从任务列表中获取选中的任务
	const selectedTask = tasks.value.find((task) => task.id === taskId);
	if (selectedTask?.tenant_id) {
		// 设置租户ID并加载账号
		form.tenant_id = selectedTask.tenant_id;
		await loadAccounts(selectedTask.tenant_id);
	}
};

// 处理租户变更
const handleTenantChange = async (tenantId: number) => {
	if (!tenantId) {
		accountList.value = [];
		return;
	}

	await loadAccounts(tenantId);
};

// 加载账号列表
const loadAccounts = async (tenantId: number) => {
	if (!tenantId) return;

	try {
		const res = await getTenantBindAccount(tenantId);
		// 将账号数据转换为账号项
		accountList.value = res.data.map((account: any) => ({
			media_account_id: account.media_account_id,
			name: account.name || `账号${account.media_account_id}`,
			startTime: '',
			endTime: '',
		}));
		selectedRows.value = []; // 重置选中行
	} catch (error) {
		console.error('获取租户绑定账号失败', error);
	}
};

// 处理表格选择变更
const handleSelectionChange = (selection: AccountItem[]) => {
	selectedRows.value = selection;
};

// 处理批量设置时间
const handleBatchSetTime = () => {
	if (selectedRows.value.length === 0) return;

	// 如果有选中的账号，则打开批量设置时间弹窗
	batchTimeForm.startTime = '';
	batchTimeForm.endTime = '';
	batchTimeDialogVisible.value = true;
};

// 确认批量设置时间
const confirmBatchSetTime = () => {
	if (!batchTimeForm.startTime || !batchTimeForm.endTime) {
		return;
	}

	// 为所有选中的账号设置相同的时间
	selectedRows.value.forEach((account) => {
		account.startTime = batchTimeForm.startTime;
		account.endTime = batchTimeForm.endTime;
	});

	batchTimeDialogVisible.value = false;
};

// 重置表单
const resetForm = () => {
	form.name = '';
	form.type = 1;
	form.tenant_id = undefined;
	form.remark = '';
	form.task_id = undefined;
	accountList.value = [];
	selectedRows.value = [];
};

// 关闭弹窗
const handleClose = () => {
	formRef.value?.resetFields();
	resetForm();
	emit('update:visible', false);
};

// 添加校验账号时间设置的函数
const validateAccountsTime = (): { valid: boolean; message: string } => {
	if (selectedRows.value.length === 0) {
		return {
			valid: false,
			message: '请选择需要校对的账号',
		};
	}

	const invalidAccounts = selectedRows.value.filter((account) => !account.startTime || !account.endTime);
	if (invalidAccounts.length > 0) {
		return {
			valid: false,
			message: `请为选中的账号设置时间范围：${invalidAccounts.map((acc) => acc.name).join('、')}`,
		};
	}

	return {
		valid: true,
		message: '',
	};
};

// 修改提交表单函数
const handleSubmit = async () => {
	if (!formRef.value) return;

	await formRef.value.validate(async (valid) => {
		if (valid) {
			// 校验账号时间设置
			const { valid: accountsValid, message } = validateAccountsTime();
			if (!accountsValid) {
				ElMessage.warning(message);
				return;
			}
			// 构建账号时间配置
			const accountsConfig = selectedRows.value
				.filter((account) => account.startTime && account.endTime)
				.map((account) => ({
					media_account_id: account.media_account_id,
					start_time: account.startTime,
					end_time: account.endTime,
				}));

			// 根据模式构建不同的提交数据
			if (mode.value === 'task') {
				// 新建任务
				const submitData = {
					tenant_id: form.tenant_id!,
					name: form.name,
					type: form.type,
					remark: form.remark,
					targets: accountsConfig,
				};
				emit('submit', submitData);
			} else {
				// 新增目标
				const submitData = {
					items: accountsConfig.map((account) => ({
						...account,
						task_id: form.task_id!,
					})),
				};
				emit('submit', submitData);
			}

			handleClose();
		}
	});
};

// 组件挂载时
onMounted(() => {
	if (props.visible) {
		// 设置模式
		mode.value = props.dialogMode;

		// 加载任务列表（仅目标模式）
		if (mode.value === 'target') {
			loadTasks();
		}
	}
});

// 时间校验函数
const disabledStartDate = (time: Date) => {
	return time.getTime() > Date.now();
};

const disabledEndDate = (time: Date, startTime: string) => {
	if (!startTime) return false;
	return time.getTime() < new Date(startTime).getTime();
};

const validateTimeRange = (row: AccountItem) => {
	if (row.startTime && row.endTime) {
		const start = new Date(row.startTime).getTime();
		const end = new Date(row.endTime).getTime();
		if (start > end) {
			row.endTime = '';
		}
	}
};

// 添加批量时间校验函数
const validateBatchTimeRange = () => {
	if (batchTimeForm.startTime && batchTimeForm.endTime) {
		const start = new Date(batchTimeForm.startTime).getTime();
		const end = new Date(batchTimeForm.endTime).getTime();
		if (start > end) {
			batchTimeForm.endTime = '';
		}
	}
};

// 添加row-key属性和tableKey强制重新渲染机制
const getRowKey = (row: AccountItem) => row.media_account_id;
const tableKey = ref(0);
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.account-config-container {
	border: 1px solid #ebeef5;
	border-radius: 4px;
	padding: 10px;
	width: 100%;
}

.account-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}

.time-range-container {
	display: flex;
	align-items: center;
	gap: 5px;
}

.time-separator {
	margin: 0 5px;
}
</style> 
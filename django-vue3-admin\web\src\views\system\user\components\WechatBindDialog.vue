<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isBound ? '微信绑定信息' : '绑定微信公众号'"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <!-- 已绑定状态：显示绑定信息 -->
    <div v-if="isBound" class="bind-info-container">
      <div class="bind-info">
        <el-icon class="wechat-icon" color="#07C160" size="32">
          <svg viewBox="0 0 1024 1024">
            <path d="M331.5 288.8c22.6 0 37.7 15.1 37.7 37.7s-15.1 37.7-37.7 37.7-37.7-15.1-37.7-37.7 15.1-37.7 37.7-37.7z m113.2 0c22.6 0 37.7 15.1 37.7 37.7s-15.1 37.7-37.7 37.7-37.7-15.1-37.7-37.7 15.1-37.7 37.7-37.7z m-56.6-113.2c-135.5 0-248.4 83.5-296.8 201.4 22.6 7.5 41.5 15.1 56.6 22.6l94.3-188.6h145.9z m384 169.8c0-22.6-15.1-37.7-37.7-37.7s-37.7 15.1-37.7 37.7 15.1 37.7 37.7 37.7 37.7-15.1 37.7-37.7z m113.2 0c0-22.6-15.1-37.7-37.7-37.7s-37.7 15.1-37.7 37.7 15.1 37.7 37.7 37.7 37.7-15.1 37.7-37.7z m56.6 188.6c0-30.2-7.5-60.4-22.6-83l-37.7 98.1c-26.4-7.5-49-18.9-71.7-30.2 120.8-90.6 120.8-241.5 0-332.1-45.3-34-98.1-52.8-154.7-52.8s-109.4 18.9-154.7 52.8c-120.8 90.6-120.8 241.5 0 332.1 45.3 34 98.1 52.8 154.7 52.8 30.2 0 60.4-7.5 86.8-18.9l98.1 37.7c11.3-15.1 18.9-34 18.9-56.6z"/>
          </svg>
        </el-icon>
        <div class="bind-details">
          <div class="bind-title">已绑定微信账号</div>
          <div class="bind-username">{{ boundWechatInfo.nickname || boundWechatInfo.username }}</div>
        </div>
      </div>
      <div class="bind-actions">
        <el-button type="danger" @click="handleUnbind" :loading="unbindLoading">
          解除绑定
        </el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </div>

    <!-- 未绑定状态：显示二维码 -->
    <div v-else class="qrcode-container">
      <div ref="qrcodeRef" class="qrcode"></div>
      <div class="qrcode-tip">请使用微信扫描二维码进行绑定</div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, watch } from 'vue';
import QRCode from 'qrcodejs2-fixes';
import { request } from '/@/utils/service';
import { ElMessage, ElMessageBox } from 'element-plus';

export default defineComponent({
  name: 'WechatBindDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: [Number, String],
      required: true
    }
  },
  emits: ['update:visible', 'bind-success'],
  setup(props, { emit }) {
    const dialogVisible = ref(false);
    const qrcodeRef = ref<HTMLElement | null>(null);
    const qrcodeUrl = ref('');
    const bindKey = ref('');
    const isBound = ref(false);
    const boundWechatInfo = ref<any>({});
    const unbindLoading = ref(false);
    
    // 监听visible属性变化
    watch(() => props.visible, (newVal) => {
      console.log('WechatBindDialog visible 属性变化', newVal);
      console.log('props.userId =', props.userId);
      dialogVisible.value = newVal;
      if (newVal) {
        // 当对话框显示时，先检查绑定状态
        console.log('准备检查绑定状态');
        checkBindStatus();
      } else {
        // 当对话框隐藏时，停止轮询
        console.log('对话框隐藏，停止轮询');
        stopPolling();
      }
    });
    
    // 监听dialogVisible变化，同步更新父组件的visible属性
    watch(dialogVisible, (newVal) => {
      console.log('dialogVisible 变化，新值 =', newVal);
      console.log('发送 update:visible 事件');
      emit('update:visible', newVal);
      if (!newVal) {
        // 当对话框关闭时，停止轮询
        console.log('dialogVisible为false，停止轮询');
        stopPolling();
      }
    });
    
    // 检查绑定状态
    const checkBindStatus = async () => {
      console.log('checkBindStatus 方法被调用，userId =', props.userId);
      try {
        const response = await request({
          url: '/api/hyqm/user-wechat-bind/',
          method: 'get',
          params: {
            user_id: props.userId
          }
        });
        
        console.log('绑定状态检查响应:', response);
        
        if (response.code === 2000 && response.data) {
          const isAlreadyBound = Array.isArray(response.data) && response.data.length > 0;
          console.log('绑定状态检查结果:', { isAlreadyBound, dataLength: response.data.length });
          
          if (isAlreadyBound) {
            // 已绑定，显示绑定信息
            isBound.value = true;
            boundWechatInfo.value = response.data[0]; // 假设取第一个绑定的微信账号
            console.log('用户已绑定微信:', boundWechatInfo.value);
          } else {
            // 未绑定，显示二维码
            isBound.value = false;
            getQRCode();
          }
        } else {
          console.log('检查绑定状态响应异常，显示二维码');
          isBound.value = false;
          getQRCode();
        }
      } catch (error) {
        console.error('检查绑定状态失败:', error);
        // 出错时默认显示二维码
        isBound.value = false;
        getQRCode();
      }
    };

    // 获取绑定二维码
    const getQRCode = async () => {
      console.log('getQRCode 方法被调用，userId =', props.userId);
      try {
        console.log('准备发送请求获取二维码');
        const response = await request({
          url: '/api/hyqm/user-wechat-qrcode/',
          method: 'get',
          params: {
            user_id: props.userId
          }
        });
        
        console.log('获取二维码响应:', response);
        // 根据后端API的实际响应格式调整
        if (response.code === 2000) { // 使用实际接口返回的状态码
          qrcodeUrl.value = response.data.qrcode_url;
          bindKey.value = response.data.bind_key;
          console.log('二维码URL:', qrcodeUrl.value);
          console.log('绑定Key:', bindKey.value);
          
          // 生成二维码
          generateQRCode();
          
          // 开始轮询检查绑定状态
          startPollingBindStatus();
        } else {
          console.error('获取二维码失败，响应码:', response.code, '消息:', response.msg);
          ElMessage.error(response.msg || '获取二维码失败');
        }
      } catch (error) {
        console.error('获取二维码失败:', error);
        ElMessage.error('获取二维码失败，请稍后重试');
      }
    };
    
    // 生成二维码
    const generateQRCode = () => {
      if (qrcodeRef.value) {
        qrcodeRef.value.innerHTML = '';
        // 直接显示微信返回的二维码图片，不使用QRCode库重新生成
        const img = document.createElement('img');
        img.src = qrcodeUrl.value;
        img.style.width = '200px';
        img.style.height = '200px';
        img.alt = '微信绑定二维码';
        qrcodeRef.value.appendChild(img);
      }
    };
    
    // 轮询检查绑定状态
    let pollingTimer: number | null = null;
    const startPollingBindStatus = () => {
      // 清除之前的定时器
      if (pollingTimer) {
        clearInterval(pollingTimer);
      }
      
      // 每5秒检查一次绑定状态
      pollingTimer = window.setInterval(async () => {
        try {
          console.log('正在检查绑定状态, userId =', props.userId);
          const response = await request({
            url: '/api/hyqm/user-wechat-bind/',
            method: 'get',
            params: {
              user_id: props.userId
            }
          });
          
          console.log('绑定状态检查响应:', response);
          
          // 根据后端API的实际响应格式调整
          if (response.code === 2000 && response.data) {
            // 检查是否已绑定（根据实际API响应结构调整判断条件）
            // 后端返回的是数组格式，如果数组有数据说明已绑定
            const isBound = Array.isArray(response.data) && response.data.length > 0;
            console.log('绑定状态检查结果:', { isBound, dataLength: response.data.length });
            
            if (isBound) {
              console.log('检测到绑定成功，停止轮询');
              // 绑定成功，停止轮询
              stopPolling();
              
              // 关闭对话框
              dialogVisible.value = false;
              
              // 通知父组件绑定成功
              emit('bind-success');
              
              ElMessage.success('微信绑定成功');
            }
          } else {
            console.log('绑定状态检查响应异常:', response);
          }
        } catch (error) {
          console.error('检查绑定状态失败:', error);
        }
      }, 5000);
    };
    
    // 停止轮询
    const stopPolling = () => {
      if (pollingTimer) {
        clearInterval(pollingTimer);
        pollingTimer = null;
      }
    };
    
    // 解除绑定
    const handleUnbind = async () => {
      if (!boundWechatInfo.value.id) {
        ElMessage.error('绑定信息不完整，无法解绑');
        return;
      }
      
      // 确认解绑
      try {
        await ElMessageBox.confirm(
          `确定要解除与微信账号的绑定吗？`,
          '确认解绑',
          {
            confirmButtonText: '确定解绑',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        );
      } catch {
        // 用户取消解绑
        return;
      }
      
      unbindLoading.value = true;
      try {
        const response = await request({
          url: `/api/hyqm/user-wechat-bind/${boundWechatInfo.value.id}/`,
          method: 'delete'
        });
        
        if (response.code === 2000) {
          ElMessage.success('解绑成功');
          // 重置状态
          isBound.value = false;
          boundWechatInfo.value = {};
          // 关闭对话框
          dialogVisible.value = false;
          // 通知父组件
          emit('bind-success');
        } else {
          ElMessage.error(response.msg || '解绑失败');
        }
      } catch (error) {
        console.error('解绑失败:', error);
        ElMessage.error('解绑失败，请稍后重试');
      } finally {
        unbindLoading.value = false;
      }
    };
    
    // 组件挂载时
    onMounted(() => {
      // 如果对话框已经显示，则检查绑定状态
      if (props.visible) {
        checkBindStatus();
      }
    });
    
    // 组件卸载时清除定时器
    onUnmounted(() => {
      stopPolling();
    });
    
    return {
      dialogVisible,
      qrcodeRef,
      isBound,
      boundWechatInfo,
      unbindLoading,
      handleUnbind
    };
  }
});
</script>

<style scoped>
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.qrcode {
  margin-bottom: 20px;
}

.qrcode-tip {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.bind-info-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.bind-info {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  width: 100%;
}

.wechat-icon {
  margin-right: 15px;
  flex-shrink: 0;
}

.bind-details {
  flex: 1;
}

.bind-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.bind-username {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.bind-actions {
  display: flex;
  gap: 10px;
}
</style>
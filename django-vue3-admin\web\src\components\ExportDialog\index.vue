<template>
	<el-dialog v-model="dialogVisible" title="导出配置" width="50%" :close-on-click-modal="false" destroy-on-close>
		<el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="export-form">
			<el-form-item label="平台" v-if="showPlatformInfo">
				<el-tag :type="getPlatformTagType(platform)">{{ getPlatformName(platform) }}</el-tag>
			</el-form-item>

			<el-form-item label="是否导出作品" prop="export_works" v-if="showExportWorksOption">
				<el-radio-group v-model="form.export_works" @change="handleExportWorksChange">
					<el-radio :value="true">导出作品数据</el-radio>
					<el-radio :value="false">不导出作品数据</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item label="导出字段" prop="export_columns" v-if="!showExportWorksOption || form.export_works">
				<div class="export-header">
					<el-checkbox v-model="allColumnsSelected" @change="handleSelectAllColumns">全选</el-checkbox>
					<span class="field-count">(已选择 {{ checkedColumns.length }} 个字段)</span>
				</div>
				<div class="export-columns-container">
					<el-checkbox-group v-model="checkedColumns" @change="handleColumnsChange" class="checkbox-grid">
						<el-checkbox v-for="item in exportOptions" :key="item.value" :value="item.value">
							{{ item.label }}
						</el-checkbox>
					</el-checkbox-group>
				</div>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认导出</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getPlatformExportOptions, getDefaultSelectedFields } from '/@/views/statistics/rank/types';

// 定义属性
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	platform: {
		type: String,
		required: true,
		validator: (value) => ['wechat', 'douyin', 'weibo'].includes(value),
	},
	showPlatformInfo: {
		type: Boolean,
		default: true,
	},
	showExportWorksOption: {
		type: Boolean,
		default: false,
	},
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm']);

// 表单引用
const formRef = ref();

// 状态
const dialogVisible = ref(props.modelValue);
const submitLoading = ref(false);
const checkedColumns = ref([]);

// 获取平台名称
const getPlatformName = (platform) => {
	const platformNames = {
		wechat: '微信公众号',
		douyin: '抖音',
		weibo: '微博',
	};
	return platformNames[platform] || platform;
};

// 获取平台标签类型
const getPlatformTagType = (platform) => {
	const tagTypes = {
		wechat: 'success',
		douyin: 'warning',
		weibo: 'danger',
	};
	return tagTypes[platform] || 'info';
};

// 导出选项列表（根据平台动态生成）
const exportOptions = computed(() => {
	return getPlatformExportOptions(props.platform);
});

// 全选状态
const allColumnsSelected = computed({
	get: () => {
		const allOptions = exportOptions.value.map((item) => item.value);
		return checkedColumns.value.length === allOptions.length && allOptions.length > 0;
	},
	set: (val) => {
		handleSelectAllColumns(val);
	},
});

// 表单数据
const form = ref({
	export_works: true,
	export_columns: '',
});

// 表单验证规则
const rules = computed(() => {
	const baseRules = {};

	// 如果显示导出作品选项，则添加相应验证
	if (props.showExportWorksOption) {
		baseRules.export_works = [
			{
				required: true,
				message: '请选择是否导出作品',
				trigger: 'change',
			},
		];

		// 只有在选择导出作品时才验证字段选择
		if (form.value.export_works) {
			baseRules.export_columns = [
				{
					required: true,
					message: '请至少选择一个导出字段',
					trigger: 'change',
				},
			];
		}
	} else {
		// 不显示导出作品选项时，直接验证字段选择
		baseRules.export_columns = [
			{
				required: true,
				message: '请至少选择一个导出字段',
				trigger: 'change',
			},
		];
	}

	return baseRules;
});

// 监听外部modelValue属性变化
watch(
	() => props.modelValue,
	(val) => {
		dialogVisible.value = val;
		if (val) {
			// 打开弹窗时初始化数据
			resetForm();
		}
	}
);

// 监听本地dialogVisible状态变化，同步回父组件
watch(dialogVisible, (val) => {
	emit('update:modelValue', val);
	if (!val) {
		// 关闭弹窗时重置表单
		resetForm();
	}
});

// 监听平台变化，重新初始化字段选择
watch(
	() => props.platform,
	() => {
		if (dialogVisible.value) {
			initializeFields();
		}
	}
);

// 处理是否导出作品变化
const handleExportWorksChange = (value) => {
	form.value.export_works = value;
	if (!value) {
		// 不导出作品时，清空字段选择
		checkedColumns.value = [];
		form.value.export_columns = '';
	} else {
		// 导出作品时，恢复默认字段选择
		initializeFields();
	}
};

// 处理列选择变化
const handleColumnsChange = (values) => {
	checkedColumns.value = values;
	form.value.export_columns = values.join(',');
};

// 处理全选
const handleSelectAllColumns = (val) => {
	if (val) {
		const allValues = exportOptions.value.map((item) => item.value);
		checkedColumns.value = [...allValues];
		form.value.export_columns = allValues.join(',');
	} else {
		checkedColumns.value = [];
		form.value.export_columns = '';
	}
};

// 初始化字段选择
const initializeFields = () => {
	// 根据平台获取默认选中的字段
	const defaultFields = getDefaultSelectedFields(props.platform);
	checkedColumns.value = [...defaultFields];
	form.value.export_columns = defaultFields.join(',');
};

// 重置表单
const resetForm = () => {
	if (formRef.value) {
		formRef.value.resetFields();
	}

	// 重新设置默认值
	form.value = {
		export_works: true,
		export_columns: '',
	};

	// 重新初始化字段选择
	initializeFields();
};

// 表单提交
const handleSubmit = async () => {
	if (!formRef.value) return;

	try {
		await formRef.value.validate();

		submitLoading.value = true;

		// 检查字段选择
		const shouldExportWorks = props.showExportWorksOption ? form.value.export_works : true;
		if (shouldExportWorks && checkedColumns.value.length === 0) {
			ElMessage.warning('请至少选择一个导出字段');
			submitLoading.value = false;
			return;
		}

		// 构建导出参数
		const exportParams = {
			export_works: shouldExportWorks,
			export_columns: shouldExportWorks ? checkedColumns.value : [],
			platform: props.platform,
		};

		// 触发确认事件
		emit('confirm', exportParams);

		// 注意：这里不关闭弹窗，由父组件在导出成功后关闭
	} catch (error) {
		console.error('表单验证失败', error);
		ElMessage.warning('请检查表单配置');
		submitLoading.value = false;
	}
};

// 设置loading状态（供父组件调用）
const setLoading = (loading) => {
	submitLoading.value = loading;
};

// 关闭弹窗（供父组件调用）
const closeDialog = () => {
	dialogVisible.value = false;
};

// 初始化
onMounted(() => {
	// 初始化字段选择
	initializeFields();
});

// 导出组件接口
defineExpose({
	dialogVisible,
	handleSubmit,
	resetForm,
	setLoading,
	closeDialog,
});
</script>

<style lang="scss" scoped>
.export-form {
	max-height: 65vh;
	overflow-y: auto;
	padding-right: 10px;
}

.export-header {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
	margin-right: 20px;

	.field-count {
		margin-left: 12px;
		color: #909399;
		font-size: 12px;
	}
}

.export-columns-container {
	padding: 15px;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	background-color: #f9fafc;
	margin-top: 5px;
	width: 100%;
}

.checkbox-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 12px 20px;

	.el-checkbox {
		margin-right: 0;
		display: flex;
		align-items: center;

		:deep(.el-checkbox__label) {
			font-size: 14px;
		}
	}

	@media (max-width: 768px) {
		grid-template-columns: repeat(2, 1fr);
	}
}

.form-item-tip {
	font-size: 12px;
	color: #909399;
	margin-top: 4px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}
</style>
<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from 'vue';
import { Check, Close } from '@element-plus/icons-vue';
import flowRecord from './components/flowRecord.vue';
import preChangeContent from './components/preChangeContent.vue';
import ProcessRender from '../../wflow/design/process/ProcessRender.vue';
import { request } from '/@/utils/service';
import { ElMessage } from 'element-plus';

// 定义Props类型
interface Props {
	modelValue: boolean;
	items?: {
		id: string | number;
		flow_type?: string;
		[key: string]: any;
	};
}

// 定义Emits类型
interface Emits {
	(e: 'update:modelValue', value: boolean): void;
	(e: 'handleSubmit'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 定义响应数据类型
interface ObjData {
	id?: string | number;
	name: string;
	status: number;
	no: string;
	start_user_name: string;
	create_datetime: string;
	[key: string]: any;
}

const objData = ref<ObjData>({
	name: '',
	status: 0,
	no: '',
	start_user_name: '',
	create_datetime: '',
});

const flowVisiable = computed({
	get() {
		return props.modelValue;
	},
	set(val: boolean) {
		emit('update:modelValue', val);
	},
});

const getObj = () => {
	if (!props.items?.id) return;

	request({
		url: `/api/dvadmin3_flow/flow_data/${props.items.id}/`,
		method: 'get',
	}).then((res: any) => {
		const { data } = res;
		objData.value = data;
	});
};

const activeName = ref('formData');
const zoom = ref(100);
const processData = ref();

const handleVisiable = ref(false);
const handleOpinions = ref('');
const handleMode = ref<'pass' | 'reject'>('pass');

const onHandler = (type: 'pass' | 'reject') => {
	handleVisiable.value = true;
	handleMode.value = type;
	handleOpinions.value = '';
};

const setStatus = (val: number) => {
	return [
		{ value: 0, label: '进行中', type: 'primary' },
		{ value: 1, label: '审核通过', type: 'success' },
		{ value: 2, label: '审核驳回', type: 'danger' },
		// { value: 3, label: '审核撤销', type: 'warning' },
	].find((item) => item.value === val);
};

const handleSubmit = () => {
	handleVisiable.value = false;

	if (!objData.value.id) return;

	if (handleMode.value === 'pass') {
		request({
			url: `/api/dvadmin3_flow/flow_data/${objData.value.id}/handle_pass/`,
			method: 'post',
			data: {
				reason: handleOpinions.value,
			},
		}).then((res: any) => {
			ElMessage.success('审核通过');
			handleVisiable.value = false;
			getFlowRecord();
			getObj();
		});
	} else {
		request({
			url: `/api/dvadmin3_flow/flow_data/${objData.value.id}/handle_reject/`,
			method: 'post',
			data: {
				reason: handleOpinions.value,
			},
		}).then((res: any) => {
			ElMessage.success('审核驳回');
			handleVisiable.value = false;
			getFlowRecord();
			getObj();
		});
	}
};

const changeContent = ref();

// 获取预改变数据
const getPreChangeContent = () => {
	if (!props.items?.id) return Promise.resolve();

	return request({
		url: `/api/dvadmin3_flow/flow_data/${props.items.id}/get_pre_change_content/`,
		method: 'get',
	}).then((res: any) => {
		const { data } = res;
		changeContent.value = data;
	});
};

const recordList = ref();

//获取流转记录
const getFlowRecord = () => {
	if (!props.items?.id) return Promise.resolve();

	return request({
		url: `/api/dvadmin3_flow/flow_data/${props.items.id}/get_flow_record/`,
		method: 'get',
	}).then((res: any) => {
		const { data } = res;
		recordList.value = data;
	});
};

// 获取流程图
const getFlowProcess = () => {
	if (!props.items?.id) return Promise.resolve();

	return request({
		url: `/api/dvadmin3_flow/flow_data/${props.items.id}/get_flow_process/`,
		method: 'get',
	}).then((res: any) => {
		const { data } = res;
		processData.value = data;
	});
};

onMounted(() => {
	getObj();
	getPreChangeContent();
});

const onChangeTab = (tab: any) => {
	const { name } = tab.props;
	if (name === 'flowImg') {
		getFlowProcess();
	} else if (name === 'flowRecord') {
		getFlowRecord();
	} else if (name === 'formData') {
		getPreChangeContent();
	}
};
</script>

<template>
	<div v-if="flowVisiable">
		<el-drawer v-model="flowVisiable" title="流程实例详情" size="40%">
			<div class="flow-dialog">
				<div>
					<span class="flow-dialog-title">{{ objData.name }}</span>
					<el-tag v-if="setStatus(objData.status)" :type="setStatus(objData.status)!.type as any">
						{{ setStatus(objData.status)!.label }}
					</el-tag>
				</div>
				<div class="flow-dialog-no">审批号:{{ objData.no }}</div>
				<div>
					<span>{{ objData.start_user_name }}</span>
					<el-divider direction="vertical" />
					<span class="flow-dialog-time">提交于 {{ objData.create_datetime }}</span>
				</div>
				<div class="flow-dialog-tabs">
					<el-tabs v-model="activeName" @tab-click="onChangeTab">
						<el-tab-pane label="数据信息" name="formData">
							<preChangeContent v-if="changeContent" v-model="changeContent"></preChangeContent>
						</el-tab-pane>
						<el-tab-pane label="流转记录" name="flowRecord">
							<flowRecord v-model="recordList" :status="objData.status"></flowRecord>
						</el-tab-pane>
						<el-tab-pane label="流程图" name="flowImg">
							<process-render ref="processRender" :style="`transform: scale(${zoom / 100})`" v-model="processData" :readonly="true" />
						</el-tab-pane>
					</el-tabs>
				</div>
			</div>
			<template #footer v-if="objData.status == 0 && objData.can_handle">
				<el-divider></el-divider>
				<div class="flow-dialog-footer">
					<div>
						<el-button type="success" :icon="Check" @click="onHandler('pass')">同意</el-button>
					</div>
					<div>
						<el-button type="danger" :icon="Close" @click="onHandler('reject')">拒绝</el-button>
					</div>
				</div>
			</template>
		</el-drawer>

		<div>
			<el-dialog v-model="handleVisiable" title="同意" width="30%">
				<template #header>
					<div style="height: 30px; font-size: 1.2em">
						{{ handleMode === 'pass' ? '同意' : '拒绝' }}
						<el-divider></el-divider>
					</div>
				</template>
				<el-form label-position="top">
					<el-form-item label="审批意见">
						<el-input v-model="handleOpinions" :rows="3" type="textarea" placeholder="请输入处理意见" />
					</el-form-item>
				</el-form>

				<template #footer>
					<div>
						<el-button @click="handleVisiable = false">取消</el-button>
						<el-button type="primary" @click="handleSubmit">确定</el-button>
					</div>
				</template>
			</el-dialog>
		</div>
	</div>
</template>

<style scoped lang="less">
.flow-dialog {
	padding: 20px;
	color: #5c5c5c;
	.flow-dialog-title {
		font-size: 1.2em;
		font-weight: bold;
		margin-right: 10px;
	}
	.flow-dialog-no {
		font-size: 1.1em;
		margin: 20px 0;
	}
	.flow-dialog-tabs {
		margin-top: 10px;
	}
}
.flow-dialog-footer {
	height: 50px;
	display: flex;
	justify-content: flex-start;
	justify-items: center;
	gap: 10px;
	padding: 0 20px;
}
</style>

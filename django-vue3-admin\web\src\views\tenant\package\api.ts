import { request } from '/@/utils/service';
import { PageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/system/tenant_package/';

// 租户套餐列表数据类型
export interface PackageItemType {
	id?: string | number;
	name: string; // 套餐名称，必填，最大30字符
	menu_ids: string; // 关联的菜单编号，多个用逗号分隔，必填，最大2048字符
	description?: string; // 描述，可选，最大255字符
	modifier?: string; // 修改人，可选，最大255字符
	dept_belong_id?: string; // 数据归属部门，可选，最大255字符
	status: number; // 租户状态（0正常 1停用），必填
	remark?: string; // 备注，可选，最大256字符
	creator?: number; // 创建人，可选
	create_datetime?: string;
	update_datetime?: string;
	modifier_name?: string;
	creator_name?: string;
}

// 套餐权限配置抽屉组件参数数据类型
export interface PackageDrawerType {
	/** 是否显示抽屉*/
	drawerVisible: boolean;
	/** 套餐id*/
	packageId: string | number | undefined;
	/** 套餐名称*/
	packageName: string | undefined;
}

// 定义查询参数接口
export interface PackageQueryParams extends PageQuery {
	name?: string;
	status?: boolean;
}

// 菜单权限数据类型
export interface MenuPermissionType {
	id: string | number;
	name: string;
	parent: string | number | null;
	icon?: string;
	path?: string;
	component?: string;
	is_catalog: boolean;
	children?: MenuPermissionType[];
	buttons?: ButtonPermissionType[];
	fields?: FieldPermissionType[];
	checked?: boolean; // 是否选中
	checkedButtons?: number[]; // 选中的按钮ID列表
}

// 按钮权限数据类型
export interface ButtonPermissionType {
	id: string | number;
	name: string;
	value: string;
	checked?: boolean; // 是否选中
}

// 字段权限数据类型
export interface FieldPermissionType {
	id: string | number;
	name: string;
	field_name: string;
	is_query?: boolean;
	is_create?: boolean;
	is_update?: boolean;
	checked?: boolean; // 是否选中
	checkedPermissions?: string[]; // 选中的权限类型列表
}

// 获取套餐列表
export function getList(query: PackageQueryParams) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 获取单个套餐详情
export function getObj(id: InfoReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

// 创建套餐
export function addObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新套餐
export function updateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 删除套餐
export function delObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

// 获取套餐菜单权限数据（新增时不传packageId，编辑时传packageId）
export function getPackageMenuPermissions(packageId?: string | number) {
	const url = packageId ? `${apiPrefix}get_package_menu/?packageId=${packageId}` : `${apiPrefix}get_package_menu/`;
	return request({
		url,
		method: 'get',
	});
}

// 更新套餐权限配置
export function updatePackagePermissions(packageId: number | string, permissions: any) {
	return request({
		url: apiPrefix + packageId + '/permissions/',
		method: 'put',
		data: permissions,
	});
}

// 获取套餐列表
export function getAllPackageList() {
	return request({
		url: apiPrefix + 'select_list/',
		method: 'get',
	});
}
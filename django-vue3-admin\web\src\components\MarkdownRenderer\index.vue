<template>
	<div class="markdown-renderer" v-html="renderedMarkdown"></div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

const props = defineProps({
	content: {
		type: String,
		default: '',
	},
	// 是否启用代码高亮
	highlight: {
		type: Boolean,
		default: true,
	},
	// 是否启用表格
	tables: {
		type: Boolean,
		default: true,
	},
	// 是否启用链接
	linkify: {
		type: Boolean,
		default: true,
	},
});

// 初始化markdown-it实例
const md = new MarkdownIt({
	html: true,
	linkify: props.linkify,
	typographer: true,
	highlight: props.highlight
		? function (str, lang) {
				if (lang && hljs.getLanguage(lang)) {
					try {
						return '<pre class="hljs"><code>' + hljs.highlight(str, { language: lang, ignoreIllegals: true }).value + '</code></pre>';
					} catch (__) {}
				}
				return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>';
		  }
		: undefined,
});

// 渲染markdown内容
const renderedMarkdown = computed(() => {
	if (!props.content) return '';
	return md.render(props.content);
});
</script>

<style scoped>
.markdown-renderer {
	font-family: 'Microsoft YaHei', Arial, sans-serif;
	line-height: 1.6;
	color: #333;
	font-size: 18px;
}

/* 标题样式 */
.markdown-renderer :deep(h1) {
	font-size: 28px;
	font-weight: bold;
	color: #008ccc;
	margin: 20px 0 15px 0;
	padding-bottom: 10px;
	border-bottom: 2px solid #008ccc;
}

.markdown-renderer :deep(h2) {
	font-size: 24px;
	font-weight: bold;
	color: #008ccc;
	margin: 18px 0 12px 0;
}

.markdown-renderer :deep(h3) {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	margin: 16px 0 10px 0;
}

.markdown-renderer :deep(h4) {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	margin: 14px 0 8px 0;
}

.markdown-renderer :deep(h5) {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	margin: 12px 0 6px 0;
}

/* 段落样式 */
.markdown-renderer :deep(p) {
	margin: 10px 0;
	text-align: justify;
}

/* 列表样式 - 统一的AI分析报告列表样式 */
.markdown-renderer :deep(ul) {
	margin: 15px 0 !important;
	padding-left: 30px !important;
	list-style-type: disc !important;
}

.markdown-renderer :deep(ol) {
	margin: 15px 0 !important;
	padding-left: 30px !important;
	list-style-type: decimal !important;
}

.markdown-renderer :deep(li) {
	margin: 8px 0 !important;
	line-height: 1.8 !important;
	text-indent: 0 !important;
}

/* 嵌套列表样式 */
.markdown-renderer :deep(ul ul) {
	margin: 5px 0 !important;
	list-style-type: circle !important;
}

.markdown-renderer :deep(ul ul ul) {
	list-style-type: square !important;
}

.markdown-renderer :deep(ol ol) {
	margin: 5px 0 !important;
	list-style-type: lower-alpha !important;
}

/* 确保段落在列表中不受全局段落样式影响 */
.markdown-renderer :deep(li p) {
	text-indent: 0 !important;
	margin: 0 !important;
}

/* 强调文本样式 */
.markdown-renderer :deep(strong) {
	font-weight: bold;
	color: #000;
}

.markdown-renderer :deep(em) {
	font-style: italic;
}

/* 表格样式 */
.markdown-renderer :deep(table) {
	width: 100%;
	border-collapse: collapse;
	margin: 20px 0;
	border: 1px solid #ddd;
}

.markdown-renderer :deep(th),
.markdown-renderer :deep(td) {
	padding: 12px 8px;
	text-align: left;
	border: 1px solid #ddd;
}

.markdown-renderer :deep(th) {
	background-color: #f8f8f9;
	font-weight: bold;
	color: #515a6e;
}

.markdown-renderer :deep(tr:nth-child(even)) {
	background-color: #f9f9f9;
}

/* 代码样式 */
.markdown-renderer :deep(code) {
	background-color: #f4f4f4;
	padding: 2px 4px;
	border-radius: 3px;
	font-family: 'Courier New', monospace;
	font-size: 14px;
}

.markdown-renderer :deep(pre) {
	background-color: #f6f8fa;
	padding: 16px;
	border-radius: 6px;
	overflow-x: auto;
	margin: 16px 0;
}

.markdown-renderer :deep(pre code) {
	background-color: transparent;
	padding: 0;
}

/* 引用样式 */
.markdown-renderer :deep(blockquote) {
	border-left: 4px solid #008ccc;
	padding-left: 16px;
	margin: 16px 0;
	color: #666;
	background-color: #f9f9f9;
	padding: 10px 16px;
}

/* 分割线样式 */
.markdown-renderer :deep(hr) {
	border: none;
	border-top: 2px solid #eee;
	margin: 20px 0;
}

/* 链接样式 */
.markdown-renderer :deep(a) {
	color: #008ccc;
	text-decoration: none;
}

.markdown-renderer :deep(a:hover) {
	text-decoration: underline;
}

/* 数字和特殊标记样式 */
.markdown-renderer :deep(.text-bold) {
	font-weight: bold;
}

.markdown-renderer :deep(.text-blue) {
	color: #00abf9;
}
</style> 
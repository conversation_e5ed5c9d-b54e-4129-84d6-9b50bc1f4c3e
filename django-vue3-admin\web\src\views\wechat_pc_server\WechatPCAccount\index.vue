<template>
    <fs-page class="PageWechatPCAccount">
        <fs-crud ref="crudRef" v-bind="crudBinding">
            <template #cell_status="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                    {{ scope.row.status === 1 ? '在线' : '离线' }}
                </el-tag>
            </template>
        </fs-crud>
    </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue'
import { useFs } from '@fast-crud/fast-crud'
import createCrudOptions from './crud'

export default defineComponent({
    name: "WechatPCAccount",
    setup() {
        // crud组件的ref
        const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions })

        // 在组件挂载后加载数据
        onMounted(() => {
            crudExpose.doRefresh();
        });

        return {
            crudBinding,
            crudRef,
            crudExpose
        }
    }
})
</script>

<style scoped>
:deep(.fs-page) {
    padding: 0;
}
</style>

import { CrudO<PERSON>s, AddReq, DelReq, EditReq, CrudExpose, UserPageQuery, CreateCrudOptionsRet, dict, asyncCompute, compute } from '@fast-crud/fast-crud'
import * as api from './api'
import * as packageApi from './package/api'
import { request } from '/@/utils/service'
import { auth } from "/@/utils/authFunction"
import { h, ref } from 'vue'
import { ElTag, ElMessage, ElMessageBox, ElDialog, ElTable, ElTableColumn } from 'element-plus'
import { dictionary } from '/@/utils/dictionary'
import { ENUM_TYPE } from '/@/stores/constants/enum'
import { getEnumDatas } from '/@/stores/enum';

import router from '/@/router/index';


export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    // 存储编辑行是否有套餐的状态
    const editingHasPackage = ref(false)

    const pageRequest = async (query: UserPageQuery) => {
        return await api.getList(query)
    }

    const addRequest = async ({ form }: AddReq) => {
        return await api.addTenant(form)
    }

    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateTenant(form)
    }

    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteTenant(row.id)
    }

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
                infoRequest: async ({ mode, row }: { mode: string, row: any }) => {
                    if (mode === 'edit') {
                        // 编辑的时候保存当前行是否有套餐的状态
                        editingHasPackage.value = !!(row.package_id)
                    }
                    // 其他情况下直接使用表格中的行数据作为初始表单
                    return row
                }
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('Tenants:Create'),
                        text: '新增租户'
                    },
                    sync: {
                        text: '同步租户',
                        type: 'primary',
                        show: auth('Tenants:Sync'),
                        click: async () => {
                            try {
                                const res = await api.syncTenants()
                                if (res.code === 2000) {
                                    ElMessage.success(`租户同步完成：租户成功${res.data.tenant_sync.success_count}个，失败${res.data.tenant_sync.fail_count}个；租户绑定关系同步成功${res.data.relation_sync.success_count}个，失败${res.data.relation_sync.fail_count}个`)
                                    if (res.data.tenant_sync.error_messages?.length > 0) {
                                        ElMessageBox.alert(res.data.tenant_sync.error_messages.join('\n'), '同步失败详情')
                                    }
                                    crudExpose.doRefresh()
                                } else {
                                    ElMessage.error(res.message || '同步失败')
                                }
                            } catch (e) {
                                console.error(e)
                                ElMessage.error('同步失败')
                            }
                        }
                    }
                }
            },
            toolbar: {
                show: false,
            },
            rowHandle: {
                width: 280,
                buttons: {
                    edit: {
                        link: true,
                        show: auth('Tenants:Update')
                    },
                    remove: {
                        link: true,
                        show: auth('Tenants:Delete')
                    },
                    view: {
                        show: false
                    },
                    bindAccounts: {
                        text: '查看绑定账号',
                        type: 'primary',
                        link: true,
                        click: async ({ row }) => {
                            router.push({
                                path: '/wechat_official_account/account',
                                query: {
                                    tenant_id: row.id
                                }
                            })
                        },
                        show: auth('ShowAccounts:Button')
                    },
                    bindArticles: {
                        text: '查看租户绑定账号文章',
                        type: 'primary',
                        link: true,
                        click: async ({ row }) => {
                            router.push({
                                path: '/wechat_official_account/article',
                                query: {
                                    tenant_id: row.id
                                }
                            })
                        },
                        show: auth('ShowTenantArticles:Button')
                    },
                    syncErrorSentence: {
                        text: '同步错误句子',
                        type: 'primary',
                        link: true,
                        click: async ({ row }) => {
                            ElMessageBox.confirm(`确认同步错误句子吗？`, '同步确认', {
                                confirmButtonText: '确认',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(async () => {
                                return await api.syncErrorSentences(row.id).then(() => {
                                    ElMessage.success('同步成功')
                                    crudExpose.doRefresh()
                                }).catch((err: any) => {
                                    ElMessage.error(err.msg)
                                })
                            }).catch(() => {
                                return Promise.reject('取消同步')
                            })
                        },
                        show: auth('ErrorSentences:Sync')
                    },
                    bindMediaAccounts: {
                        text: '绑定监管账号',
                        type: 'success',
                        link: true,
                        click: async ({ row }) => {
                            // 触发账号绑定弹窗
                            // 这里需要通过事件总线或者其他方式通知父组件打开弹窗
                            const event = new CustomEvent('openAccountBindDialog', {
                                detail: { tenantId: row.id, tenantName: row.name }
                            });
                            window.dispatchEvent(event);
                        },
                        show: auth('TenantMediaAccounts:Bind')
                    }
                }
            },
            form: {
                col: { span: 12 },
                labelWidth: '120px',
                wrapper: {
                    is: 'el-dialog',
                    width: '800px',
                },
            },
            columns: {
                id: {
                    title: 'ID',
                    type: 'text',
                    form: { show: false },
                    search: { show: true }
                },
                name: {
                    title: '租户名称',
                    type: 'text',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '请输入租户名称' }],
                        component: {
                            placeholder: '请输入租户名称'
                        }
                    }
                },
                logo: {
                    title: '租户头像',
                    type: "cropper-uploader",
                    form: {
                        col: { span: 24 },
                    }
                },
                package_id: {
                    title: '租户套餐',
                    type: 'dict-select',
                    dict: dict({
                        getData: async () => {
                            const res = await packageApi.getAllPackageList()
                            return res.data.map((item: any) => ({
                                label: item.name,
                                value: item.id
                            }))
                        },
                        value: 'value',
                        label: 'label',
                        cache: true
                    }),
                    search: { show: false },
                    column: {
                        show: false
                    },
                    form: {
                        rules: [{ required: true, message: '请选择租户套餐' }],
                        component: {
                            filterable: true,
                            placeholder: '请选择租户套餐'
                        }
                    }
                },
                contact_name: {
                    title: '联系人名称',
                    type: 'text',
                    search: { show: false },
                    column: {
                        show: false
                    },
                    form: {
                        component: {
                            placeholder: '请输入联系人名称'
                        }
                    }
                },
                contact_phone: {
                    title: '联系人号码',
                    type: 'text',
                    search: { show: false },
                    column: {
                        show: false
                    },
                    form: {
                        rules: [
                            {
                                pattern: /^1[3-9]\d{9}$/,
                                message: '请输入正确的手机号码'
                            }
                        ],
                        component: {
                            placeholder: '请输入联系人号码',
                            maxlength: 20
                        }
                    }
                },
                expire_time: {
                    title: '过期时间',
                    type: 'datetime',
                    search: { show: false },
                    column: {
                        show: false
                    },
                    form: {
                        component: {
                            placeholder: '请选择过期时间',
                            type: 'datetime',
                            valueFormat: 'YYYY-MM-DD HH:mm:ss'
                        }
                    }
                },
                admin_username: {
                    title: '管理员账号',
                    type: 'text',
                    search: { show: false },
                    column: {
                        show: false
                    },
                    form: {
                        rules: [{ required: true, message: '请输入管理员账号' }],
                        component: {
                            placeholder: '请输入管理员账号'
                        }
                    },
                    addForm: { show: true },
                    editForm: {
                        show: compute(() => {
                            return !editingHasPackage.value
                        })
                    }
                },
                admin_password: {
                    title: '管理员账号密码',
                    type: 'password',
                    search: { show: false },
                    column: {
                        show: false
                    },
                    form: {
                        rules: [{ required: true, message: '请输入管理员账号密码' }],
                        component: {
                            placeholder: '请输入管理员账号密码',
                            showPassword: true
                        }
                    },
                    addForm: { show: true },
                    editForm: {
                        show: compute(() => {
                            return !editingHasPackage.value
                        })
                    }
                },

                type: {
                    title: '租户类型',
                    type: 'dict-select',
                    search: { show: true },
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.CUSTOMER_TYPE)
                    }),
                    form: {
                        value: '企业'
                    }
                },
                priority: {
                    title: '优先级',
                    type: 'text',
                    search: { show: true }
                },
                stage: {
                    title: '客户阶段',
                    type: 'dict-select',
                    search: { show: true },
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.CUSTOMER_STAGE)
                    })
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'daterange',
                                valueFormat: 'YYYY-MM-DD'
                            }
                        }
                    },
                    form: { show: false }
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime',
                    form: { show: false }
                },

            }
        }
    }
} 
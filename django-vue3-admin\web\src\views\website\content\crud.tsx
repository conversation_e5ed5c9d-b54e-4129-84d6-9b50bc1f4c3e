import * as api from './api';
import { ContentType, ContentStatus } from './api';
import { CreateCrudOptionsProps, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed } from 'vue';

export default function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      request: {
        pageRequest: async (query: any) => {
          const res = await api.getContentList(query);
          // 转换后端数据格式为Fast-CRUD期望的格式
          return {
            ...res,
            data: {
              results: res.data || [],
              pagination: {
                page: res.page || 1,
                page_size: res.limit || 20,
                total: res.total || 0,
                total_pages: Math.ceil((res.total || 0) / (res.limit || 20))
              }
            }
          };
        },
        addRequest: async ({ form }: any) => {
          const res = await api.createContent(form);
          ElMessage.success(res.msg || '内容创建成功');
          return res.data;
        },
        editRequest: async ({ form, row }: any) => {
          const res = await api.updateContent(row.id, form);
          ElMessage.success(res.msg || '内容更新成功');
          return res.data;
        },
        delRequest: async ({ row }: any) => {
          const res = await api.deleteContent(row.id);
          ElMessage.success(res.msg || '内容删除成功');
          return res.data;
        },
      },
      
      // 页面配置
      container: {
        is: 'fs-layout-card'
      },
      
      // 表格配置
      table: {
        size: 'default',
        stripe: true,
        border: false,
      },
      
      // 搜索表单配置
      search: {
        show: true,
        initialForm: {},
        options: {
          labelWidth: '100px',
        },
      },
      
      // 操作栏配置
      actionbar: {
        buttons: {
          add: {
            text: '添加内容',
            type: 'primary',
          },
          // 批量操作按钮
          batchExtract: {
            text: '批量重新提取',
            type: 'success',
            icon: 'fas fa-sync',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的内容');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要批量重新提取选中的 ${selectedRowKeys.length} 个内容吗？这将重新进行内容提取处理。`, 
                  '批量重新提取'
                );
                
                const res = await api.batchExtractContent(selectedRowKeys);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量重新提取失败:', error);
                  ElMessage.error('批量操作失败');
                }
              }
            }
          },
          batchMarkManual: {
            text: '批量标记人工处理',
            type: 'warning',
            icon: 'fas fa-hand-paper',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的内容');
                return;
              }
              
              try {
                await ElMessageBox.confirm(`确定要将选中的 ${selectedRowKeys.length} 个内容标记为人工处理吗？`, '批量标记人工处理');
                const res = await api.batchUpdateContentStatus(selectedRowKeys, 'mark_manual');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量标记人工处理失败:', error);
                }
              }
            }
          },
          batchDelete: {
            text: '批量删除',
            type: 'danger',
            icon: 'fas fa-trash',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要删除的内容');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要删除选中的 ${selectedRowKeys.length} 个内容吗？此操作不可恢复！`, 
                  '批量删除',
                  { type: 'error' }
                );
                const res = await api.batchUpdateContentStatus(selectedRowKeys, 'delete');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量删除失败:', error);
                }
              }
            }
          }
        }
      },
      
      // 行操作配置
      rowHandle: {
        width: 380,
        buttons: {
          edit: { text: '编辑', type: 'primary' },
          remove: { text: '删除', type: 'danger' },
          // 内容提取操作
          extractContent: {
            text: '重新提取',
            type: 'success',
            icon: 'fas fa-sync',
            show: ({ row }: { row: ContentType }) => {
              return [ContentStatus.PENDING, ContentStatus.FAILED, ContentStatus.NO_RULE_MATCH].includes(row.status);
            },
            click: async ({ row }: { row: ContentType }) => {
              try {
                await ElMessageBox.confirm(
                  `确定要重新提取内容 \"${row.title}\" 吗？\\n\\n这将重新进行内容提取处理。`, 
                  '确认重新提取'
                );
                const res = await api.extractContent(row.id);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('重新提取失败:', error);
                  ElMessage.error('重新提取失败');
                }
              }
            }
          },
          // 状态操作
          markManual: {
            text: '标记人工处理',
            type: 'warning',
            icon: 'fas fa-hand-paper',
            show: ({ row }: { row: ContentType }) => {
              return row.status !== ContentStatus.MANUAL && row.status !== ContentStatus.EXTRACTED;
            },
            click: async ({ row }: { row: ContentType }) => {
              try {
                await ElMessageBox.confirm(`确定要将内容 \"${row.title}\" 标记为人工处理吗？`, '标记人工处理');
                const res = await api.markContentManual(row.id);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('标记人工处理失败:', error);
                }
              }
            }
          },
          resetStatus: {
            text: '重置状态',
            type: 'info',
            icon: 'fas fa-undo',
            show: ({ row }: { row: ContentType }) => {
              return row.status !== ContentStatus.PENDING;
            },
            click: async ({ row }: { row: ContentType }) => {
              try {
                await ElMessageBox.confirm(`确定要重置内容 \"${row.title}\" 的状态吗？`, '重置状态');
                const res = await api.resetContentStatus(row.id);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('重置状态失败:', error);
                }
              }
            }
          },
          // 查看详情
          viewDetail: {
            text: '查看详情',
            type: 'info',
            icon: 'fas fa-eye',
            click: ({ row }: { row: ContentType }) => {
              console.log('查看内容详情:', row);
              ElMessage.info('内容详情功能开发中');
            }
          }
        }
      },

      columns: {
        // 多选框
        _index: {
          title: '选择',
          form: { show: false },
          column: {
            type: 'selection',
            width: 60,
            align: 'center'
          }
        },
        
        id: {
          title: 'ID',
          type: 'number',
          form: { show: false },
          column: { 
            width: 80,
            sortable: true 
          }
        },
        
        title: {
          title: '内容标题',
          type: 'input',
          search: { show: true },
          form: {
            rules: [{ required: true, message: '请输入内容标题' }],
          },
          column: { 
            minWidth: 200,
            showOverflowTooltip: true,
          }
        },
        
        content_summary: {
          title: '内容摘要',
          form: { show: false },
          column: { 
            minWidth: 250,
            showOverflowTooltip: true,
          }
        },
        
        site_name: {
          title: '所属站点',
          type: 'input',
          form: { show: false },
          search: {
            show: true,
            component: {
              placeholder: '站点名称'
            }
          },
          column: { 
            width: 150,
            showOverflowTooltip: true,
          }
        },
        
        column_name: {
          title: '所属栏目',
          type: 'input',
          form: { show: false },
          search: {
            show: true,
            component: {
              placeholder: '栏目名称'
            }
          },
          column: { 
            width: 150,
            showOverflowTooltip: true,
          }
        },
        
        status: {
          title: '提取状态',
          type: 'dict-select',
          dict: dict({
            data: [
              { value: ContentStatus.PENDING, label: '待提取', color: 'warning' },
              { value: ContentStatus.EXTRACTED, label: '已提取', color: 'success' },
              { value: ContentStatus.FAILED, label: '提取失败', color: 'danger' },
              { value: ContentStatus.MANUAL, label: '人工处理', color: 'info' },
              { value: ContentStatus.NO_RULE_MATCH, label: '无法匹配规则', color: 'warning' },
              { value: ContentStatus.RULE_GENERATION_FAILED, label: '规则生成失败', color: 'danger' },
            ]
          }),
          search: { 
            show: true,
            component: {
              placeholder: '选择状态',
              clearable: true,
              style: { width: '140px' }
            }
          },
          form: {
            value: ContentStatus.PENDING
          },
          column: {
            width: 140,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            }
          }
        },
        
        rule_name: {
          title: '提取规则',
          form: { show: false },
          column: {
            width: 150,
            showOverflowTooltip: true,
          }
        },
        
        author: {
          title: '作者',
          type: 'input',
          search: {
            show: true,
            component: {
              placeholder: '作者名称'
            }
          },
          form: {
            component: {
              placeholder: '内容作者'
            }
          },
          column: { 
            width: 120,
            showOverflowTooltip: true,
          }
        },
        
        source: {
          title: '来源',
          type: 'input',
          form: {
            component: {
              placeholder: '内容来源'
            }
          },
          column: { 
            show: false,
            width: 120,
            showOverflowTooltip: true
          }
        },
        
        publish_time_display: {
          title: '发布时间',
          form: { show: false },
          column: { 
            width: 160,
            sortable: true,
          }
        },
        
        create_time_display: {
          title: '创建时间',
          form: { show: false },
          column: { 
            width: 160,
            sortable: true
          }
        },
        
        update_time_display: {
          title: '更新时间',
          form: { show: false },
          column: { 
            show: false,
            width: 160,
            sortable: true
          }
        },
      },
    },
  };
}
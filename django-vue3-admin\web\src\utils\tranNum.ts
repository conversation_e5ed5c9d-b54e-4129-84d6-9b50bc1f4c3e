/**
 * 将数字转换为带单位的字符串表示
 * @param {number} num - 需要转换的数字
 * @returns {string} - 转换后的字符串
 */
export function tranNumber(num: number): string {
	const isNegative = num < 0;
	const absNum = Math.abs(num);
	let result: string | number;
	if (absNum < 9999) {
		result = absNum;
	} else if (absNum < 99995000) {
		const wan = absNum / 10000;
		// 先取三位小数，然后四舍五入到两位
		const roundedWan = Math.round(wan * 100) / 100;
		if (roundedWan >= 10000) {
			result = '1亿';
		}
		const formattedWan = roundedWan.toFixed(2);
		result = (formattedWan.endsWith('.00') ? formattedWan.slice(0, -3) : formattedWan.replace(/\.?0+$/, '')) + '万';
	} else {
		const yi = absNum / 100000000;
		// 同样先取三位小数，然后四舍五入到两位
		const roundedYi = Math.round(yi * 1000) / 1000;
		const formattedYi = roundedYi.toFixed(2);
		result = (formattedYi.endsWith('.00') ? formattedYi.slice(0, -3) : formattedYi.replace(/\.?0+$/, '')) + '亿';
	}

	return isNegative ? '-' + result : result.toString();
}

/**
 * 测试 tranNumber 函数
 */
export function testTranNumber(): void {
	interface TestCase {
		input: number;
		expected: string;
	}

	const testCases: TestCase[] = [
		{ input: -999, expected: '-999' },
		{ input: -9999.4, expected: '-1万' },
		{ input: -9999.5, expected: '-1万' },
		{ input: -9999, expected: '-1万' },
		{ input: -10000, expected: '-1万' },
		{ input: -10001, expected: '-1万' },
		{ input: -56789, expected: '-5.68万' },
		{ input: -999599, expected: '-99.96万' },
		{ input: -999499, expected: '-99.95万' },
		{ input: -999500, expected: '-99.95万' },
		{ input: -1770000000, expected: '-17.7亿' },
		{ input: -99999994, expected: '-1亿' },
		{ input: -1234567890123, expected: '-12345.68亿' },

		{ input: 999, expected: '999' },
		{ input: 1000, expected: '1000' },
		{ input: 9999, expected: '1万' },
		{ input: 9999.4, expected: '1万' },
		{ input: 9999.5, expected: '1万' },
		{ input: 10000, expected: '1万' },
		{ input: 10001, expected: '1万' },
		{ input: 10100, expected: '1.01万' },
		{ input: 15203, expected: '1.52万' },
		{ input: 56789, expected: '5.68万' },
		{ input: 56489, expected: '5.65万' },
		{ input: 56449, expected: '5.65万' },
		{ input: 56445, expected: '5.65万' },
		{ input: 56444, expected: '5.64万' },
		{ input: 99955, expected: '10万' },
		{ input: 99959, expected: '10万' },
		{ input: 99960, expected: '10万' },
		{ input: 99961, expected: '10万' },
		{ input: 99949, expected: '9.99万' },
		{ input: 999599, expected: '99.96万' },
		{ input: 999499, expected: '99.95万' },
		{ input: 9995000, expected: '999.5万' },
		{ input: 9994999, expected: '999.5万' },
		{ input: -9995000, expected: '-999.5万' },
		{ input: -9994999, expected: '-999.5万' },
		{ input: 100000, expected: '10万' },
		{ input: 9999999, expected: '1000万' },
		{ input: 99999995, expected: '1亿' },
		{ input: 100000000, expected: '1亿' },
		{ input: 1770000000, expected: '17.7亿' },
		{ input: 1234567890123, expected: '12345.68亿' },
		// 极限值测试
		{ input: 9998, expected: '9998' },
		{ input: 9999, expected: '1万' },
		{ input: 999950, expected: '100万' },
		{ input: 9999950, expected: '1000万' },
		{ input: 99999950, expected: '1亿' },
	];

	let passCount = 0;
	let failCount = 0;

	testCases.forEach(({ input, expected }) => {
		const actual = tranNumber(input);
		if (actual === expected) {
			console.log(`测试通过: ${input} 正确返回 "${actual}"`);
			passCount++;
		} else {
			console.error(`测试失败: ${input} 应该返回 "${expected}", 实际返回 "${actual}"`);
			failCount++;
		}
	});

	console.log(`测试完成: 通过 ${passCount} 项, 失败 ${failCount} 项`);
}

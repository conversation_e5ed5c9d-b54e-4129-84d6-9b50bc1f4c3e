# 组件开发指南

## 📦 组件分类

### 1. 基础组件 (Base Components)
位置: `src/components/`
用途: 通用的UI组件，跨模块复用

```vue
<!-- 示例: 状态标签组件 -->
<template>
  <el-tag 
    :type="config.type" 
    :effect="effect"
    :size="size"
  >
    <el-icon v-if="config.icon" class="mr-1">
      <component :is="config.icon" />
    </el-icon>
    {{ config.label }}
  </el-tag>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

interface Props {
  status: string;
  effect?: 'light' | 'dark' | 'plain';
  size?: 'large' | 'default' | 'small';
}

export default defineComponent({
  name: 'StatusTag',
  props: {
    status: { type: String, required: true },
    effect: { type: String, default: 'light' },
    size: { type: String, default: 'default' },
  },
  setup(props: Props) {
    const statusConfigs = {
      active: { label: '运行中', type: 'success', icon: 'Check' },
      inactive: { label: '已停用', type: 'info', icon: 'Close' },
      // ... 更多状态配置
    };

    const config = computed(() => statusConfigs[props.status] || statusConfigs.default);

    return {
      config,
    };
  },
});
</script>
```

### 2. 业务组件 (Business Components)
位置: `src/views/[module]/[feature]/components/`
用途: 特定业务逻辑的组件

```vue
<!-- 示例: 站点创建表单 -->
<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item label="网站URL" prop="start_url">
      <el-input
        v-model="form.start_url"
        placeholder="https://example.com"
        @blur="handleUrlBlur"
      />
    </el-form-item>
    <!-- 更多表单项 -->
    
    <el-form-item>
      <el-button 
        type="primary" 
        :loading="loading"
        @click="handleSubmit"
      >
        {{ loading ? '创建中...' : '创建站点' }}
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'SiteCreateForm',
  setup() {
    const form = ref({
      start_url: '',
      // ... 其他字段
    });
    
    const loading = ref(false);
    
    const rules = {
      start_url: [
        { required: true, message: '请输入网站URL', trigger: 'blur' }
      ]
    };

    const handleUrlBlur = () => {
      // URL处理逻辑
    };

    const handleSubmit = async () => {
      loading.value = true;
      try {
        // 提交逻辑
      } finally {
        loading.value = false;
      }
    };

    return {
      form,
      rules,
      loading,
      handleUrlBlur,
      handleSubmit,
    };
  },
});
</script>
```

### 3. 布局组件 (Layout Components)  
位置: `src/layout/`
用途: 页面布局相关组件

## 🛠 组件开发规范

### 1. 组件命名
- **文件名**: PascalCase (如: `StatusDropdown.vue`)
- **组件名**: 与文件名保持一致
- **Props**: camelCase
- **Events**: kebab-case

### 2. Props定义
```typescript
export default defineComponent({
  props: {
    modelValue: { type: String, required: true },
    options: { type: Array, required: true },
    size: { type: String, default: 'default' },
    disabled: { type: Boolean, default: false },
    placeholder: { type: String, default: '请选择' },
  },
  setup(props, { emit }) {
    // 组件逻辑
    return {};
  },
});
```

### 3. 事件定义
```typescript
export default defineComponent({
  emits: ['update:modelValue', 'change', 'blur'],
  setup(props, { emit }) {
    const handleChange = (value: string, option: Option) => {
      emit('update:modelValue', value);
      emit('change', value, option);
    };

    return { handleChange };
  },
});
```

### 4. 插槽使用
```vue
<template>
  <div class="my-component">
    <!-- 默认插槽 -->
    <slot />
    
    <!-- 具名插槽 -->
    <div class="header">
      <slot name="header" :data="headerData" />
    </div>
    
    <!-- 作用域插槽 -->
    <ul>
      <li v-for="item in items" :key="item.id">
        <slot name="item" :item="item" :index="item.id" />
      </li>
    </ul>
  </div>
</template>
```

## 🎨 样式规范

### 1. CSS Modules
```vue
<template>
  <div :class="$style.container">
    <div :class="$style.header">标题</div>
  </div>
</template>

<style module>
.container {
  padding: 16px;
}

.header {
  font-size: 18px;
  font-weight: 600;
}
</style>
```

### 2. Scoped样式
```vue
<style scoped>
.my-component {
  /* 组件专用样式 */
}

/* 深度选择器 */
:deep(.el-button) {
  margin: 4px;
}
</style>
```

### 3. CSS变量
```scss
// 使用设计系统变量
.my-component {
  color: var(--el-color-primary);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
}
```

## 🔄 组件通信

### 1. 父子组件通信
```vue
<!-- 父组件 -->
<template>
  <ChildComponent
    v-model="value"
    :options="options"
    @change="handleChange"
  />
</template>

<!-- 子组件 -->
<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    @change="emit('change', $event)"
  >
    <!-- 选项 -->
  </el-select>
</template>
```

### 2. 跨组件通信（Provide/Inject）
```vue
<!-- 祖先组件 -->
<script lang="ts">
import { defineComponent, provide, ref } from 'vue';

export default defineComponent({
  setup() {
    const theme = ref('light');
    provide('theme', theme);
    
    return { theme };
  },
});
</script>

<!-- 后代组件 -->
<script lang="ts">
import { defineComponent, inject } from 'vue';

export default defineComponent({
  setup() {
    const theme = inject('theme', 'light'); // 默认值
    
    return { theme };
  },
});
</script>
```

### 3. 全局状态管理（Pinia）
```typescript
// stores/feature.ts
export const useFeatureStore = defineStore('feature', () => {
  const items = ref([]);
  
  const getItems = async () => {
    // 获取数据逻辑
  };
  
  return { items, getItems };
});

// 在组件中使用
export default defineComponent({
  setup() {
    const featureStore = useFeatureStore();
    
    onMounted(async () => {
      await featureStore.getItems();
    });
    
    return { featureStore };
  },
});
```

## 📚 组件文档
每个复杂组件应包含 README.md 文档:

```markdown
# StatusDropdown 组件

## 功能描述
用于状态切换的下拉菜单组件

## Props
| 属性 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| modelValue | string | 是 | - | 当前状态值 |
| options | Array | 是 | - | 状态选项 |

## Events  
| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | (value: string) | 状态改变时触发 |

## 使用示例
\`\`\`vue
<StatusDropdown
  v-model="status"
  :options="statusOptions"
  @change="handleStatusChange"
/>
\`\`\`
```
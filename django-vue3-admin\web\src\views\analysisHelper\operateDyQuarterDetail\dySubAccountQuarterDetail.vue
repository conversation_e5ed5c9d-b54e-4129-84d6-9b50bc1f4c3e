<template>
	<div>
		<!-- 添加导出按钮 -->
		<!-- 添加导出内容包裹层 -->
		<div>
			<div>
				<div class="top">
					<div class="top-title">{{ title.split('季')[0] }}季度下级抖音运营报告</div>
					<!-- <div style="font-size: 16px; margin-top: 20px">{{ yearMonth }}</div>
          <div style="font-size: 14px; float: right; color: #4678ab">
            汇远轻媒出品
          </div> -->
				</div>
			</div>
			<div v-if="!hasAccounts">
				<div style="width: 100%; margin: 10px auto; font-size: 26px; text-align: center">无绑定账号</div>
			</div>
			<div v-else>
				<!-- 当账号数量大于3时，分开导出两个模块 -->
				<div v-if="dci.length > 3">
					<!-- 账号传播力指数榜单 - 单独模块 -->
					<div id="dy-sub-1">
						<div class="title-wrapper">
							<div class="title1">账号传播力指数榜单</div>
						</div>
						<div class="table-wrapper">
							<el-table :data="dci" border>
								<el-table-column label="排序" align="center" type="index" width="100" />
								<el-table-column label="账号名称" align="center" prop="accountName" />
								<el-table-column label="传播力指数" align="center" prop="dic" :resizable="false">
									<template #default="scope">
										<div style="display: flex; justify-content: center">
											<span>{{ tranNum(scope.row.dic) }}</span>
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
					<!-- 作品发布 - 单独模块 -->
					<div id="dy-sub-1-2">
						<div class="title-wrapper"><div class="title1">作品发布</div></div>
						<div class="table-wrapper">
							<el-table :data="worksIssue" border>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="count" min-width="120px" />
								<el-table-column label="较上季" align="center" prop="diff" min-width="100px">
									<template #default="scope">
										{{ scope.row.worksStatus === '持平' ? '持平' : scope.row.worksStatus === '增加' ? '' : '-'
										}}{{ scope.row.worksStatus === '持平' ? '' : tranNum(Math.abs(scope.row.diff)) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="120px">
									<template #default="scope">
										{{ scope.row.worksStatus === '持平' ? '持平' : scope.row.worksStatus === '增加' ? '' : '-'
										}}{{ scope.row.worksStatus === '持平' ? '' : scope.row.rate }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<!-- 当账号数量小于等于3时，保持原有的合并模块 -->
				<div v-else>
					<div id="dy-sub-1">
						<div class="title-wrapper">
							<div class="title1">账号传播力指数榜单</div>
						</div>
						<div class="table-wrapper">
							<el-table :data="dci" border>
								<el-table-column label="排序" align="center" type="index" width="100" />
								<el-table-column label="账号名称" align="center" prop="accountName" />
								<el-table-column label="传播力指数" align="center" prop="dic" :resizable="false">
									<template #default="scope">
										<div style="display: flex; justify-content: center">
											<span>{{ tranNum(scope.row.dic) }}</span>
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="title-wrapper"><div class="title1">作品发布</div></div>
						<div class="table-wrapper">
							<el-table :data="worksIssue" border>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="count" min-width="120px" />
								<el-table-column label="较上季" align="center" prop="diff" min-width="100px">
									<template #default="scope">
										{{ scope.row.worksStatus === '持平' ? '持平' : scope.row.worksStatus === '增加' ? '' : '-'
										}}{{ scope.row.worksStatus === '持平' ? '' : tranNum(Math.abs(scope.row.diff)) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="120px">
									<template #default="scope">
										{{ scope.row.worksStatus === '持平' ? '持平' : scope.row.worksStatus === '增加' ? '' : '-'
										}}{{ scope.row.worksStatus === '持平' ? '' : scope.row.rate }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<div id="dy-sub-2">
					<!-- 视频、图文类型分布饼图 -->
					<div class="sub-title">内容类型分布</div>
					<div class="chart-wrapper">
						<pie :data="contentTypePie.data" :total="contentTypePie.total" style="width: 100%; height: 370px" :label-font-size="18" />
					</div>
					<div class="sub-title">发布时间分布</div>
					<!-- 发布时间区间分布图 -->
					<div class="chart-wrapper">
						<line-chart
							:data="timeDistribution"
							style="width: 100%; height: 350px"
							:axis-font-size="16"
							:legend-font-size="16"
							:axis-name-font-size="16"
						/>
					</div>
				</div>
				<!-- 当账号数量大于3时，拆分dy-sub-3模块 -->
				<div v-if="dci.length > 3">
					<!-- 作品传播 + 点赞量 - 单独模块 -->
					<div id="dy-sub-3">
						<!-- 1.1.4 作品传播报告 -->
						<div class="title-wrapper">
							<div class="title1">作品传播</div>
						</div>

						<!-- 点赞量表格 -->
						<div class="title-wrapper">
							<div class="title2">点赞量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksLike"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均点赞量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高点赞作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="点赞数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
					<!-- 评论量 - 单独模块 -->
					<div id="dy-sub-3-2">
						<div class="title-wrapper">
							<div class="title2">评论量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksComment"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均评论量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高评论作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="评论数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<!-- 当账号数量小于等于3时，保持原有的合并模块 -->
				<div v-else>
					<div id="dy-sub-3">
						<!-- 1.1.4 作品传播报告 -->
						<div class="title-wrapper">
							<div class="title1">作品传播</div>
						</div>

						<!-- 点赞量表格 -->
						<div class="title-wrapper">
							<div class="title2">点赞量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksLike"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均点赞量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高点赞作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="点赞数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>

						<!-- 评论量表格 -->
						<div class="title-wrapper">
							<div class="title2">评论量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksComment"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均评论量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高评论作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="评论数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<!-- 当账号数量大于3时，拆分dy-sub-4模块 -->
				<div v-if="dci.length > 3">
					<!-- 转发量 - 单独模块 -->
					<div id="dy-sub-4">
						<div class="title-wrapper">
							<div class="title2">转发量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksForward"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均转发量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高转发作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="转发数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
					<!-- 收藏量 - 单独模块 -->
					<div id="dy-sub-4-2">
						<div class="title-wrapper">
							<div class="title2">收藏量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksCollect"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均收藏量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高收藏作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="收藏数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<!-- 当账号数量小于等于3时，保持原有的合并模块 -->
				<div v-else>
					<div id="dy-sub-4">
						<!-- 转发量表格 -->
						<div class="title-wrapper">
							<div class="title2">转发量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksForward"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均转发量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高转发作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="转发数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>

						<!-- 收藏量表格 -->
						<div class="title-wrapper">
							<div class="title2">收藏量</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="worksCollect"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="150px" />
								<el-table-column label="本季值" align="center" prop="data" min-width="80px">
									<template #default="scope">
										{{ tranNum(scope.row.data) }}
									</template>
								</el-table-column>
								<el-table-column label="较上季" align="center" min-width="80px">
									<template #default="scope">
										{{ scope.row.status === '持平' ? '持平' : (scope.row.status === '增加' ? '' : '-') + tranNum(scope.row.diff) }}
									</template>
								</el-table-column>
								<el-table-column label="环比增长率" align="center" prop="rate" min-width="100px" />
								<el-table-column label="篇均收藏量" align="center" prop="avg" min-width="100px">
									<template #default="scope">
										{{ tranNum(scope.row.avg) }}
									</template>
								</el-table-column>
								<el-table-column label="最高收藏作品" align="center" prop="maxTitle" min-width="350px" />
								<el-table-column label="收藏数" align="center" prop="maxData" min-width="80px">
									<template #default="scope">
										{{ scope.row.maxTitle ? tranNum(scope.row.maxData) : '-' }}
									</template>
								</el-table-column>
								<el-table-column label="发布时间" align="center" prop="maxIssueTime" min-width="120px">
									<template #default="scope">
										{{ scope.row.maxTitle ? scope.row.maxIssueTime : '-' }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
				<div id="dy-sub-5">
					<!-- 作品分析表格 -->
					<div class="title-wrapper">
						<div class="title1">作品分析</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatWorksAnalysisTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="排名" align="center" prop="rank" min-width="70px" />
							<el-table-column label="作品标题" align="center" prop="title" min-width="400px" />
							<el-table-column label="点赞量" align="center" prop="like" min-width="80px" />
							<el-table-column label="评论量" align="center" prop="comment" min-width="80px" />
							<el-table-column label="收藏量" align="center" prop="collect" min-width="80px" />
							<el-table-column label="转发量" align="center" prop="forward" min-width="80px" />
							<el-table-column label="来源账号" align="center" prop="account" min-width="130px" />
						</el-table>
					</div>
				</div>
				<!-- 当账号数量大于3时，拆分dy-sub-6模块 -->
				<div v-if="dci.length > 3">
					<!-- 用户分析 + 总粉丝量 - 单独模块 -->
					<div id="dy-sub-6">
						<div class="title-wrapper"><div class="title1">用户分析</div></div>
						<div class="title-wrapper"><div class="title2">总粉丝量</div></div>
						<div class="table-wrapper">
							<el-table
								:data="totalFans.data"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="序号" align="center" type="index" width="80" />
								<el-table-column label="账号名称" align="center" prop="name" min-width="200px" />
								<el-table-column label="粉丝总数" align="center" prop="count" min-width="120px">
									<template #default="scope">
										{{ tranNum(scope.row.count) }}
									</template>
								</el-table-column>
							</el-table>
						</div>
					</div>
					<!-- 总关注量占比图 - 单独模块 -->
					<div id="dy-sub-6-2" v-if="fansPieData.length > 1">
						<div class="chart-wrapper">
							<div class="sub-title">总关注量占比关系</div>
							<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 350px" :label-font-size="18"> </pie>
						</div>
					</div>
				</div>
				<!-- 当账号数量小于等于3时，保持原有的合并模块 -->
				<div v-else>
					<div id="dy-sub-6">
						<div class="title-wrapper"><div class="title1">用户分析</div></div>
						<div class="title-wrapper"><div class="title2">总粉丝量</div></div>
						<div class="table-wrapper">
							<el-table
								:data="totalFans.data"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="序号" align="center" type="index" width="80" />
								<el-table-column label="账号名称" align="center" prop="name" min-width="200px" />
								<el-table-column label="粉丝总数" align="center" prop="count" min-width="120px">
									<template #default="scope">
										{{ tranNum(scope.row.count) }}
									</template>
								</el-table-column>
							</el-table>
						</div>

						<!-- 总关注量占比图 -->
						<div v-if="fansPieData.length > 1" class="chart-wrapper">
							<div class="sub-title">总关注量占比关系</div>
							<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 350px" :label-font-size="18"> </pie>
						</div>
					</div>
				</div>
			</div>

			<!-- 超一个月未更新抖音号列表 -->
			<div v-if="props.noUpdateAccount && props.noUpdateAccount.length > 0">
				<div class="top">
					<div class="top-title">超一个月未更新抖音号</div>
				</div>
				<div id="dy-no-update">
					<div class="title-wrapper">
						<div class="title1" style="width: 40%">超一个月及更久未更新抖音号列表</div>
					</div>
					<div class="table-wrapper">
						<el-table :data="props.noUpdateAccount" border>
							<el-table-column label="序号" align="center" type="index" width="100" />
							<el-table-column label="账号" align="center" prop="account_name" />
							<el-table-column label="不更新天数" align="center" prop="no_update_days" width="150">
								<template #default="scope"> {{ scope.row.no_update_days }}天 </template>
							</el-table-column>
							<el-table-column label="最后发文时间" align="center" prop="last_update_date" />
						</el-table>
					</div>
					<div style="text-align: right; font-size: 16px; color: #000; margin: 20px 30px; padding-bottom: 20px">
						<p>注：不更新天数计算，截止日期为{{ props.date.end_date }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, defineProps, defineExpose, watch } from 'vue';
import pie from '/@/components/Echarts/pie.vue';
import lineChart from '/@/components/Echarts/line.vue';
import { tranNumber } from '/@/utils/tranNum';

const props = defineProps({
	id: {
		type: [String, Number],
		default: '',
	},
	subReportData: {
		type: Object,
		default: () => ({}),
	},
	title: {
		type: String,
		default: '',
	},
	date: {
		type: Object,
		default: () => ({}),
	},
	sameAccount: {
		type: Array,
		default: () => [],
	},
	noUpdateAccount: {
		type: Array,
		default: () => [],
	},
	createTime: {
		type: String,
		default: '',
	},
	operateType: {
		type: String,
		default: '',
	},
});

// 数据定义
const reportType = ref('季');
const pageLoading = ref(true);
// 基础信息
const reportDate = ref(null);
const reportDateRange = ref(null);
const reportMonth = ref('');
const hasAccounts = ref(false);
const month = ref('');
const yearMonth = ref('');
const title = ref('');
const endTime = ref(null);

// 传播力指数相关
const dci = ref([]);
const wciDesc = ref([]);
const wciList = ref([]);

// 作品发布相关
const worksIssue = ref([]);
const articleCountDesc = ref([]);
const contentTypePie = ref({
	data: [],
	total: { count: 0, name: '总计' },
});
const timeDistribution = ref({
	title: '发文时间区间分布情况',
	row: [],
	dates: [],
});

// 作品传播相关
const worksLike = ref([]);
const worksComment = ref([]);
const worksForward = ref([]);
const worksCollect = ref([]);
const worksTopLike = ref([]);
const minVideo = ref(null);

// 用户分析相关
const totalFans = ref({});
const fansPieData = ref([]);
const fansPieTotal = ref({});
const totalUsersList = ref([]);
const totalUsersPie = ref([]);
const totalUsersPieTotal = ref({});
const newUsersDesc = ref([]);
const netUsersDesc = ref([]);

// 数字转换
const tranNum = (num) => {
	if (num !== undefined && num !== null && num !== '') {
		return tranNumber(num);
	}
	return '';
};

// 处理数据方法
const processData = (data) => {
	// 设置基本信息
	reportType.value = data.reportType === '1' ? '季' : '周';
	hasAccounts.value = data.dci?.length > 0;
	month.value = data.month;
	yearMonth.value = data.yearMonth;

	// 设置标题，优先使用props的title
	title.value = props.title || data.title;

	// 传播力指数报告
	dci.value = data.dci || [];

	// 作品发布报告
	worksIssue.value = data.worksIssue || [];

	// 作品传播处理
	worksLike.value = data.worksLike || [];

	// 评论量
	worksComment.value = data.worksComment || [];

	// 转发量
	worksForward.value = data.worksForward || [];

	// 收藏量
	worksCollect.value = data.worksCollect || [];

	// 作品分析
	worksTopLike.value = data.worksTopLike || [];

	// 点赞最少的视频
	minVideo.value = data.minVideo;

	// 用户分析处理
	if (data.totalFans) {
		totalFans.value = data.totalFans;
		fansPieData.value = data.totalFans.data || [];
		fansPieTotal.value = data.totalFans.total || {};
	}

	// 内容类型饼图和时间分布图
	contentTypePie.value.data = data.contentTypePie?.data || [];
	contentTypePie.value.total = data.contentTypePie?.total || { count: 0, name: '总计' };
	timeDistribution.value = data.timeDistribution || {
		title: '发文时间区间分布情况',
		row: [],
		dates: [],
	};

	pageLoading.value = false;
};

// 监听subReportData变化，处理数据
watch(
	() => props.subReportData,
	(newVal) => {
		if (newVal) {
			processData(newVal);
		}
	},
	{ immediate: true, deep: true }
);

// 定义导出的模块ID列表，供父组件使用
const moduleIds = computed(() => {
	let baseIds = [];

	// 根据账号数量决定导出方式
	if (dci.value.length > 3) {
		// 账号数量大于3时，分开导出
		baseIds = ['dy-sub-1', 'dy-sub-1-2', 'dy-sub-2', 'dy-sub-3', 'dy-sub-3-2', 'dy-sub-4', 'dy-sub-4-2', 'dy-sub-5', 'dy-sub-6'];
		// 如果有粉丝占比图，添加dy-sub-6-2
		if (fansPieData.value.length > 1) {
			baseIds.push('dy-sub-6-2');
		}
	} else {
		// 账号数量小于等于3时，合并导出
		baseIds = ['dy-sub-1', 'dy-sub-2', 'dy-sub-3', 'dy-sub-4', 'dy-sub-5', 'dy-sub-6'];
	}

	// 如果存在不更新账号列表，添加该模块ID
	if (props.noUpdateAccount && props.noUpdateAccount.length > 0) {
		baseIds.push('dy-no-update');
	}

	return baseIds;
});

defineExpose({
	moduleIds,
});

// 添加formatWorksAnalysisTableData函数
const formatWorksAnalysisTableData = () => {
	const tableData = [];

	// 添加前三名作品
	worksTopLike.value.forEach((work, index) => {
		tableData.push({
			rank: index + 1,
			title: work.title,
			like: tranNum(work.like),
			comment: tranNum(work.comment),
			collect: tranNum(work.collect),
			forward: tranNum(work.forward),
			account: work.account,
		});
	});

	// 添加最少点赞作品
	if (minVideo.value) {
		tableData.push({
			rank: '最少',
			title: minVideo.value.title,
			like: tranNum(minVideo.value.like),
			comment: tranNum(minVideo.value.comment),
			collect: tranNum(minVideo.value.collect),
			forward: tranNum(minVideo.value.forward),
			account: minVideo.value.account,
		});
	}

	return tableData;
};
</script>

<style scoped>
:deep(.el-table .cell) {
	font-size: 18px;
	color: #000;
}
:deep(.el-table--border) {
	border-top: 1px solid #fff;
}
.top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	overflow: hidden;
	height: 200px;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}
.title-wrapper {
	width: 100%;
	/* padding: 20px; */
}
.title1 {
	font-size: 30px;
	width: 25%;
	margin: 0 auto 15px;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 15px;
	line-height: 1.5;
}

.title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	margin: 0px auto 10px;
	text-align: center;
	padding-bottom: 10px;
	line-height: 1.5;
}

.sub-title {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}
.content-wrapper {
	position: relative;
	margin: 0 30px 20px;
	padding: 7px 20px 20px 20px;
	border: #9d9c9a 1px solid;
	overflow: visible;
}
.content::first-line {
	text-indent: 2em; /* 设置为两个空格的宽度 */
}
.content {
	font-size: 18px;
	line-height: 2;
	white-space: inherit !important;
}
.table-wrapper {
	margin: 20px 30px;
}
.chart-wrapper {
	margin: 20px 30px 40px;
}
.text-bold {
	font-weight: bold;
}
.text-blue {
	color: #00abf9;
}
.green-icon {
	padding-top: 12px;
	color: green;
}
.red-icon {
	padding-top: 12px;
	color: red;
}
:deep(.el-loading-spinner) {
	top: 300px;
}
:deep(.el-table) {
	width: 100%;
}
:deep(.el-table th) {
	font-weight: bold;
	color: #000;
	background-color: #f8f8f9;
	padding: 15px 4px !important;
}
:deep(.el-table td) {
	padding: 15px 4px !important;
}
/* 添加按钮样式 */
.btn-wrapper {
	position: absolute;
	right: 50px;
	top: 95px;
	z-index: 1000;
}
* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}
</style>

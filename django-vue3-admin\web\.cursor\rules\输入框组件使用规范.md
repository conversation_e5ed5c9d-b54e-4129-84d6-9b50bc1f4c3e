# 输入框组件使用规范

## 普通输入框

```typescript
fieldName: {
    title: '字段名称',
    type: 'input',
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
            },
            placeholder: '请输入关键词',
        },
    },
    column: {
        minWidth: 120,
    },
    form: {
        rules: [
            {
                required: true,
                message: '必填项',
            },
            {
                max: 50,
                message: '最多50个字符',
            },
        ],
        component: {
            span: 12, // 占据的栅格宽度
            placeholder: '请输入',
        },
    },
}
```

## 密码输入框

```typescript
password: {
    title: '密码',
    type: 'input',
    column: {
        show: false,
    },
    editForm: {
        show: false, // 编辑表单不显示
    },
    form: {
        rules: [
            {
                required: true,
                message: '密码必填项',
            },
        ],
        component: {
            span: 12,
            showPassword: true,
            placeholder: '请输入密码',
        },
    },
}
```

## 数字输入框

```typescript
fieldName: {
    title: '数值字段',
    type: 'number',
    column: {
        minWidth: 100,
    },
    form: {
        component: {
            min: 0,
            max: 100,
            step: 1,
            precision: 0, // 精度
        },
    },
}
```

# 自定义页面开发流程指南

## 📖 概述

本文档记录完整的自定义页面开发流程，包含前端Vue页面创建、菜单配置、后端API接口等所有必要步骤。

## 🎯 开发流程

### 第一阶段：需求分析和规划

1. **明确业务需求**
   - 页面功能定义
   - 用户交互设计
   - 数据展示要求

2. **制定技术方案**
   - 页面结构设计
   - 组件拆分规划
   - API接口规划

### 第二阶段：前端页面开发

#### 1. 创建页面目录结构

```bash
# 标准目录结构
src/views/[module]/[feature]/
├── index.vue              # 主页面组件
├── api.ts                # API接口定义
├── components/           # 功能组件（可选）
│   ├── FilterForm.vue
│   ├── DataTable.vue
│   └── ...
└── types.ts              # TypeScript类型定义（可选）
```

#### 2. 开发API接口层 (api.ts)

```typescript
// 遵循项目约定
import { request } from '/@/utils/service';

// 定义接口类型
export interface ApiResponse<T> {
  code: number;
  data: T;
  msg: string;
}

// 实现API函数
export function getData(params: any): Promise<ApiResponse<any>> {
  return request({
    url: '/api/module/endpoint/',
    method: 'get',
    params,
  });
}
```

#### 3. 开发Vue组件

**技术约定：**
- 使用 `<script setup>` 语法
- 遵循Element Plus UI组件规范
- 实现响应式设计

**主页面模板：**
```vue
<template>
  <div class="page-container">
    <!-- 筛选区域 -->
    <FilterForm v-model="filterParams" @search="handleSearch" />
    
    <!-- 内容展示区域 -->
    <div class="content-area">
      <!-- 业务组件 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
// 业务逻辑实现
</script>
```

### 第三阶段：菜单配置（重要）

#### 1. 创建菜单迁移文件

**文件位置：**
```
backend/apps/[module]/migrations/menu/
└── YYYYMMDD_序号_功能描述.py
```

**文件命名规范：**
- `YYYYMMDD`：当前日期（如：20250813）
- `序号`：001-999，当天内的执行顺序
- `功能描述`：英文简述，下划线分隔

**示例：**`20250813_004_ai_accuracy_analysis_menu.py`

#### 2. 菜单迁移文件内容

```python
# -*- coding: utf-8 -*-

"""
模块名 - 功能描述菜单创建迁移
说明：功能描述和用途
"""

def apply(module_ops):
    # 创建父级菜单（如果需要）
    module_ops.create_menu_if_not_exists({
        'name': '父级菜单名',
        'web_path': '/parent',
        'component': '',
        'icon': 'fa fa-icon-name',
        'is_catalog': True,  # 目录类型
        'sort': 10,
        'status': True,
        'visible': True,
    })
    
    # 创建子菜单，注意:子菜单不需要icon
    module_ops.create_menu_if_not_exists({
        'name': '页面菜单名',
        'parent_name': '父级菜单名',  # 可选：指定父级
        'web_path': '/module/feature',
        'component': 'module/feature/index',
        'component_name': 'PageComponentName',
        'is_catalog': False,
        'sort': 1,
        'status': True,
        'visible': True,
        'description': '页面功能描述'
    })
```

#### 3. 执行菜单迁移

```bash
# 进入后端目录
cd backend/

# 执行指定模块菜单迁移
python manage.py apply_menu_migrations --module [module_name]

# 查看迁移状态
python manage.py list_menu_migrations --module [module_name]
```

**重要说明：**
- 菜单迁移具有幂等性，可重复执行
- `web_path` 必须与前端路由路径一致
- `component` 路径对应Vue组件的实际位置

### 第四阶段：后端API开发（可选）

如果需要新的后端接口，需要：

1. **创建ViewSet类**
2. **定义序列化器**
3. **配置URL路由**
4. **添加权限控制**

### 第五阶段：测试和优化

1. **功能测试**
   - 页面加载测试
   - 交互功能测试
   - 数据展示测试

2. **兼容性测试**
   - 浏览器兼容性
   - 响应式适配
   - 性能优化

## ⚠️ 关键注意事项

### 前端开发约定
- **组件语法**：必须使用 `<script setup>` 语法
- **路径导入**：同目录使用相对路径 `import * as api from './api'`
- **UI组件**：统一使用Element Plus组件库
- **类型安全**：使用TypeScript定义接口类型

### 菜单配置要点
- **路径一致性**：前端路由与菜单 `web_path` 必须一致
- **文件命名**：严格按照日期_序号_描述格式
- **执行顺序**：按时间戳和序号顺序执行迁移
- **幂等性**：迁移可重复执行，已存在的菜单会跳过
- **菜单配置**：子菜单配置不需要icon,父菜单需要icon

### 目录结构规范
- **功能目录**：使用PascalCase命名
- **API文件**：放置在功能目录内
- **组件拆分**：复杂功能拆分为独立组件

## 🔧 工具和命令

### 常用Git操作
```bash
# 添加新文件
git add src/views/module/feature/

# 提交代码
git commit -m "新增[功能名称]页面"

# 推送代码
git push origin [branch_name]
```

### 菜单管理命令
```bash
# 执行所有菜单迁移
python manage.py apply_menu_migrations

# 执行指定模块迁移
python manage.py apply_menu_migrations --module [module]

# 查看迁移状态
python manage.py list_menu_migrations

# 检查模式（不执行）
python manage.py apply_menu_migrations --check
```

## 📚 参考文档

- [[菜单迁移系统设计文档]](../backend/docs/菜单迁移系统设计文档.md) - 详细的菜单迁移系统说明
- [[05-组件开发指南]] - Vue组件开发规范
- [[03-开发流程指南]] - 标准化开发流程

## 🎯 快速检查清单

**开发前：**
- [ ] 明确页面功能需求
- [ ] 设计页面结构和组件拆分
- [ ] 规划API接口

**开发中：**
- [ ] 创建标准目录结构
- [ ] 实现API接口层
- [ ] 开发Vue组件
- [ ] 遵循技术约定和代码规范

**菜单配置：**
- [ ] 创建菜单迁移文件
- [ ] 按规范命名文件
- [ ] 正确配置菜单属性
- [ ] 执行菜单迁移命令
- [ ] 验证菜单显示正确

**测试验证：**
- [ ] 页面功能测试
- [ ] 响应式适配测试
- [ ] 浏览器兼容性测试

---

**文档版本**: v1.0  
**维护人**: 开发团队  
**更新频率**: 根据项目需要更新
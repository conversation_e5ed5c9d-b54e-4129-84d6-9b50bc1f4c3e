// 排行榜相关类型定义

// 字段映射配置接口
export interface FieldMapping {
	label: string; // 显示标签
	field: string; // 字段名
	is_link?: boolean; // 是否为链接字段
	needs_account_lookup?: boolean; // 是否需要账号查找
	formatter?: string; // 格式化函数名
}

// 平台字段映射配置
export interface PlatformFieldMappings {
	[key: string]: FieldMapping;
}

// 导出字段选项接口
export interface ExportFieldOption {
	value: string; // 字段值
	label: string; // 显示标签
}

// 平台类型
export type PlatformType = 'wechat' | 'douyin' | 'weibo';

// 微信平台字段映射配置（按阅读数倒序）
export const WECHAT_FIELD_MAPPINGS: PlatformFieldMappings = {
	index: { label: '序号', field: 'index' },
	account_unique_id: { label: '公众号唯一标识', field: 'account_unique_id' },
	account_name: { label: '公众号名称', field: 'account_name' },
	work_id: { label: '作品ID', field: 'work_id' },
	title: { label: '文章标题', field: 'title', is_link: true },
	idx: { label: '位置', field: 'idx', formatter: '_get_position_text' },
	publish_time: { label: '发布时间', field: 'publish_time', formatter: '_format_datetime' },
	read_count: { label: '阅读数', field: 'read_count' },
	viewing_count: { label: '在看数', field: 'viewing_count' },
	like_count: { label: '点赞数', field: 'like_count' },
	forward_count: { label: '转发数', field: 'forward_count' },
	reward_count: { label: '打赏量', field: 'reward_count' },
	is_original: { label: '是否原创', field: 'is_original', formatter: '_format_boolean' },
	create_time: { label: '创建时间', field: 'create_time', formatter: '_format_datetime' },
	update_time: { label: '更新时间', field: 'update_time', formatter: '_format_datetime' },
};

// 抖音平台字段映射配置（按点赞量倒序）
export const DOUYIN_FIELD_MAPPINGS: PlatformFieldMappings = {
	index: { label: '序号', field: 'index' },
	work_id: { label: '作品ID', field: 'work_id' },
	title: { label: '作品标题', field: 'title', is_link: true },
	account_name: { label: '账号昵称', field: 'account_name' },
	like_count: { label: '点赞量', field: 'like_count' },
	viewing_count: { label: '收藏量', field: 'viewing_count' },
	comment_count: { label: '评论量', field: 'comment_count' },
	forward_count: { label: '分享量', field: 'forward_count' },
	content_type: { label: '内容类型', field: 'content_type', formatter: '_get_content_type_text' },
	publish_time: { label: '发布时间', field: 'publish_time', formatter: '_format_datetime' },
	create_time: { label: '创建时间', field: 'create_time', formatter: '_format_datetime' },
	update_time: { label: '更新时间', field: 'update_time', formatter: '_format_datetime' },
};

// 微博平台字段映射配置（按点赞量倒序）
export const WEIBO_FIELD_MAPPINGS: PlatformFieldMappings = {
	index: { label: '序号', field: 'index' },
	work_id: { label: '微博作品ID', field: 'work_id' },
	title: { label: '作品标题', field: 'title', is_link: true },
	account_name: { label: '账号昵称', field: 'account_name' },
	read_count: { label: '视频播放量', field: 'read_count' },
	like_count: { label: '点赞量', field: 'like_count' },
	comment_count: { label: '评论量', field: 'comment_count' },
	forward_count: { label: '分享量', field: 'forward_count' },
	is_original: { label: '是否原创', field: 'is_original', formatter: '_format_boolean' },
	content_type: { label: '发文类型', field: 'content_type', formatter: '_get_content_type_text' },
	publish_time: { label: '发布时间', field: 'publish_time', formatter: '_format_datetime' },
	create_time: { label: '创建时间', field: 'create_time', formatter: '_format_datetime' },
	update_time: { label: '更新时间', field: 'update_time', formatter: '_format_datetime' },
};

// 根据平台获取字段映射配置
export const getPlatformFieldMappings = (platform: PlatformType): PlatformFieldMappings => {
	switch (platform) {
		case 'wechat':
			return WECHAT_FIELD_MAPPINGS;
		case 'douyin':
			return DOUYIN_FIELD_MAPPINGS;
		case 'weibo':
			return WEIBO_FIELD_MAPPINGS;
		default:
			return WECHAT_FIELD_MAPPINGS;
	}
};

// 根据平台获取导出字段选项
export const getPlatformExportOptions = (platform: PlatformType): ExportFieldOption[] => {
	const mappings = getPlatformFieldMappings(platform);
	return Object.keys(mappings).map((key) => ({
		value: key,
		label: mappings[key].label,
	}));
};

// 默认选中的字段（按平台）
export const getDefaultSelectedFields = (platform: PlatformType): string[] => {
	switch (platform) {
		case 'wechat':
			return [
				'index',
				'account_name',
				'title',
				'idx',
				'publish_time',
				'read_count',
				'viewing_count',
				'like_count',
				'forward_count',
			];
		case 'douyin':
			return [
				'index',
				'title',
				'account_name',
				'like_count',
				'viewing_count',
				'comment_count',
				'forward_count',
				'publish_time',
			];
		case 'weibo':
			return [
				'index',
				'title',
				'account_name',
				'read_count',
				'like_count',
				'comment_count',
				'forward_count',
				'publish_time',
			];
		default:
			return [];
	}
};

<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell_match_type="scope">
				<dict-tag :options="$getEnumDatas($ENUM.CORRECT_DICT_WORD_TYPE)" :value="scope.row.match_type" />
			</template>
			<template #cell_status="scope">
				<dict-tag :options="$getEnumDatas($ENUM.ENABLE_STATUS)" :value="scope.row.status" :color-type="['success', 'warning']" />
			</template>
			<template #cell_error_type="scope">
				{{ scope.row.error_type }}
			</template>
			<template #cell_word_example="scope">
				<el-tooltip :content="scope.row.word_example" placement="top" :show-after="200" popper-class="word-tooltip-popper">
					<div class="truncate-text">{{ formatText(scope.row.word_example) }}</div>
				</el-tooltip>
			</template>
			<template #cell_word_explain="scope">
				<el-tooltip :content="scope.row.word_explain" placement="top" :show-after="200" popper-class="word-tooltip-popper">
					<div class="truncate-text">{{ formatText(scope.row.word_explain) }}</div>
				</el-tooltip>
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import type { CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
	name: 'CorrectDictWordList',
	setup() {
		const { crudBinding, crudRef, crudExpose } = useFs<CreateCrudOptionsRet>({ createCrudOptions });

		// 文本截取方法，超过50个字符显示省略号
		const formatText = (text: string) => {
			if (!text) return '';
			return text.length > 50 ? text.substring(0, 50) + '...' : text;
		};

		// 页面打开后获取列表数据
		onMounted(() => {
			crudExpose.doRefresh();
		});

		return {
			crudBinding,
			crudRef,
			formatText,
		};
	},
});
</script>

<style scoped>
.truncate-text {
	display: inline-block;
	width: 100%;
}
</style>

<style>
.word-tooltip-popper {
	width: 300px !important;
	max-width: 300px;
	white-space: normal;
	word-break: break-all;
	word-wrap: break-word;
	line-height: 1.5;
}
</style> 
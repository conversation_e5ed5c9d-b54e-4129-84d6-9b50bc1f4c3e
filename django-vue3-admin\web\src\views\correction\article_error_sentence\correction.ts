import { request } from '/@/utils/service';

// 错误句子信息
export interface ErrorSentenceInfo {
	id: number;
	ai_checks: any[];
	article_uid: string;
	article_title: string;
	article_url: string;
	sentence: string;
	sentence_start_pos: number;
	sentence_end_pos: number;
	error_type: string;
	wrong_word: string;
	correct_word: string;
	human_judge: number; // 0-未判定, 1-引擎误报, 2-文章错误
	sensitivity_level?: number; // 敏感程度 (1-一般, 2-严重)
	ai_evaluation?: number; // 人工评价
	comment_text?: string; // 人工评论
	account_name: string;
	judge_user: string;
	create_time: string;
	judge_reason?: string | null; // 新增：判定原因
}

// 查询参数
export interface QueryParams {
	id?: number;
	target_id?: string | number;
	task_id?: string | number;
	article_title?: string;
	page?: number;
	page_size?: number;
	error_type?: string;
	account_name?: string;
	human_judge?: number;
	start_time?: string;
	end_time?: string;
	article_uid?: string;
	ai_evaluation?: number;
	ai_judge?: string;
	has_ai_judge?: number;
	wrong_word?: string;
	judge_user_id?: number;
	correct_word_id?: number;
	error_word_id?: number;
	sensitivity_level?: number; // 敏感程度 (1-一般, 2-严重)
	has_correct_word: number; // 是否显示正确词命中错误句子，0-否，1-是
	tenant_id?: number; // 租户id
}

// API响应接口
export interface ApiResponse<T> {
	code: number;
	message: string;
	data: T;
}

// 列表响应数据
export interface ListResponseData {
	total: number;
	page: number;
	page_size: number;
	total_pages: number;
	results: ErrorSentenceInfo[];
}
const apiPrefix = '/api/regulate/results/';
// 获取错误句子列表
export function getArticleErrorSentences(params: QueryParams): Promise<ApiResponse<ListResponseData>> {
	return request({
		url: apiPrefix,
		method: 'get',
		params,
	});
}

// 更新人工判断结果
export function updateJudge(data: { id: number; human_judge: number }): Promise<ApiResponse<any>> {
	return request({
		url: apiPrefix + 'update_judge/',
		method: 'post',
		data,
	});
}

// 设置敏感程度
export function setWarning(data: { id: number; sensitivity_level: number }): Promise<ApiResponse<any>> {
	return request({
		url: apiPrefix + 'set_warning/',
		method: 'post',
		data,
	});
}

// 获取错误类型列表
export function getErrorTypes(): Promise<ApiResponse<string[]>> {
	return request({
		url: apiPrefix + 'error_types/',
		method: 'get',
	});
}

// 导出

export function exportResult(data: any): Promise<ApiResponse<any>> {
	return request({
		url: apiPrefix + 'export_result/',
		method: 'post',
		data,
		responseType: 'blob',
		timeout: 60000,
	});
}
// api/regulate/results/user_map/
export function getSystemUserMap(): Promise<ApiResponse<any>> {
	return request({
		url: apiPrefix + 'user_map/',
		method: 'get',
	});
}

// 搜索关键词
export function searchKeyword(keyword: string): Promise<ApiResponse<any>> {
	return request({
		url: '/api/speech_crawler/search/',
		method: 'get',
		params: { keyword },
	});
}
// 更新判定原因
export function updateJudgeReason(data: { id: number; judge_reason: string }): Promise<ApiResponse<any>> {
	return request({
		url: apiPrefix + 'update_judge_reason/',
		method: 'post',
		data,
	});
}

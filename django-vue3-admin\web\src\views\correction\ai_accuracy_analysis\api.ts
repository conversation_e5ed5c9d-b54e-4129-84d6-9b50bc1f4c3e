import { request } from '/@/utils/service';

// 筛选参数接口
export interface FilterParams {
	tenant_id?: string;
	start_date?: string;
	end_date?: string;
	days?: number;
	judge_user_id?: string;
	error_type?: string;
	judge_status?: string;
}

// 分页参数接口
export interface PaginationParams {
	page?: number;
	page_size?: number;
}

// API响应接口
export interface ApiResponse<T> {
	code: number;
	page: number;
	limit: number;
	total: number;
	data: T;
	msg: string;
}

// 统计数据接口
export interface StatsData {
	total_count: number;
	accuracy_rate: number;
	consistent_count: number;
	ai_false_positive: number;
	ai_false_negative: number;
	ai_uncertain: number;
}

// 错误类型分析数据接口
export interface ErrorTypeAnalysisData {
	error_type: string;
	total_count: number;
	consistent_count: number;
	accuracy_rate: number;
}

// 时间趋势分析数据接口
export interface TrendAnalysisData {
	date: string;
	accuracy_rate: number;
	total_count: number;
}

// 详细记录数据接口
export interface DetailedRecordItem {
	id: number;
	article_id: string;
	error_type: string;
	wrong_word: string;
	correct_word: string;
	sentence: string;
	human_judge: number;
	human_judge_display: string;
	ai_judge: number;
	ai_judge_display: string;
	ai_reason: string;
	create_time: string;
	is_consistent: boolean;
}

// 详细记录响应数据接口
export interface DetailedRecordsData {
	records: DetailedRecordItem[];
	total_count: number;
	page: number;
	page_size: number;
	total_pages: number;
	has_next: boolean;
	has_previous: boolean;
}

// 选项数据接口
export interface OptionData {
	value: string;
	label: string;
}

const apiPrefix = '/api/regulate/ai-accuracy-analysis/';

// 1. 获取统计数据
export function getStats(params: FilterParams): Promise<ApiResponse<StatsData>> {
	return request({
		url: apiPrefix + 'stats/',
		method: 'get',
		params,
	});
}

// 2. 获取错误类型分析数据
export function getErrorTypeAnalysis(params: FilterParams): Promise<ApiResponse<ErrorTypeAnalysisData[]>> {
	return request({
		url: apiPrefix + 'error-type-analysis/',
		method: 'get',
		params,
	});
}

// 3. 获取时间趋势分析数据
export function getTrendAnalysis(params: FilterParams): Promise<ApiResponse<TrendAnalysisData[]>> {
	return request({
		url: apiPrefix + 'trend-analysis/',
		method: 'get',
		params,
	});
}

// 4. 获取详细记录数据
export function getDetailedRecords(params: FilterParams & PaginationParams): Promise<ApiResponse<DetailedRecordsData>> {
	return request({
		url: apiPrefix + 'detailed-records/',
		method: 'get',
		params,
	});
}

// 5. 获取租户选项
export function getTenantOptions(): Promise<ApiResponse<OptionData[]>> {
	return request({
		url: apiPrefix + 'tenant-options/',
		method: 'get',
	});
}

// 6. 获取判定人员选项
export function getJudgeUserOptions(): Promise<ApiResponse<OptionData[]>> {
	return request({
		url: apiPrefix + 'judge-user-options/',
		method: 'get',
	});
}

// 错误类型选项（固定选项）
export const ERROR_TYPE_OPTIONS = [
	{ value: '同音错误', label: '同音错误' },
	{ value: '错别字', label: '错别字' },
	{ value: '标点符号', label: '标点符号' },
	{ value: '语法错误', label: '语法错误' },
	{ value: '其他', label: '其他' },
];

// 判断状态选项（固定选项）
export const JUDGE_STATUS_OPTIONS = [
	{ value: 'consistent', label: '一致判断' },
	{ value: 'ai_false_positive', label: 'AI误报' },
	{ value: 'ai_false_negative', label: 'AI漏报' },
];

// 时间快速选择选项
export const TIME_RANGE_OPTIONS = [
	{ value: 7, label: '最近7天' },
	{ value: 30, label: '最近30天' },
	{ value: 90, label: '最近90天' },
];
<template>
	<div class="filter-container">
		<div class="filter-item">
			<span class="label">ID</span>
			<el-input v-model="searchId" placeholder="请输入目标ID" clearable style="width: 220px" />
		</div>
		<div class="filter-item">
			<span class="label">校对账号</span>
			<el-input v-model="searchWechatName" placeholder="请输入校对账号" clearable style="width: 220px" />
		</div>
		<div class="filter-item">
			<span class="label">校对状态</span>
			<el-select v-model="searchTaskStatus" placeholder="请选择校对状态" clearable filterable style="width: 220px">
				<el-option v-for="task in $getEnumDatas($ENUM.TARGET_STATUS)" :key="task.value" :label="task.label" :value="task.value" />
			</el-select>
		</div>
		<div class="filter-buttons">
			<el-button type="primary" @click="handleSearch" icon="Search">查询</el-button>
			<el-button @click="handleReset" icon="Refresh">重置</el-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import type { CorrectionTaskInfo } from '../api';

// 定义属性
const props = defineProps({
	id: {
		type: Number,
		default: undefined,
	},
	taskId: {
		type: Number,
		default: undefined,
	},
	wechatName: {
		type: String,
		default: '',
	},
	status: {
		type: Number,
		default: undefined,
	},
	taskOptions: {
		type: Array as () => CorrectionTaskInfo[],
		required: true,
	},
});

// 定义事件
const emit = defineEmits(['update:id', 'update:taskId', 'update:wechatName', 'update:status', 'search', 'reset']);

// 本地状态
const searchId = ref(props.id);
const searchTaskId = ref(props.taskId);
const searchWechatName = ref(props.wechatName);
const searchTaskStatus = ref(props.status);

// 监听属性变化
watch(
	() => props.id,
	(newVal) => {
		searchId.value = newVal;
	}
);

watch(
	() => props.taskId,
	(newVal) => {
		searchTaskId.value = newVal;
	}
);

watch(
	() => props.wechatName,
	(newVal) => {
		searchWechatName.value = newVal;
	}
);

watch(
	() => props.status,
	(newVal) => {
		searchTaskStatus.value = newVal;
	}
);

// 同步本地状态到父组件
watch(searchId, (newVal) => {
	emit('update:id', newVal);
});

watch(searchTaskId, (newVal) => {
	emit('update:taskId', newVal);
});

watch(searchWechatName, (newVal) => {
	emit('update:wechatName', newVal);
});

watch(searchTaskStatus, (newVal) => {
	emit('update:status', newVal);
});

// 处理搜索
const handleSearch = () => {
	emit('search');
};

// 处理重置
const handleReset = () => {
	searchId.value = undefined;
	searchTaskId.value = undefined;
	searchWechatName.value = '';
	searchTaskStatus.value = undefined;
	emit('reset');
};
</script>

<style scoped>
.filter-container {
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	gap: 15px;
	margin-bottom: 10px;
	padding: 15px;
	background-color: #f5f7fa;
	border-radius: 4px;
}

.filter-item {
	display: flex;
	align-items: center;
	min-width: 300px;
	margin: 0;
}

.label {
	min-width: 70px;
	margin-right: 10px;
	white-space: nowrap;
	color: #606266;
	font-size: 14px;
}

.filter-buttons {
	display: flex;
	gap: 10px;
	margin-left: auto;
}

@media screen and (max-width: 768px) {
	.filter-container {
		flex-direction: column;
		align-items: stretch;
	}

	.filter-item {
		width: 100%;
	}

	.filter-buttons {
		margin-left: 0;
		justify-content: flex-end;
	}
}
</style>

<script lang="ts">
export default {
	name: 'TargetFilter',
};
</script>

import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';
import { PlatformType } from '/@/stores/constants/platformPermissions';

// Tab项接口定义
export interface TabItem {
	label: string;
	value: string | number;
	icon?: string | object;
	disabled?: boolean;
	permission?: string;
}

/**
 * 生成带权限的平台Tab项
 * @param permissions 权限映射对象，key为平台类型，value为权限字符串
 * @returns 带权限的Tab项数组
 */
export function generatePlatformTabsWithPermissions(permissions: Record<PlatformType, string>): TabItem[] {
	const enumData = getEnumDatas(ENUM_TYPE.PLATFORM_TYPE).slice(0, 3); // 获取平台类型枚举数据前三个

	return enumData.map((item) => ({
		label: item.label,
		value: item.value,
		permission: permissions[item.value as PlatformType],
	}));
}

/**
 * 生成不带权限的平台Tab项（向后兼容）
 * @returns 不带权限的Tab项数组
 */
export function generatePlatformTabs(): TabItem[] {
	const enumData = getEnumDatas(ENUM_TYPE.PLATFORM_TYPE);

	return enumData.map((item) => ({
		label: item.label,
		value: item.value,
	}));
}

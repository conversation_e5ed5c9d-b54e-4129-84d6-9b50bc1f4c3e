<template>
	<el-main class="bg-gray-100">
		<el-row :gutter="15">
			<el-col :xl="6" :lg="8" :md="12" :sm="24" :xs="24">
				<el-card class="stats-card" shadow="hover">
					<template #header>
						<div class="card-header">
							<span>今日文章新增数量</span>
						</div>
					</template>
					<div class="card-body">
						<div class="stats-number">{{ crudBinding.data?.today_article_count || 0 }}</div>
						<div class="stats-icon">
							<el-icon><Document /></el-icon>
						</div>
					</div>
				</el-card>
			</el-col>
			<el-col :xl="6" :lg="8" :md="12" :sm="24" :xs="24">
				<el-card class="stats-card" shadow="hover">
					<template #header>
						<div class="card-header">
							<span>快照文章新增数量</span>
						</div>
					</template>
					<div class="card-body">
						<div class="stats-number">{{ crudBinding.data?.snapshot_article_count || 0 }}</div>
						<div class="stats-icon">
							<el-icon><PictureRounded /></el-icon>
						</div>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</el-main>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { Document, PictureRounded } from '@element-plus/icons-vue';

export default defineComponent({
	name: 'WechatArticleStats',
	components: {
		Document,
		PictureRounded,
	},
	setup() {
		const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

		onMounted(() => {
			crudExpose.doRefresh();
		});

		return {
			crudRef,
			crudBinding,
			crudExpose,
		};
	},
});
</script>

<style lang="scss" scoped>
.stats-card {
	margin-bottom: 20px;
	border-radius: 8px;

	:deep(.el-card__header) {
		padding: 15px 20px;
		border-bottom: 1px solid var(--el-border-color-light);
	}
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;

	span {
		font-size: 16px;
		font-weight: 500;
		color: var(--el-text-color-primary);
	}
}

.card-body {
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.stats-number {
	font-size: 32px;
	font-weight: bold;
	color: var(--el-color-primary);
}

.stats-icon {
	.el-icon {
		font-size: 48px;
		color: var(--el-color-primary-light-8);
	}
}

:deep(.el-main) {
	padding: 20px;
}
</style> 
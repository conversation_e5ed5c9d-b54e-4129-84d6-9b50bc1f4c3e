<template>
	<div class="statistics-container">
		<div class="statistics-title">
			<h3>AI校对效果统计概览</h3>
		</div>
		<el-row :gutter="24">
			<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
				<div class="stat-card total-samples">
					<div class="stat-icon">
						<el-icon><Document /></el-icon>
					</div>
					<div class="stat-content">
						<div class="stat-value">{{ formatNumber(statisticsData.total_count) }}</div>
						<div class="stat-label">总样本数</div>
					</div>
				</div>
			</el-col>
			<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
				<div class="stat-card accuracy-rate">
					<div class="stat-icon">
						<el-icon><SuccessFilled /></el-icon>
					</div>
					<div class="stat-content">
						<div class="stat-value">{{ formatPercentage(statisticsData.accuracy_rate) }}%</div>
						<div class="stat-label">AI准确率</div>
					</div>
				</div>
			</el-col>
			<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
				<div class="stat-card consistent-judge">
					<div class="stat-icon">
						<el-icon><Select /></el-icon>
					</div>
					<div class="stat-content">
						<div class="stat-value">{{ formatNumber(statisticsData.consistent_count) }}</div>
						<div class="stat-label">一致判断数</div>
					</div>
				</div>
			</el-col>
			<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
				<div class="stat-card false-positive">
					<div class="stat-icon">
						<el-icon><Warning /></el-icon>
					</div>
					<div class="stat-content">
						<div class="stat-value">{{ formatNumber(statisticsData.ai_false_positive) }}</div>
						<div class="stat-label">AI误报数</div>
					</div>
				</div>
			</el-col>
			<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
				<div class="stat-card false-negative">
					<div class="stat-icon">
						<el-icon><Close /></el-icon>
					</div>
					<div class="stat-content">
						<div class="stat-value">{{ formatNumber(statisticsData.ai_false_negative) }}</div>
						<div class="stat-label">AI漏报数</div>
					</div>
				</div>
			</el-col>
			<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="4">
				<div class="stat-card uncertain">
					<div class="stat-icon">
						<el-icon><QuestionFilled /></el-icon>
					</div>
					<div class="stat-content">
						<div class="stat-value">{{ formatNumber(statisticsData.ai_uncertain) }}</div>
						<div class="stat-label">AI无法判断</div>
					</div>
				</div>
			</el-col>
		</el-row>

		<!-- 加载状态 -->
		<div v-if="loading" v-loading="loading" class="loading-container">
			<div style="height: 120px;"></div>
		</div>

		<!-- 无数据状态 -->
		<div v-if="!loading && statisticsData.total_count === 0" class="no-data">
			<el-empty description="暂无数据" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Document, SuccessFilled, Select, Warning, Close, QuestionFilled } from '@element-plus/icons-vue';
import { StatsData } from '../api';

defineOptions({
	name: 'StatisticsCards',
});

interface Props {
	data: StatsData;
	loading: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	data: () => ({
		total_count: 0,
		accuracy_rate: 0,
		consistent_count: 0,
		ai_false_positive: 0,
		ai_false_negative: 0,
		ai_uncertain: 0,
	}),
	loading: false,
});

// 计算属性，确保数据安全
const statisticsData = computed(() => props.data);

// 数字格式化函数
const formatNumber = (num: number): string => {
	if (num >= 10000) {
		return (num / 10000).toFixed(1) + 'w';
	} else if (num >= 1000) {
		return (num / 1000).toFixed(1) + 'k';
	}
	return num.toString();
};

// 百分比格式化函数
const formatPercentage = (num: number): string => {
	return Number(num).toFixed(1);
};
</script>

<style lang="scss" scoped>
.statistics-container {
	margin-bottom: 24px;

	.statistics-title {
		margin-bottom: 16px;

		h3 {
			margin: 0;
			color: #303133;
			font-size: 18px;
			font-weight: 600;
		}
	}

	.stat-card {
		background: white;
		border-radius: 8px;
		padding: 20px;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
		border: 1px solid #f0f0f0;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		margin-bottom: 16px;
		min-height: 80px;

		&:hover {
			box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.12);
			transform: translateY(-2px);
		}

		.stat-icon {
			margin-right: 16px;
			width: 40px;
			height: 40px;
			border-radius: 8px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 20px;
		}

		.stat-content {
			flex: 1;

			.stat-value {
				font-size: 24px;
				font-weight: 700;
				line-height: 1.2;
				margin-bottom: 4px;
			}

			.stat-label {
				font-size: 14px;
				color: #666666;
				font-weight: 500;
			}
		}

		// 不同卡片的颜色主题
		&.total-samples {
			.stat-icon {
				background-color: #e3f2fd;
				color: #1976d2;
			}
			.stat-value {
				color: #1976d2;
			}
		}

		&.accuracy-rate {
			.stat-icon {
				background-color: #e8f5e8;
				color: #388e3c;
			}
			.stat-value {
				color: #388e3c;
			}
		}

		&.consistent-judge {
			.stat-icon {
				background-color: #f3e5f5;
				color: #7b1fa2;
			}
			.stat-value {
				color: #7b1fa2;
			}
		}

		&.false-positive {
			.stat-icon {
				background-color: #fff3e0;
				color: #f57c00;
			}
			.stat-value {
				color: #f57c00;
			}
		}

		&.false-negative {
			.stat-icon {
				background-color: #ffebee;
				color: #d32f2f;
			}
			.stat-value {
				color: #d32f2f;
			}
		}

		&.uncertain {
			.stat-icon {
				background-color: #fafafa;
				color: #757575;
			}
			.stat-value {
				color: #757575;
			}
		}
	}

	.loading-container {
		background: white;
		border-radius: 8px;
		margin-bottom: 16px;
	}

	.no-data {
		background: white;
		border-radius: 8px;
		padding: 40px;
		text-align: center;
	}
}

// 响应式适配
@media (max-width: 768px) {
	.statistics-container {
		.stat-card {
			padding: 16px;
			min-height: 70px;

			.stat-icon {
				width: 36px;
				height: 36px;
				margin-right: 12px;
				font-size: 18px;
			}

			.stat-content {
				.stat-value {
					font-size: 20px;
				}

				.stat-label {
					font-size: 13px;
				}
			}
		}
	}
}
</style>
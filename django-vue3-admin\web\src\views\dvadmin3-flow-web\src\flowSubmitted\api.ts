import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/dvadmin3_flow/flow_data/my_pending_handle/';
export function GetList(query: UserPageQuery) {
	query['handle_status'] = 2;
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}
export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + id,
		method: 'get',
	});
}

export function AddObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

export function UpdateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export function DelObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

// 创建独立流程的接口
export function CreateCustomFlow(data: { flow_info_id: number; name: string; pre_change_content: any }) {
	return request({
		url: '/api/dvadmin3_flow/flow_data/start_flow/',
		method: 'post',
		data: data,
	});
}

// 获取可用的流程定义列表（如果需要）
export function GetFlowDefinitions() {
	return request({
		url: '/api/dvadmin3_flow/flow_info/select_list/',
		method: 'get',
	});
}

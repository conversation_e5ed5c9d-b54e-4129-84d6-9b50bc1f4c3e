import { CrudOptions, EditReq, CrudExpose, UserPageQuery, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud'
import * as api from './api'
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';
import { useRouter } from 'vue-router'
import { auth } from "/@/utils/authFunction"

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getList(query)
    }
    const router = useRouter();

    return {
        crudOptions: {
            request: {
                pageRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    }
                }
            },
            toolbar:{
                show: false,
            },
            rowHandle: {
                width: 180,
                buttons: {
                    edit: {
                        text: '查看报表详情',
                        link: true,
                        show: auth('correctionAnalysis:Retrieve'),
                        click: ({ row }) => {
                            router.push({
                                path: '/analysisHelper/correctionDetail',
                                query: { id: row.id}
                            });
                        }
                    },
                    remove: {
                        show: false
                    },
                    view: {
                        show: false
                    },
                    // export: {
                    //     text: '导出报表',
                    //     link: true,
                    //     type: 'primary',
                    //     show: true,
                    //     click: ({ row }) => {
                    //     ElMessageBox.confirm('确认导出报表吗？', '提示', {
                    //         confirmButtonText: '确定',
                    //         cancelButtonText: '取消',
                    //         type: 'warning'
                    //     }).then(async () => {
                    //         try {
                    //             ElNotification.info('报表导出中...');
                    //             console.log('开始导出报表, 行ID:', row.id);
                    //             const res = await api.exportReport(row.id);
                    //             console.log('导出接口返回数据:', res);
                    //             console.log('返回数据类型:', typeof res);
                    //             console.log('返回数据headers:', res.headers);
                    //             console.log('返回数据status:', res.status);
                                
                    //             // 检查res和res.data是否为Blob类型
                    //             const isResBlob = res instanceof Blob;
                    //             const isDataBlob = res.data instanceof Blob;
                    //             console.log('res是否为Blob:', isResBlob);
                    //             console.log('res.data是否为Blob:', isDataBlob);
                                
                    //             if(isDataBlob) {
                    //                 console.log('res.data的类型:', res.data.type);
                    //                 console.log('res.data的大小:', res.data.size);
                    //             }
                                
                    //             const fileName = `${row.title} _${new Date().toLocaleDateString('zh-CN', {
                    //                 year: 'numeric',
                    //                 month: '2-digit',
                    //                 day: '2-digit',
                    //                 hour: '2-digit',
                    //                 minute: '2-digit',
                    //                 second: '2-digit',
                    //             }).replace(/\D/g, '')}.docx`;
                                
                    //             console.log('准备下载文件, 文件名:', fileName);
                    //             const downloadResult = await download.word(
                    //                 res,
                    //                 fileName
                    //             );
                                
                    //             console.log('下载结果:', downloadResult);
                    //             if (downloadResult.success) {
                    //                 ElMessage.success('导出成功');
                    //             } else {
                    //                 ElMessage.warning(downloadResult.message);
                    //             }
                    //         } catch (error: any) {
                    //             console.error('导出失败', error);
                    //             ElMessage.warning(error.message || '导出失败：未知错误');
                    //         }
                    //     }).catch(() => {
                    //         ElMessage.info('已取消导出');
                    //     });

                    //     }
                    // }
                }
            },
            columns: {
                id: {
                    title: 'ID',
                    type: 'text',
                    form: { show: false },
                    search: { show: true }
                },
                title: {
                    title: '标题',
                    type: 'text',
                    search: { show: true },
                    form: { show: true }
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.REPORT_STATUS)
                    }),
                    search: { show: true },
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'daterange',
                                valueFormat: 'YYYY-MM-DD'
                            }
                        }
                    },
                    form: { show: false }
                }
            }
        }
    }
} 
<template>
  <el-dialog
    title="添加词语解释"
    :visible.sync="dialogVisible"
    width="500px"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="explainForm"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="错误词">
        <el-input v-model="form.wrong_word" disabled />
      </el-form-item>
      <el-form-item label="正确词">
        <el-input v-model="form.correct_word" disabled />
      </el-form-item>
      <el-form-item label="解释" prop="explanation">
        <el-input
          v-model="form.explanation"
          type="textarea"
          :rows="4"
          placeholder="请输入解释（建议20-30字）"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button type="warning" @click="handleAskAI">询问AI</el-button>
    </div>
  </el-dialog>
</template>

<script>
// import { createWordExplain } from '@/api/correction'

export default {
  name: 'WordExplainDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    wrongWord: {
      type: String,
      default: ''
    },
    correctWord: {
      type: String,
      default: ''
    },
    copyText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        wrong_word: '',
        correct_word: '',
        explanation: ''
      },
      rules: {
        explanation: [
          { required: true, message: '请输入解释', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.form.wrong_word = this.wrongWord
        this.form.correct_word = this.correctWord
      }
    }
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false
    },
    handleDialogClosed() {
      this.$refs.explainForm.resetFields()
      this.form = {
        wrong_word: '',
        correct_word: '',
        explanation: ''
      }
    },
    async handleSubmit() {
      try {
        await this.$refs.explainForm.validate()
        await createWordExplain(this.form)
        this.$message.success('添加解释成功')
        this.dialogVisible = false
        this.$emit('success')
      } catch (error) {
        if (error !== false) {
          console.error('添加词语解释失败:', error)
        }
      }
    },
    handleAskAI() {
      console.log(this.copyText)
      debugger
      const copyText = `请评估"${this.copyText} 建议是否正确。请给出20-30字的解释，说明为什么这个词语用错了，以及二者的区别。`
      this.$copyText(copyText).then(() => {
        this.$message.success('已复制提示语，请粘贴到AI对话框中')
      })
    }
  }
}
</script>

# Fast-CRUD 使用规范

基于项目中现有模块（tenant、user等）和官方文档的最佳实践指南。

## 📋 目录结构规范

### 标准模块结构
```
views/[module]/
├── index.vue          # 主页面组件
├── crud.tsx          # CRUD配置文件
├── api.ts            # API接口定义
└── components/       # 模块专用组件（可选）
    └── Dialog.vue
```

## 🔧 技术栈要求

### 组件语法选择
根据项目现状，推荐使用：

**推荐：`<script lang="ts" setup>` 语法（如tenant模块）**
```vue
<script lang="ts" setup>
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
</script>
```

**备选：`defineComponent` 语法（如user模块）**  
仅在需要复杂状态管理时使用。

## 📡 API接口规范

### 1. API文件结构（api.ts）
```typescript
import { request } from '/@/utils/service';

// 类型定义
export interface ModuleType {
  id: number;
  name: string;
  // ... 其他字段
}

export interface ModuleQuery {
  page?: number;
  limit?: number;
  search?: string;
  // ... 筛选参数
}

// 基础CRUD接口
export function getList(params?: ModuleQuery) {
  return request({
    url: '/api/module/',
    method: 'get',
    params,
  });
}

export function create(data: Partial<ModuleType>) {
  return request({
    url: '/api/module/',
    method: 'post',
    data,
  });
}

export function update(id: number, data: Partial<ModuleType>) {
  return request({
    url: `/api/module/${id}/`,
    method: 'put',
    data,
  });
}

export function remove(id: number) {
  return request({
    url: `/api/module/${id}/`,
    method: 'delete',
  });
}
```

### 2. 数据格式约定
API响应必须遵循Django分页格式：
```json
{
  "code": 2000,
  "msg": "success", 
  "page": 1,
  "limit": 10,
  "total": 100,
  "data": [...] // 记录数组
}
```

## 🎛️ CRUD配置规范

### 1. 基础结构（crud.tsx）
```typescript
import { CreateCrudOptionsRet, CrudExpose } from '@fast-crud/fast-crud';
import * as api from './api';

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
  
  // 请求函数定义
  const pageRequest = async (query: any) => {
    return await api.getList(query); // ✅ 直接返回API结果
  };

  const addRequest = async ({ form }: any) => {
    return await api.create(form);
  };

  const editRequest = async ({ form, row }: any) => {
    return await api.update(row.id, form);  
  };

  const delRequest = async ({ row }: any) => {
    return await api.remove(row.id);
  };

  return {
    crudOptions: {
      request: {
        pageRequest,    // ✅ 关键：直接返回API结果，不要自定义转换
        addRequest,
        editRequest,
        delRequest,
      },
      
      // 表格配置
      table: {
        size: 'default',
        stripe: true,
      },
      
      // 字段配置
      columns: {
        // 字段定义...
      }
    }
  };
}
```

### 2. ⚠️ 关键规则：pageRequest数据处理

**✅ 正确做法：直接返回API结果**
```typescript
const pageRequest = async (query: any) => {
  const res = await api.getList(query);
  return res; // 让全局transformRes处理数据转换
};
```

**❌ 错误做法：自定义数据转换**
```typescript
const pageRequest = async (query: any) => {
  const res = await api.getList(query);
  return {
    records: res.data,  // ❌ 不要手动转换
    total: res.total,   // ❌ 这会破坏全局配置
  };
};
```

### 3. 全局数据转换机制
项目在`src/settings.ts`中配置了全局`transformRes`：
```typescript
transformRes: ({res}: any) => {
  return {
    records: res.data, 
    currentPage: res.page, 
    pageSize: res.limit, 
    total: res.total
  };
}
```

**所有pageRequest必须依赖此全局配置，不要重复实现！**

## 🎨 字段配置规范

### 1. 基础字段类型
```typescript
columns: {
  id: {
    title: 'ID',
    type: 'number',
    form: { show: false },
    column: { width: 80, sortable: true }
  },
  
  name: {
    title: '名称',
    type: 'input',
    search: { show: true },
    form: {
      rules: [{ required: true, message: '请输入名称' }]
    },
    column: { minWidth: 150 }
  }
}
```

### 2. 字典选择字段
```typescript
status: {
  title: '状态',
  type: 'dict-select',
  dict: [  // ✅ 使用静态数组
    { value: 'active', label: '激活', color: 'success' },
    { value: 'inactive', label: '禁用', color: 'danger' }
  ],
  search: { show: true },
  column: {
    component: {
      name: 'fs-dict-select',  // ✅ 使用fs-dict-select组件
      color: 'auto'
    }
  }
}
```

### 3. 时间字段
```typescript
created_at: {
  title: '创建时间',
  type: 'datetime',
  form: { show: false },
  column: { 
    width: 160,
    sortable: true
    // ❌ 不要使用formatter返回HTML字符串！
    // formatter: ({ value }: { value: string }) => {
    //   return value ? new Date(value).toLocaleString('zh-CN') : '';
    // }
  }
}
```

### 4. ⚠️ Formatter函数使用警告

**❌ 错误做法：返回HTML字符串**
```typescript
column: {
  formatter: ({ value }: { value: string }) => {
    return `<span class="text-primary">${value}</span>`; // ❌ 会显示HTML标签
  }
}
```

**✅ 正确做法：依赖Element Plus组件**
```typescript
column: {
  component: {
    name: 'fs-dict-select',  // ✅ 使用组件自动渲染
    color: 'auto'
  }
}

// 或者完全移除formatter，让系统自动处理
column: {
  width: 120,
  sortable: true  // ✅ 依赖默认渲染
}
```

**说明**：Fast-CRUD框架会自动处理数据显示，包括时间格式化、状态标签等。手动返回HTML字符串会导致标签被直接显示而不是渲染。

## 🚀 组件模板规范

### 标准模板（推荐）
**方式一：`<script setup>` 语法（推荐）**
```vue
<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <!-- 自定义单元格模板 -->
      <template #cell_status="scope">
        <dict-tag 
          :options="statusOptions" 
          :value="scope.row.status"
        />
      </template>
    </fs-crud>
  </fs-page>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// ✅ 关键：页面挂载后自动加载数据
onMounted(() => {
  crudExpose.doRefresh();
});
</script>
```

**方式二：`defineComponent` 语法（也支持）**
```vue
<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
  name: 'ModuleName',
  setup() {
    const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

    // ✅ 关键：页面挂载后自动加载数据
    onMounted(() => {
      crudExpose.doRefresh();
    });

    return { crudBinding, crudRef, crudExpose };
  },
});
</script>
```

## 🔒 权限控制规范

### 按钮权限
```typescript
actionbar: {
  buttons: {
    add: {
      show: auth('module:create')  // 使用auth函数
    },
    export: {
      show: auth('module:export'),
      click: () => exportData()
    }
  }
},

rowHandle: {
  buttons: {
    edit: { 
      show: auth('module:update')
    },
    remove: { 
      show: auth('module:delete') 
    }
  }
}
```

## 📋 最佳实践清单

### ✅ 开发检查清单
- [ ] **页面自动加载数据**: 在onMounted中调用 `crudExpose.doRefresh()` 
- [ ] pageRequest直接返回API结果，不做数据转换
- [ ] API接口遵循标准Django分页格式  
- [ ] 使用静态dict数组而非loadDict函数
- [ ] 字典字段使用fs-dict-select组件
- [ ] 添加适当的权限控制
- [ ] 表单验证规则完整
- [ ] 时间字段正确格式化
- [ ] 搜索字段合理配置

### ❌ 常见错误
- ❌ **忘记自动加载数据**: 页面显示"暂无数据"，需要手动点击刷新才能加载
- ❌ 在pageRequest中手动转换数据格式
- ❌ 使用不存在的loadDict函数
- ❌ 忘记配置form.show控制字段显示
- ❌ 没有为dict-select字段配置fs-dict-select组件
- ❌ 缺少必要的权限控制
- ❌ 在formatter函数中返回HTML字符串（会导致HTML直接显示）

## 🧪 测试验证

### 功能测试点
1. **列表加载**：页面能正常显示数据列表
2. **分页功能**：分页切换正常工作
3. **搜索筛选**：搜索条件生效
4. **增删改查**：基础CRUD操作正常
5. **权限控制**：按钮权限正确显示/隐藏
6. **数据验证**：表单验证规则生效

### 调试技巧
- 检查浏览器控制台是否有错误
- 验证API响应格式是否符合预期
- 确认transformRes全局配置是否生效
- 使用Vue DevTools检查数据流

---

**遵循此规范可避免99%的常见问题，确保代码质量和维护性。**
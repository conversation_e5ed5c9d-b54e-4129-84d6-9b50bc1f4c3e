<template>
	<el-dialog v-model="dialogVisible" title="导出排行榜图片" width="50%" :before-close="handleClose" destroy-on-close>
		<el-form :model="form" label-width="100px" class="export-form">
			<el-form-item label="导出数量" required>
				<el-radio-group v-model="form.exportCount">
					<el-radio value="10">前10名</el-radio>
					<el-radio value="20">前20名</el-radio>
					<el-radio value="custom">自定义数量</el-radio>
					<el-radio value="all"
						>全部 <span v-if="props.totalCount > 0">({{ props.totalCount }}条)</span></el-radio
					>
				</el-radio-group>

				<!-- 自定义数量输入框 -->
				<div v-if="form.exportCount === 'custom'" class="custom-count-input">
					<el-input-number v-model="form.customCount" :min="1" :max="props.totalCount || 1000" placeholder="请输入导出数量" style="width: 200px" />
					<span class="input-suffix">名</span>
				</div>

				<div class="form-tip">选择要导出的排行榜数据数量</div>
			</el-form-item>
			<el-form-item label="标题设置">
				<div class="title-inputs">
					<div class="title-row">
						<label>第一行：</label>
						<el-input v-model="form.titleLine1" placeholder="请输入第一行标题（可选）" maxlength="30" show-word-limit />
					</div>
					<div class="title-row">
						<label>第二行：</label>
						<el-input v-model="form.titleLine2" placeholder="请输入第二行标题（可选）" maxlength="30" show-word-limit />
					</div>
					<div class="title-row">
						<label>第三行：</label>
						<el-input v-model="form.titleLine3" placeholder="请输入第三行标题（可选）" maxlength="30" show-word-limit />
					</div>
				</div>
				<div class="form-tip">设置排行榜图片的标题内容，所有行都是可选的。如果某行没有内容则不会显示</div>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleConfirm" :loading="loading">确定导出</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	defaultTitle: {
		type: String,
		default: '',
	},
	totalCount: {
		type: Number,
		default: 0,
	},
});

const emit = defineEmits(['update:modelValue', 'confirm']);

const dialogVisible = ref(props.modelValue);
const loading = ref(false);

const form = ref({
	exportCount: '10', // 默认导出前10名
	customCount: 10, // 自定义数量默认值
	titleLine1: props.defaultTitle || '',
	titleLine2: '',
	titleLine3: '',
});

// 重置表单
const resetForm = () => {
	form.value = {
		exportCount: '10',
		customCount: 10,
		titleLine1: props.defaultTitle || '',
		titleLine2: '',
		titleLine3: '',
	};
};

watch(
	() => props.modelValue,
	(val) => {
		dialogVisible.value = val;
		if (val) {
			resetForm();
		}
	}
);

watch(
	() => props.defaultTitle,
	(val) => {
		form.value.titleLine1 = val;
	}
);

watch(dialogVisible, (val) => {
	emit('update:modelValue', val);
});

const handleClose = () => {
	dialogVisible.value = false;
	loading.value = false;
};

const handleConfirm = () => {
	// 表单验证
	if (!form.value.exportCount) {
		ElMessage.warning('请选择导出数量');
		return;
	}

	// 如果选择了自定义数量，验证自定义数量
	if (form.value.exportCount === 'custom') {
		if (!form.value.customCount || form.value.customCount < 1) {
			ElMessage.warning('请输入有效的导出数量');
			return;
		}
		if (props.totalCount > 0 && form.value.customCount > props.totalCount) {
			ElMessage.warning(`导出数量不能超过总数量(${props.totalCount})`);
			return;
		}
	}

	// 如果第一行标题为空，使用默认标题
	if (!form.value.titleLine1 || (typeof form.value.titleLine1 === 'string' && !form.value.titleLine1.trim())) {
		form.value.titleLine1 = props.defaultTitle || '排行榜';
	}

	loading.value = true;
	emit('confirm', form.value, loading);
};

const setLoading = (val) => {
	loading.value = val;
};

defineExpose({
	setLoading,
});
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.export-form {
	padding: 10px;
}

.form-tip {
	font-size: 12px;
	color: #909399;
	margin-top: 5px;
	line-height: 1.4;
}

:deep(.el-radio) {
	margin-right: 15px;
	margin-bottom: 5px;
}

:deep(.el-form-item) {
	margin-bottom: 20px;
}

.custom-count-input {
	margin-top: 10px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.input-suffix {
	color: #606266;
	font-size: 14px;
}

.title-inputs {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.title-row {
	display: flex;
	align-items: center;
	gap: 10px;
}

.title-row label {
	min-width: 60px;
	font-size: 14px;
	color: #606266;
}
</style>
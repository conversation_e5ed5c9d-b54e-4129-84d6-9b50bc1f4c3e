<template>
	<fs-page class="PageWechatOfficialAccountArticle">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #actionbar-left>
				<common-tabs v-model="currentTab" :items="platformItems" @change="handleTabChange" :size="'large'" />
			</template>
			<template #search_issue_time="scope">
				<el-date-picker
					style="width: 300px"
					v-model="scope.form.issue_time"
					type="daterange"
					value-format="YYYY-MM-DD"
					@change="scope.form.date_range = scope.form.issue_time.join(',')"
				/>
			</template>
			<template #cell_is_original="scope">
				<el-tag :type="scope.row.is_original ? 'success' : 'warning'" size="small">
					{{ scope.row.is_original ? '是' : '否' }}
				</el-tag>
			</template>
			<template #cell_title="scope">
				<a :href="scope.row.url" target="_blank" class="article-title">{{ scope.row.title ? scope.row.title : `暂无标题` }}</a>
			</template>
			<template #cell_content_text="scope">
				<a :href="scope.row.url" target="_blank" class="article-title">{{ scope.row.content_text ? scope.row.content_text : `暂无标题` }}</a>
			</template>
			<template #cell_issue_time="scope">
				{{ scope.row.issue_time }}
			</template>
		</fs-crud>

		<!-- 导出弹窗 -->
		<ExportDialog
			v-model="exportDialogVisible"
			:platform="currentPlatformType"
			:show-platform-info="true"
			:show-export-works-option="false"
			@confirm="handleExportConfirm"
			ref="exportDialogRef"
		/>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, defineAsyncComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { useRoute, useRouter } from 'vue-router';
import { WECHAT_ARTICLE_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions, TabItem } from '/@/utils/platformTabsHelper';
import { ElMessage } from 'element-plus';
import * as api from './api';
import download from '/@/utils/download';

// 动态导入组件
const CommonTabs = defineAsyncComponent(() => import('/@/components/CommonTabs/index.vue'));
const ExportDialog = defineAsyncComponent(() => import('/@/components/ExportDialog/index.vue'));

export default defineComponent({
	name: 'WechatOfficialAccountArticle',
	components: {
		CommonTabs,
		ExportDialog,
	},
	setup() {
		const route = useRoute();
		const router = useRouter();

		// 优先使用platform_type参数，其次使用tab_type参数，如果都没有则默认为'wechat'
		const currentTab = ref((route.query.tab_type as string) || (route.query.platform_type as string) || 'wechat');

		// 定义当前平台类型
		const currentPlatformType = ref(currentTab.value);
		// 平台类型选项数据
		const platformItems = ref<TabItem[]>([]);
		// 获取路由中的tenant_id
		const tenantId = route.query.tenant_id ? Number(route.query.tenant_id) : undefined;

		// 导出相关状态
		const exportDialogVisible = ref(false);
		const exportDialogRef = ref();

		// 获取平台类型数据
		const loadPlatformTypes = () => {
			platformItems.value = generatePlatformTabsWithPermissions(WECHAT_ARTICLE_PERMISSIONS);
		};

		// 显示导出弹窗
		const showExportDialog = (platform: string) => {
			currentPlatformType.value = platform;
			exportDialogVisible.value = true;
		};

		// crud组件的ref
		const { crudBinding, crudRef, crudExpose } = useFs({
			createCrudOptions,
			context: { currentPlatformType, showExportDialog },
		});

		// Tab变更处理函数
		const handleTabChange = (value: string) => {
			console.log(value, '切换平台类型');
			// 更新当前选中的Tab
			currentTab.value = value;
			// 更新当前平台类型
			currentPlatformType.value = value;

			// 获取当前的搜索表单数据
			const currentSearchForm = crudExpose.getSearchFormData();
			const currentTenantId = currentSearchForm?.tenant_id !== undefined ? currentSearchForm.tenant_id : tenantId;
			const urlPlatformType = route.query.platform_type as string;
			// 初始化搜索参数
			const searchFormData: any = {
				platform_type: value,
				tenant_id: currentTenantId,
				wechat_official_account: undefined,
				wechat_unique_id: undefined,
			};

			// 严格按照原有逻辑：只有当前tab与URL中platform_type一致时，才传递id参数
			if (value !== 'wechat' && value === urlPlatformType && route.query.id) {
				console.log('添加wechat_official_account参数', route.query.id);
				searchFormData.wechat_official_account = route.query.id as string;
			}
			if (value === 'wechat' && route.query.unique_id) {
				console.log('添加wechat_unique_id参数', route.query.unique_id);
				searchFormData.wechat_unique_id = route.query.unique_id as string;
			}

			// 设置搜索表单
			crudExpose.setSearchFormData({
				form: searchFormData,
			});

			// 更新路由参数，保留所有现有参数，同时更新tab_type和platform_type
			const query = { ...route.query, tab_type: value, tenant_id: currentTenantId };
			router.replace({ query });
		};

		// 处理导出确认
		const handleExportConfirm = async (exportParams: any) => {
			try {
				// 设置loading状态
				exportDialogRef.value?.setLoading(true);
				ElMessage.info('正在导出数据，请稍候...');

				// 获取当前查询参数
				const query = crudExpose.getSearchFormData();

				// 直接设置导出字段
				query.export_columns = exportParams.export_columns.join(',');

				let res: any;
				let fileName = '';
				if (currentPlatformType.value === 'wechat') {
					res = await api.exportWechatOfficialAccountArticleList(query);
					fileName = `${query.wechat_official_account_name || '微信公众号'}作品.xlsx`;
				} else if (currentPlatformType.value === 'weibo') {
					res = await api.exportWeiboOfficialAccountArticleList(query);
					fileName = `${query.account_nickname || '微博'}作品.xlsx`;
				} else if (currentPlatformType.value === 'douyin') {
					res = await api.exportDouyinOfficialAccountArticleList(query);
					fileName = `${query.account_nickname || '抖音'}作品.xlsx`;
				}

				const downloadResult = await download.excel(res.data, fileName);

				if (downloadResult.success) {
					ElMessage.success('导出成功');
					exportDialogRef.value?.closeDialog();
				} else {
					ElMessage.warning(downloadResult.message);
				}
			} catch (error: any) {
				console.error('导出失败', error);
				ElMessage.warning(error.message || '导出失败：未知错误');
			} finally {
				// 无论成功还是失败，都将loading状态设置为false
				exportDialogRef.value?.setLoading(false);
			}
		};

		// 在组件挂载后加载数据
		onMounted(() => {
			loadPlatformTypes();
			crudExpose.doRefresh();
		});

		return {
			crudBinding,
			crudRef,
			crudExpose,
			currentTab,
			platformItems,
			handleTabChange,
			exportDialogVisible,
			exportDialogRef,
			currentPlatformType,
			handleExportConfirm,
		};
	},
});
</script>

<style scoped>
:deep(.fs-page) {
	padding: 0;
}

:deep(.article-title) {
	color: #409eff;
	text-decoration: underline;
	cursor: pointer;
}

:deep(.article-title:hover) {
	color: #66b1ff;
}
</style> 
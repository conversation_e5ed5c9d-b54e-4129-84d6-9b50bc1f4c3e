<template>
	<div>
		<Transfer
			filterable
			v-model="localCurrentBoundAccount"
			:data="data"
			:hide-footer="true"
			@change="dataChange"
			:titles="titles"
			:render-content="renderContent"
			ref="transferRef"
		>
		</Transfer>
	</div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { Transfer } from './index';
import { ElTooltip } from 'element-plus';
import { defineProps, defineEmits } from 'vue';

// 组件Props定义
const props = defineProps({
	// 当前已绑定的账号
	currentBoundAccount: {
		type: Array,
		default: () => [],
	},
	// 账号列表数据
	data: {
		type: Array,
		default: () => [],
	},
	// 自定义标题
	titles: {
		type: Array,
		default: () => ['未绑定', '已绑定'],
	},
});

// 组件Emits定义
const emit = defineEmits(['dataChange']);

// 组件引用和响应式状态
const transferRef = ref(null);
const localCurrentBoundAccount = ref([]);

onMounted(() => {
	console.log(transferRef.value);
	// 初始化已绑定账号
	localCurrentBoundAccount.value = JSON.parse(JSON.stringify(props.currentBoundAccount));
});

// 监听 currentBoundAccount 变化
watch(
	() => props.currentBoundAccount,
	(val) => {
		console.log(val, '监听');
		localCurrentBoundAccount.value = val;
		nextTick(() => {
			// Vue3中访问子组件的方式发生了变化
			// 清空左边搜索
			if (transferRef.value && transferRef.value.leftPanel) {
				transferRef.value.leftPanel.query = '';
			}
			// 清空右边搜索
			if (transferRef.value && transferRef.value.rightPanel) {
				transferRef.value.rightPanel.query = '';
			}
		});
	},
	{ immediate: true }
);

// 数据变化事件
const dataChange = (value) => {
	console.log(value, 'value');
	// 将穿梭框结果发送出去
	emit('dataChange', value);
};

// 检查文本是否超出
const checkTextOverflow = (text) => {
	// 创建一个临时span元素来测量文本宽度
	const span = document.createElement('span');
	span.style.visibility = 'hidden';
	span.style.whiteSpace = 'nowrap';
	span.innerHTML = text;
	document.body.appendChild(span);

	// 获取文本宽度
	const textWidth = span.offsetWidth;
	// 获取容器宽度（transfer-panel的item宽度减去padding和其他空间）
	let containerWidth = 150; // 预设一个合理的宽度值
	// 宽度要根据屏幕大小来设置
	if (window.innerWidth <= 1000) {
		containerWidth = 70;
	} else if (window.innerWidth <= 1160) {
		containerWidth = 140;
	} else if (window.innerWidth <= 1200) {
		containerWidth = 170;
	} else {
		containerWidth = 200;
	}

	document.body.removeChild(span);
	return textWidth > containerWidth;
};

// 自定义渲染内容
const renderContent = (h, option) => {
	// 检查文本是否超出
	const isOverflow = checkTextOverflow(option.label);

	// 如果超出则使用tooltip，否则直接显示文本
	if (isOverflow) {
		return h(
			ElTooltip,
			{
				class: 'item',
				effect: 'dark',
				content: option.label,
				placement: 'top',
			},
			{
				default: () => h('span', { class: 'transfer-item-text' }, option.label),
			}
		);
	} else {
		return h('span', { class: 'transfer-item-text' }, option.label);
	}
};

// 暴露组件方法和属性
defineExpose({
	transferRef,
	localCurrentBoundAccount,
});
</script>

<style scoped>
/* 添加注释 */

/* 小屏幕设备样式 */
@media screen and (max-width: 1177px) {
	:deep(.el-transfer-panel) {
		width: 35%;
		height: 350px;
	}
	:deep(.el-transfer-panel__list.is-filterable) {
		height: 230px;
	}
	:deep(.el-transfer__buttons) {
		padding: 0 10px;
	}
}
@media screen and (min-width: 1178px) and (max-width: 1440px) {
	:deep(.el-transfer-panel) {
		width: 38%;
		height: 350px;
	}
	:deep(.el-transfer-panel__list.is-filterable) {
		height: 230px;
	}
	:deep(.el-transfer__buttons) {
		padding: 0 10px;
	}
}

/* 中等屏幕设备样式 */
@media screen and (min-width: 1441px) and (max-width: 1920px) {
	:deep(.el-transfer-panel) {
		width: 40%;
		height: 400px;
	}
	:deep(.el-transfer-panel__list.is-filterable) {
		height: 280px;
	}
	:deep(.el-transfer__buttons) {
		padding: 0 15px;
	}
}

/* 大屏幕设备样式 */
@media screen and (min-width: 1921px) {
	:deep(.el-transfer-panel) {
		width: 40%;
		height: 450px;
	}
	:deep(.el-transfer-panel__list.is-filterable) {
		height: 330px;
	}
}

/* 添加新的样式 */
:deep(.el-transfer-panel__item) {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
:deep(.el-transfer-panel__body) {
	height: 90%;
}
</style>

<template>
	<div class="table-container">
		<el-table
			v-loading="loading"
			:data="list"
			border
			fit
			highlight-current-row
			style="width: 100%"
			:height="tableHeight"
			:row-key="getRowKey"
			:key="tableKey"
		>
			<el-table-column prop="id" label="ID" width="60" />
			<el-table-column prop="sentence" label="错误句子" min-width="300">
				<template #default="{ row }">
					<div class="sentence-wrapper" @click="handleCopySentenceData(row)">
						<span v-if="row.sentence">
							{{ row.sentence.slice(0, row.sentence_start_pos)
							}}<span class="error-word">{{ row.sentence.slice(row.sentence_start_pos, row.sentence_end_pos) }}</span
							>{{ row.sentence.slice(row.sentence_end_pos) }}
						</span>
					</div>
				</template>
			</el-table-column>
			<el-table-column label="文章标题" min-width="130">
				<template #default="{ row }">
					<div class="article-title-container">
						<!-- 
						<el-tooltip v-if="row" :content="JSON.stringify(row)" placement="top" show-after="1000">
							<el-icon><InfoFilled /></el-icon>
						</el-tooltip>
						显示行数据的详细信息，用于调试和查看完整数据 -->
						<el-tooltip v-if="row.article_title && row.article_title.length > 15" :content="row.article_title" placement="top" :show-after="1000">
							<a :href="row.article_url" target="_blank" class="article-title-link">{{ row.article_title.slice(0, 15) }}...</a>
						</el-tooltip>
						<a v-else :href="row.article_url" target="_blank" class="article-title-link">{{ row.article_title }}</a>
						<el-button type="primary" link size="small" class="error-preview-btn" @click="handleErrorPreview(row.article_id)">
							<el-icon><View /></el-icon>错误预览
						</el-button>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="wrong_word" label="错误词" min-width="120">
				<template #default="{ row }">
					<span class="wrong-word-clickable" @click="handleSearchWord(row.wrong_word)">{{ row.wrong_word }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="correct_word" label="建议修正词" width="100" />
			<el-table-column label="人工判定结果" width="100">
				<template #default="{ row }">
					<el-dropdown trigger="click" @command="(value: number) => handleHumanConfidenceChange(row, value)">
						<dict-tag
							:options="$getEnumDatas($ENUM.JUDGE_STATUS)"
							:value="Number(row.human_judge)"
							:color-type="['info', 'danger', 'success', 'warning']"
							class="clickable-tag"
						/>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item v-for="item in $getEnumDatas($ENUM.JUDGE_STATUS)" :key="item.value" :command="item.value">
									<dict-tag :options="$getEnumDatas($ENUM.JUDGE_STATUS)" :value="item.value" :color-type="['info', 'danger', 'success', 'warning']" />
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</el-table-column>
			<el-table-column label="敏感程度" width="100">
				<template #default="{ row }">
					<el-dropdown trigger="click" @command="(value: number) => handleSensitivityChange(row, value)">
						<template v-if="row.sensitivity_level !== null && row.sensitivity_level !== undefined && row.sensitivity_level !== ''">
							<dict-tag
								:options="$getEnumDatas($ENUM.SENSITIVITY_LEVEL)"
								:value="Number(row.sensitivity_level)"
								:color-type="['info', 'warning', 'danger']"
								class="clickable-tag"
							/>
						</template>
						<template v-else>
							<el-tag type="info">未判定</el-tag>
						</template>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item v-for="item in $getEnumDatas($ENUM.SENSITIVITY_LEVEL)" :key="item.value" :command="item.value">
									<dict-tag :options="$getEnumDatas($ENUM.SENSITIVITY_LEVEL)" :value="item.value" :color-type="['info', 'warning', 'danger']" />
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</el-table-column>
			<el-table-column label="AI判定" width="100">
				<template #default="{ row }">
					<dict-tag
						v-if="row.ai_checks[0]"
						:options="$getEnumDatas($ENUM.JUDGE_STATUS)"
						:value="Number(row.ai_checks[0].ai_judge)"
						:color-type="['info', 'danger', 'success', 'warning']"
					/>
				</template>
			</el-table-column>
			<el-table-column prop="judge_reason" label="判定原因" width="100">
				<template #default="{ row }">
					<span v-if="row.judge_reason">
						<el-tooltip v-if="row.judge_reason && row.judge_reason.length > 10" :content="row.judge_reason" placement="top">
							<span>{{ row.judge_reason.slice(0, 10) }}...</span>
						</el-tooltip>
						<span v-else>{{ row.judge_reason }}</span>
					</span>
					<span v-else-if="row.ai_checks && row.ai_checks[0] && row.ai_checks[0].ai_reason">
						<el-tooltip
							v-if="row.ai_checks[0].ai_reason && row.ai_checks[0].ai_reason.length > 10"
							:content="row.ai_checks[0].ai_reason"
							placement="top"
						>
							<span>{{ row.ai_checks[0].ai_reason.slice(0, 10) }}...</span>
						</el-tooltip>
						<span v-else>{{ row.ai_checks[0].ai_reason }}</span>
					</span>
					<el-button
						type="primary"
						link
						size="small"
						v-auth="'ProofreadTask:EditResult'"
						class="error-preview-btn"
						@click="handleEditCorrectionResult(row.id)"
					>
						<el-icon><Edit /></el-icon>编辑
					</el-button>
				</template>
			</el-table-column>
			<el-table-column prop="error_type" label="错误类型" width="100" />
			<el-table-column prop="account_name" label="账号名称" width="150" />
			<el-table-column prop="human_judge_nickname" label="校对人" width="100" />
			<el-table-column prop="correct_word_name" label="命中正确词词库" width="100" />
			<el-table-column prop="correct_word_id" label="命中正确词词库id" width="100" />
			<el-table-column prop="create_time" label="检测时间" width="120" />
			<el-table-column prop="issue_time" label="发文时间" width="120" />
		</el-table>

		<el-dialog v-model="dialogVisible" title="搜索结果" width="80%">
			<div v-if="searchLoading" class="search-loading">
				<el-icon class="is-loading"><loading /></el-icon>
				<p>正在搜索中...</p>
			</div>
			<div v-else class="search-result-container">
				<div style="text-align: center; font-size: 18px; font-weight: bold">关键词: {{ searchResult.keyword }}</div>

				<div class="statistics-container">
					<h4>年度出现次数统计</h4>
					<div v-if="searchResult.statistics && searchResult.statistics.length > 0" class="statistics-chart">
						<div v-for="stat in searchResult.statistics" :key="stat.year" class="stat-item">
							<div class="year">{{ stat.year }}</div>
							<div class="count-bar" :style="{ height: `${Math.min(stat.count * 3, 180)}px` }">
								<span class="count-text">{{ stat.count }}</span>
							</div>
						</div>
					</div>
					<div v-else class="empty-data">
						<el-icon><warning /></el-icon>
						<span>暂无统计数据</span>
					</div>
				</div>

				<div class="articles-container">
					<div v-if="Object.keys(searchResult.articles || {}).length > 0">
						<el-tabs v-model="activeYear" type="card">
							<el-tab-pane
								v-for="year in Object.keys(searchResult.articles).sort((a, b) => Number(b) - Number(a))"
								:key="year"
								:label="year + '年'"
								:name="year"
							>
								<div v-for="(article, index) in searchResult.articles[year]" :key="index" class="article-item">
									<div class="article-header">
										<a :href="article.url" target="_blank" class="article-title">{{ article.title }}</a>
										<span class="article-source">{{ article.source }}</span>
									</div>
									<div class="article-sentences">
										<p
											v-for="(sentence, sentIdx) in article.sentences"
											:key="sentIdx"
											class="sentence"
											v-html="highlightKeyword(sentence, searchResult.keyword)"
										></p>
									</div>
								</div>
							</el-tab-pane>
						</el-tabs>
					</div>
					<div v-else class="empty-data">
						<el-icon><warning /></el-icon>
						<span>暂无相关文章</span>
					</div>
				</div>
			</div>
		</el-dialog>

		<!-- 新增：编辑判定原因弹窗 -->
		<el-dialog v-model="editReasonDialogVisible" title="编辑判定原因" width="60%">
			<div class="edit-reason-container">
				<div class="reason-input-section">
					<!-- <label class="section-label">判定原因：</label> -->
					<el-input v-model="editingReasonContent" type="textarea" :rows="5" placeholder="请输入判定原因"></el-input>
				</div>
				<div class="explain-word-section">
					<el-button type="primary" v-auth="'ExplainWord:Choose'" @click="handleOpenExplainWordDialog">选择解释词</el-button>
				</div>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleCancelEdit">取消</el-button>
					<el-button type="primary" @click="handleSaveReason">保存</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 新增：解释词选择弹窗 -->
		<el-dialog v-model="explainWordDialogVisible" title="选择解释词" width="70%" :append-to-body="true">
			<div class="transfer-container">
				<Transfer
					:current-bound-account="selectedExplainWords"
					:data="formattedExplainWordList"
					:titles="['可选解释词', '已选解释词']"
					@dataChange="handleExplainWordDataChange"
				/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleCancelExplainWordSelect">取消</el-button>
					<el-button type="primary" @click="handleConfirmExplainWordSelect">确认选择</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ErrorSentenceInfo } from '../correction';
import { ref, computed, watch, nextTick } from 'vue';
import { searchKeyword } from '/@/views/correction/article_error_sentence/correction';
import { getExplainWordSelectList } from '/@/views/regulate/explain-dict-word/api';
import transfer from '/@/components/transfer/transfer.vue';
import { ElMessage } from 'element-plus';

defineOptions({
	name: 'ErrorTable',
});

interface Props {
	list: ErrorSentenceInfo[];
	loading: boolean;
	tableHeight: number;
}

const props = defineProps<Props>();

const emit = defineEmits(['update-judge', 'error-preview', 'update-sensitivity', 'update-judge-reason']);

const handleHumanConfidenceChange = (row: ErrorSentenceInfo, value: number) => {
	emit('update-judge', { id: row.id, value });
};

// 处理错误预览
const handleErrorPreview = (articleId: string) => {
	emit('error-preview', articleId);
};

interface StatItem {
	year: number;
	count: number;
}

interface Article {
	title: string;
	url: string;
	source: string;
	sentences: string[];
}

interface SearchResult {
	keyword: string;
	statistics: StatItem[];
	articles: Record<string, Article[]>;
}

const dialogVisible = ref(false);
const searchLoading = ref(false);
const searchResult = ref<SearchResult>({
	keyword: '',
	statistics: [],
	articles: {},
});
const activeYear = ref('');

// 新增：编辑判定原因相关状态
const editReasonDialogVisible = ref(false);
const currentEditingRow = ref<ErrorSentenceInfo | null>(null);
const editingReasonContent = ref('');

// 新增：解释词选择相关状态
const explainWordDialogVisible = ref(false);
const explainWordList = ref<Array<{ key: string; label: string; explanation: string }>>([]);
const selectedExplainWords = ref<string[]>([]);

const handleSearchWord = async (keyword: string) => {
	try {
		dialogVisible.value = true;
		searchLoading.value = true;

		const response = await searchKeyword(keyword);
		console.log(response);
		// 直接使用返回数据，因为API已经返回正确格式
		searchResult.value = response as unknown as SearchResult;

		// 设置默认显示的年份标签为数字最大（最新）的年份
		const years = Object.keys(searchResult.value.articles || {});
		if (years.length > 0) {
			// 按数字从大到小排序年份
			const sortedYears = years.sort((a, b) => Number(b) - Number(a));
			// 选中最大的年份（第一个）
			activeYear.value = sortedYears[0];
		}
	} catch (error) {
		console.error('搜索关键词出错:', error);
	} finally {
		searchLoading.value = false;
	}
};

// 新增：处理编辑判定原因按钮点击
const handleEditCorrectionResult = (rowId: number) => {
	const row = props.list.find((item) => item.id === rowId);
	if (row) {
		currentEditingRow.value = row;
		if (row.judge_reason) {
			editingReasonContent.value = row.judge_reason;
		} else if (row.ai_checks && row.ai_checks[0] && row.ai_checks[0].ai_reason) {
			editingReasonContent.value = row.ai_checks[0].ai_reason;
		} else {
			editingReasonContent.value = ''; // 如果都为空，则显示空输入框
		}
		editReasonDialogVisible.value = true;
	}
};

// 新增：处理保存判定原因
const handleSaveReason = () => {
	// TODO: 实现保存逻辑，调用后端API更新数据
	console.log('Saving reason:', editingReasonContent.value, 'for row:', currentEditingRow.value?.id);
	emit('update-judge-reason', { id: currentEditingRow.value?.id, judge_reason: editingReasonContent.value });
	editReasonDialogVisible.value = false;
};

// 新增：处理取消编辑
const handleCancelEdit = () => {
	editReasonDialogVisible.value = false;
	currentEditingRow.value = null;
	editingReasonContent.value = '';
};

const highlightKeyword = (text: string, keyword: string) => {
	if (!keyword) return text;
	const regex = new RegExp(keyword, 'gi');
	return text.replace(regex, (match) => `<span class="highlight-keyword">${match}</span>`);
};

// 处理敏感程度变化
const handleSensitivityChange = (row: ErrorSentenceInfo, value: number) => {
	emit('update-sensitivity', { id: row.id, value });
};

const getRowKey = (row: ErrorSentenceInfo) => row.id;
const tableKey = ref(0);

// 监听数据变化，但不强制重新渲染表格
watch(
	() => props.list,
	async (newList, oldList) => {
		// 只有在数据真正发生结构性变化时才重新渲染
		if (newList.length !== oldList.length) {
			tableKey.value += 1;
			await nextTick();
		}
	},
	{ deep: true }
);

// 新增：获取解释词列表
const loadExplainWordList = async () => {
	try {
		const response = await getExplainWordSelectList();
		if (response && response.data) {
			explainWordList.value = response.data.map((item: any) => ({
				key: String(item.id),
				label: item.correct_word,
				explanation: item.explanation,
			}));
		}
	} catch (error) {
		console.error('获取解释词列表失败:', error);
	}
};

// 新增：打开解释词选择弹窗
const handleOpenExplainWordDialog = async () => {
	await loadExplainWordList();
	selectedExplainWords.value = [];
	explainWordDialogVisible.value = true;
};

// 新增：取消解释词选择
const handleCancelExplainWordSelect = () => {
	explainWordDialogVisible.value = false;
	selectedExplainWords.value = [];
};

// 新增：确认解释词选择
const handleConfirmExplainWordSelect = () => {
	const selectedWords = explainWordList.value.filter((item) => selectedExplainWords.value.includes(item.key));

	if (selectedWords.length > 0) {
		const explanations = selectedWords.map((word) => word.explanation).join('\n');
		// 如果输入框已有内容，则在末尾添加分号和新内容
		if (editingReasonContent.value.trim()) {
			editingReasonContent.value += `\n${explanations}`;
		} else {
			editingReasonContent.value = explanations;
		}
	}

	explainWordDialogVisible.value = false;
	selectedExplainWords.value = [];
};

const formattedExplainWordList = computed(() =>
	explainWordList.value.map((item) => ({
		key: item.key,
		label: item.label,
		explanation: item.explanation,
	}))
);

const handleExplainWordDataChange = (data: string[]) => {
	selectedExplainWords.value = data;
};

// 新增：复制句子数据功能
const handleCopySentenceData = (row: ErrorSentenceInfo) => {
	try {
		// 创建要复制的数据对象
		const copyData = {
			correct_word: row.correct_word || '',
			error_type: row.error_type || '',
			relative_start: row.sentence_start_pos || 0,
			relative_end: row.sentence_end_pos || 0,
			sentence: row.sentence || '',
			wrong_word: row.wrong_word || '',
		};

		// 将数据转换为JSON字符串
		const jsonString = JSON.stringify(copyData, null, 2);

		// 复制到剪贴板
		navigator.clipboard
			.writeText(jsonString)
			.then(() => {
				// 显示成功提示
				ElMessage({
					message: '句子数据已复制到剪贴板',
					type: 'success',
					duration: 2000,
				});
			})
			.catch((err) => {
				console.error('复制失败:', err);
				ElMessage({
					message: '复制失败，请重试',
					type: 'error',
					duration: 2000,
				});
			});
	} catch (error) {
		console.error('处理数据时出错:', error);
		ElMessage({
			message: '处理数据时出错',
			type: 'error',
			duration: 2000,
		});
	}
};
</script>

<style lang="scss" scoped>
::v-deep(.el-table__header th) {
	color: #606266;
}

.table-container {
	margin: 10px 20px;
}

.sentence-wrapper {
	display: flex;
	align-items: center;
	cursor: pointer;
	position: relative;
	padding-right: 25px;

	.error-word {
		color: #f56c6c;
		font-weight: bold;
		text-decoration: underline;
	}
}

.article-title-container {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8px;
}

.article-title-link {
	color: #409eff;
	text-decoration: none;
	&:hover {
		text-decoration: underline;
	}
}

.error-preview-btn {
	margin-left: 5px;
}

.clickable-tag {
	cursor: pointer;

	:deep(.el-tag) {
		margin-right: 0;
	}
}

.wrong-word-clickable {
	color: #f56c6c;
	cursor: pointer;
	font-weight: bold;
	text-decoration: underline;

	&:hover {
		opacity: 0.8;
	}
}

.search-result-container {
	padding: 10px;

	h3 {
		margin-top: 0;
		margin-bottom: 20px;
		text-align: center;
	}
}

.statistics-container {
	margin-bottom: 20px;

	h4 {
		margin-bottom: 10px;
	}
}

.statistics-chart {
	display: flex;
	align-items: flex-end;
	height: 230px;
	gap: 5px;
	padding: 10px;
	border: 1px solid #ebeef5;
	border-radius: 4px;
	overflow-x: auto;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 50px;

	.year {
		font-size: 12px;
		margin-top: 5px;
	}

	.count-bar {
		background-color: #409eff;
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		min-height: 20px;
		border-radius: 2px;
		position: relative;

		.count-text {
			position: absolute;
			width: 100%;
			text-align: center;
			font-size: 12px;
			font-weight: bold;
			text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
		}
	}
}

.articles-container {
	border: 1px solid #ebeef5;
	border-radius: 4px;
}

.article-item {
	margin-bottom: 20px;
	padding: 10px;
	border-bottom: 1px solid #ebeef5;

	&:last-child {
		border-bottom: none;
	}
}

.article-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;

	.article-title {
		color: #409eff;
		font-weight: bold;
		text-decoration: none;

		&:hover {
			text-decoration: underline;
		}
	}

	.article-source {
		color: #909399;
		font-size: 12px;
	}
}

.article-sentences {
	.sentence {
		line-height: 1.5;
		margin: 5px 0;
	}
}

:deep(.highlight-keyword) {
	color: #f56c6c;
	font-weight: bold;
	background-color: #ffeaea;
}

.search-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px;

	p {
		margin-top: 10px;
		color: #909399;
	}
}

.empty-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 150px;
	color: #909399;
	background-color: #f8f8f9;
	border-radius: 4px;

	.el-icon {
		font-size: 30px;
		margin-bottom: 10px;
	}

	span {
		font-size: 14px;
	}
}

.edit-reason-container {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.reason-input-section {
	display: flex;
	flex-direction: column;
}

.section-label {
	font-weight: bold;
	margin-bottom: 5px;
}

.explain-word-section {
	display: flex;
	justify-content: flex-end;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	margin-top: 10px;
}

.explain-word-item {
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}

	.word-title {
		font-weight: bold;
		color: #303133;
		margin-bottom: 4px;
	}

	.word-explanation {
		font-size: 12px;
		color: #606266;
		line-height: 1.4;
	}
}

.transfer-container {
	padding: 10px;
}
</style> 
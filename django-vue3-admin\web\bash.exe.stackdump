Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FEBA
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210285FF9, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB750  0002100690B4 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA30  00021006A49D (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9E8590000 ntdll.dll
7FF9E6890000 KERNEL32.DLL
7FF9E5680000 KERNELBASE.dll
7FF9E6220000 USER32.dll
7FF9E5C20000 win32u.dll
7FF9E6990000 GDI32.dll
7FF9E5E90000 gdi32full.dll
7FF9E5C50000 msvcp_win.dll
7FF9E6100000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9E65B0000 advapi32.dll
7FF9E7150000 msvcrt.dll
7FF9E6670000 sechost.dll
7FF9E5E60000 bcrypt.dll
7FF9E6A00000 RPCRT4.dll
7FF9E4CF0000 CRYPTBASE.DLL
7FF9E5A60000 bcryptPrimitives.dll
7FF9E69C0000 IMM32.DLL

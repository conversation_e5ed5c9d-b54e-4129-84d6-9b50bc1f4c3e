<template>
	<div>
		<el-drawer
			v-model="designerShow"
			size="80%"
			title="流程配置"
			append-to-body
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			@close="closeClick"
		>
			<form-process-designer
				v-model="designData"
				destroy-on-close
				@onSaveSuccess="handleSaveSuccess"
				@onClose="handleDesignerClose"
			></form-process-designer>
		</el-drawer>
	</div>
</template>

<script lang="ts" setup>
import FormProcessDesigner from '../../wflow/admin/FormProcessDesigner.vue';
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import { request } from '/@/utils/service';

const route = useRoute();
const router = useRouter();
const designData = ref();

const props = defineProps({
	modelValue: Boolean,
	mainId: Number,
	tempData: Object,
});
const emit = defineEmits(['update:modelValue', 'onClose', 'close']);

const designerShow = computed({
	get() {
		return props.modelValue;
	},
	set(val) {
		emit('update:modelValue', val);
	},
});

const getDesignData = () => {
	if (!props.mainId && props.tempData) {
		designData.value = props.tempData;
		return;
	}

	if (props.mainId) {
		request({
			url: `/api/dvadmin3_flow/flow_info/${props.mainId}/`,
			method: 'get',
		}).then((res) => {
			const { data } = res;
			designData.value = data;
		});
	}
};

// 处理保存成功事件
const handleSaveSuccess = (result: any) => {
	console.log('保存成功，准备关闭弹窗并刷新列表:', result);
	// 关闭弹窗并刷新父页面列表
	designerShow.value = false;
	emit('close'); // 这会触发父组件的closeClick -> refreshList
};

// 处理设计器关闭事件
const handleDesignerClose = () => {
	console.log('设计器关闭');
	designerShow.value = false;
	emit('close'); // 这会触发父组件的closeClick -> refreshList
};

const closeClick = () => {
	emit('close');
};

onMounted(() => {
	getDesignData();
});
</script>

<style scoped></style>

import { CrudOptions, CrudExpose, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { ref, computed, Ref } from 'vue'

export default function ({ crudExpose, context }: { crudExpose: CrudExpose, context?: any }): CreateCrudOptionsRet {
    // 从外部传入或使用默认值
    const currentPlatformType = context?.currentPlatformType || ref('wechat');

    const pageRequest = async (query: any) => {
        // 添加platform_type到查询参数
        query.platform_type = currentPlatformType.value;
        console.log('快照查询参数:', query);
        return await api.getWechatOfficialAccountArticleSnapshotList(query)
    }

    return {
        currentPlatformType, // 暴露给外部
        crudOptions: {
            request: {
                pageRequest
            },
            toolbar: {
                show: false
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    }
                }
            },
            rowHandle: {
                show: false
            },
            columns: {
                article: {
                    title: '作品ID',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'wechat') },
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                video_id: {
                    title: '作品ID',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'douyin') },
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin')
                    }
                },
                weibo_id: {
                    title: '作品ID',
                    type: 'text',
                    search: { show: computed(() => currentPlatformType.value === 'weibo') },
                    column: {
                        show: computed(() => currentPlatformType.value === 'weibo')
                    }
                },
                post_content: {
                    title: '作品标题',
                    type: 'text',
                    column: {
                        show: computed(() => currentPlatformType.value === 'weibo')
                    }
                },
                video_title: {
                    title: '作品标题',
                    type: 'text',
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin')
                    }
                },
                article_title: {
                    title: '作品标题',
                    type: 'text',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                account_name: {
                    title: '公众号名称',
                    type: 'text',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                account_nickname: {
                    title: '账号昵称',
                    type: 'text',
                    column: {
                        show: computed(() => currentPlatformType.value === 'douyin' || currentPlatformType.value === 'weibo')
                    }
                },
                read_count: {
                    title: '阅读数',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                viewing_count: {
                    title: '在看数',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                like_count: {
                    title: '点赞数',
                    type: 'number',
                    column: {
                        width: 80
                    }
                },
                forward_count: {
                    title: '转发量',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                reward_count: {
                    title: '打赏量',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                share_count: {
                    title: '分享量',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'douyin' || currentPlatformType.value === 'weibo')
                    }
                },
                comment_count: {
                    title: '评论量',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'douyin' || currentPlatformType.value === 'weibo')
                    }
                },
                collect_count: {
                    title: '收藏量',
                    type: 'number',
                    column: {
                        width: 80,
                        show: computed(() => currentPlatformType.value === 'douyin')
                    }
                },
                snapshot_time: {
                    title: '快照时间',
                    type: 'datetime',
                    column: {
                        width: 160,
                        show: computed(() => currentPlatformType.value === 'douyin' || currentPlatformType.value === 'wechat')
                    }
                },
                create_time: {
                    title: '快照时间',
                    type: 'datetime',
                    column: {
                        width: 160,
                        show: computed(() => currentPlatformType.value === 'weibo')
                    }
                },
            }
        }
    }
} 
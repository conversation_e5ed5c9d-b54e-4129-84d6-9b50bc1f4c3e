/**
 * 平台类型权限枚举
 * 为不同页面的平台Tab提供权限配置基础
 */

// 基础平台类型
export enum PlatformType {
	WECHAT = 'wechat',
	DOUYIN = 'douyin',
	WEIBO = 'weibo',
}

// 平台权限前缀
export const PLATFORM_PERMISSION_PREFIX = {
	// 运营报表页面
	OPERATE_REPORT: 'OperateReport',
	// AI报告页面
	AI_REPORT: 'AiReport',
	// 微信公众号管理
	WECHAT_ACCOUNT: 'WechatAccount',
	// 微信公众号文章
	WECHAT_ARTICLE: 'WechatArticle',
	// 微信公众号快照
	WECHAT_SNAPSHOT: 'WechatSnapshot',
	// 统计排行榜
	STATISTICS_RANK: 'StatisticsRank',
} as const;

// 平台权限后缀
export const PLATFORM_PERMISSION_SUFFIX = {
	[PlatformType.WECHAT]: 'Wx',
	[PlatformType.DOUYIN]: 'Dy',
	[PlatformType.WEIBO]: 'Wb',
} as const;

/**
 * 生成平台权限字符串
 * @param prefix 权限前缀
 * @param platformType 平台类型
 * @param action 操作类型，默认为'List'
 * @returns 完整的权限字符串
 */
export function generatePlatformPermission(prefix: string, platformType: PlatformType, action: string = 'List'): string {
	const suffix = PLATFORM_PERMISSION_SUFFIX[platformType];
	return `${prefix}:${action}${suffix}`;
}

/**
 * 运营报表页面的平台权限
 * 根据实际权限格式定义
 */
export const OPERATE_REPORT_PERMISSIONS = {
	[PlatformType.WECHAT]: 'OperateReport:ListWx',
	[PlatformType.WEIBO]: 'OperationReport:ListWb',
	[PlatformType.DOUYIN]: 'OperationReport:ListDy',
} as const;

/**
 * AI报告页面的平台权限
 */
export const AI_REPORT_PERMISSIONS = {
	[PlatformType.WECHAT]: generatePlatformPermission(PLATFORM_PERMISSION_PREFIX.AI_REPORT, PlatformType.WECHAT),
	[PlatformType.DOUYIN]: generatePlatformPermission(PLATFORM_PERMISSION_PREFIX.AI_REPORT, PlatformType.DOUYIN),
	[PlatformType.WEIBO]: generatePlatformPermission(PLATFORM_PERMISSION_PREFIX.AI_REPORT, PlatformType.WEIBO),
} as const;

/**
 * 微信公众号管理页面的平台权限
 */
export const WECHAT_ACCOUNT_PERMISSIONS = {
	[PlatformType.WECHAT]: 'WxPlatform:Button',
	[PlatformType.WEIBO]: 'WbPlatform:Button',
	[PlatformType.DOUYIN]: 'DyPlatform:Button',
} as const;

/**
 * 微信公众号文章页面的平台权限
 */
export const WECHAT_ARTICLE_PERMISSIONS = {
	[PlatformType.WECHAT]: 'WxArticlesPlatform:Button',
	[PlatformType.WEIBO]: 'WbArticlesPlatform:Button',
	[PlatformType.DOUYIN]: 'DyArticlesPlatform:Button',
} as const;

/**
 * 微信公众号快照页面的平台权限
 */
export const WECHAT_SNAPSHOT_PERMISSIONS = {
	[PlatformType.WECHAT]: 'WxWorkPlatform:Button',
	[PlatformType.WEIBO]: 'WbWorkPlatform:Button',
	[PlatformType.DOUYIN]: 'DyWorkPlatform:Button',
} as const;

/**
 * 统计排行榜页面的平台权限
 */
export const STATISTICS_RANK_PERMISSIONS = {
	[PlatformType.WECHAT]: generatePlatformPermission(PLATFORM_PERMISSION_PREFIX.STATISTICS_RANK, PlatformType.WECHAT),
	[PlatformType.DOUYIN]: generatePlatformPermission(PLATFORM_PERMISSION_PREFIX.STATISTICS_RANK, PlatformType.DOUYIN),
	[PlatformType.WEIBO]: generatePlatformPermission(PLATFORM_PERMISSION_PREFIX.STATISTICS_RANK, PlatformType.WEIBO),
} as const;

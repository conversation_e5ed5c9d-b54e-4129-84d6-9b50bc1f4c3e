import { dict, asyncCompute, CrudOptions, AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { auth } from "/@/utils/authFunction"
import { ElMessage } from 'element-plus'
import { type TenantInfo } from '/@/views/tenant/api'
import { request } from '/@/utils/service'
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';


// 缓存租户数据
let tenantCache: TenantInfo[] = []
let tenantOptionsCache: { label: string; value: number }[] = []

// 获取租户数据的方法
const getTenantData = async () => {
    if (tenantCache.length === 0) {
        const res = await request({
            url: '/api/hyqm/tenants/select_list/',
            method: 'get'
        })
        tenantCache = res.data
        tenantOptionsCache = res.data.map((item: TenantInfo) => ({
            label: item.name,
            value: item.id
        }))
    }
    return {
        tenants: tenantCache,
        options: tenantOptionsCache
    }
}

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    // 定义请求方法
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateAccount(form)
    }
    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteAccount(row.id)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createAccount(form)
    }

    // 复制链接方法
    const handleCopyLink = async ({ row }: EditReq) => {
        try {
            const res = await api.generateLink({id: row.id, show_type: row.show_type})
            if (res.success && res.data.url) {
                navigator.clipboard.writeText(res.data.url).then(() => {
                    ElMessage.success('链接已复制到剪贴板');
                }).catch(() => {
                    ElMessage.error('复制链接失败');
                });
            }
        } catch (error) {
            ElMessage.error('复制链接失败')
        }
    }

    // 打开链接方法
    const handleOpenLink = async ({ row }: EditReq) => {
        try {
            const res = await api.generateLink({id: row.id, show_type: row.show_type})
            if (res.success && res.data.url) {
                window.open(res.data.url, '_blank')
            }
        } catch (error) {
            ElMessage.error('打开链接失败')
        }
    }

    // 刷新字数方法
    const handleRefreshWordCount = async ({ row }: EditReq) => {
        try {
            const res = await api.refreshWordCount(row.id)
            if (res.success) {
                ElMessage.success('刷新成功')
                crudExpose.doRefresh()
            }
        } catch (error) {
            ElMessage.error('刷新失败')
        }
    }

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    }
                }
            },
            toolbar:{
                show: false,
            },
            rowHandle: {
                width: 300,
                buttons: {
                    edit: {
                        show: false
                    },
                    remove: {
                        show: false
                    },
                    // 自定义按钮
                    copyLink: {
                        text: '复制链接',
                        click: handleCopyLink,
                        show: auth('aijiaodui:account:generate_link')
                    },
                    openLink: {
                        text: '打开链接',
                        click: handleOpenLink,
                        show: auth('aijiaodui:account:generate_link')
                    },
                    refreshWord: {
                        text: '刷新字数',
                        click: handleRefreshWordCount,
                        show: auth('aijiaodui:account:refresh_word_count')
                    },
                    view:{
                        show: false
                    }
                }
            },
            columns: {
                tenant: {
                    title: '客户名称',
                    type: 'dict-select',
                    search: {
                        show: true,
                        component: {
                            props: {
                                clearable: true,
                                filterable: true,
                                multiple: true,
                                options: asyncCompute({
                                    asyncFn: async () => {
                                        const { options } = await getTenantData()
                                        return options
                                    }
                                })
                            }
                        }
                    },
                    form: {
                        rules: [{ required: true, message: '请选择客户' }]
                    },
                    column: {
                        formatter({value}){
                            console.log('tenantCache',tenantCache,value);
                            
                            const tenant = tenantCache.find((item: { id: number; name: string }) => item.id === value) || { name: '暂无客户' };
                            return tenant.name;
                        }
                    }
                },
                phone: {
                    title: '手机号',
                    type: 'text',
                    search: { show: true },
                    form: {
                        rules: [
                            { required: true, message: '请输入手机号' },
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
                        ]
                    }
                },
                function_type: {
                    title: '功能角色',
                    type: 'dict-select',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '请选择功能角色' }]
                    },
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.FUNCTION_TYPE),
                    }),
                },
                account_type_display: {
                    title: '账号类型',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.AI_JIAO_ACCOUNT),
                    }),
                    form: {
                        rules: [{ required: true, message: '请选择账号类型' }]
                    }
                },
                high_speed_words: {
                    title: '高速校对字数',
                    type: 'number',
                    form: { show: false }  // 表单中不显示，只读
                },
                show_type: {
                    title: '链接类型',
                    type: 'dict-select',
                    form: { show: false },  // 表单中不显示，只读
                }
            }
        }
    }
} 
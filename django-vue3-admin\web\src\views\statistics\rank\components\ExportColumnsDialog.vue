<template>
	<ExportDialog
		v-model="dialogVisible"
		:platform="platform"
		:show-platform-info="showPlatformInfo"
		:show-export-works-option="true"
		@confirm="handleConfirm"
		ref="exportDialogRef"
	/>
</template>

<script setup>
import { ref, watch } from 'vue';
import ExportDialog from '/@/components/ExportDialog/index.vue';

// 定义属性
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	platform: {
		type: String,
		required: true,
		validator: (value) => ['wechat', 'douyin', 'weibo'].includes(value),
	},
	showPlatformInfo: {
		type: Boolean,
		default: true,
	},
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm']);

// 组件引用
const exportDialogRef = ref();

// 状态
const dialogVisible = ref(props.modelValue);

// 监听外部modelValue属性变化
watch(
	() => props.modelValue,
	(val) => {
		dialogVisible.value = val;
	}
);

// 监听本地dialogVisible状态变化，同步回父组件
watch(dialogVisible, (val) => {
	emit('update:modelValue', val);
});

// 处理确认事件
const handleConfirm = (exportParams) => {
	emit('confirm', exportParams);
};

// 设置loading状态（供父组件调用）
const setLoading = (loading) => {
	if (exportDialogRef.value) {
		exportDialogRef.value.setLoading(loading);
	}
};

// 关闭弹窗（供父组件调用）
const closeDialog = () => {
	if (exportDialogRef.value) {
		exportDialogRef.value.closeDialog();
	}
};

// 导出组件接口
defineExpose({
	setLoading,
	closeDialog,
});
</script>

<style lang="scss" scoped>
// 样式由共用组件提供
</style>
<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell-rowHandle-left="scope">
				<el-button type="primary" size="small" @click="onHandle(scope)">查看</el-button>
			</template>
		</fs-crud>

		<!-- 流程详情对话框 -->
		<flowDialog v-if="flowDialogShow" v-model="flowDialogShow" :items="rowItem"></flowDialog>
	</fs-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
const flowDialog = defineAsyncComponent(() => import('../components/flowDialog/index.vue'));

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});

// 现有的查看功能
const rowItem = ref<any>({});
const flowDialogShow = ref(false);
const onHandle = (scope: any) => {
	flowDialogShow.value = true;
	rowItem.value = scope.row;
};
</script>



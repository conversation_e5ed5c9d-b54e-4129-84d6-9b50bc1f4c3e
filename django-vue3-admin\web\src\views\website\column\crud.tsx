import * as api from './api';
import { ColumnType, ColumnStatus, CrawlStatus } from './api';
import { CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed } from 'vue';

export default function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      request: {
        pageRequest: async (query: any) => {
          const res = await api.getColumnList(query);
          return res;
        },
        addRequest: async ({ form }: any) => {
          const res = await api.createColumn(form);
          ElMessage.success(res.msg || '栏目创建成功');
          return res.data;
        },
        editRequest: async ({ form, row }: any) => {
          const res = await api.updateColumn(row.id, form);
          ElMessage.success(res.msg || '栏目更新成功');
          return res.data;
        },
        delRequest: async ({ row }: any) => {
          const res = await api.deleteColumn(row.id);
          ElMessage.success(res.msg || '栏目删除成功');
          return res.data;
        },
      },
      
      // 页面配置
      container: {
        is: 'fs-layout-card'
      },
      
      // 表格配置
      table: {
        size: 'default',
        stripe: true,
        border: false,
      },
      
      // 搜索表单配置
      search: {
        show: true,
        initialForm: {},
        options: {
          labelWidth: '100px',
        },
      },
      
      // 操作栏配置
      actionbar: {
        buttons: {
          add: {
            text: '添加栏目',
            type: 'primary',
          },
          // 批量操作按钮
          batchGenerateRules: {
            text: '批量生成规则',
            type: 'success',
            icon: 'fas fa-robot',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的栏目');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要为选中的 ${selectedRowKeys.length} 个栏目生成AI规则吗？这可能需要一些时间。`, 
                  '批量生成规则'
                );
                
                // 串行处理每个栏目的规则生成
                let successCount = 0;
                let failedCount = 0;
                
                for (const columnId of selectedRowKeys) {
                  try {
                    await api.generateColumnRule(columnId);
                    successCount++;
                  } catch (error) {
                    failedCount++;
                    console.error(`栏目 ${columnId} 规则生成失败:`, error);
                  }
                }
                
                ElMessage.success(`批量规则生成完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量生成规则失败:', error);
                  ElMessage.error('批量操作失败');
                }
              }
            }
          },
          batchCrawlContent: {
            text: '批量抓取内容',
            type: 'warning',
            icon: 'fas fa-spider',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的栏目');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要批量抓取选中的 ${selectedRowKeys.length} 个栏目内容吗？这可能需要较长时间。`, 
                  '批量抓取内容'
                );
                
                // 串行处理每个栏目的内容抓取
                let successCount = 0;
                let failedCount = 0;
                
                for (const columnId of selectedRowKeys) {
                  try {
                    await api.crawlColumnContent(columnId, 'deep');
                    successCount++;
                  } catch (error) {
                    failedCount++;
                    console.error(`栏目 ${columnId} 抓取失败:`, error);
                  }
                }
                
                ElMessage.success(`批量内容抓取完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量抓取失败:', error);
                  ElMessage.error('批量操作失败');
                }
              }
            }
          },
          batchActivate: {
            text: '批量激活',
            type: 'success',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要操作的栏目');
                return;
              }
              
              try {
                await ElMessageBox.confirm(`确定要激活选中的 ${selectedRowKeys.length} 个栏目吗？`, '批量激活');
                const res = await api.batchUpdateColumnStatus(selectedRowKeys, 'activate');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量激活失败:', error);
                }
              }
            }
          }
        }
      },
      
      // 行操作配置
      rowHandle: {
        width: 350,
        buttons: {
          edit: { text: '编辑', type: 'primary' },
          remove: { text: '删除', type: 'danger' },
          // 规则操作
          generateRule: {
            text: 'AI生成规则',
            type: 'success',
            icon: 'fas fa-robot',
            show: ({ row }: { row: ColumnType }) => {
              return !row.has_rule;
            },
            click: async ({ row }: { row: ColumnType }) => {
              try {
                await ElMessageBox.confirm(
                  `确定要为栏目 "${row.name}" 生成AI规则吗？\\n\\n这将自动分析栏目页面结构并生成抓取规则。`, 
                  '确认生成规则'
                );
                const res = await api.generateColumnRule(row.id);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('生成规则失败:', error);
                  ElMessage.error('生成规则失败');
                }
              }
            }
          },
          // 抓取操作  
          crawlContent: {
            text: '抓取内容',
            type: 'warning',
            icon: 'fas fa-spider',
            show: ({ row }: { row: ColumnType }) => {
              return row.has_rule && row.crawl_status !== 'completed';
            },
            click: async ({ row }: { row: ColumnType }) => {
              try {
                await ElMessageBox.confirm(
                  `确定要抓取栏目 "${row.name}" 的内容吗？\\n\\n这将进行深度抓取，获取历史内容。`, 
                  '确认抓取内容'
                );
                const res = await api.crawlColumnContent(row.id, 'deep');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('抓取内容失败:', error);
                  ElMessage.error('抓取内容失败');
                }
              }
            }
          },
          updateContent: {
            text: '更新内容',
            type: 'info',
            icon: 'fas fa-sync',
            show: ({ row }: { row: ColumnType }) => {
              return row.has_rule && row.progress_info.should_use_update_crawl;
            },
            click: async ({ row }: { row: ColumnType }) => {
              try {
                await ElMessageBox.confirm(
                  `确定要更新栏目 "${row.name}" 的内容吗？\\n\\n这将进行增量更新，获取最新内容。`, 
                  '确认更新内容'
                );
                const res = await api.crawlColumnContent(row.id, 'update');
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('更新内容失败:', error);
                  ElMessage.error('更新内容失败');
                }
              }
            }
          },
          // 查看详情
          viewDetail: {
            text: '查看详情',
            type: 'info',
            icon: 'fas fa-eye',
            click: ({ row }: { row: ColumnType }) => {
              console.log('查看栏目详情:', row);
              ElMessage.info('栏目详情功能开发中');
            }
          }
        }
      },

      columns: {
        // 多选框
        _index: {
          title: '选择',
          form: { show: false },
          column: {
            type: 'selection',
            width: 60,
            align: 'center'
          }
        },
        
        id: {
          title: 'ID',
          type: 'number',
          form: { show: false },
          column: { 
            width: 80,
            sortable: true 
          }
        },
        
        name: {
          title: '栏目名称',
          type: 'input',
          search: { show: true },
          form: {
            rules: [{ required: true, message: '请输入栏目名称' }],
          },
          column: { 
            minWidth: 150,
            showOverflowTooltip: true 
          }
        },
        
        sitemap_url: {
          title: '栏目URL',
          type: 'input',
          search: { 
            show: true,
            component: {
              placeholder: 'URL关键词'
            }
          },
          form: { show: false },
          column: { 
            minWidth: 200,
            showOverflowTooltip: true,
          }
        },
        
        site_name: {
          title: '所属站点',
          type: 'input',
          form: { show: false },
          search: {
            show: true,
            component: {
              placeholder: '站点名称'
            }
          },
          column: { 
            width: 150,
            showOverflowTooltip: true
          }
        },
        
        status: {
          title: '栏目状态',
          type: 'dict-select',
          dict: [
            { value: ColumnStatus.ACTIVE, label: '活跃', color: 'success' },
            { value: ColumnStatus.INACTIVE, label: '非活跃', color: 'info' },
            { value: ColumnStatus.ARCHIVED, label: '已归档', color: 'warning' },
            { value: ColumnStatus.ERROR, label: '错误状态', color: 'danger' },
          ],
          search: { 
            show: true,
            component: {
              placeholder: '选择状态'
            }
          },
          form: {
            value: ColumnStatus.ACTIVE
          },
          column: {
            width: 100,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            }
          }
        },
        
        has_rule: {
          title: '规则状态',
          type: 'dict-select',
          dict: [
            { value: true, label: '已匹配', color: 'success' },
            { value: false, label: '未匹配', color: 'warning' },
          ],
          search: { 
            show: true,
            component: {
              placeholder: '规则状态'
            }
          },
          form: { show: false },
          column: {
            width: 100,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            }
          }
        },
        
        rule_name: {
          title: '关联规则',
          form: { show: false },
          column: {
            width: 150,
            showOverflowTooltip: true,
          }
        },
        
        content_count: {
          title: '内容数量',
          type: 'number',
          form: { show: false },
          column: { 
            width: 100,
            align: 'center',
          }
        },
        
        crawl_status: {
          title: '抓取状态',
          type: 'dict-select',
          dict: [
            { value: CrawlStatus.NEVER_CRAWLED, label: '从未抓取', color: 'info' },
            { value: CrawlStatus.PARTIAL_CRAWLED, label: '部分抓取', color: 'warning' },
            { value: CrawlStatus.COMPLETED, label: '抓取完成', color: 'success' },
          ],
          search: { 
            show: true,
            component: {
              placeholder: '抓取状态'
            }
          },
          form: { show: false },
          column: {
            width: 120,
          }
        },
        
        last_crawled_page: {
          title: '抓取进度',
          form: { show: false },
          column: {
            width: 120,
          }
        },
        
        create_time: {
          title: '创建时间',
          type: 'datetime',
          form: { show: false },
          column: { 
            width: 160,
            sortable: true,
          }
        },
      },
    },
  };
}
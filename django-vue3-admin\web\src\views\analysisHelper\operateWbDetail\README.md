# 微博运营报告模块

本模块用于展示微博运营报告数据，包括本级和下级账号的各项指标数据。

## 开发说明

### 模拟数据使用

当前环境配置为开发环境时，系统会自动使用模拟数据代替实际 API 调用。我们提供了多种不同场景的模拟数据，可以通过不同的 ID 参数进行测试：

| ID 参数 | 模拟数据类型 | 描述                                   |
| ------- | ------------ | -------------------------------------- |
| 1       | standard     | 标准配置：1 个本级账号，3 个下级账号   |
| 2       | multiple     | 多账号配置：2 个本级账号，5 个下级账号 |
| 3       | minimal      | 最小配置：1 个本级账号，1 个下级账号   |
| 4       | noSubAccount | 无下级配置：2 个本级账号，无下级账号   |
| 5       | noMyAccount  | 无本级配置：无本级账号，3 个下级账号   |
| 6       | error        | 错误状态：模拟报表生成失败的情况       |

### 测试方法

在开发环境中，可以通过以下 URL 访问不同类型的模拟数据：

```
http://localhost:端口/analysisHelper/operateWbDetail?id=1  // 标准配置
http://localhost:端口/analysisHelper/operateWbDetail?id=2  // 多账号配置
http://localhost:端口/analysisHelper/operateWbDetail?id=3  // 最小配置
http://localhost:端口/analysisHelper/operateWbDetail?id=4  // 无下级配置
http://localhost:端口/analysisHelper/operateWbDetail?id=5  // 无本级配置
http://localhost:端口/analysisHelper/operateWbDetail?id=6  // 错误状态
```

### 模拟数据结构

模拟数据结构与 API 返回的数据结构保持一致，包括：

1. 本级账号数据 (`report_data.my_account`)

   - 传播力指数数据
   - 作品发布数据
   - 作品传播数据（点赞、评论、转发、播放）
   - 粉丝数据

2. 下级账号数据 (`report_data.sub_account`)
   - 传播力指数榜单
   - 作品发布统计
   - 作品传播数据
   - 作品 Top10
   - 粉丝数据

### 接口说明

实际生产环境中，系统会调用真实 API 接口获取数据：

```javascript
// 获取报告详情
const res = await api.getReportDetail(reportId);
```

API 接口定义位于 `src/views/analysisHelper/operateReport/api.ts`。

## 导出 PDF 功能

该模块支持将报告导出为 PDF 文件，导出的内容包括：

1. 本级微博运营报告（如有）
2. 下级微博运营报告（如有）

导出时会按模块进行分页处理，保证 PDF 布局美观。

# 适用于django-vue3-admin的定时任务插件前端



## 特性：



1.支持前端添加定时任务

2.前端开启/停用任务

3.前端查看任务运行日志，结果，状态等信息

3.调度模型前端配置（支持crontabschedule，IntervalSchedule）



## 注意：dvadmin3-celery-web支持后端dvadmin-celery版本为1.0.5之后版本，dvadmin-celery后端插件同时支持dvadmin2和dvadmin3的web插件，即通用后端插件



## 使用：

​	方法1：

Clone gitee 仓库到dvadmin3的插件目录

```sh
// cd /你的项目路径/django-vue3-admin/web/src/views/plugins/

git clone https://gitee.com/huge-dream/dvadmin3-celery-web.git
```



方法2：

选一即可，使用你喜欢的npm包管理器

```js
npm install dvadmin3-celery-web

yarn add dvadmin3-celery-web

pnpm add dvadmin3-celery-web
```



安装完成：

![image-20230416020327668](https://images-warehouse.oss-cn-hangzhou.aliyuncs.com/img/202304160203708.png)



前端会提示已安装的插件



demo图

![image-20230416015420422](https://images-warehouse.oss-cn-hangzhou.aliyuncs.com/img/202304160154979.png)

![image-20230416015441535](https://images-warehouse.oss-cn-hangzhou.aliyuncs.com/img/202304160154562.png)

![image-20230416015452951](https://images-warehouse.oss-cn-hangzhou.aliyuncs.com/img/202304160154988.png)

![image-20230416015527730](https://images-warehouse.oss-cn-hangzhou.aliyuncs.com/img/202304160155762.png)

![image-20230416015600034](https://images-warehouse.oss-cn-hangzhou.aliyuncs.com/img/202304160156073.png)
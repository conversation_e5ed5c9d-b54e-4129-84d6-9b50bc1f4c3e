import * as api from './api';
import { RuleType, RuleDetailType } from './api';
import { CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed } from 'vue';

export default function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      request: {
        pageRequest: async (query: any) => {
          const res = await api.getRuleList(query);
          return res;
        },
        addRequest: async ({ form }: any) => {
          const res = await api.createRule(form);
          ElMessage.success(res.msg || '规则创建成功');
          return res.data;
        },
        editRequest: async ({ form, row }: any) => {
          const res = await api.updateRule(row.id, form);
          ElMessage.success(res.msg || '规则更新成功');
          return res.data;
        },
        delRequest: async ({ row }: any) => {
          const res = await api.deleteRule(row.id);
          ElMessage.success(res.msg || '规则删除成功');
          return res.data;
        },
      },
      
      // 页面配置
      container: {
        is: 'fs-layout-card'
      },
      
      // 表格配置
      table: {
        size: 'default',
        stripe: true,
        border: false,
      },
      
      // 搜索表单配置
      search: {
        show: true,
        initialForm: {},
        options: {
          labelWidth: '100px',
        },
      },
      
      // 操作栏配置
      actionbar: {
        buttons: {
          add: {
            text: '创建规则',
            type: 'primary',
          },
          // 批量操作按钮
          batchActivate: {
            text: '批量激活',
            type: 'success',
            icon: 'fas fa-play',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要激活的规则');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要批量激活选中的 ${selectedRowKeys.length} 个规则吗？激活后的规则将可以用于内容提取。`, 
                  '批量激活规则'
                );
                
                const res = await api.batchActivateRules(selectedRowKeys);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量激活失败:', error);
                  ElMessage.error('批量激活失败');
                }
              }
            }
          },
          batchDeactivate: {
            text: '批量禁用',
            type: 'warning',
            icon: 'fas fa-pause',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要禁用的规则');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要批量禁用选中的 ${selectedRowKeys.length} 个规则吗？禁用后的规则将不会用于内容提取。`, 
                  '批量禁用规则'
                );
                
                const res = await api.batchDeactivateRules(selectedRowKeys);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量禁用失败:', error);
                  ElMessage.error('批量禁用失败');
                }
              }
            }
          },
          batchDelete: {
            text: '批量删除',
            type: 'danger',
            icon: 'fas fa-trash',
            show: computed(() => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              return selectedRowKeys && selectedRowKeys.length > 0;
            }),
            click: async () => {
              const selectedRowKeys = crudExpose?.getSelectedRowKeys?.();
              if (!selectedRowKeys || selectedRowKeys.length === 0) {
                ElMessage.warning('请先选择要删除的规则');
                return;
              }
              
              try {
                await ElMessageBox.confirm(
                  `确定要删除选中的 ${selectedRowKeys.length} 个规则吗？此操作不可恢复！相关联的内容将清除规则关联。`, 
                  '批量删除规则',
                  { type: 'error' }
                );
                const res = await api.batchDeleteRules(selectedRowKeys);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('批量删除失败:', error);
                  ElMessage.error('批量删除失败');
                }
              }
            }
          }
        }
      },
      
      // 行操作配置
      rowHandle: {
        width: 420,
        buttons: {
          edit: { text: '编辑', type: 'primary' },
          remove: { text: '删除', type: 'danger' },
          // 激活/禁用
          toggleStatus: {
            text: ({ row }: { row: RuleType }) => row.is_active ? '禁用' : '激活',
            type: ({ row }: { row: RuleType }) => row.is_active ? 'warning' : 'success',
            icon: ({ row }: { row: RuleType }) => row.is_active ? 'fas fa-pause' : 'fas fa-play',
            click: async ({ row }: { row: RuleType }) => {
              try {
                const action = row.is_active ? '禁用' : '激活';
                await ElMessageBox.confirm(
                  `确定要${action}规则 "${row.name}" 吗？`, 
                  `${action}规则`
                );
                const res = await api.toggleRuleStatus(row.id, !row.is_active);
                ElMessage.success(res.msg);
                crudExpose?.doRefresh?.();
              } catch (error) {
                if (error !== 'cancel') {
                  console.error('切换状态失败:', error);
                  ElMessage.error('操作失败');
                }
              }
            }
          },
          // 测试规则
          testRule: {
            text: '测试',
            type: 'info',
            icon: 'fas fa-flask',
            click: ({ row }: { row: RuleType }) => {
              console.log('测试规则:', row);
              ElMessage.info('规则测试功能开发中');
            }
          },
          // 应用规则
          applyRule: {
            text: '应用',
            type: 'success',
            icon: 'fas fa-magic',
            show: ({ row }: { row: RuleType }) => row.is_active,
            click: ({ row }: { row: RuleType }) => {
              console.log('应用规则:', row);
              ElMessage.info('规则应用功能开发中');
            }
          },
          // 查看详情
          viewDetail: {
            text: '详情',
            type: 'info',
            icon: 'fas fa-eye',
            click: ({ row }: { row: RuleType }) => {
              console.log('查看规则详情:', row);
              ElMessage.info('规则详情功能开发中');
            }
          }
        }
      },

      columns: {
        // 多选框
        _index: {
          title: '选择',
          form: { show: false },
          column: {
            type: 'selection',
            width: 60,
            align: 'center'
          }
        },
        
        id: {
          title: 'ID',
          type: 'number',
          form: { show: false },
          column: { 
            width: 80,
            sortable: true 
          }
        },
        
        name: {
          title: '规则名称',
          type: 'input',
          search: { show: true },
          form: {
            rules: [{ required: true, message: '请输入规则名称' }],
            component: {
              placeholder: '请输入规则名称'
            }
          },
          column: { 
            minWidth: 200,
            showOverflowTooltip: true
          }
        },
        
        site_name: {
          title: '所属站点',
          type: 'input',
          form: { show: false },
          search: {
            show: true,
            component: {
              placeholder: '站点名称'
            }
          },
          column: { 
            width: 200,
            showOverflowTooltip: true
          }
        },
        
        site_id: {
          title: '站点',
          type: 'dict-select',
          form: {
            rules: [{ required: true, message: '请选择站点' }],
            component: {
              // 这里需要动态加载站点数据
              placeholder: '请选择站点'
            }
          },
          column: { show: false }
        },
        
        rule_config_summary: {
          title: '规则配置',
          form: { show: false },
          column: { 
            minWidth: 300,
            showOverflowTooltip: true
          }
        },
        
        rule_config: {
          title: '规则配置',
          type: 'textarea',
          form: {
            rules: [{ required: true, message: '请输入规则配置' }],
            component: {
              placeholder: '请输入JSON格式的规则配置',
              rows: 10,
              maxlength: 10000,
              showWordLimit: true
            },
            helper: '请输入有效的JSON格式配置，包含title_selector等字段'
          },
          column: { show: false }
        },
        
        is_active: {
          title: '状态',
          type: 'dict-select',
          dict: [
            { value: true, label: '启用', color: 'success' },
            { value: false, label: '禁用', color: 'danger' }
          ],
          search: { 
            show: true,
            component: {
              placeholder: '选择状态'
            }
          },
          form: {
            value: true,
            component: {
              placeholder: '选择规则状态'
            }
          },
          column: {
            width: 100,
            component: {
              name: 'fs-dict-select',
              color: 'auto'
            }
          }
        },
        
        usage_statistics: {
          title: '使用统计',
          form: { show: false },
          column: {
            width: 150
          }
        },
        
        success_count: {
          title: '成功次数',
          type: 'number',
          form: { show: false },
          search: {
            show: true,
            component: {
              placeholder: '成功次数'
            }
          },
          column: { 
            width: 100,
            sortable: true
          }
        },
        
        total_attempts: {
          title: '总尝试',
          type: 'number',
          form: { show: false },
          search: {
            show: true,
            component: {
              placeholder: '总尝试次数'
            }
          },
          column: { 
            width: 100,
            sortable: true
          }
        },
        
        created_at_display: {
          title: '创建时间',
          form: { show: false },
          column: { 
            width: 160,
            sortable: 'created_at'
          }
        },
        
        updated_at_display: {
          title: '更新时间',
          form: { show: false },
          column: { 
            show: false,
            width: 160,
            sortable: 'updated_at'
          }
        },
      },
    },
  };
}
import { request } from '/@/utils/service';

// 规则数据类型
export interface RuleType {
  id: number;
  name: string;
  site_id: number;
  site_name: string;
  site_domain: string;
  rule_config: Record<string, any>;
  rule_config_summary: string;
  success_count: number;
  created_at: string;
  created_at_display: string;
  updated_at: string;
  updated_at_display: string;
}

// 规则详情类型
export interface RuleDetailType extends RuleType {
  site_info: {
    id: number;
    name: string;
    domain: string;
    description?: string;
    status: string;
    status_display: string;
  };
  rule_config_display: Record<string, any>;
  recent_contents: Array<{
    id: number;
    title: string;
    status: string;
    status_display: string;
    update_time: string;
  }>;
}

// 规则查询参数类型
export interface RuleQuery {
  page?: number;
  page_size?: number;
  name?: string;
  site_id?: number;
  site_name?: string;
  site_domain?: string;
  is_active?: boolean;
  success_count_min?: number;
  success_count_max?: number;
  total_attempts_min?: number;
  total_attempts_max?: number;
  success_rate_min?: number;
  success_rate_max?: number;
  date_from?: string;
  date_to?: string;
  rule_config_search?: string;
  search?: string;
  ordering?: string;
}

// 规则创建数据类型
export interface RuleCreateData {
  name: string;
  site_id: number;
  rule_config: Record<string, any>;
  is_active?: boolean;
}

// 规则更新数据类型
export interface RuleUpdateData {
  name?: string;
  rule_config?: Record<string, any>;
  is_active?: boolean;
}

// 统一API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 规则统计类型
export interface RuleStatistics {
  total_rules: number;
  active_rules: number;
  inactive_rules: number;
  rules_by_site: Record<string, {
    site_id: number;
    total_rules: number;
    active_rules: number;
    inactive_rules: number;
  }>;
  overall_success_rate: number;
  total_attempts: number;
  total_successes: number;
  recent_active_rules: number;
}

// 规则测试结果类型
export interface RuleTestResult {
  success: boolean;
  extracted_data?: Record<string, any>;
  validation?: {
    is_valid: boolean;
    warnings: string[];
    errors: string[];
    field_scores: Record<string, number>;
    overall_score: number;
  };
  url: string;
  rule_config: Record<string, any>;
  test_time: string;
  page_info?: {
    title: string;
    content_length: number;
    selector_matches: Record<string, number>;
  };
  error?: string;
}

// 规则测试参数类型
export interface RuleTestData {
  test_url: string;
  rule_config?: Record<string, any>;
}

// 批量操作结果类型
export interface BatchOperationResult {
  total_count: number;
  success_count: number;
  failed_count: number;
  results: Array<{
    rule_id: number;
    rule_name: string;
    success: boolean;
    message: string;
  }>;
}

// 批量操作参数类型
export interface BatchOperationData {
  rule_ids: number[];
  action: 'activate' | 'deactivate' | 'delete';
}

// 规则应用结果类型
export interface RuleApplyResult {
  rule_id: number;
  rule_name: string;
  total_count: number;
  success_count: number;
  failed_count: number;
  results: Array<{
    content_id: number;
    content_title: string;
    success: boolean;
    message: string;
    extracted_data: Record<string, any>;
  }>;
}

// ========== 基础CRUD操作 ==========

// 获取规则列表
export function getRuleList(params?: RuleQuery) {
  return request<ApiResponse<PaginatedResponse<RuleType>>>({
    url: '/api/website/api/rules/',
    method: 'get',
    params,
  });
}

// 创建规则
export function createRule(data: RuleCreateData) {
  return request<ApiResponse<RuleDetailType>>({
    url: '/api/website/api/rules/',
    method: 'post',
    data,
  });
}

// 更新规则
export function updateRule(id: number, data: RuleUpdateData) {
  return request<ApiResponse<RuleDetailType>>({
    url: `/api/website/api/rules/${id}/`,
    method: 'put',
    data,
  });
}

// 部分更新规则
export function patchRule(id: number, data: Partial<RuleUpdateData>) {
  return request<ApiResponse<RuleDetailType>>({
    url: `/api/website/api/rules/${id}/`,
    method: 'patch',
    data,
  });
}

// 删除规则
export function deleteRule(id: number) {
  return request<ApiResponse<{
    deleted_rule_id: number;
    deleted_rule_name: string;
    affected_content_count: number;
  }>>({
    url: `/api/website/api/rules/${id}/`,
    method: 'delete',
  });
}

// 获取单个规则详情
export function getRuleDetail(id: number) {
  return request<ApiResponse<RuleDetailType>>({
    url: `/api/website/rules/${id}/`,
    method: 'get',
  });
}

// ========== 统计信息 ==========

// 获取规则统计信息
export function getRuleStatistics(params?: RuleQuery) {
  return request<ApiResponse<RuleStatistics>>({
    url: '/api/website/rules/statistics/',
    method: 'get',
    params,
  });
}

// ========== 规则测试 ==========

// 测试规则
export function testRule(id: number, data: RuleTestData) {
  return request<ApiResponse<RuleTestResult>>({
    url: `/api/website/rules/${id}/test_rule/`,
    method: 'post',
    data,
  });
}

// ========== 规则应用 ==========

// 将规则应用到指定内容
export function applyRuleToContent(id: number, content_ids: number[]) {
  return request<ApiResponse<RuleApplyResult>>({
    url: `/api/website/rules/${id}/apply_to_content/`,
    method: 'post',
    data: { content_ids },
  });
}

// ========== 批量操作 ==========

// 批量激活规则
export function batchActivateRules(rule_ids: number[]) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/rules/batch_activate/',
    method: 'post',
    data: { rule_ids, action: 'activate' },
  });
}

// 批量禁用规则
export function batchDeactivateRules(rule_ids: number[]) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/rules/batch_deactivate/',
    method: 'post',
    data: { rule_ids, action: 'deactivate' },
  });
}

// 批量删除规则
export function batchDeleteRules(rule_ids: number[]) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/rules/batch_delete/',
    method: 'post',
    data: { rule_ids, action: 'delete' },
  });
}

// ========== 导出数据 ==========

// 导出规则数据
export function exportRuleData(params?: RuleQuery) {
  return request<ApiResponse<{
    count: number;
    rules: RuleType[];
  }>>({
    url: '/api/website/rules/export_rules/',
    method: 'get',
    params,
  });
}

// ========== 快速操作函数 ==========

// 激活规则
export function activateRule(id: number) {
  return patchRule(id, { is_active: true });
}

// 禁用规则
export function deactivateRule(id: number) {
  return patchRule(id, { is_active: false });
}

// 切换规则激活状态
export function toggleRuleStatus(id: number, is_active: boolean) {
  return patchRule(id, { is_active });
}

// ========== 站点相关 ==========

// 获取站点列表（用于下拉选择）
export function getSiteOptions() {
  return request<ApiResponse<Array<{
    id: number;
    name: string;
    domain: string;
    status: string;
    status_display: string;
  }>>>({
    url: '/api/website/sites/',
    method: 'get',
    params: { page_size: 1000 }, // 获取所有站点
  });
}

// 根据站点ID获取规则列表
export function getRulesBySite(site_id: number) {
  return getRuleList({ site_id, page_size: 1000 });
}

// ========== 内容相关 ==========

// 获取可应用规则的内容列表
export function getApplicableContent(params?: {
  site_id?: number;
  status?: string;
  has_rule?: boolean;
  page_size?: number;
}) {
  return request<ApiResponse<PaginatedResponse<{
    id: number;
    title: string;
    status: string;
    status_display: string;
    site_name: string;
    column_name?: string;
  }>>>({
    url: '/api/website/contents/',
    method: 'get',
    params,
  });
}

// ========== 预设规则配置 ==========

// 常用规则配置模板
export const RULE_TEMPLATES = {
  // 新闻类网站模板
  news: {
    name: '新闻类通用规则',
    rule_config: {
      title_selector: 'h1, .title, .news-title',
      content_selector: '.content, .news-content, .article-content p',
      author_selector: '.author, .news-author, .by-author',
      publish_time_selector: '.time, .publish-time, .news-time',
      source_selector: '.source, .news-source'
    }
  },
  // 政府网站模板
  government: {
    name: '政府网站通用规则',
    rule_config: {
      title_selector: 'h1, .title, .article-title',
      content_selector: '.content, .article-content, .text-content p',
      author_selector: '.author, .source-info',
      publish_time_selector: '.time, .date, .publish-date',
      source_selector: '.source, .department'
    }
  },
  // 博客类网站模板
  blog: {
    name: '博客类通用规则',
    rule_config: {
      title_selector: 'h1.entry-title, .post-title, .blog-title',
      content_selector: '.entry-content, .post-content, .blog-content p',
      author_selector: '.author, .post-author, .by-line',
      publish_time_selector: '.date, .post-date, .entry-date',
      source_selector: '.blog-name, .site-name'
    }
  }
};
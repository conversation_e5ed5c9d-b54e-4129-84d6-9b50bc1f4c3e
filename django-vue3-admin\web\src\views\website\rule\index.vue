<template>
  <fs-page class="PageWebsiteRuleManage">
    <!-- 统计信息面板 -->
    <el-row :gutter="20" class="stats-panel">
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.total_rules || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-cogs"></i>
              规则总数
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number success">{{ statistics?.active_rules || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-play-circle"></i>
              启用规则
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number warning">{{ statistics?.inactive_rules || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-pause-circle"></i>
              禁用规则
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.overall_success_rate || 0 }}%</div>
            <div class="stat-label">
              <i class="fas fa-chart-line"></i>
              总成功率
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number info">{{ statistics?.total_attempts || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-calculator"></i>
              总尝试数
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number primary">{{ statistics?.recent_active_rules || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-clock"></i>
              近期活跃
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作面板 -->
    <el-card class="quick-actions-card">
      <template #header>
        <div class="card-header">
          <i class="fas fa-bolt"></i>
          <span>快速操作</span>
        </div>
      </template>
      <div class="quick-actions-content">
        <div class="action-group">
          <div class="action-info">
            <i class="fas fa-info-circle text-info"></i>
            <span>管理规则状态，批量激活或禁用规则以控制内容提取行为</span>
          </div>
          <div class="action-buttons">
            <el-button-group>
              <el-button type="success" @click="quickActivateRules">
                <i class="fas fa-play"></i>
                激活所有规则
              </el-button>
              <el-button type="warning" @click="quickDeactivateRules">
                <i class="fas fa-pause"></i>
                禁用所有规则
              </el-button>
            </el-button-group>
          </div>
        </div>
        <div class="action-group">
          <div class="action-info">
            <i class="fas fa-info-circle text-warning"></i>
            <span>规则配置和测试工具，支持导出规则配置和批量测试验证</span>
          </div>
          <div class="action-buttons">
            <el-button-group>
              <el-button type="info" @click="exportRules">
                <i class="fas fa-download"></i>
                导出规则
              </el-button>
              <el-button type="primary" @click="refreshStatistics">
                <i class="fas fa-refresh"></i>
                刷新统计
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 站点分布信息 -->
    <el-card class="site-distribution-card" v-if="statistics?.rules_by_site">
      <template #header>
        <div class="card-header">
          <i class="fas fa-chart-pie"></i>
          <span>站点分布</span>
        </div>
      </template>
      <div class="site-distribution-content">
        <div class="site-item" v-for="(siteData, siteName) in statistics.rules_by_site" :key="siteName">
          <div class="site-info">
            <div class="site-name">{{ siteName }}</div>
            <div class="site-stats">
              <span class="stat-item">
                <i class="fas fa-cogs"></i>
                总计: {{ siteData.total_rules }}
              </span>
              <span class="stat-item success">
                <i class="fas fa-check-circle"></i>
                启用: {{ siteData.active_rules }}
              </span>
              <span class="stat-item warning" v-if="siteData.inactive_rules > 0">
                <i class="fas fa-pause-circle"></i>
                禁用: {{ siteData.inactive_rules }}
              </span>
            </div>
          </div>
          <div class="site-progress">
            <el-progress 
              :percentage="Math.round((siteData.active_rules / siteData.total_rules) * 100)"
              :color="siteData.active_rules === siteData.total_rules ? '#67c23a' : '#e6a23c'"
              :stroke-width="8"
              text-inside
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 规则列表 -->
    <fs-crud ref="crudRef" v-bind="crudBinding">
    </fs-crud>
  </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import createCrudOptions from './crud';
import * as api from './api';

export default defineComponent({
  name: 'WebsiteRuleManage',
  setup() {
    // 统计信息
    const statistics = ref<any>(null);
    
    // 初始化CRUD
    const { crudBinding, crudRef, crudExpose } = useFs({ 
      createCrudOptions,
    });
    
    // 加载统计信息
    const loadStatistics = async () => {
      try {
        const res = await api.getRuleStatistics();
        statistics.value = res.data;
      } catch (error) {
        console.error('加载统计信息失败:', error);
      }
    };
    
    // 刷新统计信息
    const refreshStatistics = async () => {
      ElMessage.info('正在刷新统计信息...');
      await loadStatistics();
      ElMessage.success('统计信息已刷新');
    };
    
    // 快速激活规则
    const quickActivateRules = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要激活所有规则吗？这将使所有规则都可用于内容提取。',
          '批量激活规则'
        );
        
        // 获取所有禁用的规则ID
        const listRes = await api.getRuleList({ 
          is_active: false, 
          page_size: 1000 
        });
        
        const inactiveRules = listRes.data.results || listRes.data.data?.results || [];
        
        if (inactiveRules.length === 0) {
          ElMessage.info('没有需要激活的规则');
          return;
        }
        
        const ruleIds = inactiveRules.map((rule: any) => rule.id);
        
        ElMessage.info(`开始激活 ${ruleIds.length} 个规则...`);
        const res = await api.batchActivateRules(ruleIds);
        ElMessage.success(res.msg);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('快速激活规则失败:', error);
          ElMessage.error('批量激活失败');
        }
      }
    };
    
    // 快速禁用规则
    const quickDeactivateRules = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要禁用所有规则吗？禁用后的规则将不会用于内容提取。',
          '批量禁用规则',
          { type: 'warning' }
        );
        
        // 获取所有启用的规则ID
        const listRes = await api.getRuleList({ 
          is_active: true, 
          page_size: 1000 
        });
        
        const activeRules = listRes.data.results || listRes.data.data?.results || [];
        
        if (activeRules.length === 0) {
          ElMessage.info('没有需要禁用的规则');
          return;
        }
        
        const ruleIds = activeRules.map((rule: any) => rule.id);
        
        ElMessage.info(`开始禁用 ${ruleIds.length} 个规则...`);
        const res = await api.batchDeactivateRules(ruleIds);
        ElMessage.success(res.msg);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('快速禁用规则失败:', error);
          ElMessage.error('批量禁用失败');
        }
      }
    };
    
    // 导出规则
    const exportRules = async () => {
      try {
        ElMessage.info('正在导出规则数据...');
        
        const res = await api.exportRuleData();
        const { count, rules } = res.data;
        
        // 创建JSON文件并下载
        const jsonStr = JSON.stringify(rules, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `rules_export_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        ElMessage.success(`成功导出 ${count} 个规则配置`);
        
      } catch (error) {
        console.error('导出规则失败:', error);
        ElMessage.error('导出规则失败');
      }
    };
    
    // 生命周期
    onMounted(() => {
      console.log('WebsiteRuleManage组件已挂载');
      loadStatistics();
    });

    return {
      statistics,
      crudBinding,
      crudRef,
      crudExpose,
      refreshStatistics,
      quickActivateRules,
      quickDeactivateRules,
      exportRules,
    };
  },
});
</script>

<style lang="scss" scoped>
.PageWebsiteRuleManage {
  padding: 20px;
}

.stats-panel {
  margin-bottom: 20px;
  
  .stat-card {
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
        
        &.success {
          color: #67c23a;
        }
        
        &.warning {
          color: #e6a23c;
        }
        
        &.danger {
          color: #f56c6c;
        }
        
        &.info {
          color: #909399;
        }
        
        &.primary {
          color: #409eff;
        }
      }
      
      .stat-label {
        font-size: 14px;
        color: #606266;
        
        i {
          margin-right: 4px;
          color: #409eff;
        }
      }
    }
  }
}

.quick-actions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
    color: #409eff;
  }
}

.quick-actions-content {
  .action-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f0f2f6;
    }
    
    .action-info {
      flex: 1;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 10px;
        font-size: 16px;
        
        &.text-info {
          color: #409eff;
        }
        
        &.text-warning {
          color: #e6a23c;
        }
      }
      
      span {
        color: #606266;
        line-height: 1.5;
      }
    }
    
    .action-buttons {
      margin-left: 20px;
    }
  }
}

.site-distribution-card {
  margin-bottom: 20px;
}

.site-distribution-content {
  .site-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f2f6;
    
    &:last-child {
      border-bottom: none;
    }
    
    .site-info {
      flex: 1;
      margin-right: 20px;
      
      .site-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .site-stats {
        display: flex;
        gap: 12px;
        
        .stat-item {
          font-size: 12px;
          color: #909399;
          
          i {
            margin-right: 4px;
          }
          
          &.success {
            color: #67c23a;
          }
          
          &.warning {
            color: #e6a23c;
          }
        }
      }
    }
    
    .site-progress {
      width: 200px;
    }
  }
}

// 表格自定义样式
:deep(.fs-crud) {
  .el-tag {
    font-size: 12px;
  }
  
  .text-primary {
    color: #409eff;
  }
  
  .text-muted {
    color: #909399;
  }
  
  .text-success {
    color: #67c23a;
  }
  
  .text-warning {
    color: #e6a23c;
  }
  
  .text-danger {
    color: #f56c6c;
  }
  
  .text-info {
    color: #17a2b8;
  }
  
  .font-medium {
    font-weight: 500;
  }
  
  .text-sm {
    font-size: 12px;
  }
  
  .text-xs {
    font-size: 11px;
  }
}
</style>
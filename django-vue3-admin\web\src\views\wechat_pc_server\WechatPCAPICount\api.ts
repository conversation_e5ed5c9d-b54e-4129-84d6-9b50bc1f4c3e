import { request } from '/@/utils/service'

export interface WechatPCAPICountType {
  id: number
  wechat_pc_account: number
  wechat_pc_account_nick_name: string
  business_type: number
  date: string
  count: number
}

export interface WechatPCAPICountListParams {
  page?: number
  limit?: number
  wechat_pc_account?: number
  business_type?: number
  date_start?: string
  date_end?: string
}

export const apiPrefix = '/api/wechat_pc_server/wechat_pc_api_count/'

export async function getWechatPCAPICountList(query: WechatPCAPICountListParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
}

export async function createWechatPCAPICount(obj: Partial<WechatPCAPICountType>) {
  return await request({
    url: apiPrefix,
    method: 'post',
    data: obj
  })
}

export async function updateWechatPCAPICount(obj: Partial<WechatPCAPICountType>) {
  return await request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj
  })
}

export async function deleteWechatPCAPICount(id: number) {
  return await request({
    url: apiPrefix + id + '/',
    method: 'delete'
  })
}
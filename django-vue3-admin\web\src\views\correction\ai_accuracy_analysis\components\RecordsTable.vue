<template>
	<div class="records-container">
		<div class="records-title">
			<h3>详细校对记录</h3>
			<div class="records-actions">
				<span class="total-count">共 {{ totalCount }} 条记录</span>
			</div>
		</div>

		<!-- 数据表格 -->
		<el-table
			:data="records"
			:loading="loading"
			:height="tableHeight"
			border
			stripe
			class="records-table"
		>
			<el-table-column prop="id" label="ID" width="80" align="center" />
			
			<el-table-column prop="article_id" label="文章ID" width="120" align="center">
				<template #default="{ row }">
					<el-tag size="small" type="info">{{ row.article_id }}</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="error_type" label="错误类型" width="100" align="center">
				<template #default="{ row }">
					<el-tag size="small" :type="getErrorTypeTagType(row.error_type)">
						{{ row.error_type }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="wrong_word" label="错误词" width="100" align="center">
				<template #default="{ row }">
					<span class="wrong-word">{{ row.wrong_word }}</span>
				</template>
			</el-table-column>

			<el-table-column prop="correct_word" label="正确词" width="100" align="center">
				<template #default="{ row }">
					<span class="correct-word">{{ row.correct_word }}</span>
				</template>
			</el-table-column>

			<el-table-column prop="sentence" label="句子" min-width="300">
				<template #default="{ row }">
					<el-tooltip :content="row.sentence" placement="top" :disabled="row.sentence.length <= 50">
						<span class="sentence-text">
							{{ row.sentence.length > 50 ? row.sentence.substring(0, 50) + '...' : row.sentence }}
						</span>
					</el-tooltip>
				</template>
			</el-table-column>

			<el-table-column prop="human_judge_display" label="人工判定" width="100" align="center">
				<template #default="{ row }">
					<el-tag size="small" :type="getHumanJudgeTagType(row.human_judge)">
						{{ row.human_judge_display }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="ai_judge_display" label="AI判定" width="100" align="center">
				<template #default="{ row }">
					<el-tag size="small" :type="getAIJudgeTagType(row.ai_judge)">
						{{ row.ai_judge_display }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="is_consistent" label="判断一致性" width="110" align="center">
				<template #default="{ row }">
					<el-tag size="small" :type="row.is_consistent ? 'success' : 'danger'">
						{{ row.is_consistent ? '一致' : '不一致' }}
					</el-tag>
				</template>
			</el-table-column>

			<el-table-column prop="ai_reason" label="AI原因" width="150">
				<template #default="{ row }">
					<el-tooltip :content="row.ai_reason" placement="top" :disabled="!row.ai_reason || row.ai_reason.length <= 20">
						<span class="ai-reason">
							{{ row.ai_reason && row.ai_reason.length > 20 ? row.ai_reason.substring(0, 20) + '...' : (row.ai_reason || '-') }}
						</span>
					</el-tooltip>
				</template>
			</el-table-column>

			<el-table-column prop="create_time" label="创建时间" width="150" align="center">
				<template #default="{ row }">
					<span class="create-time">{{ formatDateTime(row.create_time) }}</span>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页组件 -->
		<div class="pagination-wrapper">
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:total="totalCount"
				:page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper"
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 无数据状态 -->
		<div v-if="!loading && records.length === 0" class="no-data">
			<el-empty description="暂无数据" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DetailedRecordItem } from '../api';

defineOptions({
	name: 'RecordsTable',
});

interface Props {
	records: DetailedRecordItem[];
	loading: boolean;
	totalCount: number;
	currentPage: number;
	pageSize: number;
	tableHeight?: number;
}

const props = withDefaults(defineProps<Props>(), {
	records: () => [],
	loading: false,
	totalCount: 0,
	currentPage: 1,
	pageSize: 20,
	tableHeight: 400,
});

const emit = defineEmits(['update:currentPage', 'update:pageSize', 'size-change', 'current-change']);

// 双向绑定当前页码
const currentPage = computed({
	get: () => props.currentPage,
	set: (val) => emit('update:currentPage', val),
});

// 双向绑定页大小
const pageSize = computed({
	get: () => props.pageSize,
	set: (val) => emit('update:pageSize', val),
});

// 错误类型标签颜色
const getErrorTypeTagType = (errorType: string): string => {
	const typeMap: Record<string, string> = {
		'同音错误': 'warning',
		'错别字': 'danger',
		'标点符号': 'info',
		'语法错误': 'success',
	};
	return typeMap[errorType] || 'default';
};

// 人工判定标签颜色
const getHumanJudgeTagType = (humanJudge: number): string => {
	switch (humanJudge) {
		case 1:
			return 'danger'; // 确认错误
		case 2:
			return 'success'; // 误报
		default:
			return 'info';
	}
};

// AI判定标签颜色
const getAIJudgeTagType = (aiJudge: number): string => {
	switch (aiJudge) {
		case 1:
			return 'danger'; // 确认错误
		case 2:
			return 'success'; // 误报
		case 3:
			return 'warning'; // 不确定
		default:
			return 'info';
	}
};

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
	if (!dateTime) return '-';
	const date = new Date(dateTime);
	return date.toLocaleString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
	});
};

// 处理页大小变化
const handleSizeChange = (val: number) => {
	emit('size-change', val);
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
	emit('current-change', val);
};
</script>

<style lang="scss" scoped>
.records-container {
	margin-bottom: 24px;

	.records-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			color: #303133;
			font-size: 18px;
			font-weight: 600;
		}

		.records-actions {
			.total-count {
				color: #666666;
				font-size: 14px;
			}
		}
	}

	.records-table {
		background: white;
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);

		:deep(.el-table__header) {
			background-color: #fafafa;

			th {
				font-weight: 600;
				color: #303133;
			}
		}

		:deep(.el-table__body) {
			tr:hover > td {
				background-color: #f5f7fa;
			}
		}

		.wrong-word {
			color: #f56c6c;
			font-weight: 500;
		}

		.correct-word {
			color: #67c23a;
			font-weight: 500;
		}

		.sentence-text {
			line-height: 1.4;
			word-break: break-all;
		}

		.ai-reason {
			color: #666666;
			font-size: 12px;
			line-height: 1.3;
		}

		.create-time {
			color: #909399;
			font-size: 12px;
		}
	}

	.pagination-wrapper {
		display: flex;
		justify-content: center;
		padding: 20px 0;
		background: white;
		border-radius: 0 0 8px 8px;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
	}

	.no-data {
		background: white;
		border-radius: 8px;
		padding: 40px;
		text-align: center;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
	}
}

// 响应式适配
@media (max-width: 768px) {
	.records-container {
		.records-title {
			flex-direction: column;
			align-items: flex-start;
			gap: 8px;

			.records-actions {
				width: 100%;
				text-align: right;
			}
		}

		.records-table {
			:deep(.el-table__body-wrapper) {
				overflow-x: auto;
			}
		}

		.pagination-wrapper {
			padding: 16px;

			:deep(.el-pagination) {
				justify-content: center;
			}
		}
	}
}
</style>
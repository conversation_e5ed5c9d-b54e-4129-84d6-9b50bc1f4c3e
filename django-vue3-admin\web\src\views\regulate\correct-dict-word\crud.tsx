import { asyncCompute, CrudOptions, AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud'
import * as api from './api'
import { getAllTenants, type TenantInfo } from '/@/views/tenant/api'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag, ElMessageBox, ElMessage } from 'element-plus'
import { ENUM_TYPE } from '/@/stores/constants/enum'
import { getEnumDatas } from '/@/stores/enum'
import { getErrorTypes } from '/@/views/correction/article_error_sentence/correction';

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    // 定义请求方法
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateData(form)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createData(form)
    }

    // 获取错误类型列表
    const errorTypeList = async () => {
        const res = await getErrorTypes()
        return res.data.map((item: string) => ({
            label: item,
            value: item
        }))
    }
    let tenantCache: TenantInfo[] = []
    let tenantOptionsCache: { label: string; value: number }[] = []
    // 获取租户数据的方法
    const getTenantData = async () => {
        if (tenantCache.length === 0) {
            const res = await getAllTenants()
            tenantCache = res.data
            tenantOptionsCache = res.data.map((item: TenantInfo) => ({
                label: item.name,
                value: item.id
            }))
        }
        return {
            tenants: tenantCache,
            options: tenantOptionsCache
        }
    }
    return {
        crudOptions: {
            // 请求配置
            request: {
                pageRequest,
                addRequest,
                editRequest
            },
            // 操作栏配置
            actionbar: {
                buttons: {
                    add: {
                        show: auth('correctWord:Create'),
                    }
                }
            },
            toolbar: {
                show: false,
            },
            // 行操作配置
            rowHandle: {
                width: 260,
                buttons: {
                    edit: {
                        show: auth('correctWord:Update'),
                    },
                    remove: {
                        show: auth('correctWord:Delete'),

                        click: ({ row }) => {
                            ElMessageBox.confirm(`确认删除词条 "${row.word}" 吗？`, '删除确认', {
                                confirmButtonText: '确认',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(async () => {
                                return await api.deleteData(row.id).then(() => {
                                    ElMessage.success('删除成功')
                                    crudExpose.doRefresh()
                                }).catch((err: any) => {
                                    ElMessage.error(err.msg)
                                })
                            }).catch(() => {
                                return Promise.reject('取消删除')
                            })
                        }
                    }
                }
            },
            // 列配置
            columns: {
                id: {
                    title: 'ID',
                    type: 'number',
                    column: {
                        width: 80
                    },
                    form: {
                        show: false
                    },
                    search: { show: true, autoSearchTrigger: false }
                },
                word: {
                    title: '正确词条',
                    type: 'text',
                    search: { show: true, autoSearchTrigger: false },
                    column: {
                        width: 250
                    },
                    form: {
                        rules: [{ required: false, message: '请填写正确词条' }]
                    }
                },
                match_type: {
                    title: '词条类型',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.CORRECT_DICT_WORD_TYPE)
                    }),
                    search: { show: true },
                    column: {
                        width: 90,
                        component: {
                            name: 'fs-dict-select'
                        }
                    },
                    form: {
                        value: 1,
                        rules: [{ required: false, message: '请选择词条类型' }]
                    }
                },
                error_types: {
                    title: '错误类型',
                    type: 'dict-select',
                    dict: dict({
                        getData: errorTypeList,
                        value: 'value',
                        label: 'label',
                        cache: true
                    }),
                    search: {
                        show: true,
                        component: {
                            props: {
                                clearable: true,
                                filterable: true
                            }
                        }
                    },
                    column: {
                        width: 120,
                        formatter({ value }) {
                            if (!value || !Array.isArray(value) || value.length === 0) {
                                return '无';
                            }
                            return value.join(', ');
                        }
                    },
                    form: {
                        rules: [{ required: false, message: '请选择错误类型' }],
                        component: {
                            span: 12,
                            filterable: true,
                            multiple: true,
                            placeholder: '请选择错误类型(可多选)'
                        }
                    }
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.ENABLE_STATUS)
                    }),
                    column: {
                        width: 80,
                        component: {
                            name: 'fs-dict-select'
                        }
                    },
                    search: { show: true },
                    form: {
                        value: 0,
                        rules: [{ required: false, message: '请选择状态' }]
                    }
                },

                // hint: {
                //     title: '提示信息',
                //     type: 'text',
                //     form: {
                //         rules: [{ required: false, message: '请输入提示信息' }]
                //     }
                // },
                // recommend: {
                //     title: '推荐修改',
                //     type: 'text',
                //     form: {
                //         rules: [{ required: false, message: '请输入推荐修改' }]
                //     }
                // },
                word_explain: {
                    title: '正确原因',
                    type: 'textarea',
                    column: {
                        show: true,
                        width: 300,
                    },
                    form: {
                        rules: [{ required: false, message: '请解释这个词条为什么正确' }]
                    }
                },
                word_example: {
                    title: '错误原句示例',
                    type: 'textarea',
                    column: {
                        show: true,
                        width: 300,
                    },
                    form: {
                        rules: [{ required: false, message: '请提供示例' }]
                    }
                },
                creator_name: {
                    title: '创建者',
                    type: 'text',
                    column: {
                        width: 80
                    },
                    form: {
                        show: false
                    },
                    search: { show: false }
                },
                updater_name: {
                    title: '更新者',
                    type: 'text',
                    column: {
                        width: 80
                    },
                    form: {
                        show: false
                    },
                    search: { show: false }
                },
                // account_id: {
                //     title: '账号ID',
                //     type: 'text',
                //     search: { show: true },
                //     column: {
                //         width: 80
                //     },
                //     form: {
                //         helper: '默认值为0表示通用词条，其他为指定账号的词条'
                //     }
                // },
                tenant_ids: {
                    title: '客户',
                    type: 'dict-select',
                    dict: dict({
                        getData: async () => {
                            const data = await getTenantData();
                            return data.options;
                        },
                        value: 'value',
                        label: 'label',
                        cache: true
                    }),
                    search: {
                        show: false
                    },
                    column: {
                        minWidth: 150,
                        formatter({ value }) {
                            if (!value || !Array.isArray(value) || value.length === 0) {
                                return '通用';
                            }
                            // 根据租户ID查找对应的租户名称并拼接
                            const names = value.map(id => {
                                const tenant = tenantCache.find((item: { id: number; name: string }) => item.id === id);
                                return tenant ? tenant.name : `ID:${id}`;
                            });
                            return names.join(', ');
                        }
                    },
                    form: {
                        component: {
                            filterable: true,
                            multiple: true,
                            placeholder: '请选择客户(可多选)'
                        },
                        helper: '不选择表示通用词条，选择后表示仅对所选客户生效'
                    }
                },

                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: 100
                    },
                    form: {
                        show: false
                    }
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime',
                    column: {
                        width: 100
                    },
                    form: {
                        show: false
                    }
                }
            }
        }
    }
} 
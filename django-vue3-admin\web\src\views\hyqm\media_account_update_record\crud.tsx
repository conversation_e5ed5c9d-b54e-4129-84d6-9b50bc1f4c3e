import { asyncCompute, CrudExpose, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import * as tenantApi from '/@/views/tenant/api'
import { auth } from "/@/utils/authFunction"
export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    
    // 定义请求方法
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }


    return {
        crudOptions: {
            // 请求配置
            request: {
                pageRequest,
            },
            // 操作栏配置
            actionbar: {
                buttons: {
                    add: {
                        show: false // 不显示添加按钮
                    }
                }
            },
            toolbar:{
                show: false,
            },
            // 行操作配置
            // 禁用行操作按钮
            rowHandle: {
                show: false
            },
            search: {
                autoSearchTrigger: false, //关闭自动筛选
            },
            // 列配置
            columns: {
                id: {
                    title: 'ID',
                    type: 'text',
                    column: {
                        width: 80
                    },
                },
                account_name: {
                    title: '账号名称',
                    type: 'text',
                    search: {
                        show: true,
                        component: {
                            props: {
                                placeholder: '请输入账号名称'
                            }
                        }
                    },
                },
                tenant_id: {
                    title: '租户',
                    type: 'dict-select',
                    search: {
                        show: auth('Tenants:SelectList'),
                        component: {
                            props: {
                                clearable: true, // 可清除
                                filterable: true, // 可过滤
                                options: asyncCompute({
                                    asyncFn: async () => {
                                        if (auth('Tenants:SelectList')) {
                                            const res = await tenantApi.getAllTenants()
                                            return res.data.map((item: any) => ({
                                                label: item.name,
                                                value: item.id
                                            })) // 转换数据格式
                                        }
                                    }
                                })
                            }
                        }
                    },
                    column: {
                        minWidth: 120,
                        show: false
                    },
                    form: { show: false }
                },
                media_account_id: {
                    title: '媒体账号ID',
                    type: 'text',
                    search: {
                        show: true,
                        component: {
                            props: {
                                placeholder: '请输入媒体账号ID'
                            }
                        }
                    },
                },
                inactive_days: {
                    title: '不更新天数',
                    type: 'text',
                    search: {
                        show: true,
                    },
                    column: {
                        formatter({value}) {
                            return value + '天'
                        }
                    }
                },
                last_update_time: {
                    title: '最后更新时间',
                    type: 'datetime',
                    search: {
                        show: false,
                    },
                },
                last_update_date: {
                    title: '最后更新时间',
                    type: 'date',
                    search: {
                        show: true,
                        autoSearchTrigger: false, //关闭自动筛选
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'date',
                                valueFormat: 'YYYY-MM-DD'
                            }
                        },
                    },
                    column: {
                        show: false
                    }
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: 180,
                    },
                    form: {
                        show: false
                    }
                }
            }
        }
    }
} 
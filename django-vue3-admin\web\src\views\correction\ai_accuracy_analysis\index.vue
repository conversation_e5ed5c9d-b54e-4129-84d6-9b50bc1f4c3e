<template>
	<div class="ai-accuracy-analysis-container">
		<!-- 页面头部 -->
		<div class="page-header">
			<h2>AI校对效果分析</h2>
			<p>量化AI校对系统准确性，识别优化方向</p>
		</div>

		<!-- 筛选表单 -->
		<FilterForm
			v-model="filterParams"
			@search="handleSearch"
			@reset="handleReset"
		/>

		<!-- 统计卡片 -->
		<StatisticsCards
			:data="statisticsData"
			:loading="statisticsLoading"
		/>

		<!-- 图表分析 -->
		<ChartsContainer
			:stats-data="statisticsData"
			:error-type-data="errorTypeData"
			:trend-data="trendData"
			:loading="chartsLoading"
			@chart-click="handleChartClick"
		/>

		<!-- 详细记录 -->
		<RecordsTable
			:records="recordsData.records"
			:loading="recordsLoading"
			:total-count="recordsData.total_count"
			v-model:current-page="recordsData.page"
			v-model:page-size="recordsData.page_size"
			:table-height="tableHeight"
			@size-change="handleRecordsSizeChange"
			@current-change="handleRecordsCurrentChange"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import FilterForm from './components/FilterForm.vue';
import StatisticsCards from './components/StatisticsCards.vue';
import ChartsContainer from './components/ChartsContainer.vue';
import RecordsTable from './components/RecordsTable.vue';
import {
	FilterParams,
	StatsData,
	ErrorTypeAnalysisData,
	TrendAnalysisData,
	DetailedRecordsData,
	getStats,
	getErrorTypeAnalysis,
	getTrendAnalysis,
	getDetailedRecords,
} from './api';

defineOptions({
	name: 'AiAccuracyAnalysis',
});

// 响应式数据
const tableHeight = ref(400);

// 筛选参数
const filterParams = ref<FilterParams>({
	tenant_id: undefined,
	start_date: undefined,
	end_date: undefined,
	days: undefined,
	judge_user_id: undefined,
	error_type: undefined,
	judge_status: undefined,
});

// 统计数据
const statisticsData = ref<StatsData>({
	total_count: 0,
	accuracy_rate: 0,
	consistent_count: 0,
	ai_false_positive: 0,
	ai_false_negative: 0,
	ai_uncertain: 0,
});

// 错误类型分析数据
const errorTypeData = ref<ErrorTypeAnalysisData[]>([]);

// 时间趋势分析数据
const trendData = ref<TrendAnalysisData[]>([]);

// 详细记录数据
const recordsData = reactive<DetailedRecordsData>({
	records: [],
	total_count: 0,
	page: 1,
	page_size: 20,
	total_pages: 0,
	has_next: false,
	has_previous: false,
});

// 加载状态
const statisticsLoading = ref(false);
const chartsLoading = ref(false);
const recordsLoading = ref(false);

// 获取统计数据
const fetchStatisticsData = async () => {
	statisticsLoading.value = true;
	try {
		const res = await getStats(filterParams.value);
		statisticsData.value = res.data;
	} catch (error) {
		console.error('获取统计数据失败', error);
		ElMessage.error('获取统计数据失败');
	} finally {
		statisticsLoading.value = false;
	}
};

// 获取图表数据
const fetchChartsData = async () => {
	chartsLoading.value = true;
	try {
		// 并行请求错误类型分析和时间趋势数据
		const [errorTypeRes, trendRes] = await Promise.all([
			getErrorTypeAnalysis(filterParams.value),
			getTrendAnalysis(filterParams.value),
		]);

		errorTypeData.value = errorTypeRes.data || [];
		trendData.value = trendRes.data || [];
	} catch (error) {
		console.error('获取图表数据失败', error);
		ElMessage.error('获取图表数据失败');
	} finally {
		chartsLoading.value = false;
	}
};

// 获取详细记录数据
const fetchRecordsData = async () => {
	recordsLoading.value = true;
	try {
		const params = {
			...filterParams.value,
			page: recordsData.page,
			page_size: recordsData.page_size,
		};

		const res = await getDetailedRecords(params);
		Object.assign(recordsData, res.data);
	} catch (error) {
		console.error('获取详细记录失败', error);
		ElMessage.error('获取详细记录失败');
		// 出错时重置记录数据
		Object.assign(recordsData, {
			records: [],
			total_count: 0,
			page: 1,
			page_size: 20,
			total_pages: 0,
			has_next: false,
			has_previous: false,
		});
	} finally {
		recordsLoading.value = false;
	}
};

// 获取所有数据
const fetchAllData = async () => {
	await Promise.all([
		fetchStatisticsData(),
		fetchChartsData(),
		fetchRecordsData(),
	]);
};

// 搜索处理
const handleSearch = async () => {
	// 重置分页到第一页
	recordsData.page = 1;
	await nextTick();
	fetchAllData();
};

// 重置处理
const handleReset = async () => {
	// 重置筛选参数
	Object.assign(filterParams.value, {
		tenant_id: undefined,
		start_date: undefined,
		end_date: undefined,
		days: undefined,
		judge_user_id: undefined,
		error_type: undefined,
		judge_status: undefined,
	});

	// 重置分页
	recordsData.page = 1;
	recordsData.page_size = 20;

	await nextTick();
	fetchAllData();
};

// 图表点击处理（实现图表联动）
const handleChartClick = async (clickData: { type: string; value: string }) => {
	const { type, value } = clickData;

	// 根据点击的图表元素更新筛选条件
	if (type === 'judge_status') {
		filterParams.value.judge_status = value;
	} else if (type === 'error_type') {
		filterParams.value.error_type = value;
	}

	// 重置分页并更新数据
	recordsData.page = 1;
	await nextTick();
	
	// 只更新详细记录数据，因为图表数据不需要重新加载
	await fetchRecordsData();
	
	ElMessage.success(`已筛选 ${type === 'judge_status' ? '判断状态' : '错误类型'}: ${type === 'judge_status' ? getJudgeStatusLabel(value) : value}`);
};

// 获取判断状态标签
const getJudgeStatusLabel = (value: string): string => {
	const labelMap: Record<string, string> = {
		'consistent': '一致判断',
		'ai_false_positive': 'AI误报',
		'ai_false_negative': 'AI漏报',
	};
	return labelMap[value] || value;
};

// 详细记录页大小变化处理
const handleRecordsSizeChange = async (val: number) => {
	recordsData.page = 1; // 重置到第一页
	recordsData.page_size = val;
	recordsData.records = []; // 清空当前列表，避免DOM冲突
	await nextTick();
	fetchRecordsData();
};

// 详细记录页码变化处理
const handleRecordsCurrentChange = async (val: number) => {
	recordsData.page = val;
	recordsData.records = []; // 清空当前列表，避免DOM冲突
	await nextTick();
	fetchRecordsData();
};

// 处理窗口大小变化
const handleResize = () => {
	tableHeight.value = Math.max(400, window.innerHeight - 800);
};

// 组件挂载
onMounted(async () => {
	// 设置表格高度
	handleResize();
	window.addEventListener('resize', handleResize);

	// 加载初始数据
	await fetchAllData();
});

// 组件卸载
onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
.ai-accuracy-analysis-container {
	min-height: calc(100vh - 84px);
	padding: 20px;
	background-color: #f5f7fa;

	.page-header {
		background: white;
		padding: 24px;
		border-radius: 8px;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
		margin-bottom: 20px;

		h2 {
			margin: 0 0 8px;
			color: #303133;
			font-size: 24px;
			font-weight: 700;
		}

		p {
			margin: 0;
			color: #666666;
			font-size: 14px;
		}
	}
}

// 响应式适配
@media (max-width: 768px) {
	.ai-accuracy-analysis-container {
		padding: 16px;

		.page-header {
			padding: 20px;

			h2 {
				font-size: 20px;
			}

			p {
				font-size: 13px;
			}
		}
	}
}
</style>
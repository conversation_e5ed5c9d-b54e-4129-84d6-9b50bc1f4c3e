import { CrudOptions, CrudExpose, CreateCrudOptionsRet, dict, compute } from '@fast-crud/fast-crud'
import * as api from './api'
import { auth } from "/@/utils/authFunction"
import { h, ref, computed } from 'vue'
import { ElMessageBox, ElCheckboxGroup, ElCheckbox, ElMessage } from 'element-plus'
import { successMessage, errorMessage } from '/@/utils/message';

export default function ({ crudExpose, context }: { crudExpose: CrudExpose, context?: any }): CreateCrudOptionsRet {
    // 从 context 中获取 tenantId，避免在这里使用 useRoute()
    const tenantId = context?.tenantId;

    // 从外部传入或使用默认值
    const currentPlatformType = context?.currentPlatformType || ref('wechat');
    const selectedIds = ref<number[]>([]);

    const pageRequest = async (query: any) => {
        // 添加platform_type到查询参数
        query.platform_type = currentPlatformType.value;
        // 添加tenant_id到查询参数
        if (tenantId) {
            query.tenant_id = Number(tenantId);
        }
        console.log('监管账号列表查询参数:', query);
        return await api.getMediaAccountsList(query)
    }

    // 运营类型选项数据
    const operatingTypeOptions = [
        { label: '下级账号', value: 'is_manage' },
        { label: '本级账号', value: 'is_operating' },
        { label: '同类账号', value: 'is_same_type' },
    ];

    // 设置单个账号运营类型
    const handleSetOperatingType = async (row: any) => {
        try {
            let selectedTypes = ref<string[]>([]);

            // 初始化已选择的类型
            const initialTypes: string[] = [];
            if (row.is_same_type) initialTypes.push('is_same_type');
            if (row.is_operating) initialTypes.push('is_operating');
            if (row.is_manage) initialTypes.push('is_manage');
            selectedTypes.value = initialTypes;

            const content = () => {
                return h('div', { style: 'width: 100%; padding: 20px 0;' }, [
                    h('div', {
                        style: 'display: flex; flex-direction: column; margin-bottom: 10px;'
                    }, [
                        h('span', { style: 'margin-bottom: 10px; font-weight: bold;' }, '运营类型（可多选）：'),
                        h(ElCheckboxGroup, {
                            modelValue: selectedTypes.value,
                            'onUpdate:modelValue': (val: any) => {
                                selectedTypes.value = val
                            }
                        }, {
                            default: () => operatingTypeOptions.map(option =>
                                h(ElCheckbox, {
                                    key: option.value,
                                    label: option.label,
                                    value: option.value
                                })
                            )
                        })
                    ])
                ])
            }

            await ElMessageBox({
                title: `设置账号运营类型 - ${row.account_name}`,
                message: content,
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                beforeClose: async (action, instance, done) => {
                    if (action === 'confirm') {
                        try {
                            await api.batchSetOperatingType({
                                media_account_ids: [Number(row.media_account_id)],
                                is_same_type: selectedTypes.value.includes('is_same_type'),
                                is_operating: selectedTypes.value.includes('is_operating'),
                                is_manage: selectedTypes.value.includes('is_manage'),
                            });
                            successMessage('设置运营类型成功');
                            crudExpose.doRefresh();
                            done();
                        } catch (error: any) {
                            errorMessage(error.message || '设置运营类型失败');
                        }
                    } else {
                        done();
                    }
                }
            });
        } catch (error: any) {
            if (error !== 'cancel') {
                errorMessage(error.message || '设置运营类型失败');
            }
        }
    };

    // 批量设置运营类型
    const handleBatchSetOperatingType = async (accountIds: number[]) => {
        try {
            let selectedTypes = ref<string[]>([]);

            const content = () => {
                return h('div', { style: 'width: 100%; padding: 20px 0;' }, [
                    h('div', {
                        style: 'display: flex; flex-direction: column; margin-bottom: 10px;'
                    }, [
                        h('span', { style: 'margin-bottom: 10px; font-weight: bold;' }, '运营类型（可多选）：'),
                        h(ElCheckboxGroup, {
                            modelValue: selectedTypes.value,
                            'onUpdate:modelValue': (val: any) => {
                                selectedTypes.value = val
                            }
                        }, {
                            default: () => operatingTypeOptions.map(option =>
                                h(ElCheckbox, {
                                    key: option.value,
                                    value: option.value,
                                    style: 'display: block; margin-bottom: 8px;'
                                }, {
                                    default: () => option.label
                                })
                            )
                        })
                    ]),
                    h('div', {
                        style: 'margin-top: 15px; color: #666; font-size: 14px;'
                    }, `将为 ${accountIds.length} 个账号设置运营类型`)
                ])
            }

            await ElMessageBox({
                title: '批量设置运营类型',
                message: content,
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                beforeClose: async (action, instance, done) => {
                    if (action === 'confirm') {
                        try {
                            await api.batchSetOperatingType({
                                media_account_ids: accountIds,
                                is_same_type: selectedTypes.value.includes('is_same_type'),//同类账号
                                is_operating: selectedTypes.value.includes('is_operating'),//本级账号
                                is_manage: selectedTypes.value.includes('is_manage'),//下级账号
                            });
                            successMessage('批量设置运营类型成功');
                            crudExpose.doRefresh();
                            done();
                        } catch (error: any) {
                            errorMessage(error.message || '批量设置运营类型失败');
                        }
                    } else {
                        done();
                    }
                }
            });
        } catch (error: any) {
            if (error !== 'cancel') {
                errorMessage(error.message || '批量设置运营类型失败');
            }
        }
    };

    // 选择变更回调
    const onSelectionChange = (changed: any) => {
        selectedIds.value = changed.map((item: any) => item.media_account_id);
        console.log("selection", changed, selectedIds.value);

        // 如果有外部传入的选择变更回调，则调用它
        if (context?.onSelectionChange) {
            context.onSelectionChange(changed);
        }
    };

    return {
        currentPlatformType, // 将currentPlatformType导出，以便在index.vue中使用
        selectedIds, // 导出选中的ID数组
        handleBatchSetOperatingType, // 导出批量设置方法供index.vue使用
        crudOptions: {
            request: {
                pageRequest,
            },
            search: {
                initialForm: {
                    tenant_id: tenantId ? Number(tenantId) : undefined
                }
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false // 隐藏新增按钮
                    }
                }
            },
            toolbar: {
                show: false, // 隐藏工具栏
            },
            table: {
                rowKey: "media_account_id", //设置你的主键id
                onSelectionChange
            },
            rowHandle: {
                width: 150,
                buttons: {
                    edit: {
                        show: false // 隐藏编辑按钮
                    },
                    remove: {
                        show: false // 隐藏删除按钮
                    },
                    view: {
                        show: false // 隐藏查看按钮
                    },
                    setOperatingType: {
                        text: '设置运营类型',
                        show: computed(() => {
                            return auth('TenantMediaAccounts:SetOperatingType')
                        }),
                        link: true,
                        type: 'primary',
                        click: ({ row }) => {
                            handleSetOperatingType(row);
                        }
                    }
                }
            },
            columns: {
                $checked: {
                    title: "选择",
                    form: { show: false },
                    column: {
                        type: "selection",
                        align: "center",
                        width: "55px",
                    }
                },
                media_account_id: {
                    title: 'ID',
                    type: 'text',
                    form: { show: false },
                    search: { show: true },
                    width: 80
                },
                account_name: {
                    title: '账号名称',
                    type: 'text',
                    search: { show: true },
                    form: { show: false },
                    width: 200
                },
                account_id: {
                    title: computed(() => {
                        switch (currentPlatformType.value) {
                            case 'wechat':
                                return '微信ID';
                            case 'douyin':
                                return '抖音ID';
                            case 'weibo':
                                return '微博ID';
                            default:
                                return '账号ID';
                        }
                    }),
                    type: 'text',
                    search: {
                        show: true,
                        component: {
                            placeholder: computed(() => {
                                switch (currentPlatformType.value) {
                                    case 'wechat':
                                        return '请输入微信ID';
                                    case 'douyin':
                                        return '请输入抖音ID';
                                    case 'weibo':
                                        return '请输入微博ID';
                                    default:
                                        return '请输入账号ID';
                                }
                            })
                        }
                    },
                    form: { show: false },
                    width: 150
                },
                operating_type: {
                    title: '运营类型',
                    type: 'text',
                    search: { show: false },
                    form: { show: false },
                    column: {
                        formatter: ({ value, row, index }) => {
                            const types: string[] = [];
                            if (row.is_manage) types.push('下级账号');
                            if (row.is_operating) types.push('本级账号');
                            if (row.is_same_type) types.push('同类账号');

                            return types.length > 0 ? types.join(', ') : '未设置';
                        }
                    },
                    width: 150
                }
            }
        }
    }
} 
# 项目架构分析

## 🏗 目录结构
```
src/
├── api/                    # API接口管理
│   ├── login/             # 登录相关接口
│   ├── menu/              # 菜单权限接口
│   ├── system/            # 系统管理接口
│   └── website/           # 网站爬虫接口
├── assets/                # 静态资源
├── components/            # 公共组件
├── layout/                # 布局组件
├── plugin/                # 插件系统
├── router/                # 路由配置
├── stores/                # 状态管理(Pinia)
├── utils/                 # 工具函数
├── views/                 # 页面组件
│   ├── system/           # 系统管理模块
│   ├── aijiaodui/        # AI校对模块
│   ├── statistics/       # 统计分析模块
│   ├── website/          # 网站管理模块
│   ├── wechat_official_account/ # 微信公众号模块
│   ├── wechat_pc_server/ # 微信PC服务模块
│   ├── correction/       # 校对模块
│   ├── manuscript/       # 稿件管理模块
│   └── [其他业务模块]/
└── types/                 # TypeScript类型定义
```

## 核心架构特点

### 1. Fast-CRUD驱动的开发模式
- **快速CRUD**: 基于配置的表格、表单、搜索功能
- **统一API**: 标准化的增删改查接口
- **权限集成**: 内置按钮级和列级权限控制
- **组件化**: 高度可复用的业务组件

### 2. 权限系统架构
- **菜单权限**: 基于角色的路由访问控制
- **按钮权限**: 精确到每个操作按钮的权限控制
- **列权限**: 表格字段级别的显示权限
- **权限指令**: `v-auth` 指令进行权限判断

### 3. 模块化设计
- **业务模块独立**: 每个业务模块独立目录
- **API分层**: api、views、components三层分离
- **类型安全**: 完整的TypeScript类型定义

## 架构设计原则
1. **单一职责**: 每个模块/组件只负责一个功能
2. **开放封闭**: 对扩展开放，对修改封闭
3. **依赖倒置**: 依赖抽象而不是具体实现
4. **接口隔离**: 接口应该小而专
5. **最少知识**: 组件间耦合度最小化
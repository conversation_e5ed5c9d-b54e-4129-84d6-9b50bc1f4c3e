<template>
	<div class="el-transfer">
		<transfer-panel
			v-bind="panelProps"
			ref="leftPanel"
			:data="sourceData"
			:title="titles[0] || t('el.transfer.titles.0')"
			:default-checked="leftDefaultChecked"
			:placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
			:hide-footer="hideFooter"
			@checked-change="onSourceCheckedChange"
		>
			<template #default>
				<slot name="left-footer"></slot>
			</template>
		</transfer-panel>
		<div class="el-transfer__buttons">
			<el-button
				type="primary"
				:class="['el-transfer__button', hasButtonTexts ? 'is-with-texts' : '']"
				@click="addToLeft"
				:disabled="rightChecked.length === 0"
			>
				<el-icon><ArrowLeftBold /></el-icon>
				<span v-if="buttonTexts[0] !== undefined">{{ buttonTexts[0] }}</span>
			</el-button>
			<el-button
				type="primary"
				:class="['el-transfer__button', hasButtonTexts ? 'is-with-texts' : '']"
				@click="addToRight"
				:disabled="leftChecked.length === 0"
			>
				<span v-if="buttonTexts[1] !== undefined">{{ buttonTexts[1] }}</span>
				<el-icon><ArrowRightBold /></el-icon>
			</el-button>
		</div>
		<transfer-panel
			v-bind="panelProps"
			ref="rightPanel"
			:data="targetData"
			:title="titles[1] || t('el.transfer.titles.1')"
			:default-checked="rightDefaultChecked"
			:placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
			:hide-footer="hideFooter"
			@checked-change="onTargetCheckedChange"
		>
			<template #default>
				<slot name="right-footer"></slot>
			</template>
		</transfer-panel>
	</div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import { ElButton, ElIcon } from 'element-plus';
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue';
import TransferPanel from './transfer-panel.vue';
import { useLocale } from 'element-plus';

// 组件Props定义
const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
	titles: {
		type: Array,
		default: () => [],
	},
	buttonTexts: {
		type: Array,
		default: () => [],
	},
	filterPlaceholder: {
		type: String,
		default: '',
	},
	filterMethod: Function,
	leftDefaultChecked: {
		type: Array,
		default: () => [],
	},
	rightDefaultChecked: {
		type: Array,
		default: () => [],
	},
	renderContent: Function,
	modelValue: {
		type: Array,
		default: () => [],
	},
	format: {
		type: Object,
		default: () => ({}),
	},
	filterable: Boolean,
	hideFooter: {
		type: Boolean,
		default: false,
	},
	props: {
		type: Object,
		default: () => ({
			label: 'label',
			key: 'key',
			disabled: 'disabled',
		}),
	},
	targetOrder: {
		type: String,
		default: 'original',
	},
});

// 组件Emits定义
const emit = defineEmits(['update:modelValue', 'change', 'left-check-change', 'right-check-change']);

// 国际化
const { t } = useLocale();

// 定义响应式引用
const leftPanel = ref(null);
const rightPanel = ref(null);
const leftChecked = ref([]);
const rightChecked = ref([]);

// 计算属性
const dataObj = computed(() => {
	const key = props.props.key;
	return props.data.reduce((o, cur) => (o[cur[key]] = cur) && o, {});
});

const sourceData = computed(() => {
	let valueObj = {};
	props.modelValue.forEach((item) => {
		valueObj[item] = true;
	});
	return props.data.filter((item) => !valueObj[item[props.props.key]]);
});

const targetData = computed(() => {
	if (props.targetOrder === 'original') {
		let valueObj = {};
		props.modelValue.forEach((item) => {
			valueObj[item] = true;
		});
		let data = props.data.filter((item) => valueObj[item[props.props.key]]);
		return data;
	} else {
		return props.modelValue.reduce((arr, cur) => {
			const val = dataObj.value[cur];
			if (val) {
				arr.push(val);
			}
			return arr;
		}, []);
	}
});

const hasButtonTexts = computed(() => {
	return props.buttonTexts.length === 2;
});

const panelProps = computed(() => {
	const propsToPass = { ...props };
	delete propsToPass.modelValue;
	delete propsToPass.leftDefaultChecked;
	delete propsToPass.rightDefaultChecked;
	return propsToPass;
});

// 监听器
watch(
	() => props.modelValue,
	() => {
		// Vue3不再需要dispatch到ElFormItem，元素表单会通过v-model自动处理
	}
);

// 方法
const onSourceCheckedChange = (val, movedKeys) => {
	leftChecked.value = val;
	if (movedKeys === undefined) return;
	emit('left-check-change', val, movedKeys);
};

const onTargetCheckedChange = (val, movedKeys) => {
	rightChecked.value = val;
	if (movedKeys === undefined) return;
	emit('right-check-change', val, movedKeys);
};

const addToLeft = () => {
	let currentValue = [...props.modelValue];
	rightChecked.value.forEach((item) => {
		const index = currentValue.indexOf(item);
		if (index > -1) {
			currentValue.splice(index, 1);
		}
	});
	emit('update:modelValue', currentValue);
	emit('change', currentValue, 'left', rightChecked.value);
};

const addToRight = () => {
	let currentValue = [...props.modelValue];
	const itemsToBeMoved = [];
	const key = props.props.key;
	let leftCheckedKeyPropsObj = {};
	leftChecked.value.forEach((item) => {
		leftCheckedKeyPropsObj[item] = true;
	});
	let valueKeyPropsObj = {};
	props.modelValue.forEach((item) => {
		valueKeyPropsObj[item] = true;
	});
	props.data.forEach((item) => {
		const itemKey = item[key];
		if (leftCheckedKeyPropsObj[itemKey] && !valueKeyPropsObj[itemKey]) {
			itemsToBeMoved.push(itemKey);
		}
	});
	currentValue = props.targetOrder === 'unshift' ? [...itemsToBeMoved, ...currentValue] : [...currentValue, ...itemsToBeMoved];
	emit('update:modelValue', currentValue);
	emit('change', currentValue, 'right', leftChecked.value);
};

const clearQuery = (which) => {
	if (which === 'left') {
		leftPanel.value.query = '';
	} else if (which === 'right') {
		rightPanel.value.query = '';
	}
};

// 暴露组件方法和属性
defineExpose({
	leftPanel,
	rightPanel,
	clearQuery,
});
</script>


<template>
	<div class="app-container">
		<div v-loading="pageLoading">
			<!-- 导出PDF按钮 -->
			<div class="export-buttons">
				<el-button type="primary" @click="handleExportPDF" :loading="exporting">导出PDF</el-button>
			</div>

			<!-- 我的抖音月报 -->
			<my-dy-operate-detail
				ref="myDyOperateRef"
				style="margin-bottom: 40px"
				:date="{
					start_date: reportData.start_date,
					end_date: reportData.end_date,
				}"
				:title="reportData.title"
				:self-report-data="myReportData"
				:tenant-name="reportData.tenant_name"
				:account-counts="accountCounts"
				v-if="myReportData"
			/>

			<!-- 下级抖音月报 -->
			<operate-dy-report-detail
				ref="operateDyReportRef"
				:date="{
					start_date: reportData.start_date,
					end_date: reportData.end_date,
				}"
				:title="reportData.title"
				:sub-report-data="subReportData"
				:same-account="reportData.report_data?.same_account"
				:no-update-account="reportData.report_data?.no_update_days"
				:create-time="reportData?.update_time"
				v-if="subReportData || reportData?.account_publish_info || reportData.report_data?.same_account"
			/>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElNotification } from 'element-plus';
import OperateDyReportDetail from './dySubAccountQuarterDetail.vue';
import MyDyOperateDetail from './myDyOperateQuarterDetail.vue';
import { exportToPDF } from '/@/utils/pdfExportHelper';
import * as api from '/@/views/analysisHelper/operateReport/api';

const route = useRoute();
const reportId = ref('');
const pageLoading = ref(false);
const exporting = ref(false);
const reportData = ref({});
const myDyOperateRef = ref();
const operateDyReportRef = ref();

// 计算账号数量（抖音只有本级和下级，没有同类）
const accountCounts = computed(() => {
	const myAccountCount = reportData.value.report_data?.my_account?.account_data?.length || 0;
	const subAccountCount = reportData.value.report_data?.sub_account?.dci_rank?.length || 0;

	return {
		myAccount: myAccountCount,
		subAccount: subAccountCount,
	};
});

// 我的抖音报告数据和下级抖音报告数据
const myReportData = ref(null);
const subReportData = ref(null);

// 处理我的抖音运营报表数据
const processMyDyReportData = (reportData) => {
	if (!reportData || !reportData.report_data || !reportData.report_data.my_account) {
		return null;
	}

	const data = reportData.report_data.my_account;
	const date = {
		start_date: reportData.start_date,
		end_date: reportData.end_date,
	};

	// 提取月份
	const month = new Date(date.end_date).getMonth() + 1 + '月';
	const year = new Date(date.end_date).getFullYear();
	const yearMonth = `${year}年${month}`;

	// 处理账号数据 - 适配新的数据结构
	const accountData = data.account_data && data.account_data.length > 0 ? data.account_data[0] : null;

	if (!accountData) {
		return null;
	}

	// 处理DCI数据
	const dciData = accountData.dci
		? [
				{
					accountName: accountData.account_name,
					dic: accountData.dci.dci,
					dicStatus: accountData.dci.compare_trend === 1 ? '上升' : accountData.dci.compare_trend === -1 ? '下降' : '持平',
					diff: Math.abs(accountData.dci.diff),
					rate: accountData.dci.compare_rate ? `${accountData.dci.compare_rate}%` : '0%',
				},
		  ]
		: [];

	// 处理作品发布数据
	const worksIssue = accountData.publish
		? [
				{
					accountName: accountData.account_name,
					count: accountData.publish.total_count,
					worksStatus: accountData.publish.compare.trend === 1 ? '增加' : accountData.publish.compare.trend === -1 ? '减少' : '持平',
					diff: Math.abs(accountData.publish.compare.diff),
					rate: accountData.publish.compare.rate ? `${accountData.publish.compare.rate}%` : '0%',
					contentTypePie: {
						data: [
							{ name: '视频', value: accountData.publish.total_video },
							{ name: '图文', value: accountData.publish.total_txt },
						],
						total: { count: accountData.publish.total_count, name: '总计' },
					},
					// 准备时间分布数据
					timeDistribution: {
						title: '发布时间区间分布情况',
						row: [
							{
								name: '发文数量',
								counts: accountData.publish.hour_distribution.map((h) => h.count),
								type: 'line',
							},
						],
						dates: accountData.publish.hour_distribution.map((h) => h.hour),
					},
				},
		  ]
		: [];

	// 处理点赞数据
	const worksLike = accountData.like_count_stats
		? [
				{
					accountName: accountData.account_name,
					data: accountData.like_count_stats.total,
					status: accountData.like_count_stats.compare.trend === 1 ? '增加' : accountData.like_count_stats.compare.trend === -1 ? '减少' : '持平',
					diff: Math.abs(accountData.like_count_stats.compare.diff),
					rate: accountData.like_count_stats.compare.rate ? `${accountData.like_count_stats.compare.rate}%` : '0%',
					avg: accountData.like_count_stats.article_avg,
					maxData: accountData.like_count_stats.top_article.value,
					maxTitle: accountData.like_count_stats.top_article.title ? accountData.like_count_stats.top_article.title : '-',
					maxIssueTime: accountData.like_count_stats.top_article.date,
				},
		  ]
		: [];

	// 处理评论数据
	const worksComment = accountData.comment_count_stats
		? [
				{
					accountName: accountData.account_name,
					data: accountData.comment_count_stats.total,
					status:
						accountData.comment_count_stats.compare.trend === 1 ? '增加' : accountData.comment_count_stats.compare.trend === -1 ? '减少' : '持平',
					diff: Math.abs(accountData.comment_count_stats.compare.diff),
					rate: accountData.comment_count_stats.compare.rate ? `${accountData.comment_count_stats.compare.rate}%` : '0%',
					avg: accountData.comment_count_stats.article_avg,
					maxData: accountData.comment_count_stats.top_article.value,
					maxTitle: accountData.comment_count_stats.top_article.title,
					maxIssueTime: accountData.comment_count_stats.top_article.date,
				},
		  ]
		: [];

	// 处理转发数据
	const worksForward = accountData.share_count_stats
		? [
				{
					accountName: accountData.account_name,
					data: accountData.share_count_stats.total,
					status: accountData.share_count_stats.compare.trend === 1 ? '增加' : accountData.share_count_stats.compare.trend === -1 ? '减少' : '持平',
					diff: Math.abs(accountData.share_count_stats.compare.diff),
					rate: accountData.share_count_stats.compare.rate ? `${accountData.share_count_stats.compare.rate}%` : '0%',
					avg: accountData.share_count_stats.article_avg,
					maxData: accountData.share_count_stats.top_article.value,
					maxTitle: accountData.share_count_stats.top_article.title,
					maxIssueTime: accountData.share_count_stats.top_article.date,
				},
		  ]
		: [];

	// 处理收藏数据
	const worksCollect = accountData.collect_count_stats
		? [
				{
					accountName: accountData.account_name,
					data: accountData.collect_count_stats.total,
					status:
						accountData.collect_count_stats.compare.trend === 1 ? '增加' : accountData.collect_count_stats.compare.trend === -1 ? '减少' : '持平',
					diff: Math.abs(accountData.collect_count_stats.compare.diff),
					rate: accountData.collect_count_stats.compare.rate ? `${accountData.collect_count_stats.compare.rate}%` : '0%',
					avg: accountData.collect_count_stats.article_avg,
					maxData: accountData.collect_count_stats.top_article.value,
					maxTitle: accountData.collect_count_stats.top_article.title,
					maxIssueTime: accountData.collect_count_stats.top_article.date,
				},
		  ]
		: [];

	// 处理作品分析数据
	const worksTopLike = [];
	if (data.work_analysis) {
		if (data.work_analysis.top1 && data.work_analysis.top1.title) {
			worksTopLike.push({
				title: data.work_analysis.top1.title,
				like: data.work_analysis.top1.like,
				comment: data.work_analysis.top1.comment,
				collect: data.work_analysis.top1.collect,
				forward: data.work_analysis.top1.share,
				account: data.work_analysis.top1.account_name,
			});
		}
		if (data.work_analysis.top2 && data.work_analysis.top2.account_name) {
			worksTopLike.push({
				title: data.work_analysis.top2.title,
				like: data.work_analysis.top2.like,
				comment: data.work_analysis.top2.comment,
				collect: data.work_analysis.top2.collect,
				forward: data.work_analysis.top2.share,
				account: data.work_analysis.top2.account_name,
			});
		}
		if (data.work_analysis.top3 && data.work_analysis.top3.title) {
			worksTopLike.push({
				title: data.work_analysis.top3.title,
				like: data.work_analysis.top3.like,
				comment: data.work_analysis.top3.comment,
				collect: data.work_analysis.top3.collect,
				forward: data.work_analysis.top3.share,
				account: data.work_analysis.top3.account_name,
			});
		}
	}

	// 点赞最少的视频
	const minVideo = data.work_analysis?.min
		? {
				title: data.work_analysis.min.title,
				like: data.work_analysis.min.like,
				comment: data.work_analysis.min.comment,
				collect: data.work_analysis.min.collect,
				forward: data.work_analysis.min.share,
				account: data.work_analysis.min.account_name,
		  }
		: null;

	// 处理粉丝数据
	const totalFans = {
		endTime: reportData.end_date,
		data: data.account_fans_data.total_fans.map((item) => ({
			name: item.account_name,
			count: item.total,
		})),
		total: {
			count: data.account_fans_data.total_fans.reduce((sum, item) => sum + Number(item.total), 0),
			name: '总粉丝量',
		},
		netFans: data.account_fans_data.net_fans.map((item) => ({
			accountName: item.account_name,
			count: item.total,
			status: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
			diff: Math.abs(item.compare.diff),
			rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
		})),
	};

	// 新增：整合账号数据用于表格展示
	const accountDataList = [
		{
			account_name: accountData.account_name,
			dci: accountData.dci,
			like_count_stats: accountData.like_count_stats,
			share_count_stats: accountData.share_count_stats,
			collect_count_stats: accountData.collect_count_stats,
			comment_count_stats: accountData.comment_count_stats,
			// 粉丝数据
			total_fans: data.account_fans_data.total_fans.find((item) => item.account_name === accountData.account_name),
			net_fans: data.account_fans_data.net_fans.find((item) => item.account_name === accountData.account_name),
		},
	];

	return {
		month,
		yearMonth,
		dci: dciData,
		worksIssue,
		worksLike,
		worksComment,
		worksForward,
		worksCollect,
		worksTopLike,
		minVideo,
		totalFans,
		accountDataList, // 新增：用于表格展示的账号数据
	};
};

// 处理抖音下级账号运营报表数据
const processSubDyReportData = (reportData) => {
	if (!reportData || !reportData.report_data || !reportData.report_data.sub_account) {
		return null;
	}

	const data = reportData.report_data.sub_account;
	const date = {
		start_date: reportData.start_date,
		end_date: reportData.end_date,
	};

	// 提取月份
	const month = new Date(date.end_date).getMonth() + 1 + '月';
	const year = new Date(date.end_date).getFullYear();
	const yearMonth = `${year}年${month}`;
	const reportType = '1'; // 默认为月报

	// 处理DCI数据
	const dciData = data.dci_rank.map((item) => ({
		accountName: item.account_name,
		dic: item.dci,
		dicStatus: item.compare_trend === 1 ? '上升' : item.compare_trend === -1 ? '下降' : '持平',
		diff: Math.abs(item.diff),
		rate: item.compare_rate ? `${item.compare_rate}%` : '0%',
	}));

	// 处理作品发布数据
	const worksIssue = data.publish.works.map((item) => ({
		accountName: item.account_name,
		count: item.total_count,
		worksStatus: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
		diff: Math.abs(item.compare.diff),
		rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
		totalCount: item.total_sum_count || item.total_count,
	}));

	// 内容类型分布
	const videos = data.total_video;
	const texts = data.total_txt;
	const totalContent = videos + texts;

	const contentTypePie = {
		data: [
			{ name: '视频', value: videos },
			{ name: '图文', value: texts },
		],
		total: { count: totalContent, name: '总计' },
	};

	// 发布时间分布
	const timeDistribution = {
		title: '发布时间区间分布',
		row: [
			{
				name: '发文数量',
				counts: data.hour_distribution.map((h) => h.count),
				type: 'line',
			},
		],
		dates: data.hour_distribution.map((h) => h.hour),
	};

	// 处理点赞数据
	const worksLike = data.like_data.map((item) => ({
		accountName: item.account_name,
		data: item.total,
		status: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
		diff: Math.abs(item.compare.diff),
		rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
		avg: item.article_avg,
		maxData: item.top_article.value,
		maxTitle: item.top_article.title ? item.top_article.title : '-',
		maxIssueTime: item.top_article.date ? item.top_article.date : '-',
	}));

	// 处理评论数据
	const worksComment = data.comment_data.map((item) => ({
		accountName: item.account_name,
		data: item.total,
		status: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
		diff: Math.abs(item.compare.diff),
		rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
		avg: item.article_avg,
		maxData: item.top_article.value,
		maxTitle: item.top_article.title ? item.top_article.title : '-',
		maxIssueTime: item.top_article.date ? item.top_article.date : '-',
	}));

	// 处理转发数据
	const worksForward = data.share_data.map((item) => ({
		accountName: item.account_name,
		data: item.total,
		status: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
		diff: Math.abs(item.compare.diff),
		rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
		avg: item.article_avg,
		maxData: item.top_article.value,
		maxTitle: item.top_article.title ? item.top_article.title : '-',
		maxIssueTime: item.top_article.date ? item.top_article.date : '-',
	}));

	// 处理收藏数据
	const worksCollect = data.collect_data.map((item) => ({
		accountName: item.account_name,
		data: item.total,
		status: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
		diff: Math.abs(item.compare.diff),
		rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
		avg: item.article_avg,
		maxData: item.top_article.value,
		maxTitle: item.top_article.title ? item.top_article.title : '-',
		maxIssueTime: item.top_article.date ? item.top_article.date : '-',
	}));

	// 处理作品分析数据
	const worksTopLike = [];
	if (data.work_analysis) {
		if (data.work_analysis.top1) {
			worksTopLike.push({
				title: data.work_analysis.top1.title,
				like: data.work_analysis.top1.like,
				comment: data.work_analysis.top1.comment,
				collect: data.work_analysis.top1.collect,
				forward: data.work_analysis.top1.share,
				account: data.work_analysis.top1.account_name,
			});
		}
		if (data.work_analysis.top2) {
			worksTopLike.push({
				title: data.work_analysis.top2.title,
				like: data.work_analysis.top2.like,
				comment: data.work_analysis.top2.comment,
				collect: data.work_analysis.top2.collect,
				forward: data.work_analysis.top2.share,
				account: data.work_analysis.top2.account_name,
			});
		}
		if (data.work_analysis.top3) {
			worksTopLike.push({
				title: data.work_analysis.top3.title,
				like: data.work_analysis.top3.like,
				comment: data.work_analysis.top3.comment,
				collect: data.work_analysis.top3.collect,
				forward: data.work_analysis.top3.share,
				account: data.work_analysis.top3.account_name,
			});
		}
	}

	// 点赞最少的视频
	const minVideo = data.work_analysis?.min
		? {
				title: data.work_analysis.min.title,
				like: data.work_analysis.min.like,
				comment: data.work_analysis.min.comment,
				collect: data.work_analysis.min.collect,
				forward: data.work_analysis.min.share,
				account: data.work_analysis.min.account_name,
		  }
		: null;

	// 处理粉丝数据
	const totalFans = {
		endTime: reportData.end_date,
		data: data.account_fans_data.total_fans.map((item) => ({
			name: item.account_name,
			count: item.total,
		})),
		total: {
			count: data.account_fans_data.total_fans.reduce((sum, item) => sum + Number(item.total), 0),
			name: '总粉丝量',
		},
		netFans: data.account_fans_data.net_fans
			.filter((item, index, self) => self.findIndex((t) => t.account_name === item.account_name) === index)
			.slice(0, 3)
			.map((item) => ({
				accountName: item.account_name,
				count: item.total,
				status: item.compare.trend === 1 ? '增加' : item.compare.trend === -1 ? '减少' : '持平',
				diff: Math.abs(item.compare.diff),
				rate: item.compare.rate ? `${item.compare.rate}%` : '0%',
			})),
	};

	return {
		month,
		yearMonth,
		reportType,
		dci: dciData,
		worksIssue,
		contentTypePie,
		timeDistribution,
		worksLike,
		worksComment,
		worksForward,
		worksCollect,
		worksTopLike,
		minVideo,
		totalFans,
	};
};

// 获取报告数据
const getReportData = async () => {
	pageLoading.value = true;
	try {
		const response = await api.getReportDetail(reportId.value);
		if (response && response.code === 2000) {
			reportData.value = response.data;
			console.log('获取抖音报告数据成功:', reportData.value);

			// 处理我的抖音账号数据
			myReportData.value = processMyDyReportData(reportData.value);

			// 处理下级抖音账号数据
			subReportData.value = processSubDyReportData(reportData.value);

			console.log('数据处理完成', {
				myReportData: myReportData.value,
				subReportData: subReportData.value,
			});
		} else {
			ElMessage.error('获取报告数据失败');
		}
	} catch (error) {
		console.error('获取报告数据失败:', error);
		ElMessage.error('获取报告数据失败');
	} finally {
		pageLoading.value = false;
	}
};

// 导出PDF方法
const handleExportPDF = async () => {
	try {
		exporting.value = true;
		// 导出报告逻辑
		ElNotification.info({
			title: 'PDF正在导出，请耐心等待',
			duration: 1500,
		});
		// 等待下一个渲染周期
		await nextTick();

		// 获取我的和下级报告的模块ID列表
		const myModuleIds = myDyOperateRef.value?.moduleIds || [];
		const subModuleIds = operateDyReportRef.value?.moduleIds || [];

		// 合并所有模块ID并转换为ModuleConfig格式
		const modules = [...myModuleIds, ...subModuleIds].map((id) => ({
			moduleId: id,
			pageBreak: true, // 每个模块后强制分页
		}));

		if (modules.length === 0) {
			ElMessage.warning('没有找到可导出的内容');
			return;
		}

		// 验证所有模块是否存在
		const missingModules = modules.filter((module) => !document.getElementById(module.moduleId));
		if (missingModules.length > 0) {
			console.warn('以下模块未找到:', missingModules);
		}

		// 导出PDF
		const reportTitle = reportData.value.title || '抖音月报';
		await exportToPDF(modules, `${reportTitle}.pdf`, {
			orientation: 'landscape',
			format: 'a4',
			quality: 0.3,
			scale: 2.435,
		});

		ElMessage.success('PDF导出完成');
	} catch (error) {
		console.error('导出过程出错:', error);
		ElMessage.error('导出过程出错，请重试');
	} finally {
		exporting.value = false;
	}
};

onMounted(() => {
	const { id } = route.query;
	if (id) {
		reportId.value = String(id);
		getReportData();
	} else {
		ElMessage.warning('缺少报告ID参数');
	}
});
</script>

<style scoped>
.app-container {
	background-color: #fff;
}

.export-buttons {
	position: fixed;
	top: 80px;
	right: 20px;
	z-index: 999;
}
</style>
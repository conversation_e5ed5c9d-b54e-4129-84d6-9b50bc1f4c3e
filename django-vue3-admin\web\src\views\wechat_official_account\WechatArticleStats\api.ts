import { request } from '/@/utils/service'

// 定义数据类型接口
export interface StatsDataType {
    today_article_count: number
    snapshot_article_count: number
}

// 定义查询参数接口
export interface QueryParams {
  page?: number
  limit?: number
  title?: string
  start_date?: string
  end_date?: string
}

// 定义API前缀
export const apiPrefix = '/api/wechat/official/article/stats/'

// 获取统计数据
export async function getList(query: any) {
    return await request({
        url: apiPrefix,
        method: 'get',
        params: query
    })
}

// 更新数据
export async function updateData(obj: Partial<StatsDataType>) {
  return await request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj
  })
}

// 删除数据
export async function deleteData(id: number) {
  return await request({
    url: apiPrefix + id + '/',
    method: 'delete'
  })
} 
<template>
	<el-dialog v-model="dialogVisible" title="导入结果" width="1200px" :close-on-click-modal="false" @close="handleClose" class="import-result-dialog">
		<div class="import-result-content">
			<!-- 总体统计 -->
			<div class="result-summary">
				<el-row :gutter="20">
					<el-col :span="8">
						<div class="summary-card success">
							<div class="summary-number">{{ importResult.overall_success || 0 }}</div>
							<div class="summary-label">成功导入</div>
						</div>
					</el-col>
					<el-col :span="8">
						<div class="summary-card failed">
							<div class="summary-number">{{ importResult.overall_failed || 0 }}</div>
							<div class="summary-label">导入失败</div>
						</div>
					</el-col>
					<el-col :span="8">
						<div class="summary-card total">
							<div class="summary-number">{{ importResult.total_platforms || 0 }}</div>
							<div class="summary-label">涉及平台</div>
						</div>
					</el-col>
				</el-row>
				<!-- 警告提示 -->
				<div class="warning-tip" v-if="hasWarnings">
					<el-alert type="warning" :closable="false">
						<template #title>
							<div class="warning-tip-title">
								<el-icon><warning /></el-icon>
								<span>部分账号导入成功但存在警告信息，请查看详情</span>
							</div>
						</template>
					</el-alert>
				</div>
			</div>

			<!-- 各平台详细结果 -->
			<div class="platform-results" v-if="importResult.platforms">
				<el-collapse v-model="activeCollapseNames">
					<el-collapse-item v-for="(platformData, platformName) in importResult.platforms" :key="platformName" :name="platformName">
						<template #title>
							<div class="platform-title">
								<span class="platform-name">{{ platformName }}账号导入详情</span>
								<div class="platform-stats">
									<el-tag type="success" size="small">成功: {{ platformData.success_count }}</el-tag>
									<el-tag type="danger" size="small" style="margin-left: 8px">失败: {{ platformData.failed_count }}</el-tag>
									<el-tag type="warning" size="small" style="margin-left: 8px" v-if="getWarningCount(platformName) > 0">警告: {{ getWarningCount(platformName) }}</el-tag>
								</div>
							</div>
						</template>

						<!-- 失败账号详情 -->
						<div v-if="platformData.failed_accounts && platformData.failed_accounts.length > 0" class="failed-accounts">
							<div class="failed-accounts-header">
								<h4>失败账号详情</h4>
							</div>
							<el-table :data="platformData.failed_accounts" stripe border size="small" class="failed-accounts-table" fit>
								<el-table-column prop="nick_name" label="账号昵称" width="350" v-if="platformName === '微信'" />
								<el-table-column prop="nickname" label="账号昵称" width="350" v-if="platformName !== '微信'" />
								<el-table-column prop="nick_name" label="账号昵称" width="200" v-if="platformName === '微信'" />
								<el-table-column prop="nickname" label="账号昵称" width="200" v-if="platformName !== '微信'" />
								<el-table-column :label="getAccountIdLabel(platformName)" width="200">
									<template #default="scope">
										{{ getAccountIdValue(scope.row, platformName) }}
									</template>
								</el-table-column>
								<el-table-column prop="error" label="失败原因" show-overflow-tooltip />
							</el-table>
						</div>

						<!-- 没有失败账号但有警告的情况 -->
						<div v-else-if="getSuccessWithWarningsAccounts(platformName).length > 0" class="warning-accounts">
							<div class="warning-accounts-header">
								<h4>成功导入但有警告的账号</h4>
							</div>
							<el-table :data="getSuccessWithWarningsAccounts(platformName)" stripe border size="small" class="warning-accounts-table" fit>
								<el-table-column prop="nick_name" label="账号昵称" width="200" v-if="platformName === '微信'" />
								<el-table-column prop="nickname" label="账号昵称" width="200" v-if="platformName !== '微信'" />
								<el-table-column :label="getAccountIdLabel(platformName)" width="200">
									<template #default="scope">
										{{ getAccountIdValue(scope.row, platformName) }}
									</template>
								</el-table-column>
								<el-table-column label="警告信息" show-overflow-tooltip>
									<template #default="scope">
										<div v-for="(warning, index) in scope.row.warnings" :key="index" class="warning-item">
											<el-tag type="warning" size="small">{{ warning }}</el-tag>
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<!-- 没有失败账号和警告的情况 -->
						<div v-else class="no-failed-accounts">
							<el-empty description="该平台所有账号都导入成功且无警告" :image-size="60" />
						</div>
					</el-collapse-item>
				</el-collapse>
			</div>
		</div>
	</el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import { Warning } from '@element-plus/icons-vue';

// 定义导入结果数据的类型接口
interface FailedAccount {
	row?: number;
	nickname: string;
	unique_id?: string;
	wechat_id?: string;
	douyin_id?: string;
	weibo_id?: string;
	error: string;
	platform: string;
}

interface SuccessAccount {
	row?: number;
	nickname?: string;
	nick_name?: string;
	unique_id?: string;
	wechat_id?: string;
	douyin_id?: string;
	weibo_id?: string;
	warnings?: string[];
}

interface PlatformData {
	total_rows: number;
	success_count: number;
	failed_count: number;
	failed_accounts: FailedAccount[];
	success_accounts?: SuccessAccount[];
}

interface ImportResult {
	platforms: Record<string, PlatformData>;
	overall_success: number;
	overall_failed: number;
	total_platforms: number;
}

export default defineComponent({
	name: 'ImportResultDialog',
	components: {
		Warning
	},
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		importResult: {
			type: Object as () => ImportResult,
			default: () => ({}),
		},
	},
	emits: ['update:visible', 'close'],
	setup(props, { emit }) {
		// 控制对话框显示状态
		const dialogVisible = computed({
			get: () => props.visible,
			set: (value: boolean) => {
				emit('update:visible', value);
			},
		});

		// 当前展开的折叠面板
		const activeCollapseNames = ref<string[]>([]);

		// 监听导入结果变化，自动展开有失败账号或警告的平台
		watch(
			() => props.importResult,
			(newResult) => {
				if (newResult && newResult.platforms) {
					activeCollapseNames.value = Object.keys(newResult.platforms).filter((platformName) => 
						newResult.platforms[platformName].failed_count > 0 || getWarningCount(platformName) > 0
					);
				}
			},
			{ immediate: true, deep: true }
		);
		
		// 判断是否有警告信息
		const hasWarnings = computed(() => {
			if (!props.importResult || !props.importResult.platforms) return false;
			
			return Object.keys(props.importResult.platforms).some(platformName => {
				return getWarningCount(platformName) > 0;
			});
		});
		
		// 获取平台的警告数量
		const getWarningCount = (platformName: string) => {
			if (!props.importResult || !props.importResult.platforms) return 0;
			const platformData = props.importResult.platforms[platformName];
			if (!platformData) return 0;
			
			// 统计成功账号中有警告的数量
			return getSuccessWithWarningsAccounts(platformName).length;
		};
		
		// 获取成功导入但有警告的账号
		const getSuccessWithWarningsAccounts = (platformName: string) => {
			if (!props.importResult || !props.importResult.platforms) return [];
			const platformData = props.importResult.platforms[platformName];
			if (!platformData || !platformData.success_accounts) return [];
			
			// 过滤出有警告的成功账号
			return platformData.success_accounts.filter(account => 
				account.warnings && account.warnings.length > 0
			);
		};

		// 处理关闭事件
		const handleClose = () => {
			emit('close');
			emit('update:visible', false);
		};

		// 根据平台获取账号ID字段的标签
		const getAccountIdLabel = (platformName: string) => {
			const labelMap: Record<string, string> = {
				微信: '微信号',
				抖音: '抖音号',
				微博: '微博号',
			};
			return labelMap[platformName] || '账号ID';
		};

		// 根据平台获取账号ID字段的值
		const getAccountIdValue = (row: FailedAccount, platformName: string) => {
			// 根据平台类型返回对应的字段值
			switch (platformName) {
				case '微信':
					return (row as any).wechat_id || row.unique_id || '';
				case '抖音':
					return (row as any).douyin_id || row.unique_id || '';
				case '微博':
					return (row as any).weibo_id || row.unique_id || '';
				default:
					return row.unique_id || '';
			}
		};

		return {
			dialogVisible,
			activeCollapseNames,
			handleClose,
			getAccountIdLabel,
			getAccountIdValue,
			hasWarnings,
			getWarningCount,
			getSuccessWithWarningsAccounts
		};
	},
});
</script>

<style scoped>
/* 弹窗样式 */
:deep(.import-result-dialog .el-dialog) {
	max-height: 85vh;
	overflow: hidden;
	margin-top: 5vh !important;
	margin-bottom: 5vh !important;
}

:deep(.import-result-dialog .el-dialog__body) {
	padding: 20px;
	overflow: hidden;
}

:deep(.import-result-dialog .el-overlay) {
	overflow: hidden;
}

/* 导入结果展示样式 */
.import-result-content {
	max-height: calc(85vh - 160px);
	overflow-y: auto;
	overflow-x: hidden;
}

.result-summary {
	margin-bottom: 20px;
}

.summary-card {
	text-align: center;
	padding: 20px;
	border-radius: 8px;
	color: white;
}

.summary-card.success {
	background: linear-gradient(135deg, #67c23a, #85ce61);
}

.summary-card.failed {
	background: linear-gradient(135deg, #f56c6c, #f78989);
}

.summary-card.total {
	background: linear-gradient(135deg, #409eff, #66b1ff);
}

.summary-number {
	font-size: 28px;
	font-weight: bold;
	margin-bottom: 5px;
}

.summary-label {
	font-size: 14px;
	opacity: 0.9;
}

.platform-results {
	margin-top: 20px;
}

.platform-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	padding-right: 20px;
}

.platform-name {
	font-size: 16px;
	font-weight: bold;
	color: #303133;
}

.platform-stats {
	display: flex;
	align-items: center;
}

.failed-accounts {
	padding: 10px 0;
	overflow-x: hidden;
}

.failed-accounts-header {
	margin-bottom: 10px;
}

.failed-accounts-header h4 {
	margin: 0;
	color: #606266;
	font-size: 14px;
}

.no-failed-accounts {
	text-align: center;
	padding: 20px;
}

/* 失败账号表格样式 */
.failed-accounts-table {
	font-size: 16px;
	color: #000000;
	width: 100%;
}

.failed-accounts-table :deep(.el-table__cell) {
	font-size: 16px;
	color: #000000;
}

.failed-accounts-table :deep(.el-table__header-wrapper th) {
	font-size: 16px;
	color: #000000;
}

.failed-accounts-table :deep(.el-table__body-wrapper) {
	overflow-x: hidden;
}

.failed-accounts-table :deep(.el-table) {
	overflow: hidden;
}

/* 折叠面板样式优化 */
:deep(.el-collapse-item__header) {
	background-color: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	margin-bottom: 8px;
	padding: 0 15px;
}

:deep(.el-collapse-item__wrap) {
	border: none;
	background-color: #fff;
}

:deep(.el-collapse-item__content) {
	padding: 15px;
	border: 1px solid #e9ecef;
	border-top: none;
	border-radius: 0 0 6px 6px;
	margin-top: -8px;
}

/* 警告提示样式 */
.warning-tip {
	margin-top: 15px;
}

.warning-tip-title {
	display: flex;
	align-items: center;
	gap: 8px;
}

/* 警告账号表格样式 */
.warning-accounts {
	padding: 10px 0;
	overflow-x: hidden;
}

.warning-accounts-header {
	margin-bottom: 10px;
}

.warning-accounts-header h4 {
	margin: 0;
	color: #e6a23c;
	font-size: 14px;
}

.warning-accounts-table {
	font-size: 14px;
	width: 100%;
}

.warning-item {
	margin-bottom: 4px;
}

.warning-item:last-child {
	margin-bottom: 0;
}
</style> 
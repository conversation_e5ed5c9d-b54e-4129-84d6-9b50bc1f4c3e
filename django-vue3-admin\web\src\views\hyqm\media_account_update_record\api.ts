import { request } from '/@/utils/service';

// 定义数据类型接口
export interface DataType {
	id: number;
	media_account_id: number;
	inactive_days: number;
	last_update_time: string;
	platform_type: string;
	account_name: string;
	create_time: string;
	update_time: string;
}

// 定义查询参数接口
export interface QueryParams {
	page?: number;
	limit?: number;
	media_account_id?: number;
	inactive_days_gte?: number;
	inactive_days_lte?: number;
	last_update_time_start?: string;
	last_update_time_end?: string;
	ordering?: string;
}

// 定义API前缀
export const apiPrefix = '/api/hyqm/media_account_update_records/';

// 获取列表数据
export async function getList(query: QueryParams) {
	return await request({
		url: apiPrefix,
		method: 'get',
		params: { 
			...query, page: query.page || 1, 
			limit: query.limit || 10,
			ordering: query.ordering || '-inactive_days',
		},

	});
}

// 创建数据
export async function createData(obj: Partial<DataType>) {
	return await request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新数据
export async function updateData(obj: Partial<DataType>) {
	return await request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 删除数据
export async function deleteData(id: number) {
	return await request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}

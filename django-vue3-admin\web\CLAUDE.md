# Django-Vue3-Admin 前端项目文档

## 🎯 核心架构信息

### 关键技术约定
- **Vue组件语法**: 支持两种语法
  - `<script setup>` + `useFs` (推荐，如tenant模块)
  - `defineComponent` + `useFs` (也支持，如website模块)
- **自动加载数据**: 必须在 `onMounted` 中调用 `crudExpose.doRefresh()` 实现自动加载
- **模块结构**: 功能目录PascalCase，API文件在功能目录内
- **权限格式**: `模块:资源:操作` (如: `website:site:create`)
- **导入方式**: 同目录文件使用相对路径 `import * as api from './api'`
- **类型安全**: request函数使用泛型，FastCrud dict配置使用数组格式

### 技术栈核心
- **框架**: Vue 3.4.38 + Composition API + TypeScript 4.9.4
- **UI组件**: Element Plus 2.8.0  
- **CRUD框架**: Fast-CRUD 1.21.2
- **状态管理**: Pinia 2.0.28
- **构建工具**: Vite 5.4.1

## 📚 分层文档体系

基于**根与枝叶信息架构**的专业化文档管理：

### 根信息（本文档）
- 最重要的核心约定和架构决策
- 文档索引和快速导航
- 持续更新，保持当前最有效状态

### 枝叶信息（专业文档群）
深入具体领域的详细指导文档

## 🗂 专业文档索引

### 📋 基础文档
- [[01-技术栈概览]] - Vue3+TS+ElementPlus技术栈详解
- [[02-项目架构分析]] - 目录结构和架构设计  
- [[03-开发流程指南]] - 标准化开发流程
- [[10-环境配置与维护]] - ESLint/TypeScript/工具链配置和升级指南

### 🔧 进阶指南
- [[04-权限管理机制]] - RBAC权限体系详解
- [[05-组件开发指南]] - 组件开发规范和最佳实践
- [[06-最佳实践与规范]] - 代码规范和性能优化

### 🎯 实战教程
- [[07-站点管理功能实战]] - 完整功能迁移实例
- [[08-常见问题FAQ]] - 开发中常见问题解决方案（基于真实问题）
- [[09-模块目录结构规范]] - 标准化模块组织规范
- [[自定义页面开发流程指南]] - 完整的自定义页面开发流程，包含菜单配置

## ⚠️ 关键开发约定

### Vue组件开发规范
**必须使用与现有系统一致的API模式:**

```typescript
// ✅ 正确 - 使用 defineComponent
<script lang="ts">
import { defineComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
  name: 'ComponentName',
  setup() {
    const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
    return { crudBinding, crudRef, crudExpose };
  },
});
</script>

// ❌ 错误 - 不要使用 script setup (会导致404)
<script lang="ts" setup>
import { useCrud } from '@fast-crud/fast-crud';
// 这种写法在当前系统中无法正常工作
</script>
```

### 模块结构规范
```
views/[module]/
└── [Feature]/              # PascalCase功能目录
    ├── api.ts             # API接口（在功能目录内）
    ├── crud.tsx           # Fast-CRUD配置
    ├── index.vue          # 主页面组件
    └── components/        # 功能专用组件（可选）
```

## 🤖 Sub-Agents设计思路

### 文档驱动的专业化Agent
每个专业文档对应一个潜在的Sub-Agent：

1. **frontend-developer-agent**: 基于组件开发指南，专门处理Vue组件开发
2. **module-structure-agent**: 基于目录结构规范，专门重构模块结构  
3. **permission-manager-agent**: 基于权限管理机制，专门处理RBAC配置
4. **debug-helper-agent**: 基于常见问题FAQ，专门解决开发问题

### Agent设计原则
- **专业化**: 每个Agent专注一个领域，具备深度专业知识
- **文档驱动**: Agent的知识来源于对应的专业文档
- **可迭代**: 随着文档完善，Agent能力同步提升
- **实践导向**: 从实际问题中学习，向实际开发提供帮助

## 💡 知识演进循环

```
开发实践 → 问题积累 → 文档更新 → Agent训练 → 开发辅助 → 更好实践
```

### 文档维护策略
1. **根信息更新**: 发现新的核心规则时立即更新本文档
2. **枝叶信息演进**: 具体实践经验写入对应专业文档  
3. **真实问题驱动**: 基于实际遇到的问题完善FAQ文档
4. **定期优化**: 提炼共性规律，删除过时内容

## 🎯 开发指导原则

### 新功能开发流程
1. 查阅 [[03-开发流程指南]] 了解标准流程
2. 参考 [[09-模块目录结构规范]] 创建标准结构
3. 遵循 [[05-组件开发指南]] 进行组件开发
4. 按照 [[04-权限管理机制]] 设计权限控制

### 问题解决流程  
1. 优先查阅 [[08-常见问题FAQ]] 寻找相似案例
2. 参考对应专业文档深入了解
3. 解决问题后更新FAQ文档
4. 提炼通用规律更新根信息

### 质量保证
- **开发前**: 查阅相关文档了解最佳实践
- **开发中**: 遵循文档中的检查清单  
- **完成后**: 根据经验更新对应文档

### 🔧 开发环境配置和调试经验

#### 跳过登录认证配置
开发环境可通过配置跳过登录认证：
- **环境变量**: `.env.development` 中设置 `VITE_SKIP_LOGIN = true`
- **自动效果**: 本地开发时无需登录，直接模拟管理员用户
- **IP认证**: 后端已配置本地IP(127.0.0.1)自动通过接口认证
- **用途**: 便于开发调试，避免每次刷新都需要重新登录

#### 浏览器自动化测试要点
- **正确URL格式**: Website模块使用复数形式，如 `http://localhost:8080/#/website/sites`（注意是sites不是site）
- **分辨率要求**: 截图时使用高分辨率（推荐1400x900），低分辨率会导致页面显示不完整
- **Hash路由**: 系统使用hash路由，注意URL中的 `#/` 前缀
- **等待加载**: 页面需要时间加载数据，截图前确保页面完全渲染

#### Website模块前端路由结构
```
/#/website/sites     - 站点列表
/#/website/columns   - 栏目列表  
/#/website/contents  - 内容列表
/#/website/rules     - 规则列表
```

## 📝 代码提交规范

### Git提交信息结构化格式

使用以下结构化格式提交代码，确保团队协作和项目维护的清晰性：

```
[功能描述，说明解决的问题]

## 核心功能
- [主要功能点]
- [解决的关键问题]
- [用户价值体现]

## 技术实现

### [实现模块]
- [关键实现细节和涉及的方法/类]
- [处理的业务逻辑]
- [技术架构变更]

### [其他模块]
- [相关实现细节]
- [涉及的组件/服务]
```

### 提交规范要求

1. **严格禁止标识**: 不包含任何生成标识或工具标记
2. **文件范围控制**: 只提交本次更改的核心文件，排除文档和临时文件
3. **信息结构化**: 使用上述模板确保信息完整性
4. **技术细节**: 必须包含关键类名/方法名，让他人快速理解变更内容

### 模块级README规范

**核心理念**: 每个功能模块必须创建README.md文件，采用与Git提交相同的结构化格式，让AI和团队成员能够快速理解模块的业务逻辑和技术实现。

#### README.md标准格式

```markdown
# 模块名称

## 核心功能
- [主要业务功能点]
- [解决的核心问题]
- [用户价值体现]
- [关键特性说明]

## 技术实现

### API接口层 (api.ts)
- [关键API函数名]: [功能说明]
- [数据结构定义]: [类型说明]
- [接口路径规范]: [URL规则]

### 组件架构 (components/)
- [组件名称]: [功能职责和关键实现]
- [数据流处理]: [状态管理逻辑]
- [交互逻辑]: [用户操作处理]

### 主页面控制 (index.vue)
- [核心业务逻辑]: [关键方法名和处理流程]
- [数据管理策略]: [请求优化和状态控制]
- [用户体验优化]: [响应式和交互设计]
```

#### 文件位置规范

```
src/views/[module]/[feature]/
└── README.md              # 模块说明文档
```

#### 必要信息要求

1. **业务清晰**: 明确说明模块解决什么问题，为用户提供什么价值
2. **技术详细**: 包含关键类名/方法名，让他人快速理解实现方式
3. **结构统一**: 严格按照"核心功能 + 技术实现"的两段式结构
4. **信息完整**: 涵盖API、组件、业务逻辑的完整技术栈

#### 维护要求

- **同步更新**: 功能变更时必须同步更新README
- **版本记录**: 重要功能迭代需要记录版本变更
- **例子参考**: 参考AI校对效果分析模块的README.md作为标准模板

**价值体现**: 通过这种标准化文档，AI助手能够快速理解模块业务，开发者能够快速上手维护，团队协作效率大幅提升。

## 🚀 项目愿景

通过这个文档体系，我们希望实现：
- **快速接手**: 新人能通过文档快速理解和上手项目
- **知识传承**: 开发经验能有效积累和传递
- **智能辅助**: 为未来的Sub-Agent开发奠定基础
- **持续改进**: 形成文档驱动的项目改进循环

---

**文档更新记录**:
- 2025-08-07: 建立分层文档体系，完善前端开发规范
- 2025-08-07: 增加Sub-Agents设计思路和知识演进循环
- 2025-08-07: 增加类型安全约定和环境配置文档索引
- 2025-08-07: 修复Website模块HTML渲染问题和自动加载数据问题
- 2025-08-07: 增加开发环境跳过登录配置和浏览器自动化测试经验
- 2025-08-13: 新增自定义页面开发流程指南，完善菜单配置流程文档
- 2025-08-13: 补充Git提交规范和模块级README规范，建立结构化代码提交标准
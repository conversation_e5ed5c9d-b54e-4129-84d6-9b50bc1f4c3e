<template>
  <fs-page class="PageWebsiteColumnManage">
    <!-- 站点信息卡片 -->
    <el-card class="site-info-card" v-if="currentSite">
      <template #header>
        <div class="card-header">
          <i class="fas fa-server"></i>
          <span>站点信息</span>
        </div>
      </template>
      <div class="site-info-content">
        <div class="site-basic-info">
          <h3>{{ currentSite.name }}</h3>
          <p class="site-description">{{ currentSite.description || '无描述' }}</p>
          <p><strong>域名：</strong> {{ currentSite.domain }}</p>
        </div>
        <div class="site-stats">
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.total_columns || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-list"></i>
              内容栏目总数
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.total_content_count || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-file-alt"></i>
              内容总数
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ statistics?.has_rule_columns || 0 }}</div>
            <div class="stat-label">
              <i class="fas fa-cogs"></i>
              已配置规则
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 批量操作面板 -->
    <el-card class="batch-operations-card" v-if="currentSite">
      <template #header>
        <div class="card-header">
          <i class="fas fa-robot"></i>
          <span>批量栏目操作</span>
        </div>
      </template>
      <div class="batch-operations-content">
        <div class="operation-row">
          <div class="operation-info">
            <i class="fas fa-info-circle text-info"></i>
            <span>为所有未匹配栏目规则的内容栏目页自动生成AI栏目规则</span>
          </div>
          <div class="operation-actions">
            <el-button type="success" @click="batchGenerateRules">
              <i class="fas fa-robot"></i>
              批量生成栏目规则
            </el-button>
          </div>
        </div>
        <div class="operation-row">
          <div class="operation-info">
            <i class="fas fa-info-circle text-warning"></i>
            <span>对已有规则的栏目进行批量内容抓取或更新操作</span>
          </div>
          <div class="operation-actions">
            <el-button-group>
              <el-button type="warning" @click="batchCrawlContent">
                <i class="fas fa-spider"></i>
                批量抓取
              </el-button>
              <el-button type="info" @click="batchUpdateContent">
                <i class="fas fa-sync"></i>
                批量更新
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 栏目列表 -->
    <fs-crud ref="crudRef" v-bind="crudBinding">
    </fs-crud>
  </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import createCrudOptions from './crud';
import * as api from './api';
import * as siteApi from '../site/api';

export default defineComponent({
  name: 'WebsiteColumnManage',
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // 获取站点ID参数
    const siteId = computed(() => {
      return parseInt(route.params.siteId as string) || parseInt(route.query.site_id as string) || null;
    });
    
    // 当前站点信息
    const currentSite = ref<any>(null);
    const statistics = ref<any>(null);
    
    // 初始化CRUD
    const { crudBinding, crudRef, crudExpose } = useFs({ 
      createCrudOptions,
      context: {
        siteId: siteId.value
      }
    });
    
    // 加载站点信息
    const loadSiteInfo = async () => {
      if (!siteId.value) return;
      
      try {
        const siteRes = await siteApi.getSiteDetail(siteId.value);
        currentSite.value = siteRes.data;
      } catch (error) {
        console.error('加载站点信息失败:', error);
        ElMessage.error('加载站点信息失败');
      }
    };
    
    // 加载统计信息
    const loadStatistics = async () => {
      if (!siteId.value) return;
      
      try {
        const statsRes = await api.getColumnStatistics({ site_id: siteId.value });
        statistics.value = statsRes.data;
      } catch (error) {
        console.error('加载统计信息失败:', error);
      }
    };
    
    // 批量生成规则
    const batchGenerateRules = async () => {
      if (!siteId.value) return;
      
      try {
        await ElMessageBox.confirm(
          '确定要为所有未匹配规则的栏目生成AI规则吗？这可能需要一些时间。',
          '批量生成规则'
        );
        
        // 获取未匹配规则的栏目
        const listRes = await api.getColumnList({ 
          site_id: siteId.value, 
          has_rule: false,
          page_size: 100 
        });
        
        const unmatchedColumns = listRes.data.results;
        
        if (unmatchedColumns.length === 0) {
          ElMessage.info('所有栏目都已匹配规则');
          return;
        }
        
        ElMessage.info(`开始批量生成 ${unmatchedColumns.length} 个栏目规则...`);
        
        let successCount = 0;
        let failedCount = 0;
        
        for (const column of unmatchedColumns) {
          try {
            await api.generateColumnRule(column.id);
            successCount++;
            await new Promise(resolve => setTimeout(resolve, 2000)); // 延迟2秒避免并发过多
          } catch (error) {
            failedCount++;
            console.error(`栏目 ${column.name} 规则生成失败:`, error);
          }
        }
        
        ElMessage.success(`批量规则生成完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量生成规则失败:', error);
          ElMessage.error('批量操作失败');
        }
      }
    };
    
    // 批量抓取内容
    const batchCrawlContent = async () => {
      if (!siteId.value) return;
      
      try {
        await ElMessageBox.confirm(
          '确定要对已有规则的栏目进行批量抓取吗？这可能需要较长时间。',
          '批量抓取内容'
        );
        
        // 获取有规则且未抓取的栏目
        const listRes = await api.getColumnList({ 
          site_id: siteId.value, 
          has_rule: true,
          crawl_status: 'never_crawled',
          page_size: 50 
        });
        
        const crawlableColumns = listRes.data.results;
        
        if (crawlableColumns.length === 0) {
          ElMessage.info('没有可抓取的栏目');
          return;
        }
        
        ElMessage.info(`开始批量抓取 ${crawlableColumns.length} 个栏目内容...`);
        
        let successCount = 0;
        let failedCount = 0;
        
        for (const column of crawlableColumns) {
          try {
            await api.crawlColumnContent(column.id, 'deep');
            successCount++;
            await new Promise(resolve => setTimeout(resolve, 3000)); // 延迟3秒避免并发过多
          } catch (error) {
            failedCount++;
            console.error(`栏目 ${column.name} 抓取失败:`, error);
          }
        }
        
        ElMessage.success(`批量内容抓取完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量抓取失败:', error);
          ElMessage.error('批量操作失败');
        }
      }
    };
    
    // 批量更新内容
    const batchUpdateContent = async () => {
      if (!siteId.value) return;
      
      try {
        await ElMessageBox.confirm(
          '确定要对已有规则的栏目进行批量更新吗？将从第一页开始增量抓取。',
          '批量更新内容'
        );
        
        // 获取可更新的栏目
        const listRes = await api.getColumnList({ 
          site_id: siteId.value, 
          has_rule: true,
          page_size: 50 
        });
        
        const updatableColumns = listRes.data.results.filter(column => 
          column.progress_info.should_use_update_crawl
        );
        
        if (updatableColumns.length === 0) {
          ElMessage.info('没有可更新的栏目');
          return;
        }
        
        ElMessage.info(`开始批量更新 ${updatableColumns.length} 个栏目内容...`);
        
        let successCount = 0;
        let failedCount = 0;
        
        for (const column of updatableColumns) {
          try {
            await api.crawlColumnContent(column.id, 'update');
            successCount++;
            await new Promise(resolve => setTimeout(resolve, 3000)); // 延迟3秒避免并发过多
          } catch (error) {
            failedCount++;
            console.error(`栏目 ${column.name} 更新失败:`, error);
          }
        }
        
        ElMessage.success(`批量内容更新完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);
        
        // 刷新数据
        crudExpose?.doRefresh?.();
        loadStatistics();
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量更新失败:', error);
          ElMessage.error('批量操作失败');
        }
      }
    };
    
    // 设置默认查询参数
    if (siteId.value) {
      crudBinding.search = {
        ...crudBinding.search,
        initialForm: {
          site_id: siteId.value
        }
      };
    }
    
    // 生命周期
    onMounted(() => {
      console.log('WebsiteColumnManage组件已挂载, siteId:', siteId.value);
      
      if (!siteId.value) {
        ElMessage.warning('缺少站点ID参数，返回站点列表');
        router.push('/website/site');
        return;
      }
      
      // 加载站点信息和统计数据
      loadSiteInfo();
      loadStatistics();
    });

    return {
      siteId,
      currentSite,
      statistics,
      crudBinding,
      crudRef,
      crudExpose,
      batchGenerateRules,
      batchCrawlContent,
      batchUpdateContent,
    };
  },
});
</script>

<style lang="scss" scoped>
.PageWebsiteColumnManage {
  padding: 20px;
}

.site-info-card, .batch-operations-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
    color: #409eff;
  }
}

.site-info-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.site-basic-info {
  flex: 1;
  
  h3 {
    margin: 0 0 10px 0;
    color: #303133;
  }
  
  .site-description {
    color: #606266;
    margin: 0 0 10px 0;
    line-height: 1.5;
  }
  
  p {
    margin: 5px 0;
    
    strong {
      color: #409eff;
    }
  }
}

.site-stats {
  display: flex;
  gap: 30px;
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #606266;
      
      i {
        margin-right: 4px;
        color: #409eff;
      }
    }
  }
}

.batch-operations-content {
  .operation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f0f2f6;
    }
    
    .operation-info {
      flex: 1;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 10px;
        font-size: 16px;
        
        &.text-info {
          color: #409eff;
        }
        
        &.text-warning {
          color: #e6a23c;
        }
      }
      
      span {
        color: #606266;
        line-height: 1.5;
      }
    }
    
    .operation-actions {
      margin-left: 20px;
    }
  }
}

// 表格自定义样式
:deep(.fs-crud) {
  .el-tag {
    font-size: 12px;
  }
  
  .text-primary {
    color: #409eff;
  }
  
  .text-muted {
    color: #909399;
  }
  
  .el-progress {
    width: 100px;
    
    .el-progress__text {
      font-size: 12px;
      min-width: 30px;
    }
  }
}
</style>
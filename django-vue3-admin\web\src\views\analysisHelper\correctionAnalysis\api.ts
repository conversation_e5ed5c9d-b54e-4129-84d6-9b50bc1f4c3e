import { request } from '/@/utils/service'

export interface CorrectionInfo {
    id: number
    title: string
    status: boolean
    create_time: string
    update_time: string
    content: string
}

export interface CorrectionListParams {
    page?: number
    limit?: number
    status?: boolean
    title?: string
    create_time?: string[]
}

export interface CorrectionListResponse {
    code: number;
    message: string;
    data: CorrectionInfo[];
    page: number;
    limit: number;
    total: number;
    is_next: boolean;
    is_previous: boolean;
}

export interface ReportDetailResponse {
    report_name: string;
    start_time: string;
    end_time: string;
    report_data: {
        account_stats: Array<{
            error_rate: number;
            total_errors: number;
            is_key_monitor: boolean;
            total_articles: number;
            error_rate_rank: number;
            account_name: string;
        }>;
        error_type_stats: Array<{
            name: string;
            value: number;
        }>;
        high_freq_error_stats: Array<{
            rank: number;
            error_type: string;
            wrong_word: string;
            error_count: number;
            correct_word: string;
        }>;
        time_series_stats: {
            row: Array<{
                name: string;
                counts: number[];
            }>;
            dates: string[];
        };
        top_error_article_stats: Array<{
            rank: number;
            article_id: string;
            error_count: number;
        }>;
        top_error_count_accounts: {
            period: string;
            accounts: Array<{
                error_rate: number;
                total_errors: number;
                total_articles: number;
                account_name: string;
                media_account_id:number;
                media_account_name: string;
                top_error_types:Array<{
                    count: number;
                    error_type: string;
                    percentage: number;
                }>
            }>;
        };
        top_error_rate_accounts: {
            period: string;
            accounts: Array<{
                error_rate: number;
                total_errors: number;
                total_articles: number;
                account_name: string;
            }>;
        };
    };
}

export const apiPrefix = '/api/regulate/reports/'

// 获取纠错分析列表
export async function getList(query: CorrectionListParams): Promise<CorrectionListResponse> {
    return request({
        url: apiPrefix,
        method: 'get',
        params: {
            ...query,
        }
    })
}

// 获取纠错分析详情
export async function getCorrectionDetail(id: number): Promise<ReportDetailResponse>  {
    return request({
        url: apiPrefix + id + '/report_data/',
        method: 'get'
    })
}

// 更新纠错分析
export async function updateCorrection(form: CorrectionInfo): Promise<CorrectionInfo> {
    return request({
        url: apiPrefix + form.id + '/',
        method: 'put',
        data: form
    })
} 

// 生成纠错分析 api/regulate/reports/generate_report/?task_id=999
export async function generateCorrectionReport(id: number) {
    return request({
        url: apiPrefix + 'generate_report/?task_id=' + id,
        method: 'get'
    })
}

// 导出纠错分析报表api/regulate/reports/24/export_doc/
export async function exportReport(id: number) {
    return request({
        url: apiPrefix + id + '/export_doc/',
        method: 'get',
        responseType: 'blob'
    })
}
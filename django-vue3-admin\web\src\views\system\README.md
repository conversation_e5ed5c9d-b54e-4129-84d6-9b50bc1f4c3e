# 系统管理模块

## 核心功能
- 用户管理：维护系统用户账号，支持用户信息编辑和权限分配
- 部门管理：管理组织架构，支持树形结构的部门层级展示
- 角色管理：定义用户角色和权限，支持菜单权限和按钮权限配置
- 菜单管理：维护系统菜单结构，支持动态菜单生成和权限控制
- 字典管理：维护系统数据字典，为下拉选项和枚举值提供数据支持
- 日志管理：记录用户操作日志和登录日志，支持日志查询和审计

## 技术实现

### API接口层 (api.ts)
- getUsers(): 获取用户列表数据，支持部门筛选和多条件搜索
- getDepts(): 获取部门树形数据，支持层级展示和节点操作
- getRoles(): 获取角色列表，支持权限配置和用户关联
- getMenus(): 获取菜单树形数据，支持动态权限控制
- getDictionaries(): 获取数据字典，支持分类管理和值维护

### 组件架构 (components/)
- DeptTreeCom: 部门树组件，onTreeNodeClick()处理节点选择和用户筛选
- RoleDrawer: 角色配置抽屉，RoleMenuTree和RoleMenuBtn处理权限分配
- MenuFormCom: 菜单表单组件，支持菜单信息编辑和权限配置
- WechatBindDialog: 微信绑定弹窗，处理用户微信账号关联
- UserSearch: 用户搜索组件，支持多维度用户查询

### 主页面控制 (index.vue)
- 布局管理: el-row和el-col实现响应式布局，分离树形导航和内容区域
- 数据联动: filterText实现部门搜索，filterNode()处理树节点过滤
- 状态管理: 统一管理加载状态、选中状态和表单验证状态
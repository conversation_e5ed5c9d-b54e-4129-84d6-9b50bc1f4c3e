# 网站管理模块

## 核心功能
- 站点管理：维护抓取目标网站的基础信息和配置参数
- 栏目管理：管理网站栏目结构，定义内容分类和抓取规则
- 内容管理：展示和管理从网站抓取的内容数据，支持内容审核
- 规则管理：配置网站内容抓取规则，定义抓取范围和过滤条件
- 状态监控：实时监控网站抓取状态和数据更新情况

## 技术实现

### API接口层 (api.ts)
- getSites(): 获取站点列表数据，支持分页和多条件搜索
- createSite(): 创建新站点配置，验证URL有效性和抓取参数
- updateSite(): 更新站点信息，支持状态切换和配置修改
- deleteSite(): 删除站点及其关联的栏目和内容数据
- getColumns(): 获取栏目数据，按站点层级展示栏目结构

### 组件架构 (Fast-CRUD)
- crud.tsx: Fast-CRUD配置文件，定义表格列、表单字段和操作按钮
- crudBinding: 绑定CRUD组件的配置选项和事件处理
- crudExpose: 暴露CRUD操作方法，doRefresh()实现数据刷新
- createCrudOptions: 构建CRUD选项，包含增删改查的完整配置

### 主页面控制 (index.vue)  
- 数据管理：useFs()初始化Fast-CRUD，自动处理数据流和状态
- 生命周期：onMounted()中调用doRefresh()实现页面数据自动加载
- 组件集成：fs-page和fs-crud提供完整的页面布局和CRUD功能
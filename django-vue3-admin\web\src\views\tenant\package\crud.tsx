import { CreateCrudOptionsProps, CreateCrudOptionsRet, AddReq, DelReq, EditReq, dict, compute } from '@fast-crud/fast-crud';
import * as api from './api';
import { dictionary } from '/@/utils/dictionary';
import { successMessage } from '../../../utils/message';
import { auth } from '/@/utils/authFunction';
import { nextTick, computed } from 'vue';

/**
 * 租户套餐CRUD配置
 * @param crudExpose：index传递过来的示例
 * @param context：index传递过来的自定义参数
 * @returns
 */
export const createCrudOptions = function ({ crudExpose, context }: CreateCrudOptionsProps): CreateCrudOptionsRet {
    const pageRequest = async (query: any) => {
        return await api.getList(query);
    };
    const editRequest = async ({ form, row }: EditReq) => {
        form.id = row.id;
        return await api.updateObj(form);
    };
    const delRequest = async ({ row }: DelReq) => {
        return await api.delObj(row.id);
    };
    const addRequest = async ({ form }: AddReq) => {
        return await api.addObj(form);
    };

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            pagination: {
                show: true,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('tenant_package:Create'),
                    },
                },
            },
            toolbar: {
                show: false,
            },
            rowHandle: {
                //固定右侧
                width: 280,
                buttons: {
                    view: {
                        show: false,
                    },
                    edit: {
                        link: true,
                        show: auth('tenant_package:Update'),
                    },
                    remove: {
                        link: true,
                        show: auth('tenant_package:Delete'),
                    },

                },
            },
            form: {
                col: { span: 24 },
                labelWidth: '100px',
                wrapper: {
                    is: 'el-dialog',
                    width: '1200px',
                },
            },
            columns: {
                id: {
                    title: 'ID',
                    type: 'text',
                    column: { show: true },
                    search: { show: false },
                    form: { show: false },
                },
                name: {
                    title: '套餐名称',
                    type: 'text',
                    search: {
                        show: true,
                        autoSearchTrigger: false, //关闭自动筛选
                    },
                    column: {
                        minWidth: 120,
                    },
                    form: {
                        rules: [{ required: true, message: '套餐名称必填' }],
                        component: {
                            placeholder: '请输入套餐名称',
                        },
                    },
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    dict: dict({
                        data: dictionary('button_status_number'),
                    }),
                    search: { show: true },
                    form: {
                        value: '0' 
                    }
                },
                creator_name: {
                    title: '创建人',
                    search: { show: false },
                    type: 'text',
                    column: {
                        minWidth: 150,
                    },
                    form: { show: false },
                },
                create_datetime: {
                    title: '创建时间',
                    search: { show: false },
                    type: 'datetime',
                    column: {
                        minWidth: 150,
                        sortable: 'custom',
                    },
                    form: { show: false },
                },
                update_datetime: {
                    title: '更新时间',
                    search: { show: false },
                    type: 'datetime',
                    column: {
                        minWidth: 150,
                        sortable: 'custom',
                    },
                    form: { show: false },
                },
                menu_ids: {
                    title: '菜单权限配置',
                    type: 'component',
                    search: { show: false },
                    column: { show: false },
                    form: {
                        col: { span: 24 },
                        component: {
                            name: context.PackagePermissionConfig,
                            vModel: 'modelValue',
                            props: compute(({ form }) => {
                                return {
                                    packageId: form.id // 编辑时传入套餐ID
                                };
                            }),
                        },
                    },
                },
            },
        },
    };
}; 
<template>
	<div class="example-container">
		<h3>基础用法</h3>
		<CommonTabs v-model="currentTab" :items="tabItems" @change="handleTabChange" />
		<div class="tab-content">当前选中: {{ currentTab }}</div>

		<h3>带图标</h3>
		<CommonTabs v-model="currentTabWithIcon" :items="tabItemsWithIcon" @change="handleTabChange" />

		<h3>自定义样式</h3>
		<CommonTabs
			v-model="currentTabCustom"
			:items="tabItems"
			size="small"
			active-color="#67c23a"
			active-border-color="#67c23a"
			:min-width="100"
			padding="8px 15px"
			:font-size="12"
		/>

		<h3>禁用选项</h3>
		<CommonTabs v-model="currentTabDisabled" :items="tabItemsWithDisabled" />
	</div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue';
import { Search, Setting, Message } from '@element-plus/icons-vue';

// 动态导入CommonTabs组件
const CommonTabs = defineAsyncComponent(() => import('./index.vue'));

// 基础选项卡数据
const tabItems = [
	{ label: '选项一', value: 'tab1' },
	{ label: '选项二', value: 'tab2' },
	{ label: '选项三', value: 'tab3' },
];

// 带图标的选项卡数据
const tabItemsWithIcon = [
	{ label: '搜索', value: 'search', icon: Search },
	{ label: '设置', value: 'setting', icon: Setting },
	{ label: '消息', value: 'message', icon: Message },
	{ label: '自定义图标', value: 'custom', icon: 'ele-Menu' },
];

// 带禁用选项的选项卡数据
const tabItemsWithDisabled = [
	{ label: '选项一', value: 'tab1' },
	{ label: '选项二', value: 'tab2', disabled: true },
	{ label: '选项三', value: 'tab3' },
];

// 当前选中值
const currentTab = ref('tab1');
const currentTabWithIcon = ref('search');
const currentTabCustom = ref('tab1');
const currentTabDisabled = ref('tab1');

// 变更处理函数
const handleTabChange = (value: string | number) => {
	console.log('Tab变更:', value);
};
</script>

<style lang="scss" scoped>
.example-container {
	padding: 20px;

	h3 {
		margin: 30px 0 15px 0;
		font-size: 18px;
		color: #333;
	}

	.tab-content {
		margin-top: 20px;
		padding: 15px;
		background-color: #f5f7fa;
		border-radius: 4px;
		color: #666;
	}
}
</style> 
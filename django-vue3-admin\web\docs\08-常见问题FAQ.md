# 常见问题FAQ - 真实问题记录

## 🐛 实际遇到的问题及解决方案

### Q1: Vue组件404错误 - 页面无法加载
**问题现象**: 访问新创建的Vue页面时显示404错误，页面无法正常加载

**问题分析**: 
我们在开发站点管理功能时遇到此问题。通过对比正常工作的媒体账号页面发现：
- 工作页面使用：`defineComponent` + `useFs` + `import createCrudOptions from './crud'`
- 问题页面使用：`<script setup>` + `useCrud` + `import { createCrudOptions } from './crud'`

**根本原因**: Vue组件API不兼容
- 现有系统使用 Options API (`defineComponent`)
- 新组件使用 Composition API (`<script setup>`)

**解决方案**:
```typescript
// ❌ 错误的写法（导致404）
<script lang="ts" setup name="ComponentName">
import { useCrud } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';

const { crudBinding, crudRef, crudExpose } = useCrud({
  createCrudOptions,
});
</script>

// ✅ 正确的写法
<script lang="ts">
import { defineComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud'; // 注意：不使用解构导入

export default defineComponent({
  name: 'ComponentName',
  setup() {
    const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
    return {
      crudBinding,
      crudRef, 
      crudExpose,
    };
  },
});
</script>
```

### Q2: 菜单配置SQL未生效
**问题现象**: 菜单配置SQL已执行，但页面仍然404

**解决步骤**:
1. 确认SQL执行成功：
```bash
cd backend && echo "SELECT name, web_path, component FROM dvadmin_system_menu WHERE name LIKE '%站点%';" | python manage.py dbshell
```

2. 检查组件路径是否正确：
   - 数据库中component字段：`website/site/index`
   - 实际文件路径：`src/views/website/site/index.vue`

3. 重新加载页面让菜单配置生效

### Q3: API接口调用正常但数据不显示
**问题现象**: 页面显示"暂无数据"，但API返回200状态码

**分析过程**:
```javascript
// 在浏览器控制台测试API
fetch('/api/website/sites/')
  .then(response => response.json())
  .then(data => console.log(data));
```

**结果**: API正常工作，只是数据库中确实没有站点数据，这是正常情况。

## 🔧 调试技巧

### 对比分析法
当新功能不工作时，找一个相似的正常工作的功能进行对比：
1. 检查数据库菜单配置
2. 对比Vue组件结构
3. 检查API端点是否正常
4. 对比导入方式和语法

### 数据库配置检查
```sql
-- 检查菜单配置
SELECT name, web_path, component, component_name 
FROM dvladmin_system_menu 
WHERE web_path LIKE '%your_module%';

-- 检查权限配置
SELECT name, value, api 
FROM dvadmin_system_menu_button 
WHERE menu_id = your_menu_id;
```

### 浏览器调试
```javascript
// 检查Vue组件是否正确挂载
document.querySelector('#app').__vue__

// 测试API接口
fetch('/api/your-endpoint/')
  .then(res => res.json())
  .then(data => console.log(data))
```

## 📋 开发检查清单

### 新功能开发前
- [ ] 确认使用与现有系统一致的Vue API模式
- [ ] 检查现有相似功能的实现方式
- [ ] 准备好菜单配置SQL脚本

### 功能开发后
- [ ] 执行菜单配置SQL
- [ ] 测试页面是否正常加载
- [ ] 测试API接口是否正常工作
- [ ] 检查控制台是否有错误信息

---

**更新记录**: 
- 2025-08-07: 记录Vue组件404错误和解决方案（基于站点管理功能开发经验）
<template>
	<div>
		<!-- 添加导出内容包裹层 -->
		<div>
			<!-- 1.1.1 标题部分 -->
			<div id="wb-myoperate-7">
				<div class="top">
					<div class="top-title">{{ title.split('月')[0] }}月本级微博运营报告</div>
				</div>
			</div>
			<div v-if="!hasAccounts">
				<div style="width: 100%; margin: 10px auto; font-size: 26px; text-align: center">无绑定账号</div>
			</div>
			<div v-else>
				<!-- 1.1.2 账号传播力指数报告 -->

				<!-- 1.1.3 作品发布报告 -->

				<div v-for="(account, index) in worksIssue" :key="index" style="margin-bottom: 15px">
					<div :id="`wb-my-${index + 1}-1`">
						<div class="title-wrapper" v-if="index === 0">
							<!-- 账号统计信息 -->
							<div v-if="props.tenantName" class="account-stats-container">
								<div class="account-stats-content">
									<span class="tenant-name">{{ props.tenantName }}</span>
									<span class="stats-item"
										>本级微博账号数量为<span class="count-number">{{ props.accountCounts.myAccount }}</span></span
									>
									<span class="stats-item"
										>下级微博账号数量为<span class="count-number">{{ props.accountCounts.subAccount }}</span></span
									>
								</div>
							</div>
							<div class="title1">作品发布</div>
						</div>
						<!-- 作品发布数据表格 -->
						<div class="title-wrapper">
							<div class="title2">{{ account.accountName }}作品发布数据详情表</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="formatPublishTableData(account)"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="指标分类" align="center" prop="type" min-width="120px" />
								<el-table-column label="本月值" align="center" prop="currentValue" min-width="100px" />
								<el-table-column label="较上月" align="center" prop="compareChange" min-width="100px" />
								<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
							</el-table>
						</div>
					</div>
					<div :id="`wb-my-${index + 1}-2`">
						<!-- 内容类型分布饼图 -->
						<div class="flex">
							<div class="flex-row">
								<!-- 原创类型分布饼图 -->
								<div class="sub-title">{{ account.accountName }}原创类型分布</div>
								<div class="chart-container">
									<pie :data="account.origPie.data" :total="account.origPie.total" style="width: 100%; height: 350px" :label-font-size="18" />
								</div>
							</div>
							<div class="flex-row">
								<div class="sub-title">{{ account.accountName }}内容类型分布</div>
								<div class="chart-container">
									<pie :data="account.videPie.data" :total="account.videPie.total" style="width: 100%; height: 350px" :label-font-size="18" />
								</div>
							</div>
						</div>
						<!-- 发布时间区间分布图 -->
						<div class="sub-title">{{ account.accountName }}发布时间分布</div>
						<div class="chart-container">
							<line-chart
								:data="formatLineChartData(account.timeDistribution)"
								style="width: 100%; height: 300px"
								:axis-font-size="16"
								:legend-font-size="16"
								:axis-name-font-size="16"
							/>
						</div>
					</div>
				</div>
				<div id="wb-myoperate-1">
					<!-- 1.1.4 作品传播报告 -->
					<div class="title-wrapper">
						<div class="title1">账号传播分析</div>
					</div>

					<!-- 账号传播数据详情表格 -->
					<div v-if="bciData && bciData.length > 0">
						<div v-for="(account, index) in bciData" :key="account.accountName" :id="`wb-account-detail-${index + 1}`">
							<div class="title-wrapper">
								<div class="title2">{{ account.accountName }}传播数据详情表</div>
							</div>
							<div class="table-wrapper">
								<el-table
									:data="formatAccountTableData(account)"
									border
									:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
									:cell-style="{ height: '45px' }"
								>
									<el-table-column label="指标" align="center" prop="type" min-width="120px" />
									<el-table-column label="本月值" align="center" prop="currentValue" min-width="100px" />
									<el-table-column label="较上月" align="center" prop="compareChange" min-width="100px" />
									<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
									<el-table-column label="篇均" align="center" prop="avgValue" min-width="100px" />
									<el-table-column label="原创本月值" align="center" prop="origCurrentValue" min-width="120px" />
									<el-table-column label="原创较上月" align="center" prop="origCompareChange" min-width="120px" />
									<el-table-column label="原创环比增长率" align="center" prop="origCompareRate" min-width="140px" />
									<el-table-column label="原创篇均" align="center" prop="origAvgValue" min-width="100px" />
								</el-table>
							</div>
						</div>
					</div>
					<div v-if="fansPieData.length === 1">
						<!-- 1.1.5 账号粉丝报告 -->
						<div class="title-wrapper">
							<div class="title1">账号粉丝</div>
						</div>

						<!-- 粉丝数据表格 -->
						<div class="title-wrapper">
							<div class="title2">本级账号粉丝总数汇总</div>
						</div>
						<div class="table-wrapper">
							<el-table
								:data="formatFansTableData()"
								border
								:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
								:cell-style="{ height: '45px' }"
							>
								<el-table-column label="序号" align="center" prop="index" min-width="80px" />
								<el-table-column label="账号名称" align="center" prop="accountName" min-width="200px" />
								<el-table-column label="粉丝总数" align="center" prop="totalFans" min-width="120px" />
							</el-table>
						</div>

						<!-- 总关注量占比图 -->
						<div class="chart-container" v-if="fansPieData.length > 1">
							<div class="title3">总粉丝量占比关系</div>
							<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 350px" :label-font-size="18" />
						</div>
					</div>
				</div>
				<div id="wb-myoperate-2" v-if="fansPieData.length > 1">
					<!-- 1.1.5 账号粉丝报告 -->
					<div class="title-wrapper">
						<div class="title1">账号粉丝</div>
					</div>

					<!-- 粉丝数据表格 -->
					<div class="title-wrapper">
						<div class="title2">本级账号粉丝总数汇总</div>
					</div>
					<div class="table-wrapper">
						<el-table
							:data="formatFansTableData()"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="序号" align="center" prop="index" min-width="80px" />
							<el-table-column label="账号名称" align="center" prop="accountName" min-width="200px" />
							<el-table-column label="粉丝总数" align="center" prop="totalFans" min-width="120px" />
						</el-table>
					</div>

					<!-- 总关注量占比图 -->
					<div class="chart-container" v-if="fansPieData.length > 1">
						<div class="title3">总粉丝量占比关系</div>
						<pie :data="fansPieData" :total="fansPieTotal" style="width: 100%; height: 350px" :label-font-size="18" />
					</div>
				</div>
				<!-- 转发量Top 10 - 支持分页 -->
				<div
					v-for="(chunk, chunkIndex) in paginatedForwardTop"
					:key="`forward-${chunkIndex}`"
					:id="`wb-myoperate-3${chunkIndex > 0 ? '-' + (chunkIndex + 1) : ''}`"
				>
					<div class="title-wrapper" v-if="chunkIndex === 0">
						<div class="title1">作品分析</div>
					</div>
					<div class="title2" v-if="chunkIndex === 0">转发量Top 10</div>
					<div class="table-wrapper">
						<el-table :data="chunk" border>
							<el-table-column label="排序" align="center" width="70">
								<template #default="scope">
									{{ chunkIndex * getPageSize('forward') + scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="微博内容" align="center" prop="title" min-width="500px" />
							<el-table-column label="转发量" align="center" prop="data" width="90">
								<template #default="scope">
									{{ tranNum(scope.row.data) }}
								</template>
							</el-table-column>
							<el-table-column label="发布时间" align="center" prop="issueDate" width="140" />
							<el-table-column label="来源账号" align="center" prop="account" width="100" v-if="bciData.length > 1" />
						</el-table>
					</div>
				</div>
				<!-- 评论量Top 10 - 支持分页 -->
				<div
					v-for="(chunk, chunkIndex) in paginatedCommentTop"
					:key="`comment-${chunkIndex}`"
					:id="`wb-myoperate-4${chunkIndex > 0 ? '-' + (chunkIndex + 1) : ''}`"
				>
					<div class="title2" v-if="chunkIndex === 0">评论量Top 10</div>
					<div class="table-wrapper">
						<el-table :data="chunk" border>
							<el-table-column label="排序" align="center" width="70">
								<template #default="scope">
									{{ chunkIndex * getPageSize('comment') + scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="微博内容" align="center" prop="title" min-width="500px" />
							<el-table-column label="评论量" align="center" prop="data" width="90">
								<template #default="scope">
									{{ tranNum(scope.row.data) }}
								</template>
							</el-table-column>
							<el-table-column label="发布时间" align="center" prop="issueDate" width="140" />
							<el-table-column label="来源账号" align="center" prop="account" width="100" v-if="bciData.length > 1" />
						</el-table>
					</div>
				</div>
				<!-- 点赞量Top 10 - 支持分页 -->
				<div
					v-for="(chunk, chunkIndex) in paginatedLikeTop"
					:key="`like-${chunkIndex}`"
					:id="`wb-myoperate-5${chunkIndex > 0 ? '-' + (chunkIndex + 1) : ''}`"
				>
					<div class="title2" v-if="chunkIndex === 0">点赞量Top 10</div>
					<div class="table-wrapper">
						<el-table :data="chunk" border>
							<el-table-column label="排序" align="center" width="70">
								<template #default="scope">
									{{ chunkIndex * getPageSize('like') + scope.$index + 1 }}
								</template>
							</el-table-column>
							<el-table-column label="微博内容" align="center" prop="title" min-width="600" />
							<el-table-column label="点赞量" align="center" prop="data" width="90">
								<template #default="scope">
									{{ tranNum(scope.row.data) }}
								</template>
							</el-table-column>
							<el-table-column label="发布时间" align="center" prop="issueDate" width="140" />
							<el-table-column label="来源账号" align="center" prop="account" width="100" v-if="bciData.length > 1" />
						</el-table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineExpose, watch, computed } from 'vue';
import pie from '/@/components/Echarts/pie.vue';
import lineChart from '/@/components/Echarts/line.vue';
import { tranNumber } from '/@/utils/tranNum';

const props = defineProps({
	id: {
		type: [String, Number],
		default: '',
	},
	selfReportData: {
		type: Object,
		default: () => ({}),
	},
	title: {
		type: String,
		default: '',
	},
	date: {
		type: Object,
		default: () => ({}),
	},
	operateType: {
		type: String,
		default: '',
	},
	tenantName: {
		type: String,
		default: '',
	},
	accountCounts: {
		type: Object,
		default: () => ({
			myAccount: 0,
			subAccount: 0,
			sameAccount: 0,
		}),
	},
});

// 数据定义
const pageLoading = ref(false);
const hasAccounts = ref(true);
const month = ref(null);
const yearMonth = ref(null);
const endTime = ref(null);

// 传播力指数报告
const bciData = ref([]);

// 作品发布报告
const worksIssue = ref([]);

// 作品传播报告
const worksLike = ref([]); // 点赞数据
const worksComment = ref([]); // 评论数据
const worksForward = ref([]); // 转发数据
const worksPlay = ref([]); // 播放数据
const worksTop = ref({
	like: [], // 点赞top10
	comment: [], // 评论top10
	forward: [], // 转发top10
	play: [], // 播放top10
});

const totalFans = ref([]);
const fansPieData = ref([]);
const fansPieTotal = ref({});
const newFans = ref([]); // 新增粉丝
const netFans = ref([]); // 净增粉丝
const workIds = ref([]); // 用于存储动态生成的模块ID

onMounted(() => {
	// 不再从API获取数据，只处理props中的数据
	if (props.selfReportData) {
		handleReportData(props.selfReportData);
	}
});

// 监听props的变化
watch(
	() => props.selfReportData,
	(newVal) => {
		if (newVal) {
			handleReportData(newVal);
		}
	},
	{ deep: true }
);

// 数字转换工具方法
const tranNum = (num) => {
	return tranNumber(num);
};
// 格式化作品发布表格数据
const formatPublishTableData = (account) => {
	const tableData = [];

	// 月度发布作品总量
	tableData.push({
		type: '月度发布作品总量',
		currentValue: tranNum(account.issue),
		compareChange: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${tranNum(account.diff)}`,
		compareRate: account.trend === 0 ? '持平' : `${account.trend === -1 ? '-' : ''}${account.rate}`,
	});

	// 原创微博
	tableData.push({
		type: '原创微博',
		currentValue: tranNum(account.origIssue),
		compareChange: account.origTrend === 0 ? '持平' : `${account.origTrend === -1 ? '-' : ''}${tranNum(account.origDiff)}`,
		compareRate: account.origTrend === 0 ? '持平' : `${account.origTrend === -1 ? '-' : ''}${account.origRate}`,
	});

	// 非原创微博
	tableData.push({
		type: '非原创微博',
		currentValue: tranNum(account.origNoIssue),
		compareChange: account.origNoTrend === 0 ? '持平' : `${account.origNoTrend === -1 ? '-' : ''}${tranNum(account.origNoDiff)}`,
		compareRate: account.origNoTrend === 0 ? '持平' : `${account.origNoTrend === -1 ? '-' : ''}${account.origNoRate}`,
	});

	// 非视频微博
	tableData.push({
		type: '非视频微博',
		currentValue: tranNum(account.videoNoIssue),
		compareChange: account.videoNoTrend === 0 ? '持平' : `${account.videoNoTrend === -1 ? '-' : ''}${tranNum(account.videoNoDiff)}`,
		compareRate: account.videoNoTrend === 0 ? '持平' : `${account.videoNoTrend === -1 ? '-' : ''}${account.videoNoRate}`,
	});

	// 视频微博
	tableData.push({
		type: '视频微博',
		currentValue: tranNum(account.videoIssue),
		compareChange: account.videoTrend === 0 ? '持平' : `${account.videoTrend === -1 ? '-' : ''}${tranNum(account.videoDiff)}`,
		compareRate: account.videoTrend === 0 ? '持平' : `${account.videoTrend === -1 ? '-' : ''}${account.videoRate}`,
	});

	return tableData;
};

// 格式化账号传播分析表格数据
const formatAccountTableData = (account) => {
	const tableData = [];

	// 获取对应账号的各项数据
	const bciItem = bciData.value.find((item) => item.accountName === account.accountName);
	const likeItem = worksLike.value.find((item) => item.accountName === account.accountName);
	const commentItem = worksComment.value.find((item) => item.accountName === account.accountName);
	const forwardItem = worksForward.value.find((item) => item.accountName === account.accountName);
	const playItem = worksPlay.value.find((item) => item.accountName === account.accountName);

	// 传播力指数
	if (bciItem) {
		tableData.push({
			type: '传播力指数',
			currentValue: bciItem.bci,
			compareChange: `${bciItem.trend === 0 ? '持平' : bciItem.trend === -1 ? '-' : ''}${bciItem.trend === 0 ? '' : tranNum(Math.abs(bciItem.diff))}`,
			compareRate: bciItem.trend === 0 ? '持平' : `${bciItem.trend === -1 ? '-' : ''}${bciItem.rate}`,
			avgValue: '-',
			origCurrentValue: '-',
			origCompareChange: '-',
			origCompareRate: '-',
			origAvgValue: '-',
		});
	}

	// 点赞量
	if (likeItem) {
		tableData.push({
			type: '点赞量',
			currentValue: tranNum(likeItem.data),
			compareChange: `${likeItem.trend === 0 ? '持平' : likeItem.trend === -1 ? '-' : ''}${
				likeItem.trend === 0 ? '' : tranNum(Math.abs(likeItem.diff))
			}`,
			compareRate: likeItem.trend === 0 ? '持平' : `${likeItem.trend === -1 ? '-' : ''}${likeItem.rate}`,
			avgValue: likeItem.avg,
			origCurrentValue: tranNum(likeItem.origData),
			origCompareChange: `${likeItem.origTrend === 0 ? '持平' : likeItem.origTrend === -1 ? '-' : ''}${
				likeItem.origTrend === 0 ? '' : tranNum(Math.abs(likeItem.origDiff))
			}`,
			origCompareRate: likeItem.origTrend === 0 ? '持平' : `${likeItem.origTrend === -1 ? '-' : ''}${likeItem.origRate}`,
			origAvgValue: likeItem.origAvg,
		});
	}

	// 评论量
	if (commentItem) {
		tableData.push({
			type: '评论量',
			currentValue: tranNum(commentItem.data),
			compareChange: `${commentItem.trend === 0 ? '持平' : commentItem.trend === -1 ? '-' : ''}${
				commentItem.trend === 0 ? '' : tranNum(Math.abs(commentItem.diff))
			}`,
			compareRate: commentItem.trend === 0 ? '持平' : `${commentItem.trend === -1 ? '-' : ''}${commentItem.rate}`,
			avgValue: commentItem.avg,
			origCurrentValue: tranNum(commentItem.origData),
			origCompareChange: `${commentItem.origTrend === 0 ? '持平' : commentItem.origTrend === -1 ? '-' : ''}${
				commentItem.origTrend === 0 ? '' : tranNum(Math.abs(commentItem.origDiff))
			}`,
			origCompareRate: commentItem.origTrend === 0 ? '持平' : `${commentItem.origTrend === -1 ? '-' : ''}${commentItem.origRate}`,
			origAvgValue: commentItem.origAvg,
		});
	}

	// 转发量
	if (forwardItem) {
		tableData.push({
			type: '转发量',
			currentValue: tranNum(forwardItem.data),
			compareChange: `${forwardItem.trend === 0 ? '持平' : forwardItem.trend === -1 ? '-' : ''}${
				forwardItem.trend === 0 ? '' : tranNum(Math.abs(forwardItem.diff))
			}`,
			compareRate: forwardItem.trend === 0 ? '持平' : `${forwardItem.trend === -1 ? '-' : ''}${forwardItem.rate}`,
			avgValue: forwardItem.avg,
			origCurrentValue: tranNum(forwardItem.origData),
			origCompareChange: `${forwardItem.origTrend === 0 ? '持平' : forwardItem.origTrend === -1 ? '-' : ''}${
				forwardItem.origTrend === 0 ? '' : tranNum(Math.abs(forwardItem.origDiff))
			}`,
			origCompareRate: forwardItem.origTrend === 0 ? '持平' : `${forwardItem.origTrend === -1 ? '-' : ''}${forwardItem.origRate}`,
			origAvgValue: forwardItem.origAvg,
		});
	}

	// 播放量
	if (playItem) {
		tableData.push({
			type: '播放量',
			currentValue: tranNum(playItem.data),
			compareChange: `${playItem.trend === 0 ? '持平' : playItem.trend === -1 ? '-' : ''}${
				playItem.trend === 0 ? '' : tranNum(Math.abs(playItem.diff))
			}`,
			compareRate: playItem.trend === 0 ? '持平' : `${playItem.trend === -1 ? '-' : ''}${playItem.rate}`,
			avgValue: playItem.avg,
			origCurrentValue: '-',
			origCompareChange: '-',
			origCompareRate: '-',
			origAvgValue: '-',
		});
	}

	return tableData;
};

// 格式化粉丝表格数据
const formatFansTableData = () => {
	const tableData = [];

	// 从totalFans获取粉丝数据
	if (totalFans.value && totalFans.value.length > 0) {
		totalFans.value.forEach((account, index) => {
			tableData.push({
				index: index + 1,
				accountName: account.name,
				totalFans: tranNum(account.count),
			});
		});
	}

	return tableData;
};

// 新增：格式化折线图数据的函数
const formatLineChartData = (data) => {
	// 如果已经是新格式，直接返回
	if (data && data.row && data.dates) {
		return data;
	}

	// 如果是旧格式，进行转换
	if (data && data.xAxis && data.series) {
		return {
			title: '发布时间区间分布情况',
			row: data.series.map((item) => ({
				name: item.name,
				counts: item.data,
				type: 'line',
			})),
			dates: data.xAxis,
		};
	}

	// 默认返回空数据结构
	return {
		title: '发布时间区间分布情况',
		row: [],
		dates: [],
	};
};

// 处理报告数据
const handleReportData = (data) => {
	try {
		console.log('开始处理报告数据');

		// 基础信息处理
		hasAccounts.value = data.account_data && data.account_data.length > 0; // 是否有绑定账号
		month.value = data.month || '本月'; // 月份
		yearMonth.value = data.yearMonth; // 统计时间

		// 标题可能来自props或数据中的title
		if (data.title && !props.title) {
			title.value = data.title; // 标题
		}

		endTime.value = props.date?.end_date; // 结束时间

		// 各模块数据处理
		// 基于account_data处理所有账号数据
		const accountData = data.account_data || [];

		// 传播力指数报告
		bciData.value = accountData.map((item) => ({
			accountName: item.account_name,
			bci: item.bci?.bci || 0,
			diff: item.bci?.diff || 0,
			trend: item.bci?.compare_trend || 0,
			bciStatus: item.bci?.compare_trend === 1 ? '上升' : item.bci?.compare_trend === -1 ? '下降' : '持平',
			rate: `${item.bci?.compare_rate || 0}%`,
		}));

		// 作品发布报告 - 基于account_data中的publish数据
		worksIssue.value = accountData.map((item) => {
			const publishData = item.publish || {};
			return {
				accountName: item.account_name,
				issue: publishData.total_count || 0,
				diff: publishData.total_count_compare?.diff || 0,
				trend: publishData.total_count_compare?.trend || 0,
				status: publishData.total_count_compare?.trend === 1 ? '增加' : publishData.total_count_compare?.trend === -1 ? '减少' : '持平',
				rate: `${publishData.total_count_compare?.rate || 0}%`,

				// 原创微博信息
				origIssue: publishData.orig_issue_count || 0,
				origDiff: publishData.orig_issue_count_compare?.diff || 0,
				origTrend: publishData.orig_issue_count_compare?.trend || 0,
				origStatus: publishData.orig_issue_count_compare?.trend === 1 ? '增加' : publishData.orig_issue_count_compare?.trend === -1 ? '减少' : '持平',
				origRate: `${publishData.orig_issue_count_compare?.rate || 0}%`,

				// 非原创微博信息 (转发内容)
				origNoIssue: publishData.issue_count || 0,
				origNoDiff: publishData.issue_count_compare?.diff || 0,
				origNoTrend: publishData.issue_count_compare?.trend || 0,
				origNoStatus: publishData.issue_count_compare?.trend === 1 ? '增加' : publishData.issue_count_compare?.trend === -1 ? '减少' : '持平',
				origNoRate: `${publishData.issue_count_compare?.rate || 0}%`,

				// 非视频微博信息
				videoNoIssue: publishData.total_txt || 0,
				videoNoDiff: publishData.total_txt_compare?.diff || 0,
				videoNoTrend: publishData.total_txt_compare?.trend || 0,
				videoNoStatus: publishData.total_txt_compare?.trend === 1 ? '增加' : publishData.total_txt_compare?.trend === -1 ? '减少' : '持平',
				videoNoRate: `${publishData.total_txt_compare?.rate || 0}%`,

				// 视频微博信息
				videoIssue: publishData.total_video || 0,
				videoDiff: publishData.total_video_compare?.diff || 0,
				videoTrend: publishData.total_video_compare?.trend || 0,
				videoStatus: publishData.total_video_compare?.trend === 1 ? '增加' : publishData.total_video_compare?.trend === -1 ? '减少' : '持平',
				videoRate: `${publishData.total_video_compare?.rate || 0}%`,

				// 饼图数据
				origPie: {
					data: [
						{ name: '原创', value: publishData.orig_issue_count || 0 },
						{ name: '非原创', value: publishData.issue_count || 0 },
					],
					total: { name: '总数', count: publishData.total_count || 0 },
				},

				videPie: {
					data: [
						{ name: '视频', value: publishData.total_video || 0 },
						{ name: '非视频', value: publishData.total_txt || 0 },
					],
					total: { name: '总数', count: publishData.total_count || 0 },
				},

				// 发布时间分布图
				timeDistribution: {
					title: '发布时间区间分布情况',
					row: [
						{
							name: '发布数量',
							counts: (publishData.hour_distribution || []).map((hour) => hour.count),
							type: 'line',
						},
					],
					dates: (publishData.hour_distribution || []).map((hour) => hour.hour),
				},
			};
		});

		// 点赞量 - 基于account_data中的like_count_stats
		worksLike.value = accountData.map((item) => {
			const likeData = item.like_count_stats || {};
			return {
				accountName: item.account_name,
				data: likeData.total || 0,
				diff: likeData.compare?.diff || 0,
				trend: likeData.compare?.trend || 0,
				status: likeData.compare?.trend === 1 ? '增加' : likeData.compare?.trend === -1 ? '减少' : '持平',
				rate: `${likeData.compare?.rate || 0}%`,
				avg: likeData.total_avg || 0,

				// 原创微博点赞
				origData: likeData.orig_total || 0,
				origDiff: likeData.orig_compare?.diff || 0,
				origTrend: likeData.orig_compare?.trend || 0,
				origStatus: likeData.orig_compare?.trend === 1 ? '增加' : likeData.orig_compare?.trend === -1 ? '减少' : '持平',
				origRate: `${likeData.orig_compare?.rate || 0}%`,
				origAvg: likeData.orig_total_avg || 0,

				// 非原创微博点赞
				origNoData: likeData.no_orig_total || 0,
				origNoDiff: likeData.no_orig_compare?.diff || 0,
				origNoTrend: likeData.no_orig_compare?.trend || 0,
				origNoStatus: likeData.no_orig_compare?.trend === 1 ? '增加' : likeData.no_orig_compare?.trend === -1 ? '减少' : '持平',
				origNoRate: `${likeData.no_orig_compare?.rate || 0}%`,
				origNoAvg: likeData.no_orig_total_avg || 0,
			};
		});

		// 评论量 - 基于account_data中的comment_count_stats
		worksComment.value = accountData.map((item) => {
			const commentData = item.comment_count_stats || {};
			return {
				accountName: item.account_name,
				data: commentData.total || 0,
				diff: commentData.compare?.diff || 0,
				trend: commentData.compare?.trend || 0,
				status: commentData.compare?.trend === 1 ? '增加' : commentData.compare?.trend === -1 ? '减少' : '持平',
				rate: `${commentData.compare?.rate || 0}%`,
				avg: commentData.total_avg || 0,

				// 原创微博评论
				origData: commentData.orig_total || 0,
				origDiff: commentData.orig_compare?.diff || 0,
				origTrend: commentData.orig_compare?.trend || 0,
				origStatus: commentData.orig_compare?.trend === 1 ? '增加' : commentData.orig_compare?.trend === -1 ? '减少' : '持平',
				origRate: `${commentData.orig_compare?.rate || 0}%`,
				origAvg: commentData.orig_total_avg || 0,

				// 非原创微博评论
				origNoData: commentData.no_orig_total || 0,
				origNoDiff: commentData.no_orig_compare?.diff || 0,
				origNoTrend: commentData.no_orig_compare?.trend || 0,
				origNoStatus: commentData.no_orig_compare?.trend === 1 ? '增加' : commentData.no_orig_compare?.trend === -1 ? '减少' : '持平',
				origNoRate: `${commentData.no_orig_compare?.rate || 0}%`,
				origNoAvg: commentData.no_orig_total_avg || 0,
			};
		});

		// 转发量 - 基于account_data中的share_count_stats
		worksForward.value = accountData.map((item) => {
			const shareData = item.share_count_stats || {};
			return {
				accountName: item.account_name,
				data: shareData.total || 0,
				diff: shareData.compare?.diff || 0,
				trend: shareData.compare?.trend || 0,
				status: shareData.compare?.trend === 1 ? '增加' : shareData.compare?.trend === -1 ? '减少' : '持平',
				rate: `${shareData.compare?.rate || 0}%`,
				avg: shareData.total_avg || 0,

				// 原创微博转发
				origData: shareData.orig_total || 0,
				origDiff: shareData.orig_compare?.diff || 0,
				origTrend: shareData.orig_compare?.trend || 0,
				origStatus: shareData.orig_compare?.trend === 1 ? '增加' : shareData.orig_compare?.trend === -1 ? '减少' : '持平',
				origRate: `${shareData.orig_compare?.rate || 0}%`,
				origAvg: shareData.orig_total_avg || 0,

				// 非原创微博转发
				origNoData: shareData.no_orig_total || 0,
				origNoDiff: shareData.no_orig_compare?.diff || 0,
				origNoTrend: shareData.no_orig_compare?.trend || 0,
				origNoStatus: shareData.no_orig_compare?.trend === 1 ? '增加' : shareData.no_orig_compare?.trend === -1 ? '减少' : '持平',
				origNoRate: `${shareData.no_orig_compare?.rate || 0}%`,
				origNoAvg: shareData.no_orig_total_avg || 0,
			};
		});

		// 播放量 - 基于account_data中的play_count_stats
		worksPlay.value = accountData.map((item) => {
			const playData = item.play_count_stats || {};
			return {
				accountName: item.account_name,
				data: playData.total || 0,
				diff: playData.compare?.diff || 0,
				trend: playData.compare?.trend || 0,
				status: playData.compare?.trend === 1 ? '增加' : playData.compare?.trend === -1 ? '减少' : '持平',
				rate: `${playData.compare?.rate || 0}%`,
				avg: playData.total_avg || 0,
			};
		});

		// 处理作品Top数据
		if (data.work_analysis) {
			worksTop.value = {
				like: (data.work_analysis.likeTop || []).map((item) => ({
					title: item.title,
					data: item.total,
					issueDate: item.issue_time?.split(' ')[0] || '',
					isVideo: !!item.video,
					isOrig: item.type === 1,
					account: item.account_name,
				})),
				comment: (data.work_analysis.commentTop || []).map((item) => ({
					title: item.title,
					data: item.total,
					issueDate: item.issue_time?.split(' ')[0] || '',
					isVideo: !!item.video,
					isOrig: item.type === 1,
					account: item.account_name,
				})),
				forward: (data.work_analysis.shareTop || []).map((item) => ({
					title: item.title,
					data: item.total,
					issueDate: item.issue_time?.split(' ')[0] || '',
					isVideo: !!item.video,
					isOrig: item.type === 1,
					account: item.account_name,
				})),
				play: [], // 暂无此数据
			};
		}

		// 总粉丝量
		totalFans.value = (data.account_fans_data?.total_fans || []).map((item) => ({
			name: item.account_name,
			count: item.total,
		}));

		// 处理总粉丝饼图
		fansPieData.value = (data.account_fans_data?.total_fans || []).map((item) => ({
			name: item.account_name,
			value: item.total,
		}));

		fansPieTotal.value = {
			name: '总数',
			value: (data.account_fans_data?.total_fans || []).reduce((sum, item) => sum + (parseInt(item.total) || 0), 0),
		};

		// 净增粉丝 - 使用新增粉丝数据作为替代
		netFans.value = (data.account_fans_data?.new_fans || []).map((item) => ({
			accountName: item.account_name,
			data: item.total,
			diff: item.compare?.diff || 0,
			status: item.compare?.trend === 1 ? '增加' : item.compare?.trend === -1 ? '减少' : '持平',
			rate: item.compare?.rate || 0,
		}));

		// 更新模块ID
		updateModuleIds();

		console.log('数据处理完成');
	} catch (error) {
		console.error('处理报告数据失败:', error);
	} finally {
		pageLoading.value = false;
	}
};

// 更新模块ID的函数
const updateModuleIds = () => {
	// 清空原有数据
	workIds.value = [];

	// 根据worksIssue长度生成对应ID，每个账号生成两个ID
	if (worksIssue.value && worksIssue.value.length > 0) {
		worksIssue.value.forEach((_, index) => {
			workIds.value.push(`wb-my-${index + 1}-1`); // 第一页：表格
			workIds.value.push(`wb-my-${index + 1}-2`); // 第二页：饼图和时间分布
		});
	}

	console.log('更新后的workIds:', workIds.value);
	console.log('worksIssue数据:', worksIssue.value);
};

// 检查标题长度是否超过150字
const checkTitleLength = (title) => {
	return title && title.length > 150;
};

// 计算每页显示的条数
const getPageSize = (type) => {
	const data = worksTop.value[type] || [];
	const longTitleCount = data.filter((item) => checkTitleLength(item.title)).length;

	// 如果超过2个标题大于150字，每页显示5条，否则显示10条
	return longTitleCount > 2 ? 5 : 10;
};

// 分页处理函数
const paginateData = (data, type) => {
	if (!data || data.length === 0) return [[]];

	const pageSize = getPageSize(type);
	const chunks = [];

	for (let i = 0; i < data.length; i += pageSize) {
		chunks.push(data.slice(i, i + pageSize));
	}

	return chunks.length > 0 ? chunks : [[]];
};

// 转发量Top10分页数据
const paginatedForwardTop = computed(() => {
	return paginateData(worksTop.value.forward, 'forward');
});

// 评论量Top10分页数据
const paginatedCommentTop = computed(() => {
	return paginateData(worksTop.value.comment, 'comment');
});

// 点赞量Top10分页数据
const paginatedLikeTop = computed(() => {
	return paginateData(worksTop.value.like, 'like');
});

// 创建一个计算属性来获取所有需要导出的模块ID
const allModuleIds = computed(() => {
	const baseModuleIds = ['wb-myoperate-1'];

	// 添加粉丝模块ID
	if (fansPieData.value.length > 1) {
		baseModuleIds.push('wb-myoperate-2');
	}

	// 添加分页的Top10模块ID
	const forwardPages = paginatedForwardTop.value.length;
	const commentPages = paginatedCommentTop.value.length;
	const likePages = paginatedLikeTop.value.length;

	console.log('本级微博分页信息 - 转发:', forwardPages, '评论:', commentPages, '点赞:', likePages);

	// 转发量Top10的所有页面
	for (let i = 0; i < forwardPages; i++) {
		baseModuleIds.push(i === 0 ? 'wb-myoperate-3' : `wb-myoperate-3-${i + 1}`);
	}

	// 评论量Top10的所有页面
	for (let i = 0; i < commentPages; i++) {
		baseModuleIds.push(i === 0 ? 'wb-myoperate-4' : `wb-myoperate-4-${i + 1}`);
	}

	// 点赞量Top10的所有页面
	for (let i = 0; i < likePages; i++) {
		baseModuleIds.push(i === 0 ? 'wb-myoperate-5' : `wb-myoperate-5-${i + 1}`);
	}

	// 动态添加作品模块ID
	const allIds = [...workIds.value, ...baseModuleIds];
	console.log('本级微博所有模块ID:', allIds);
	return allIds;
});

// 监听worksIssue的变化，更新模块ID
watch(
	() => worksIssue.value,
	(newVal) => {
		if (newVal && newVal.length > 0) {
			updateModuleIds();
			console.log('监听到worksIssue变化，更新workIds:', workIds.value);
		}
	},
	{ deep: true }
);

// 导出组件内部方法给父组件使用
defineExpose({
	moduleIds: allModuleIds,
});
</script>

<style scoped>
.top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	overflow: hidden;
	height: 200px;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}
.title-wrapper {
	width: 100%;
	/* padding: 20px; */
}

.title1 {
	font-size: 30px;
	width: 25%;
	margin: 0 auto 15px;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 15px;
	line-height: 1.5;
}

.title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	text-align: center;
	padding-bottom: 10px;
	line-height: 1.5;
}

.content-wrapper {
	position: relative;
	margin: 0 30px 20px;
	padding: 7px 20px 20px 20px;
	border: #9d9c9a 1px solid;
	overflow: visible;
}

.content {
	font-size: 18px;
	line-height: 2;
	/* padding: 10px 0; */
	white-space: inherit !important;
}

.sub-title {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}
:deep(.el-table .cell) {
	font-size: 18px;
	color: #000;
}
:deep(.el-table--border) {
	border-top: 1px solid #fff;
}
.chart-container {
	margin: 10px 0 0 40px;
}

.text-bold {
	font-weight: bold;
}

.text-blue {
	color: #00abf9;
}

:deep(.el-loading-spinner) {
	top: 300px;
}
:deep(.el-table th) {
	font-weight: bold;
	color: #000;
	background-color: #f8f8f9;
	padding: 15px 4px !important;
}

:deep(.el-table td) {
	padding: 15px 4px !important;
}

.title3 {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}
.table-wrapper {
	margin: 20px 30px;
}
.btn-wrapper {
	position: absolute;
	right: 50px;
	top: 95px;
	z-index: 1000;
}
* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

/* AI 分析报告 Markdown 内容样式 */
.markdown-report-content {
	padding: 7px 20px 20px 20px;
	margin: 0 30px 20px;
}

/* 账号统计信息样式 */
.account-stats-container {
	width: 100%;
	background-color: #fff;
	border-bottom: 2px solid #008ccc;
	padding: 20px 0;
	margin-bottom: 20px;
}

.account-stats-content {
	text-align: center;
	font-size: 20px;
	line-height: 1.8;
	color: #333;
}

.tenant-name {
	font-weight: bold;
	color: #008ccc;
	font-size: 20px;
	margin-right: 8px;
}

.stats-item {
	margin: 0 15px;
	color: #555;
}

.count-number {
	font-weight: bold;
	color: #e74c3c;
	font-size: 20px;
}
.flex {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
.flex-row {
	width: 50%;
}
</style> 
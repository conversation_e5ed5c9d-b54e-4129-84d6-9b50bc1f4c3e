<template>
	<div class="permission-example">
		<h2>CommonTabs权限控制使用示例</h2>

		<div class="example-section">
			<h3>1. 运营报表页面 - 带权限控制</h3>
			<p>权限配置：微信(OperateReport:ListWx)、微博(OperationReport:ListWb)、抖音(OperationReport:ListDy)</p>
			<CommonTabs v-model="currentPlatform1" :items="operateReportTabs" @change="handleChange" />
			<div class="result">当前选中: {{ currentPlatform1 }}</div>
		</div>

		<div class="example-section">
			<h3>2. 微信公众号管理页面 - 自定义权限</h3>
			<p>权限配置：微信(WechatAccount:ListWx)、微博(WechatAccount:ListWb)、抖音(WechatAccount:ListDy)</p>
			<CommonTabs v-model="currentPlatform2" :items="wechatAccountTabs" @change="handleChange" />
			<div class="result">当前选中: {{ currentPlatform2 }}</div>
		</div>

		<div class="example-section">
			<h3>3. 无权限控制 - 向后兼容</h3>
			<p>所有平台都显示，无权限限制</p>
			<CommonTabs v-model="currentPlatform3" :items="normalTabs" @change="handleChange" />
			<div class="result">当前选中: {{ currentPlatform3 }}</div>
		</div>

		<div class="example-section">
			<h3>4. 手动配置权限</h3>
			<p>手动为每个Tab配置不同的权限</p>
			<CommonTabs v-model="currentPlatform4" :items="customTabs" @change="handleChange" />
			<div class="result">当前选中: {{ currentPlatform4 }}</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { OPERATE_REPORT_PERMISSIONS, WECHAT_ACCOUNT_PERMISSIONS, PlatformType } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions, generatePlatformTabs, TabItem } from '/@/utils/platformTabsHelper';

// 当前选中的平台
const currentPlatform1 = ref('wechat');
const currentPlatform2 = ref('wechat');
const currentPlatform3 = ref('wechat');
const currentPlatform4 = ref('wechat');

// 不同的Tab配置
const operateReportTabs = ref<TabItem[]>([]);
const wechatAccountTabs = ref<TabItem[]>([]);
const normalTabs = ref<TabItem[]>([]);
const customTabs = ref<TabItem[]>([]);

// 动态导入CommonTabs组件
const CommonTabs = defineAsyncComponent(() => import('./index.vue'));

onMounted(() => {
	// 1. 运营报表页面的权限配置
	operateReportTabs.value = generatePlatformTabsWithPermissions(OPERATE_REPORT_PERMISSIONS);

	// 2. 微信公众号管理页面的权限配置
	wechatAccountTabs.value = generatePlatformTabsWithPermissions(WECHAT_ACCOUNT_PERMISSIONS);

	// 3. 无权限控制
	normalTabs.value = generatePlatformTabs();

	// 4. 手动配置权限
	customTabs.value = [
		{
			label: '微信',
			value: 'wechat',
			permission: 'custom:wechat:view',
		},
		{
			label: '抖音',
			value: 'douyin',
			permission: 'custom:douyin:view',
		},
		{
			label: '微博',
			value: 'weibo',
			// 不设置权限，默认显示
		},
	];
});

const handleChange = (value: string) => {
	console.log('平台切换:', value);
};
</script>

<style lang="scss" scoped>
.permission-example {
	padding: 20px;
	max-width: 1200px;
	margin: 0 auto;

	h2 {
		color: #333;
		margin-bottom: 30px;
		text-align: center;
	}

	.example-section {
		margin-bottom: 40px;
		padding: 20px;
		border: 1px solid #e4e7ed;
		border-radius: 8px;
		background-color: #fafafa;

		h3 {
			color: #409eff;
			margin-bottom: 10px;
		}

		p {
			color: #666;
			margin-bottom: 15px;
			font-size: 14px;
		}

		.result {
			margin-top: 15px;
			padding: 10px;
			background-color: #f0f9ff;
			border: 1px solid #b3d8ff;
			border-radius: 4px;
			color: #409eff;
			font-weight: bold;
		}
	}
}
</style> 
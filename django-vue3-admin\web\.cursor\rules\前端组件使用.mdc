---
description: 
globs: 
alwaysApply: false
---
以下是基于 `crud.tsx` 文件总结的基础组件规范模板，可作为快速参考指南。

## 下拉选择器 (Select)

### 基于字典的下拉选择器
```typescript
// 标准字典列配置模板
{
    title: '状态',          // 列标题
    type: 'dict-select',   // 字段类型：dict-select 用于字典选择
    dict: dict({           // 字典配置
        data: dictionary('button_status_bool')  // 字典数据源
    }),
    search: { show: true },  // 是否在搜索表单中显示
    form: {                  // 表单配置
        component: {         // 表单组件配置
            span: 12         // 表单项宽度
        }
    },
    column: {               // 列配置
        width: 100,         // 列宽
        component: {        // 列组件配置
            name: 'dict-select',  // 组件名称
            props: {         // 组件属性
                dict: dict({
                    data: dictionary('button_status_bool')
                })
            }
        }
    }
}
```
完整示例：
```ts
// 1. 引入必要的依赖
import { dict } from '@fast-crud/fast-crud'
import { dictionary } from '/@/utils/dictionary'

// 2. 列配置
export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    return {
        crudOptions: {
            columns: {
                status: {
                    title: '状态',
                    type: 'dict-select',
                    dict: dict({
                        data: dictionary('button_status_bool')
                    }),
                    search: { show: true },
                    form: {
                        component: {
                            span: 12
                        }
                    },
                    column: {
                        width: 100,
                        component: {
                            name: 'dict-select',
                            props: {
                                dict: dict({
                                    data: dictionary('button_status_bool')
                                })
                            }
                        }
                    }
                }
            }
        }
    }
}
```

### 树形下拉选择器
```typescript
fieldName: {
    title: '字段名称',
    type: 'dict-tree',
    dict: dict({
        isTree: true,
        url: '/api/system/endpoint/',
        value: 'id',
        label: 'name',
    }),
    column: {
        minWidth: 200,
        formatter({ value, row, index }) {
            return row.field_name_all // 显示完整路径
        }
    },
    form: {
        rules: [{ required: true, message: '必填项' }],
        component: {
            filterable: true,
            placeholder: '请选择',
            props: {
                props: {
                    value: 'id',
                    label: 'name',
                },
            },
        },
    },
}
```

### 可搜索，且数据来源于接口的下拉选择器：
```ts
tenant: {
    title: '租户',
    type: 'dict-select',
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
                filterable: true,
                multiple: true,
                options: asyncCompute({
                    asyncFn: async () => {
                        const res = await tenantApi.getList({page: 1, limit: 500})
                        return res.data.map((item: any) => ({
                            label: item.name,
                            value: item.id
                        }))
                    }
                })
            }
        }
    },
    column: {
        minWidth: 120,
        show: false
    },
    form: { show: false }
},
```

## 输入框 (Input)

### 普通输入框
```typescript
fieldName: {
    title: '字段名称',
    type: 'input',
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
            },
            placeholder: '请输入关键词',
        },
    },
    column: {
        minWidth: 120,
    },
    form: {
        rules: [
            {
                required: true,
                message: '必填项',
            },
            {
                max: 50,
                message: '最多50个字符',
            },
        ],
        component: {
            span: 12, // 占据的栅格宽度
            placeholder: '请输入',
        },
    },
}
```

### 密码输入框
```typescript
password: {
    title: '密码',
    type: 'input',
    column: {
        show: false,
    },
    editForm: {
        show: false, // 编辑表单不显示
    },
    form: {
        rules: [
            {
                required: true,
                message: '密码必填项',
            },
        ],
        component: {
            span: 12,
            showPassword: true,
            placeholder: '请输入密码',
        },
    },
}
```

### 数字输入框
```typescript
fieldName: {
    title: '数值字段',
    type: 'number',
    column: {
        minWidth: 100,
    },
    form: {
        component: {
            min: 0,
            max: 100,
            step: 1,
            precision: 0, // 精度
        },
    },
}
```

## 单选/多选组件

### 单选按钮组
```typescript
fieldName: {
    title: '字段名称',
    type: 'dict-radio',
    dict: dict({
        data: dictionary('dictionary_key'),
    }),
    form: {
        value: 0, // 默认值
        component: {
            span: 12,
        },
    },
}
```

### 开关组件
```typescript
is_active: {
    title: '状态',
    search: {
        show: true,
    },
    type: 'dict-radio',
    column: {
        component: {
            name: 'fs-dict-switch',
            activeText: '',
            inactiveText: '',
            style: '--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6',
            onChange: compute((context) => {
                return () => {
                    api.UpdateObj(context.row).then((res: APIResponseData) => {
                        successMessage(res.msg as string);
                    });
                };
            }),
        },
    },
    dict: dict({
        data: dictionary('button_status_bool'),
    }),
}
```

## 日期时间选择器

### 日期选择器
```typescript
date_field: {
    title: '日期',
    type: 'date',
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
                type: 'date',
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
            },
        },
    },
    column: {
        width: 160,
    },
    form: {
        component: {
            props: {
                type: 'date',
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
            },
        },
    },
}
```

### 日期时间选择器
```typescript
datetime_field: {
    title: '日期时间',
    type: 'datetime',
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
                type: 'datetime',
                format: 'YYYY-MM-DD HH:mm:ss',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
            },
        },
    },
    column: {
        width: 180,
    },
    form: {
        component: {
            props: {
                type: 'datetime',
                format: 'YYYY-MM-DD HH:mm:ss',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
            },
        },
    },
}
```

## 文件上传组件

### 头像上传
```typescript
avatar: {
    title: '头像',
    type: 'avatar-uploader',
    form: {
        component: {
            props: {
                limit: 1,
                uploader: {
                    action: '/api/system/upload/',
                },
            },
        },
    },
    column: {
        width: 100,
        component: {
            name: 'fs-avatar',
        },
    },
}
```

### 文件上传
```typescript
attachment: {
    title: '附件',
    type: 'file-uploader',
    form: {
        component: {
            props: {
                limit: 5,
                uploader: {
                    action: '/api/system/upload/',
                },
                accept: '.jpg,.png,.pdf,.doc,.docx',
            },
        },
    },
    column: {
        width: 200,
        component: {
            name: 'fs-files',
        },
    },
}
```

## 表格配置

```typescript
// 表格基础配置
table: {
    remove: {
        confirmMessage: '是否删除该记录？',
    },
    rowKey: 'id', // 行数据的唯一标识
    scroll: { x: 1500 }, // 表格滚动
},

// 序号列
_index: {
    title: '序号',
    form: { show: false },
    column: {
        type: 'index',
        align: 'center',
        width: '70px',
        columnSetDisabled: true, // 禁止在列设置中选择
    },
},

// 操作列
rowHandle: {
    fixed: 'right', // 固定在右侧
    width: 250,
    buttons: {
        view: {
            show: auth('permission:View'),
            text: '查看',
            type: 'primary',
            icon: 'View',
        },
        edit: {
            show: auth('permission:Update'),
            text: '编辑',
            type: 'success',
            icon: 'Edit',
        },
        remove: {
            show: auth('permission:Delete'),
            text: '删除',
            type: 'danger',
            icon: 'Delete',
        },
        custom: {
            text: '自定义按钮',
            type: 'warning',
            show: auth('permission:Custom'),
            tooltip: {
                placement: 'top',
                content: '按钮说明',
            },
            click: (ctx: any) => handleCustomAction(ctx.row),
        },
    },
},
```

## 搜索区域配置

```typescript
search: {
    container: {
        layout: 'multi-line', // 多行布局
        action: {
            col: {
                span: 10,
            },
        },
    },
    options: {
        show: true,
        columns: 4, // 每行显示的列数
    },
},

// 关键词搜索
search: {
    title: '关键词',
    column: {
        show: false,
    },
    search: {
        show: true,
        component: {
            props: {
                clearable: true,
            },
            placeholder: '请输入关键词',
        },
    },
    form: {
        show: false,
    },
},
```

## 表单配置

```typescript
form: {
    container: {
        layout: 'card', // 卡片布局
    },
    labelWidth: '120px',
    display: 'flex', // 弹性布局
    labelPosition: 'right',
    rules: {
        // 全局表单验证规则
    },
    group: {
        // 表单分组
        type: 'collapse', // 折叠面板
        accordion: true, // 手风琴模式
        groups: {
            base: {
                title: '基本信息',
                columns: ['field1', 'field2']
            },
            advanced: {
                title: '高级设置',
                collapsed: true, // 默认折叠
                columns: ['field3', 'field4']
            }
        }
    }
},
```

## 工具栏按钮配置

```typescript
actionbar: {
    buttons: {
        add: {
            show: auth('permission:Create'),
            text: '新增',
            icon: 'Plus',
        },
        export: {
            text: '导出',
            title: '导出数据',
            show: auth('permission:Export'),
            click() {
                return exportRequest(crudExpose!.getSearchFormData());
            },
        },
        import: {
            text: '导入',
            title: '导入数据',
            show: auth('permission:Import'),
            click() {
                // 处理导入逻辑
            },
        },
    },
},
```

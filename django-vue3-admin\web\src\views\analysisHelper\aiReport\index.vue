<template>
	<div class="page-ai-report">
		<!-- 搜索区域 -->
		<div class="search-container">
			<el-form ref="searchFormRef" :model="searchForm" inline>
				<el-form-item label="报告标题">
					<el-input v-model="searchForm.title" placeholder="请输入报告标题" clearable style="width: 200px" />
				</el-form-item>
				<el-form-item label="ID">
					<el-input v-model="searchForm.ids" placeholder="请输入ID" clearable style="width: 200px" />
				</el-form-item>
				<el-form-item label="创建时间">
					<el-date-picker
						v-model="dateRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD"
						style="width: 240px"
						clearable
					/>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="handleSearch" :loading="loading">
						<el-icon><Search /></el-icon>
						搜索
					</el-button>
					<el-button @click="handleReset">
						<el-icon><Refresh /></el-icon>
						重置
					</el-button>
				</el-form-item>
			</el-form>
		</div>

		<!-- 标签页 -->
		<div class="tabs-container">
			<common-tabs v-model="currentTab" :items="platformItems" @change="handleTabChange" :size="'large'" />
		</div>

		<!-- 表格 -->
		<div class="table-container">
			<el-table :data="tableData" v-loading="loading" stripe border style="width: 100%">
				<el-table-column prop="id" label="ID" align="center" width="80" />
				<el-table-column prop="title" label="报告标题" align="center" min-width="300" />
				<el-table-column prop="model_name" label="AI模型" align="center" width="120">
					<template #default="scope">
						<el-tag type="success" size="small">
							{{ scope.row.model_name }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="create_time" label="创建时间" align="center" width="180" />
				<el-table-column prop="update_time" label="更新时间" align="center" width="180" />
				<el-table-column label="操作" align="center" width="300" fixed="right">
					<template #default="scope">
						<el-button v-if="hasPermission('AiReport:Retrieve')" link type="primary" @click="handleView(scope.row)"> 查看详情 </el-button>
						<el-button v-if="hasPermission('AiReport:Update')" link type="primary" @click="handleEdit(scope.row)"> 编辑报告内容 </el-button>
						<el-button
							v-if="reportId && hasPermission('AiReport:Use')"
							link
							:type="scope.row.is_used ? 'info' : 'success'"
							:disabled="scope.row.is_used"
							@click="handleUse(scope.row)"
						>
							{{ scope.row.is_used ? '已使用' : '使用报表' }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination
					v-model:current-page="pagination.page"
					v-model:page-size="pagination.limit"
					:total="pagination.total"
					:page-sizes="[10, 20, 50, 100]"
					layout="total, sizes, prev, pager, next, jumper"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</div>

		<!-- 编辑对话框 -->
		<ReportEditDialog ref="editDialogRef" @saved="handleRefresh" />

		<!-- 查看对话框 -->
		<ReportViewDialog ref="viewDialogRef" />
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import {
	ElForm,
	ElFormItem,
	ElInput,
	ElDatePicker,
	ElButton,
	ElIcon,
	ElTable,
	ElTableColumn,
	ElTag,
	ElPagination,
	ElMessage,
	ElMessageBox,
} from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { auth } from '/@/utils/authFunction';
import { AI_REPORT_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions, TabItem } from '/@/utils/platformTabsHelper';
import * as api from './api';
import type { AiReport, AiReportListParams } from './api';

// 动态导入组件
const CommonTabs = defineAsyncComponent(() => import('/@/components/CommonTabs/index.vue'));
const ReportEditDialog = defineAsyncComponent(() => import('./components/ReportEditDialog.vue'));
const ReportViewDialog = defineAsyncComponent(() => import('./components/ReportViewDialog.vue'));

// 路由信息
const router = useRouter();
const route = router.currentRoute.value;
const aiReportIds = route.query.ids as string;
const reportId = route.query.operation_report_id as string;

// 响应式数据
const loading = ref(false);
const currentTab = ref('wechat');
const platformItems = ref<TabItem[]>([]);
const tableData = ref<AiReport[]>([]);
const searchFormRef = ref();
const editDialogRef = ref();
const viewDialogRef = ref();

// 单独的日期范围字段
const dateRange = ref<string[]>([]);

// 搜索表单
const searchForm = reactive<AiReportListParams>({
	platform_type: currentTab.value,
	ids: aiReportIds || '',
	title: '',
});

// 分页数据
const pagination = reactive({
	page: 1,
	limit: 10,
	total: 0,
});

// 权限检查
const hasPermission = (permission: string) => {
	return auth(permission);
};


// 获取平台类型数据
const loadPlatformTypes = () => {
	platformItems.value = generatePlatformTabsWithPermissions(AI_REPORT_PERMISSIONS);
};

// 获取列表数据
const getList = async () => {
	loading.value = true;
	try {
		const params: any = {
			page: pagination.page,
			limit: pagination.limit,
			platform_type: searchForm.platform_type,
			ids: searchForm.ids,
			title: searchForm.title,
		};

		// 处理日期范围
		if (dateRange.value && dateRange.value.length === 2) {
			params.start_date = dateRange.value[0];
			params.end_date = dateRange.value[1];
		}

		const res = await api.getList(params);
		tableData.value = res.data;
		pagination.total = res.total;
	} catch (error: any) {
		ElMessage.error(error.message || '获取列表失败');
	} finally {
		loading.value = false;
	}
};

// Tab变更处理
const handleTabChange = (value: string) => {
	currentTab.value = value;
	searchForm.platform_type = value;
	pagination.page = 1;
	getList();
};

// 搜索
const handleSearch = () => {
	pagination.page = 1;
	getList();
};

// 重置搜索
const handleReset = () => {
	searchFormRef.value?.resetFields();
	searchForm.ids = aiReportIds || '';
	searchForm.title = '';
	dateRange.value = [];
	pagination.page = 1;

	// 重置时清空路由参数中的id
	const currentQuery = { ...route.query };
	delete currentQuery.ai_report_ids;
	router.replace({ query: currentQuery });

	getList();
};

// 分页处理
const handleSizeChange = (val: number) => {
	pagination.limit = val;
	pagination.page = 1;
	getList();
};

const handleCurrentChange = (val: number) => {
	pagination.page = val;
	getList();
};

// 刷新数据
const handleRefresh = () => {
	getList();
};

// 查看详情
const handleView = (row: AiReport) => {
	viewDialogRef.value?.open(row);
};

// 编辑报告
const handleEdit = (row: AiReport) => {
	editDialogRef.value?.open(row);
};

// 使用报表
const handleUse = async (row: AiReport) => {
	try {
		await ElMessageBox.confirm(`确定要使用报表"${row.title}"吗？使用后将会应用该报表的内容。`, '确认使用报表', {
			confirmButtonText: '确定使用',
			cancelButtonText: '取消',
			type: 'warning',
		});

		if (reportId) {
			await api.useReport(row.id.toString(), reportId);
			ElMessage.success('报表使用成功');
			getList();
		} else {
			ElMessage.error('缺少运营报表ID');
		}
	} catch (error: any) {
		if (error !== 'cancel') {
			ElMessage.error(error.message || '使用报表失败');
		}
	}
};

// 页面初始化
onMounted(() => {
	loadPlatformTypes();
	getList();
});
</script>

<style lang="scss" scoped>
.page-ai-report {
	height: 100vh;
	display: flex;
	flex-direction: column;

	.search-container {
		padding: 16px 20px 8px 20px;
		margin-bottom: 8px;
	}

	.tabs-container {
		padding: 0 20px;
		margin-bottom: 16px;
	}

	.table-container {
		flex: 1;
		padding: 0 20px;

		.el-table {
			width: 100% !important;
		}

		.pagination-container {
			display: flex;
			margin: 20px 0;
		}
	}

	:deep(.el-tag) {
		margin: 0;
		padding: 0;
	}
}
</style> 
import { request } from '/@/utils/service';
import { Session, Local } from '/@/utils/storage';

export interface WechatOfficialAccountType {
	id: number;
	nick_name: string;
	wechat_id: string;
	wechat_original_id: string;
	unique_id: string;
	logo_url: string;
	original_article_count: number;
	ip_word: string;
	crawled_time_ranges: string;
	is_earliest_crawled: number;
	crawl_priority: number;
	status: number;
	create_time: string;
	update_time: string;
	delete_time: string;
}

export interface WeiboAccountType {
	id: number;
	nickname: string;
	account_id: string;
	avatar_url: string;
	description: string;
	fans_count: number;
	follow_count: number;
	article_count: number;
	is_verified: number;
	verified_reason: string;
	crawl_priority: number;
	status: number;
	create_time: string;
	update_time: string;
}

export interface DouyinAccountType {
	id: number;
	nickname: string;
	account_id: string;
	avatar_url: string;
	description: string;
	fans_count: number;
	like_count: number;
	video_count: number;
	is_verified: number;
	verified_reason: string;
	crawl_priority: number;
	status: number;
	create_time: string;
	update_time: string;
}

export interface AccountListParams {
	page?: number;
	limit?: number;
	status?: number;
	wechat_id?: string;
	nick_name?: string;
	platform_type?: string;
}

export const apiPrefix = '/api/wechat_official_account/accounts/';
export const weiboApiPrefix = '/api/weibo/accounts/';
export const douyinApiPrefix = '/api/douyin/accounts/';

export async function getWechatOfficialAccountList(query: AccountListParams) {
	// 确保platform_type参数被正确传递
	const platform_type = query.platform_type || 'wechat';
	console.log(`请求列表数据，平台类型: ${platform_type}`);

	return await request({
		url: apiPrefix,
		method: 'get',
		params: {
			...query,
			platform_type,
		},
	});
}

export async function createWechatOfficialAccount(obj: Partial<WechatOfficialAccountType>) {
	return await request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

export async function updateWechatOfficialAccount(obj: Partial<WechatOfficialAccountType>) {
	return await request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export async function deleteWechatOfficialAccount(id: number) {
	return await request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}

// 微博账号接口
export async function createWeiboAccount(obj: Partial<WeiboAccountType>) {
	return await request({
		url: weiboApiPrefix,
		method: 'post',
		data: obj,
	});
}

export async function updateWeiboAccount(obj: Partial<WeiboAccountType>) {
	return await request({
		url: weiboApiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export async function deleteWeiboAccount(id: number) {
	return await request({
		url: weiboApiPrefix + id + '/',
		method: 'delete',
	});
}

// 抖音账号接口
export async function createDouyinAccount(obj: Partial<DouyinAccountType>) {
	return await request({
		url: douyinApiPrefix,
		method: 'post',
		data: obj,
	});
}

export async function updateDouyinAccount(obj: Partial<DouyinAccountType>) {
	return await request({
		url: douyinApiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export async function deleteDouyinAccount(id: number) {
	return await request({
		url: douyinApiPrefix + id + '/',
		method: 'delete',
	});
}

// 获取微信账号列表
export async function getAccountList() {
	return await request({
		url: apiPrefix + 'select_list/?status=0',
		method: 'get',
	});
}
// 获取微博账号列表
export async function getWeiboAccountList() {
	return await request({
		url: weiboApiPrefix + 'select_list/?status=0',
		method: 'get',
	});
}
// 获取抖音账号列表
export async function getDouyinAccountList() {
	return await request({
		url: douyinApiPrefix + 'select_list/?status=0',
		method: 'get',
	});
}
// 添加抓取数据的API接口函数
export async function crawlWechatOfficialAccountData(account_id: number, start_date: string, end_date: string) {
	return await request({
		url: apiPrefix + 'crawl_articles/',
		method: 'get',
		params: {
			account_id,
			start_date,
			end_date,
		},
	});
}

// 批量抓取微信公众号数据的API接口函数
export async function batchCrawlWechatOfficialAccountData(account_ids: string, start_date: string, end_date: string) {
	return await request({
		url: apiPrefix + 'crawl_articles_batch/',
		method: 'get',
		params: {
			account_ids,
			start_date,
			end_date,
		},
	});
}

// 微博抓取数据的API接口函数
export async function crawlWeiboAccountData(account_id: number, start_date: string, end_date: string) {
	return await request({
		url: weiboApiPrefix + 'crawl_by_time_range/',
		method: 'get',
		params: {
			account_ids: account_id.toString(),
			start_date,
			end_date,
		},
	});
}

// 批量抓取微博数据的API接口函数
export async function batchCrawlWeiboAccountData(account_ids: string, start_date: string, end_date: string) {
	return await request({
		url: weiboApiPrefix + 'crawl_by_time_range/',
		method: 'get',
		params: {
			account_ids,
			start_date,
			end_date,
		},
	});
}

// 抖音抓取数据的API接口函数
export async function crawlDouyinAccountData(account_id: number, start_date: string, end_date: string) {
	return await request({
		url: douyinApiPrefix + 'crawl_video_by_time_range/',
		method: 'get',
		params: {
			account_ids: account_id.toString(),
			start_date,
			end_date,
		},
	});
}

// 批量抓取抖音数据的API接口函数
export async function batchCrawlDouyinAccountData(account_ids: string, start_date: string, end_date: string) {
	return await request({
		url: douyinApiPrefix + 'crawl_video_by_time_range/',
		method: 'get',
		params: {
			account_ids,
			start_date,
			end_date,
		},
	});
}
const batchAccountsApiPrefix = '/api/wechat_official_account/batch-accounts/';
// 下载导入模板
export async function downloadImportTemplate() {
	return await request({
		url: batchAccountsApiPrefix + 'download_import_template/',
		method: 'get',
		responseType: 'blob',
	});
}

// 批量添加账号
export async function batchImportAccount(data: any) {
	const token = Session.get('token');

	return await request({
		url: batchAccountsApiPrefix + 'batch_import_account/',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'multipart/form-data',
			...(token && { Authorization: 'JWT ' + token }),
		},
	});
}

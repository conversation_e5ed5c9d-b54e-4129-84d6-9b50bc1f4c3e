<template>
	<fs-page class="PageTenantMediaAccounts">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #actionbar-left>
				<common-tabs v-model="currentTab" :items="platformItems" @change="handleTabChange" />
			</template>
			<template #actionbar-right>
				<div v-auth="'TenantMediaAccounts:SetOperatingType'">
					<fs-button style="margin-left: 20px" type="primary" :disabled="selectedIds.length === 0" @click="handleBatchSetOperatingType">
						批量设置运营类型 ({{ selectedIds.length }})
					</fs-button>
				</div>
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, defineAsyncComponent, nextTick } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { useRoute, useRouter } from 'vue-router';
import { TabItem } from '/@/utils/platformTabsHelper';

// 动态导入CommonTabs组件
const CommonTabs = defineAsyncComponent(() => import('/@/components/CommonTabs/index.vue'));

export default defineComponent({
	name: 'TenantMediaAccounts',
	components: {
		CommonTabs,
	},
	setup() {
		// 获取路由实例
		const route = useRoute();
		const router = useRouter();

		// 当前选中的Tab，优先使用platform_type参数，默认为'wechat'
		const currentTab = ref((route.query.platform_type as string) || 'wechat');
		// 定义当前平台类型
		const currentPlatformType = ref(currentTab.value);
		// 获取路由中的tenant_id
		const tenantId = route.query.tenant_id ? Number(route.query.tenant_id) : undefined;
		// 平台类型选项数据
		const platformItems = ref<TabItem[]>([]);
		// 选中的账号ID数组
		const selectedIds = ref<number[]>([]);

		// 获取 useFs 返回的对象
		const { crudRef, crudBinding, crudExpose } = useFs({
			createCrudOptions: createCrudOptions,
			context: {
				tenantId: tenantId,
				currentPlatformType: currentPlatformType,
				onSelectionChange: (changed: any) => {
					selectedIds.value = changed.map((item: any) => item.media_account_id);
				},
			},
		});

		// 生成权限控制的标签页
		const initializePlatformTabs = () => {
			// 先尝试简单的手动配置，确保tab能显示
			platformItems.value = [
				{ label: '微信', value: 'wechat', permission: 'TenantMediaAccounts:WechatView' },
				{ label: '抖音', value: 'douyin', permission: 'TenantMediaAccounts:DouyinView' },
				{ label: '微博', value: 'weibo', permission: 'TenantMediaAccounts:WeiboView' },
			];
		};

		// Tab切换处理
		const handleTabChange = (tab: string) => {
			console.log('切换Tab:', tab, '当前值:', currentPlatformType.value);

			// 如果tab没有变化，则不进行任何操作
			if (currentPlatformType.value === tab) {
				console.log('Tab没有变化，跳过处理');
				return;
			}

			console.log('Tab确实发生了变化，只更新状态不调用接口');
			currentTab.value = tab;
			currentPlatformType.value = tab;

			// 更新路由参数
			const query = { ...route.query };
			query.platform_type = tab;
			router.replace({ query });

			// 不在这里调用doRefresh()，让用户手动刷新或通过其他方式触发
			console.log('平台已切换到:', tab, '，如需查看数据请手动刷新');
		};

		// 批量设置运营类型处理
		const handleBatchSetOperatingType = () => {
			if (selectedIds.value.length === 0) {
				return;
			}

			// 直接调用crud中导出的批量设置方法
			const crudOptions = createCrudOptions({
				crudExpose,
				context: {
					tenantId: tenantId,
					currentPlatformType: currentPlatformType,
				},
			});
			if (crudOptions.handleBatchSetOperatingType) {
				crudOptions.handleBatchSetOperatingType(selectedIds.value);
			}
		};

		// 组件挂载时初始化
		onMounted(async () => {
			initializePlatformTabs();
			// 使用 nextTick 确保组件完全初始化后再调用刷新
			await nextTick();
			// 延迟一下确保 fast-crud 完全准备好
			setTimeout(() => {
				console.log('开始初始化数据加载，平台类型:', currentPlatformType.value);
				crudExpose.doRefresh();
			}, 100);
		});

		return {
			crudRef,
			crudBinding,
			crudExpose,
			currentTab,
			currentPlatformType,
			platformItems,
			selectedIds,
			tenantId,
			handleTabChange,
			handleBatchSetOperatingType,
		};
	},
});
</script>

<style scoped>
.PageTenantMediaAccounts {
	height: 100%;
}
</style> 
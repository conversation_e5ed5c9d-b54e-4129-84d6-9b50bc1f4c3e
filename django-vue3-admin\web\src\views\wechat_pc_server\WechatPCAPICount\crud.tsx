import { <PERSON>rudO<PERSON>s, AddReq, DelReq, EditReq, Crud<PERSON>xpose, <PERSON>rPageQuery, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { request } from '/@/utils/service'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag } from 'element-plus'

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    const pageRequest = async (query: any) => {
        return await api.getWechatPCAPICountList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateWechatPCAPICount(form)
    }
    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteWechatPCAPICount(row.id)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createWechatPCAPICount(form)
    }

    // 获取微信账号列表
    const getWechatPCAccounts = async () => {
        const res = await request({
            url: '/api/wechat_pc_server/wechat_pc_account/',
            method: 'get'
        })
        return res.data.map((item: any) => {
            return {
                value: item.wechat_id,
                label: `${item.nick_name}(${item.wechat_id})`
            }
        })
    }

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('WechatPCAPICount:Create')
                    }
                }
            },
            rowHandle: {
                width: 260,
                buttons: {
                    edit: {
                        show: auth('WechatPCAPICount:Update')
                    },
                    remove: {
                        show: auth('WechatPCAPICount:Delete')
                    }
                }
            },
            search: {
                columns: {
                    date: {
                        title: '日期范围',
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'daterange',
                                valueFormat: 'YYYY-MM-DD',
                                rangeSeparator: '至',
                                startPlaceholder: '开始日期',
                                endPlaceholder: '结束日期'
                            }
                        },
                        valueBuilder(row: any, col: any) {
                            if (row.date_start && row.date_end) {
                                row.date = [row.date_start, row.date_end]
                            }
                        },
                        onValueChange(row: any, col: any) {
                            if (row.date && row.date.length === 2) {
                                row.date_start = row.date[0]
                                row.date_end = row.date[1]
                            } else {
                                row.date_start = null
                                row.date_end = null
                            }
                        }
                    }
                }
            },
            columns: {
                id: {
                    title: 'ID',
                    type: 'number',
                    form: { show: false },
                    column: { width: 80 }
                },
                wechat_pc_account: {
                    title: '微信账号',
                    type: 'text',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '微信账号必填' }]
                    }
                },
                wechat_pc_account_nick_name: {
                    title: '微信昵称',
                    type: 'text',
                    column: { width: 120 },
                    form: { show: false }
                },
                business_type: {
                    title: '业务类型',
                    type: 'select',
                    search: { show: true },
                    form: {
                        component: {
                            options: [
                                { value: 0, label: '获取历史文章' },
                                { value: 1, label: '获取文章五维数据' }
                            ]
                        },
                        rules: [{ required: true, message: '业务类型必填' }]
                    }
                },
                date: {
                    title: '日期',
                    type: 'date',
                    form: {
                        rules: [{ required: true, message: '日期必填' }]
                    }
                },
                count: {
                    title: '请求次数',
                    type: 'number',
                    form: {
                        rules: [{ required: true, message: '请求次数必填' }]
                    }
                }
            }
        }
    }
} 
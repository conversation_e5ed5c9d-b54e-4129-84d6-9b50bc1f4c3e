# Website模块问题收集与任务规划文档

## 🎯 问题收集目标

根据用户要求："整理记录当前所有的问题。注意，是所有的，做成一个接下来的任务规划文档，这一阶段只去收集所有的问题，注意尽可能多地收集问题"

## 🔍 已发现的问题详细清单

### 🚨 严重问题（阻止功能使用）

#### 1. ~~页面路由问题~~ ✅ 已修复
- ~~**问题**: Column栏目管理页面访问 `http://localhost:8080/#/website/column` 返回404错误~~
- ~~**影响**: 栏目管理功能完全不可用~~
- **状态**: 已修复，页面可以正常访问

#### 2. ~~规则管理页面问题~~ ✅ 已修复
- ~~**问题**: Rule规则管理页面访问 `http://localhost:8080/#/website/rule` 返回404错误~~
- ~~**影响**: 规则管理功能完全不可用~~
- **状态**: 已修复，页面可以正常访问

#### 3. 站点页面路径混淆
- **问题**: 存在 `/website/site` (404) 和 `/website/sites` (正常) 两个路径
- **影响**: 可能导致用户访问错误的URL
- **截图**: `site_page_problem_check.png` (404) vs `sites_page_problem_check.png` (正常)
- **需要确认**: 哪个是正确的URL路径

### ⚠️ 中等问题（功能异常）

#### 4. Content内容管理页面状态显示问题
- **问题**: 提取状态列显示英文原始值 `extracted`, `pending`, `failed` 而不是中文标签 `已提取`, `待提取`, `提取失败`
- **影响**: 用户体验不佳，不符合国际化要求
- **技术分析**: 
  - dict-select组件配置的字典数组正确
  - 问题可能在后端数据格式或前端数据处理
- **列宽问题**: 状态列宽度不足导致文字截断显示为"extr..."

#### 5. Content页面表格数据显示异常
- **问题**: Content页面有统计面板显示（0个各类状态），但表格区域可能存在数据加载问题
- **截图**: `content_page_problem_check.png` - 只显示统计面板和快速操作区域
- **需要验证**: 
  - 表格是否正确显示数据
  - 分页是否正常工作
  - 搜索筛选功能是否生效

### 📝 潜在问题（需要验证）

#### 6. Sites站点管理页面的深度功能
- **状态**: 基础列表显示正常（有52条数据）
- **需要验证的功能**:
  - 添加站点功能
  - 编辑站点功能  
  - 删除站点功能
  - 批量操作功能
  - 搜索筛选功能
  - 状态字典显示是否正确

#### 7. 通用技术问题
- **API数据格式**: 需要验证所有页面的API响应是否符合预期格式
- **权限控制**: 操作按钮的权限控制是否正确生效
- **表单验证**: 添加/编辑表单的验证规则是否完整
- **错误处理**: 网络错误和业务错误的提示是否合适

#### 8. Fast-CRUD配置问题
- **字典组件**: 其他可能存在类似status字段的dict-select配置问题
- **格式化函数**: 可能存在其他formatter返回HTML字符串的问题
- **自动加载**: 各页面的`crudExpose.doRefresh()`自动加载是否正确配置

### 🔧 开发环境问题

#### 9. 路由动态加载问题
- **观察**: 从backEnd.ts代码看，使用了动态路由加载机制
- **潜在风险**: 
  - component路径匹配可能存在问题
  - 动态导入的组件文件可能不存在
  - 菜单数据和实际组件文件不匹配

#### 10. 菜单数据同步问题
- **问题**: Column和Rule页面404可能表示后端菜单数据与前端组件不同步
- **需要检查**:
  - 后端菜单表中是否有对应记录
  - 菜单记录的component路径是否正确
  - 前端views目录下是否有对应的组件文件

## 📋 详细任务规划

### Phase 1: 紧急问题修复（高优先级）

#### 1.1 路由问题解决
- [ ] **检查Column页面组件文件**
  - 确认 `src/views/website/column/index.vue` 文件是否存在
  - 检查组件文件是否有语法错误
  - 验证import路径是否正确

- [ ] **检查Rule页面组件文件**
  - 确认 `src/views/website/rule/index.vue` 文件是否存在
  - 检查组件文件是否有语法错误
  - 验证import路径是否正确

- [ ] **检查后端菜单注册**
  - 确认数据库中website相关菜单记录
  - 验证菜单记录的component字段值
  - 检查菜单的权限和状态设置

- [ ] **修复路径混淆问题**
  - 确定正确的URL路径规范（site vs sites）
  - 统一所有相关引用和链接
  - 更新文档说明

#### 1.2 Content页面状态显示修复
- [ ] **分析数据流程**
  - 检查API响应的实际数据格式
  - 验证dict-select组件的数据处理逻辑
  - 确认transformRes全局配置是否影响字典显示

- [ ] **修复状态映射**
  - 确保后端返回的status值与前端字典配置匹配
  - 修复列宽问题，确保状态文字完整显示
  - 测试各种状态值的正确显示

### Phase 2: 功能完整性验证（中优先级）

#### 2.1 Sites站点管理全面测试
- [ ] **CRUD操作验证**
  - 测试添加站点功能
  - 测试编辑站点功能
  - 测试删除站点功能
  - 验证批量操作功能

- [ ] **数据显示验证**
  - 确认所有字段正确显示
  - 测试搜索筛选功能
  - 验证分页功能
  - 检查排序功能

#### 2.2 Content内容管理深度测试
- [ ] **表格功能验证**
  - 确认数据列表正确加载
  - 测试分页切换功能
  - 验证搜索筛选功能
  - 检查批量操作功能

- [ ] **业务操作测试**
  - 测试内容提取功能
  - 验证状态变更操作
  - 检查权限控制效果
  - 测试导出功能

### Phase 3: 技术债务清理（低优先级）

#### 3.1 代码质量优化
- [ ] **Fast-CRUD配置标准化**
  - 检查所有dict-select配置
  - 移除可能存在的HTML formatter
  - 统一组件配置风格
  - 完善注释和文档

#### 3.2 用户体验改进
- [ ] **界面细节优化**
  - 调整列宽和显示效果
  - 优化加载状态显示
  - 改善错误提示信息
  - 统一操作按钮样式

#### 3.3 性能和稳定性
- [ ] **系统稳定性测试**
  - 验证大数据量加载
  - 测试并发操作
  - 检查内存泄漏
  - 优化API调用频率

## 🎯 问题优先级排序

### 🔴 立即处理（阻塞性问题）
1. Column栏目管理页面404错误
2. Rule规则管理页面404错误
3. 站点页面路径混淆问题

### 🟡 接下来处理（功能异常）
1. Content页面状态字典显示问题
2. Content页面表格数据显示验证
3. Sites页面深度功能测试

### 🟢 最后优化（体验改进）
1. 代码质量标准化
2. 界面细节优化
3. 性能和稳定性改进

## 📊 问题分类统计

- **路由配置问题**: 3个（Column 404, Rule 404, 路径混淆）
- **数据显示问题**: 2个（状态字典, 表格显示）
- **功能验证需求**: 2个（Sites深度测试, Content深度测试）
- **技术债务**: 3个（代码标准化, 界面优化, 性能改进）

**总计**: 10个主要问题领域，需要系统性解决

## 📝 执行建议

### 执行原则
1. **先解决阻塞性问题**：确保所有页面都能正常访问
2. **再修复功能异常**：确保基本功能正常工作
3. **最后进行优化**：提升用户体验和代码质量

### 资源分配建议
- **紧急问题修复**: 60%工作量（预计2-3天）
- **功能完整性验证**: 30%工作量（预计1-2天）  
- **技术债务清理**: 10%工作量（预计0.5-1天）

### 质量控制
- 每个问题修复后都要进行回归测试
- 使用浏览器自动化验证修复效果
- 更新相关文档和规范
- 记录解决方案供后续参考

---

**文档创建时间**: 2025-08-07  
**问题收集范围**: Website模块全部页面  
**下一步**: 等待用户确认优先级，开始问题修复工作
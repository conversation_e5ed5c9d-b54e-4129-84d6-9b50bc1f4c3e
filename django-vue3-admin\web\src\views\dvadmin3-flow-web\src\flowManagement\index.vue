<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell_icon="scope" style="text-align: center">
				<div style="text-align: center; margin-left: 25px">
					<iconify :icon="scope.row.icon.name" :style="{ background: scope.row.icon.bgc, color: scope.row.icon.color }" height="20" />
				</div>
			</template>
			<template #actionbar-left>
				<el-button type="primary" @click="onCreate" v-auth="'flowManagement:Create'">新建流程</el-button>
			</template>
			<template #cell-rowHandle-left="scope">
				<el-button type="primary" text @click="onView(scope)"
					>查看
					<el-icon>
						<View />
					</el-icon>
				</el-button>
				<el-button type="primary" v-auth="'flowManagement:Update'" text @click="onEdit(scope)"
					>编辑
					<el-icon>
						<EditPen />
					</el-icon>
				</el-button>
			</template>
		</fs-crud>
		<flowProcess v-if="processShow" v-model="processShow" :mainId="mainId" @close="closeClick"></flowProcess>
		<flow-designer v-if="designerShow" v-model="designerShow" :mainId="mainId" :tempData="tempNewFlowData" @close="closeClick"></flow-designer>
	</fs-page>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
import { useRouter } from 'vue-router';
import nodeType from '../wflow/design/process/ProcessNodes.js';
import { Icon as iconify } from '@iconify/vue';
import flowProcess from './components/flowProcess.vue';
import FlowDesigner from './components/flowDesigner.vue';

const router = useRouter();
const processShow = ref(false);
const designerShow = ref(false);
const mainId = ref();
const tempNewFlowData = ref<any>(null); // 用于存储新建流程的临时数据

const onCreate = () => {
	// 创建临时流程数据，用于弹窗模式
	const tempFlowData = {
		name: '未命名流程',
		icon: {
			name: 'file-icons:omnigraffle',
			bgc: '#4C87F3',
			color: '#FFFFFF',
		},
		groupId: null,
		content_type: null,
		operation: null,
		formConf: {
			conf: {
				labelPosition: 'right', //标签位置
				labelWidth: 100, //标签宽度，
				size: 'default',
			},
			components: [],
		},
		//流程json
		process: [nodeType.Start.create()],
		remark: null,
		isNew: true, // 标识这是新建流程
	};

	// 设置临时数据并打开弹窗
	tempNewFlowData.value = tempFlowData;
	mainId.value = null; // 新增时设为null
	designerShow.value = true;
};

const onEdit = ({ row }) => {
	const { id } = row;
	console.log(row,'row');
	mainId.value = id;
	designerShow.value = true;
	// 清除临时数据，确保使用服务器数据
	tempNewFlowData.value = null;
};

const onView = ({ row }) => {
	const { id } = row;
	mainId.value = id;
	processShow.value = true;
};
const closeClick = () => {
	console.log(8888);
	refreshList();
};

// 独立的刷新列表方法
const refreshList = () => {
	console.log('刷新列表');
	crudExpose.doRefresh();
};

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>

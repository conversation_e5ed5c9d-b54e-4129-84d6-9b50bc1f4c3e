<template>
	<div class="full" style="text-align: center">
		<div ref="chartRef" class="chart" style="height: 100%; width: 100%; padding-top: 20px" />
	</div>
</template>

<script setup>
import { shallowRef, computed, onMounted, watch, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
	data: {
		type: Object,
		default: () => ({}),
	},
	lineColor: {
		type: Array,
		default: () => ['#5d5dc4', '#00FFFF', '#c45d5d', '#FF7F00', '#00FF00', '#FF0000', '#FF1493', '#3CB371', '#818176', '#0000FF'],
	},
	renderer: {
		type: String,
		default: 'canvas',
	},
	yAxis: {
		type: Array,
		default: () => [],
	},
	axisFontSize: {
		type: Number,
		default: 12,
	},
	legendFontSize: {
		type: Number,
		default: 12,
	},
	axisNameFontSize: {
		type: Number,
		default: 14,
	},
});

const chartRef = shallowRef(null);
const myChart = shallowRef(null);

const legendData = computed(() => {
	if (props.data.row?.length > 0) {
		return props.data.row.map((item) => ({
			name: item.name || '',
		}));
	}
	return [];
});

// 默认的样式配置
const defaultStyles = {
	line: {
		itemStyle: {
			color: '#3bbc86',
		},
		areaStyle: {
			color: {
				type: 'linear',
				x: 0,
				y: 0,
				x2: 0,
				y2: 1,
				colorStops: [
					{
						offset: 0,
						color: 'rgba(199, 237, 250,0.5)',
					},
					{
						offset: 1,
						color: 'rgba(199, 237, 250,0.2)',
					},
				],
			},
		},
		showSymbol: true,
		symbol: 'circle',
		symbolSize: 8,
		smooth: true,
	},
	bar: {
		type: 'bar',
		barWidth: 30,
		itemStyle: {
			color: {
				type: 'linear',
				x: 0,
				y: 0,
				x2: 0,
				y2: 1,
				colorStops: [
					{
						offset: 0,
						color: 'rgba(108,80,243,0.3)',
					},
					{
						offset: 1,
						color: 'rgba(108,80,243,0)',
					},
				],
			},
			borderRadius: [30, 30, 0, 0],
		},
	},
};

const defaultAxisStyle = {
	type: 'value',
	nameLocation: 'middle',
	nameTextStyle: {
		padding: [3, 4, 50, 6],
		fontSize: props.axisNameFontSize,
	},
	axisLabel: {
		fontSize: props.axisFontSize,
		color: '#666',
	},
	splitLine: {
		show: true,
		lineStyle: {
			type: 'dashed',
			color: '#f5f5f5',
		},
	},
	axisLine: { show: false },
	axisTick: { show: false },
};

const computedYAxis = computed(() => {
	if (!props.data.row?.length)
		return [
			{
				...defaultAxisStyle,
			},
		];

	return props.data.row.map((item, index) => ({
		...defaultAxisStyle,
		name: props.data.row.length > 1 ? item.name : '', // 如果只有一项的话，则不显示
		nameTextStyle: {
			padding: index === 0 ? [3, 4, 50, 6] : [50, 4, 5, 6],
			fontSize: props.axisNameFontSize,
		},
		axisLabel: {
			fontSize: props.axisFontSize,
			color: '#666',
		},
		splitLine: {
			show: index === 0,
			lineStyle: {
				type: 'dashed',
				color: '#f5f5f5',
			},
		},
		position: index === 1 ? 'right' : 'left',
	}));
});

const chatData = computed(() => {
	if (props.data.row?.length > 0) {
		return props.data.row.map((res) => {
			const type = res.type || 'line';
			const defaultStyle = defaultStyles[type];

			return {
				...defaultStyle,
				type,
				name: res.name,
				data: res.counts,
				yAxisIndex: type === 'bar' ? 1 : 0,
				// 允许覆盖默认样式
				...(res.itemStyle ? { itemStyle: res.itemStyle } : {}),
				...(res.areaStyle ? { areaStyle: res.areaStyle } : {}),
			};
		});
	}
	return [];
});

// 计算数据的最大值，并返回合适的最大刻度值和间隔
const calculateAxisRange = (data) => {
	if (!data || data.length === 0) return { max: 100, interval: 20 };

	const maxValue = Math.max(...data);
	// 将最大值向上取整到最接近的整数
	const roundedMax = Math.ceil(maxValue);

	// 计算一个合适的刻度间隔（将范围分为5-10个刻度）
	let interval = Math.ceil(roundedMax / 5); // 初始尝试将范围分为5份

	// 将间隔调整为"整齐"的数字
	const magnitude = Math.pow(10, Math.floor(Math.log10(interval)));
	const normalized = interval / magnitude;

	if (normalized <= 1) interval = magnitude;
	else if (normalized <= 2) interval = 2 * magnitude;
	else if (normalized <= 5) interval = 5 * magnitude;
	else interval = 10 * magnitude;

	// 计算最终的最大值（确保能被间隔整除）
	const finalMax = Math.ceil(roundedMax / interval) * interval;

	return {
		max: finalMax,
		interval: interval,
	};
};

const initChart = () => {
	if (!chartRef.value) return;

	// 销毁之前的实例
	if (myChart.value) {
		myChart.value.dispose();
	}

	// 创建新实例
	myChart.value = echarts.init(chartRef.value, null, { renderer: props.renderer });

	const option = {
		tooltip: {
			trigger: 'axis',
		},
		grid: {
			top: 70,
			right: 80,
			bottom: 30,
			left: 80,
			containLabel: true,
		},
		color: props.lineColor,
		legend: {
			data: props.data.row?.map((item) => item.name) || [],
			orient: 'horizontal',
			top: 0,
			left: 'center',
			type: 'scroll',
			textStyle: {
				fontSize: props.legendFontSize,
				color: '#666',
			},
		}, // 调整图例位置,垂直居中
		xAxis: {
			type: 'category',
			boundaryGap: true,
			axisTick: { show: false },
			axisLabel: {
				fontSize: props.axisFontSize,
				color: '#666',
			},
			data: props.data.dates,
		},
		yAxis: props.yAxis.length > 0 ? props.yAxis : computedYAxis.value,
		series: chatData.value,
	};

	myChart.value.setOption(option);
};

// 监听数据变化
watch(
	() => props.data,
	() => {
		initChart();
	},
	{ deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
	if (myChart.value) {
		myChart.value.resize();
	}
};

onMounted(() => {
	initChart();
	window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
	if (myChart.value) {
		myChart.value.dispose();
		myChart.value = null;
	}
	window.removeEventListener('resize', handleResize);
});
</script>
<style scoped></style>

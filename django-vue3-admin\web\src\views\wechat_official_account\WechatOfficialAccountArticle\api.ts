import { request } from '/@/utils/service'

export interface WechatOfficialAccountArticleType {
  id: string
  wechat_official_account: number
  title: string
  url: string
  idx: number
  read_count: number
  viewing_count: number
  like_count: number
  forward_count: number
  reward_count: number
  is_original: boolean
  issue_time: string
  create_time: string
  update_time: string
  delete_time: string
}

export interface WechatOfficialAccountArticleListParams {
  page?: number
  limit?: number
  title?: string
  wechat_official_account?: number
  wechat_unique_id?: number
  is_original?: boolean
  platform_type?: string
  export_columns?: string
  account_nickname?: string
  wechat_official_account_name?: string
}

export const apiPrefix = '/api/wechat_official_account/articles/'

export async function getWechatOfficialAccountArticleList(query: WechatOfficialAccountArticleListParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: {
      ...query,
      platform_type: query.platform_type ? query.platform_type : 'wechat'
    }
  })
}
// 导出微信文章数据
export async function exportWechatOfficialAccountArticleList(query: WechatOfficialAccountArticleListParams) {
  return await request({
    url: apiPrefix + 'export_excel_articles/',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导出抖音文章数据
export async function exportDouyinOfficialAccountArticleList(query: WechatOfficialAccountArticleListParams) {
  return await request({
    url: '/api/douyin/works/export_excel_works/',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
// 导出微博文章数据
export async function exportWeiboOfficialAccountArticleList(query: WechatOfficialAccountArticleListParams) {
  return await request({
    url: '/api/weibo/post/export_excel_posts/',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

# 稿件管理模块

## 核心功能
- 稿件列表：展示和管理提交的稿件，支持稿件状态跟踪和内容审核
- 草稿管理：维护未完成的草稿内容，支持草稿保存和续写功能
- 我的稿件：展示当前用户创建的稿件，提供个人稿件管理界面
- 投稿管理：处理外部投稿，支持投稿审核和稿件处理流程
- 审核流程：提供稿件审核功能，支持审核意见和状态变更
- 投稿流程：支持稿件的投稿配置和投稿状态跟踪

## 技术实现

### API接口层 (api.ts)
- getManuscriptList(): 获取稿件列表数据，支持状态筛选和搜索
- createManuscript(): 创建新稿件，处理表单验证和数据提交
- updateManuscript(): 更新稿件内容，支持草稿保存和正式提交
- auditManuscript(): 稿件审核接口，处理审核意见和状态变更
- contributeManuscript(): 稿件投稿接口，处理投稿配置和提交

### 组件架构 (components/)
- SearchForm: 搜索组件，handleSearch()和handleReset()处理查询条件
- ManuscriptTable: 稿件列表组件，支持分页、编辑、删除、审核操作
- ManuscriptDialog: 稿件编辑弹窗，handleManuscriptSubmit()处理表单提交
- AuditDialog: 审核弹窗，处理审核意见录入和状态变更
- ContributionDialog: 投稿配置弹窗，处理投稿参数和目标设置

### 主页面控制 (index.vue)
- 状态管理: loading控制加载状态，tableData管理列表数据
- 权限控制: hasPermission()检查操作权限，控制按钮显示
- 操作流程: handleAdd()、handleEdit()、handleAudit()处理各种稿件操作
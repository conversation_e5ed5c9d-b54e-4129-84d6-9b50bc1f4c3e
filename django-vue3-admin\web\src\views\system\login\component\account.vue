<template>
	<el-form ref="formRef" size="large" class="login-content-form" :model="state.ruleForm" :rules="rules" @keyup.enter="loginClick">
		<el-form-item class="login-animation1" prop="tenantname" v-if="isTenantModeEnabled && !autoTenant.isAutoMode">
			<el-input
				type="text"
				:placeholder="$t('message.account.accountPlaceholder0')"
				v-model="state.ruleForm.tenantname"
				clearable
				autocomplete="off"
				@blur="onTenantNameBlur"
			>
				<template #prefix>
					<el-icon class="el-input__icon"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation1" prop="username">
			<el-input type="text" :placeholder="$t('message.account.accountPlaceholder1')" v-model="state.ruleForm.username" clearable autocomplete="off">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="password">
			<el-input
				:type="state.isShowPassword ? 'text' : 'password'"
				:placeholder="$t('message.account.accountPlaceholder2')"
				v-model="state.ruleForm.password"
			>
				<template #prefix>
					<el-icon class="el-input__icon"><ele-Unlock /></el-icon>
				</template>
				<template #suffix>
					<i
						class="iconfont el-input__icon login-content-password"
						:class="state.isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
						@click="state.isShowPassword = !state.isShowPassword"
					>
					</i>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation3" v-if="isShowCaptcha" prop="captcha">
			<el-col :span="15">
				<el-input
					type="text"
					maxlength="4"
					:placeholder="$t('message.account.accountPlaceholder3')"
					v-model="state.ruleForm.captcha"
					clearable
					autocomplete="off"
				>
					<template #prefix>
						<el-icon class="el-input__icon"><ele-Position /></el-icon>
					</template>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="8">
				<el-button class="login-content-captcha">
					<el-image :src="state.ruleForm.captchaImgBase" @click="refreshCaptcha" />
				</el-button>
			</el-col>
		</el-form-item>
		<el-form-item class="login-animation4">
			<el-button type="primary" class="login-content-submit" round @click="loginClick" :loading="state.loading.signIn">
				<span>{{ $t('message.account.accountBtnText') }}</span>
			</el-button>
		</el-form-item>
	</el-form>
	<!--      申请试用-->
	<div style="text-align: center" v-if="showApply()">
		<el-button class="login-content-apply" link type="primary" plain round @click="applyBtnClick">
			<span>申请试用</span>
		</el-button>
	</div>
</template>

<script lang="ts">
import { reactive, defineComponent, computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, FormRules } from 'element-plus';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session, Local } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import * as loginApi from '/@/views/system/login/api';
import { useUserInfo } from '/@/stores/userInfo';
import { DictionaryStore } from '/@/stores/dictionary';
import { SystemConfigStore } from '/@/stores/systemConfig';

import { Md5 } from 'ts-md5';
import { errorMessage } from '/@/utils/message';
import { getBaseURL } from '/@/utils/baseUrl';
import { getTenantByName, type TenantInfo } from '/@/views/tenant/api';

export default defineComponent({
	name: 'loginAccount',
	setup() {
		const { t } = useI18n();
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { userInfos } = storeToRefs(useUserInfo());
		const route = useRoute();
		const router = useRouter();
		const state = reactive({
			isShowPassword: false,
			ruleForm: {
				tenantname: '',
				tenant_id: null as number | null,
				username: '',
				password: '',
				captcha: '',
				captchaKey: '',
				captchaImgBase: '',
			},
			loading: {
				signIn: false,
			},
		});

		// 自动租户相关状态
		const autoTenant = reactive({
			isAutoMode: false, // 是否为自动租户模式
			tenantName: '', // 租户名称
			tenantId: null as number | null, // 租户ID
			loading: false, // 加载状态
		});

		const rules = reactive<FormRules>({
			username: [{ required: true, message: '请填写账号', trigger: 'blur' }],
			password: [
				{
					required: true,
					message: '请填写密码',
					trigger: 'blur',
				},
			],
			captcha: [
				{
					required: true,
					message: '请填写验证码',
					trigger: 'blur',
				},
			],
		});
		const formRef = ref();
		// 时间获取
		const currentTime = computed(() => {
			return formatAxis(new Date());
		});
		// 是否关闭验证码
		const isShowCaptcha = computed(() => {
			return SystemConfigStore().systemConfig['base.captcha_state'];
		});

		// 是否启用租户模式
		const isTenantModeEnabled = computed(() => {
			// 优先使用环境变量配置，如果未配置则使用系统配置
			const envTenantMode = import.meta.env.VITE_TENANT_MODE;
			if (envTenantMode !== undefined) {
				return envTenantMode === 'true' || envTenantMode === true;
			}

			// 如果环境变量未配置，则使用系统配置，默认为true
			const tenantMode = SystemConfigStore().systemConfig['base.tenant_mode'];
			return tenantMode !== false && tenantMode !== 'false';
		});

		// 根据租户名称获取租户ID
		const getTenantIdByName = async (tenantName: string): Promise<number | null> => {
			try {
				const response = await getTenantByName(tenantName);
				const tenant = response.data as TenantInfo;
				return tenant ? tenant.id || null : null;
			} catch (error) {
				console.error('获取租户信息失败:', error);
				// 安全地处理错误消息
				const errorMsg = error instanceof Error ? error.message : '获取租户信息失败';
				errorMessage(errorMsg);
				return null;
			}
		};

		// 处理自动租户逻辑
		const handleAutoTenant = async () => {
			const tenantParam = route.query.tenant as string;

			// 如果租户模式未启用，不处理自动租户逻辑
			if (!isTenantModeEnabled.value) {
				autoTenant.isAutoMode = false;
				autoTenant.tenantName = '';
				autoTenant.tenantId = null;
				return;
			}

			if (tenantParam) {
				autoTenant.isAutoMode = true;
				autoTenant.tenantName = tenantParam;
				autoTenant.loading = true;

				try {
					const tenantId = await getTenantIdByName(tenantParam);
					if (tenantId) {
						autoTenant.tenantId = tenantId;
						// 自动填充租户名称和ID到表单（用于登录请求）
						state.ruleForm.tenantname = tenantParam;
						state.ruleForm.tenant_id = tenantId;
						console.log(`自动匹配租户: ${tenantParam} (ID: ${tenantId})`);
					} else {
						console.warn(`未找到租户: ${tenantParam}`);
						ElMessage.warning(`未找到租户: ${tenantParam}`);
						// 如果找不到租户，退出自动模式
						autoTenant.isAutoMode = false;
					}
				} catch (error) {
					console.error('处理自动租户失败:', error);
					ElMessage.error('获取租户信息失败');
					autoTenant.isAutoMode = false;
				} finally {
					autoTenant.loading = false;
				}
			} else {
				// 如果没有登录参数，但是本地有租户信息，则使用本地租户信息
				const loginTenantInfo = Local.get('loginTenantInfo');
				if (loginTenantInfo) {
					autoTenant.isAutoMode = false;
					// 在当前URL上拼接tenant参数
					if (!route.query.tenant && loginTenantInfo.tenantName) {
						const currentQuery = { ...route.query, tenant: loginTenantInfo.tenantName };
						router.replace({ query: currentQuery });
					}

					state.ruleForm.tenantname = loginTenantInfo.tenantName;
					state.ruleForm.tenant_id = loginTenantInfo.tenantId;
				} else {
					// 没有租户参数，使用正常模式
					autoTenant.isAutoMode = false;
					state.ruleForm.tenantname = '';
					state.ruleForm.tenant_id = null;
				}
			}
		};

		// 监听路由变化
		watch(
			() => route.query.tenant,
			() => {
				handleAutoTenant();
			},
			{ immediate: true }
		);

		// 处理租户名称输入框失去焦点事件
		const onTenantNameBlur = async () => {
			// 只在租户模式启用且非自动模式下处理
			if (!isTenantModeEnabled.value) return;

			if (!autoTenant.isAutoMode && state.ruleForm.tenantname && state.ruleForm.tenantname.trim()) {
				const tenantName = state.ruleForm.tenantname.trim();
				try {
					console.log(`查询租户: ${tenantName}`);
					const tenantId = await getTenantIdByName(tenantName);
					if (tenantId) {
						state.ruleForm.tenant_id = tenantId;
						console.log(`找到租户: ${tenantName} (ID: ${tenantId})`);

						// 将租户名称添加到URL中
						if (!route.query.tenant || route.query.tenant !== tenantName) {
							const currentQuery = { ...route.query, tenant: tenantName };
							router.replace({ query: currentQuery });
						}
					} else {
						state.ruleForm.tenant_id = null;
						console.warn(`未找到租户: ${tenantName}`);
						ElMessage.warning(`未找到租户: ${tenantName}`);
					}
				} catch (error) {
					console.error('查询租户失败:', error);
					state.ruleForm.tenant_id = null;
					ElMessage.error('查询租户失败，请重试');
				}
			}
		};

		const getCaptcha = async () => {
			loginApi.getCaptcha().then((ret: any) => {
				state.ruleForm.captchaImgBase = ret.data.image_base;
				state.ruleForm.captchaKey = ret.data.key;
			});
		};
		const applyBtnClick = async () => {
			window.open(getBaseURL('/api/system/apply_for_trial/'));
		};
		const refreshCaptcha = async () => {
			state.ruleForm.captcha = '';
			loginApi.getCaptcha().then((ret: any) => {
				state.ruleForm.captchaImgBase = ret.data.image_base;
				state.ruleForm.captchaKey = ret.data.key;
			});
		};
		const loginClick = async () => {
			if (!formRef.value) return;
			await formRef.value.validate(async (valid: any) => {
				if (valid) {
					// 检查租户ID是否已设置（仅在租户模式启用时检查）
					if (isTenantModeEnabled.value && !autoTenant.isAutoMode && state.ruleForm.tenantname && !state.ruleForm.tenant_id) {
						ElMessage.error('请先确保租户信息匹配成功');
						return;
					}
					// 保存当前登录时的租户信息，用于退出登录时保持上下文（仅在租户模式启用时）
					if (isTenantModeEnabled.value && (autoTenant.isAutoMode || state.ruleForm.tenant_id)) {
						const tenantInfo = {
							tenantName: autoTenant.tenantName || state.ruleForm.tenantname,
							tenantId: autoTenant.tenantId || state.ruleForm.tenant_id,
						};
						Local.set('loginTenantInfo', tenantInfo);
					}
					state.loading.signIn = true;

					try {
						const res = await loginApi.login({
							...state.ruleForm,
							password: Md5.hashStr(state.ruleForm.password),
						});

						if (res.code === 2000) {
							const { data } = res;

							// 设置登录信息
							Cookies.set('username', res.data.username);
							Session.set('token', res.data.access);

							// 更新用户信息到store
							await useUserInfo().setPwdChangeCount(data.pwd_change_count);

							if (data.pwd_change_count == 0) {
								state.loading.signIn = false;
								// 密码修改次数为0，登录页面会自动切换到修改密码tab，不需要跳转
								return;
							}

							// 初始化路由
							if (!themeConfig.value.isRequestRoutes) {
								// 前端控制路由
								await initFrontEndControlRoutes();
							} else {
								// 后端控制路由
								await initBackEndControlRoutes();
							}

							// 执行登录成功逻辑
							await loginSuccess();
						}
					} catch (err: any) {
						console.error('登录失败:', err);
						state.loading.signIn = false;
						// 登录错误之后，刷新验证码
						refreshCaptcha();

						// 显示错误信息
						const errorMsg = err?.message || '登录失败，请重试';
						ElMessage.error(errorMsg);
					}
				} else {
					errorMessage('请填写登录信息');
				}
			});
		};

		// 登录成功后的跳转
		const loginSuccess = async () => {
			try {
				//获取所有字典
				await DictionaryStore().getSystemDictionarys();

				// 初始化登录成功时间问候语
				let currentTimeInfo = currentTime.value;

				// 登录成功，跳到转首页
				const pwd_change_count = userInfos.value.pwd_change_count;
				if (pwd_change_count && pwd_change_count > 0) {
					// 确保用户信息已更新到store
					await useUserInfo().setUserInfos();

					// 添加短暂延迟确保路由和状态完全初始化
					await new Promise((resolve) => setTimeout(resolve, 100));

					// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
					if (route.query?.redirect) {
						await router.push({
							path: <string>route.query?.redirect,
							query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
						});
					} else {
						await router.push('/home');
					}

					// 登录成功提示
					const signInText = t('message.signInText');
					ElMessage.success(`${currentTimeInfo}，${signInText}`);
				}

				// 关闭 loading
				state.loading.signIn = false;
				// 添加 loading，防止第一次进入界面时出现短暂空白
				NextLoading.start();
			} catch (error) {
				console.error('登录成功后处理失败:', error);
				state.loading.signIn = false;
				ElMessage.error('登录后初始化失败，请刷新页面重试');
			}
		};
		onMounted(() => {
			console.log('登录onMounted', import.meta.env.VITE_TENANT_MODE);
			getCaptcha();
			//获取系统配置
			SystemConfigStore().getSystemConfigs();

			// 组件挂载时检查是否需要添加tenant参数到URL
			if (isTenantModeEnabled.value && !route.query.tenant) {
				const loginTenantInfo = Local.get('loginTenantInfo');
				if (loginTenantInfo && loginTenantInfo.tenantName) {
					const currentQuery = { ...route.query, tenant: loginTenantInfo.tenantName };
					router.replace({ query: currentQuery });
				}
			}
		});
		// 是否显示申请试用按钮
		const showApply = () => {
			return window.location.href.indexOf('public') != -1;
		};

		return {
			refreshCaptcha,
			loginClick,
			loginSuccess,
			isShowCaptcha,
			isTenantModeEnabled, // 导出租户模式开关状态
			state,
			formRef,
			rules,
			applyBtnClick,
			showApply,
			autoTenant, // 导出自动租户状态
			onTenantNameBlur, // 导出租户名称失去焦点处理函数
		};
	},
});
</script>

<style scoped lang="scss">
.login-content-form {
	// margin-top: 20px;

	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;

		&:hover {
			color: #909399;
		}
	}

	.login-content-captcha {
		width: 100%;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 800;
		margin-top: 15px;
	}
}
</style>

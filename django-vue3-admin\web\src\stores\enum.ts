import { defineStore } from 'pinia';
import { request } from '/@/utils/service';
import { ENUM_TYPE, VALUE_TO_COLOR_MAPPING } from '/@/stores/constants/enum';

interface EnumState {
  enumMap: Record<string, any[]>;
}

/**
 * 枚举值管理
 * @methods getAllEnums 获取所有枚举值
 * @methods getEnumByName 根据枚举名称获取特定枚举
 * @methods getEnumsByGroup 根据分组获取枚举
 */
export const useEnumStore = defineStore('Enum', {
  state: (): EnumState => ({
    enumMap: {},
  }),
  actions: {
    // 获取所有枚举
    async getAllEnums() {
      try {
        const response = await request({
          url: '/api/enums/',
          method: 'get',
        });
        
        // 处理返回的数据，将分组的数据扁平化存储
        Object.entries(response.data).forEach(([group, enums]: [string, any]) => {
          Object.entries(enums).forEach(([enumName, values]: [string, any]) => {
            this.enumMap[`${group}.${enumName}`] = values;
          });
        });
      } catch (error) {
        console.error('获取枚举失败:', error);
      }
    },

    // 根据枚举名称获取特定枚举
    async getEnumByName(enumName: string) {
      try {
        const response = await request({
          url: `/api/enums/${enumName}/`,
          method: 'get',
        });
        this.enumMap[enumName] = response.data;
        return response.data;
      } catch (error) {
        console.error(`获取枚举 ${enumName} 失败:`, error);
        return [];
      }
    },

    // 根据分组获取枚举
    async getEnumsByGroup(groupName: string) {
      try {
        const response = await request({
          url: `/api/enums/group/${groupName}/`,
          method: 'get',
        });
        
        // 处理分组数据
        Object.entries(response.data).forEach(([enumName, values]: [string, any]) => {
          this.enumMap[`${groupName}.${enumName}`] = values;
        });
        return response.data;
      } catch (error) {
        console.error(`获取分组 ${groupName} 的枚举失败:`, error);
        return {};
      }
    },
  },
  persist: {
    enabled: true,
  },
});

// 工具函数
export function getEnumDatas(enumType: string): any[] {
  const store = useEnumStore();
  return store.enumMap[enumType] || [];
}

export function getEnumData(enumType: string, value: any) {
  const enumDatas = getEnumDatas(enumType);
  if (!enumDatas || enumDatas.length === 0) {
    return undefined;
  }
  
  // 将值转换为字符串进行比较
  const strValue = String(value);
  return enumDatas.find(item => String(item.value) === strValue);
}

export function getEnumLabel(enumType: string, value: any): string {
  const enumData = getEnumData(enumType, value);
  return enumData ? enumData.label : '';
}

export function getEnumColor(value: any): string {
  return VALUE_TO_COLOR_MAPPING[value] || 'default';
} 
<template>
	<span>
		<template v-if="currentDictData">
			<!-- 默认样式 -->
			<span v-if="colorType === 'default' || !colorType" :class="currentDictData.cssClass">
				{{ currentDictData.label }}
			</span>
			<!-- Tag 样式 -->
			<el-tag v-else :disable-transitions="true" :type="getTagColor" :class="currentDictData.cssClass">
				{{ currentDictData.label }}
			</el-tag>
		</template>
	</span>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue';

interface DictData {
	label: string;
	value: string | number | boolean;
	cssClass?: string;
}

export default defineComponent({
	name: 'DictTag',
	props: {
		// 选项数组
		options: {
			type: Array as PropType<DictData[]>,
			required: true,
		},
		// 当前值
		value: {
			type: [Number, String, Boolean, Array] as PropType<number | string | boolean | Array<any>>,
			required: true,
		},
		// tag显示类型，可以是字符串或数组
		colorType: {
			type: [String, Array] as PropType<string | string[]>,
			default: 'default',
		},
	},
	setup(props) {
		// 当前字典数据
		const currentDictData = computed(() => {
			const valueStr = String(props.value);
			return props.options.find((item: DictData) => String(item.value) === valueStr);
		});

		// 获取标签颜色
		const getTagColor = computed(() => {
			if (!currentDictData.value) return 'default';

			// 如果colorType是字符串，直接返回
			if (typeof props.colorType === 'string') {
				return props.colorType;
			}

			// 如果colorType是数组
			if (Array.isArray(props.colorType)) {
				// 获取当前值在字典列表中的索引
				const index = props.options.findIndex((item: DictData) => String(item.value) === String(props.value));

				// 如果找到了索引，并且colorType数组中有对应的颜色，返回对应颜色
				if (index !== -1 && index < props.colorType.length) {
					return props.colorType[index];
				}
			}

			// 默认返回default
			return 'default';
		});

		return {
			currentDictData,
			getTagColor,
		};
	},
});
</script>

<style scoped>
.el-tag + .el-tag {
	margin-left: 10px;
}
</style> 
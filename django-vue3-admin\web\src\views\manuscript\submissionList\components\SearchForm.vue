<template>
	<div class="search-form">
		<el-form :model="searchForm" :inline="true">
			<el-form-item label="稿件标题">
				<el-input v-model="searchForm.title" placeholder="请输入稿件标题" clearable @keyup.enter="handleSearch" />
			</el-form-item>
			<el-form-item label="投稿人">
				<el-input v-model="searchForm.creator_name" placeholder="请输入投稿人" clearable @keyup.enter="handleSearch" />
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleSearch">搜索</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

interface SearchForm {
	title: string;
	creator_name: string;
}

const searchForm = reactive<SearchForm>({
	title: '',
	creator_name: '',
});

const emit = defineEmits(['search', 'reset']);

const handleSearch = () => {
	emit('search', { ...searchForm });
};

const handleReset = () => {
	searchForm.title = '';
	searchForm.creator_name = '';
	emit('reset');
};
</script>

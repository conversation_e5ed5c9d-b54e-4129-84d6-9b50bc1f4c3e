<template>
	<div class="full" style="text-align: center">
		<div ref="chartRef" class="chart" style="height: 100%; width: 100%" />
	</div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
	total: {
		type: Object,
		default: () => ({}),
	},
	fontSize: {
		type: Number,
		default: 16,
	},
	labelFontSize: {
		type: Number,
		default: 12,
	},
});

const chartRef = ref(null);
const myChart = ref(null);

const chatData = computed(() => {
	if (props.data?.length > 0) {
		const arr = props.data.map((item) => ({
			value: item.value !== undefined ? item.value : item.count,
			name: item.name,
		}));

		return {
			type: 'pie',
			radius: ['40%', '70%'],
			data: arr,
			itemStyle: {
				color: function (params) {
					const colorList = [
						'#7dbef2', // 偏亮蓝
						'#f7a4a7', // 偏亮粉
						'#96dbc8', // 偏亮绿
						'#ffc19e', // 偏亮橙
						'#c89aff', // 偏亮紫
						'#ecd292', // 偏亮黄
						'#a3b5e0', // 偏亮靛蓝
						'#ffaed0', // 偏亮玫瑰
						'#8ec8ff', // 偏亮天蓝
						'#ffb5b5', // 偏亮珊瑚
						'#a5e6d0', // 偏亮薄荷
						'#ffc8a2', // 偏亮杏色
						'#d4c4ff', // 偏亮淡紫
						'#ffe0a6', // 偏亮琥珀
						'#b3e5fc', // 偏亮湖蓝
						// 为了扩展到30个颜色，我们可以在这些颜色的基础上进行一些变体，比如调整亮度或饱和度。以下是扩展后的颜色列表：
						'#8bc9f4', // 偏亮蓝变体1
						'#f9b3b3', // 偏亮粉变体1
						'#a0e0d0', // 偏亮绿变体1
						'#ffd6a6', // 偏亮橙变体1
						'#d1a6ff', // 偏亮紫变体1
						'#f0e0a6', // 偏亮黄变体1
						'#b3cde0', // 偏亮靛蓝变体1
						'#ffb3d1', // 偏亮玫瑰变体1
						'#a0d6e6', // 偏亮天蓝变体1
						'#ffcccc', // 偏亮珊瑚变体1
						'#b2f0e0', // 偏亮薄荷变体1
						'#ffe6b3', // 偏亮杏色变体1
						'#e0d6ff', // 偏亮淡紫变体1
						'#fff2b3', // 偏亮琥珀变体1
						'#cce5ff', // 偏亮湖蓝变体1
					];
					return colorList[params.dataIndex % colorList.length];
				},
			},
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 0,
					shadowColor: 'rgba(0, 0, 0, 0.5)',
				},
			},
		};
	}

	return {
		type: 'pie',
		radius: ['40%', '70%'],
		data: [
			{
				value: 0,
				name: '暂无数据',
			},
		],
		emphasis: {
			itemStyle: {
				shadowBlur: 10,
				shadowOffsetX: 0,
				shadowColor: 'rgba(0, 0, 0, 0.5)',
			},
		},
	};
});

const setOption = () => {
	if (!myChart.value) return;

	myChart.value.setOption({
		dataset: {
			source: props.data,
		},
		tooltip: {
			trigger: 'item',
		},
		label: {
			fontSize: props.labelFontSize,
			color: '#666',
			formatter: function (params) {
				if (params.data.value === 0) {
					return params.name + ' : 0 (0%)';
				}
				return params.name + ' : ' + params.data.value + ' (' + params.percent + '%)';
			},
		},
		title: {
			text: props.total.count !== undefined ? props.total.count : props.total.value,
			subtext: props.total.name,
			left: 'center',
			top: '43%',
			textStyle: {
				fontSize: props.fontSize,
				color: '#454c5c',
				align: 'center',
			},
			subtextStyle: {
				fontSize: props.fontSize,
			},
		},
		series: chatData.value,
	});
};

watch(
	() => props.data,
	() => {
		setOption();
	},
	{ deep: true }
);

onMounted(() => {
	myChart.value = echarts.init(chartRef.value);
	setOption();
});
</script>


<template>
	<div class="platform-tabs">
		<el-radio-group v-model="currentTab" @change="handleTabChange">
			<el-radio-button v-for="item in platformTypes" :key="item.value" :value="item.value">
				{{ item.label }}
			</el-radio-button>
		</el-radio-group>
	</div>
</template>

<script lang="ts">
import { ref, defineComponent, watch } from 'vue';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';

export default defineComponent({
	name: 'PlatformTabs',
	props: {
		// 初始选中的值
		modelValue: {
			type: String,
			default: '',
		},
	},
	emits: ['update:modelValue', 'change'],
	setup(props, { emit }) {
		// 平台类型列表
		const platformTypes = getEnumDatas(ENUM_TYPE.PLATFORM_TYPE);

		// 当前选中的Tab
		const currentTab = ref(props.modelValue || platformTypes[0].value);

		// 监听外部值变化
		watch(
			() => props.modelValue,
			(newVal) => {
				if (newVal && newVal !== currentTab.value) {
					currentTab.value = newVal;
				}
			}
		);

		// Tab变更处理函数
		const handleTabChange = (value: string) => {
			// 触发更新和变更事件
			emit('update:modelValue', value);
			emit('change', value);
		};

		return {
			platformTypes,
			currentTab,
			handleTabChange,
		};
	},
});
</script>

<style lang="scss" scoped>
.platform-tabs {
	display: inline-block;
	margin-right: 16px;

	:deep(.el-radio-button__inner) {
		min-width: 120px;
		padding: 12px 20px;
		font-size: 14px;
		background-color: #ffffff;
		color: #409eff;
		border-color: #dcdfe6;
		height: auto;
	}

	:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
		background-color: #ffffff;
		color: #409eff;
		border-color: #409eff;
		box-shadow: -1px 0 0 0 #409eff;
	}

	:deep(.el-radio-group) {
		display: flex;
	}
}
</style> 
import { request } from '/@/utils/service';

// 栏目状态枚举
export enum ColumnStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive', 
  ARCHIVED = 'archived',
  ERROR = 'error'
}

// 抓取状态枚举
export enum CrawlStatus {
  NEVER_CRAWLED = 'never_crawled',
  PARTIAL_CRAWLED = 'partial_crawled',
  COMPLETED = 'completed'
}

// 栏目数据类型
export interface ColumnType {
  id: number;
  sitemap_unique_id: string;
  site_id: number;
  name: string;
  description?: string;
  status: ColumnStatus;
  status_display: string;
  crawl_status: CrawlStatus;
  crawl_status_display: string;
  column_rule_id?: number;
  has_rule: boolean;
  rule_name?: string;
  last_crawled_page: number;
  total_pages?: number;
  max_crawl_pages: number;
  crawl_frequency: string;
  last_publish_time?: string;
  parent_column_id?: number;
  content_count: number;
  progress_info: {
    crawl_status: string;
    last_crawled_page: number;
    total_pages?: number;
    max_crawl_pages: number;
    progress_percentage: number;
    should_use_update_crawl: boolean;
  };
  sitemap_url?: string;
  sitemap_title?: string;
  site_name?: string;
  create_time: string;
  update_time: string;
}

// 查询参数类型
export interface ColumnQuery {
  page?: number;
  page_size?: number;
  site_id?: number;
  status?: ColumnStatus | string;
  crawl_status?: CrawlStatus | string;
  has_rule?: boolean | string;
  search?: string;
  ordering?: string;
}

// 创建栏目数据类型
export interface ColumnCreateData {
  sitemap_unique_id: string;
  site_id: number;
  name: string;
  description?: string;
  status?: ColumnStatus;
  column_rule_id?: number;
  max_crawl_pages?: number;
  crawl_frequency?: string;
  parent_column_id?: number;
}

// 统一API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 栏目统计类型
export interface ColumnStatistics {
  total_columns: number;
  total_content_count: number;
  avg_content_per_column: number;
  active_columns: number;
  inactive_columns: number;
  archived_columns: number;
  error_columns: number;
  has_rule_columns: number;
  no_rule_columns: number;
  never_crawled_columns: number;
  partial_crawled_columns: number;
  completed_crawled_columns: number;
}

// 批量操作结果类型
export interface BatchOperationResult {
  total_count: number;
  success_count: number;
  failed_count: number;
  results: Array<{
    column_id: number;
    column_name: string;
    success: boolean;
    old_status?: string;
    new_status?: string;
    error?: string;
  }>;
}

// SiteMap类型（用于栏目创建）
export interface SiteMapForColumn {
  id: number;
  unique_id: string;
  url: string;
  title: string;
  content_type: string;
  content_type_display: string;
  last_crawl_time?: string;
  created_at: string;
  has_column: boolean;
  column_info?: {
    id: number;
    name: string;
    status: string;
    crawl_status: string;
    content_count: number;
  };
}

// ========== 基础CRUD操作 ==========

// 获取栏目列表
export function getColumnList(params?: ColumnQuery) {
  return request<ApiResponse<PaginatedResponse<ColumnType>>>({
    url: '/api/website/api/columns/',
    method: 'get',
    params,
  });
}

// 创建栏目
export function createColumn(data: ColumnCreateData) {
  return request<ApiResponse<ColumnType>>({
    url: '/api/website/api/columns/',
    method: 'post',
    data,
  });
}

// 更新栏目
export function updateColumn(id: number, data: Partial<ColumnCreateData>) {
  return request<ApiResponse<ColumnType>>({
    url: `/api/website/api/columns/${id}/`,
    method: 'put',
    data,
  });
}

// 部分更新栏目
export function patchColumn(id: number, data: Partial<ColumnCreateData>) {
  return request<ApiResponse<ColumnType>>({
    url: `/api/website/api/columns/${id}/`,
    method: 'patch',
    data,
  });
}

// 删除栏目
export function deleteColumn(id: number) {
  return request<ApiResponse<{ deleted_column_name: string; deleted_column_id: number }>>({
    url: `/api/website/api/columns/${id}/`,
    method: 'delete',
  });
}

// 获取单个栏目详情
export function getColumnDetail(id: number) {
  return request<ApiResponse<ColumnType>>({
    url: `/api/website/api/columns/${id}/`,
    method: 'get',
  });
}

// ========== 统计信息 ==========

// 获取栏目统计信息
export function getColumnStatistics(params?: ColumnQuery) {
  return request<ApiResponse<ColumnStatistics>>({
    url: '/api/website/api/columns/statistics/',
    method: 'get',
    params,
  });
}

// ========== 状态管理 ==========

// 更新栏目状态
export function updateColumnStatus(id: number, action: string) {
  return request<ApiResponse<{
    column: ColumnType;
    old_status: string;
    new_status: string;
  }>>({
    url: `/api/website/api/columns/${id}/status/`,
    method: 'patch',
    data: { action },
  });
}

// 批量状态管理
export function batchUpdateColumnStatus(column_ids: number[], action: string) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/api/columns/batch_status/',
    method: 'post',
    data: { column_ids, action },
  });
}

// ========== 栏目操作 ==========

// 生成栏目规则
export function generateColumnRule(id: number) {
  return request<ApiResponse<{
    column_id: number;
    rule_id?: number;
    rule_name?: string;
    already_exists: boolean;
    statistics?: any;
  }>>({
    url: `/api/website/api/columns/${id}/generate_rule/`,
    method: 'post',
  });
}

// 抓取栏目内容
export function crawlColumnContent(id: number, crawl_mode: 'deep' | 'update' = 'deep') {
  return request<ApiResponse<{
    column_id: number;
    column_name: string;
    crawled_count: number;
    crawl_mode: string;
    statistics?: any;
    progress_info: any;
  }>>({
    url: `/api/website/api/columns/${id}/crawl_content/`,
    method: 'post',
    data: { crawl_mode },
  });
}

// 获取可用的SiteMap列表
export function getAvailableSiteMaps(site_id: number) {
  return request<ApiResponse<SiteMapForColumn[]>>({
    url: '/api/website/api/columns/available_sitemaps/',
    method: 'get',
    params: { site_id },
  });
}

// ========== 快速操作函数 ==========

// 激活栏目
export function activateColumn(id: number) {
  return updateColumnStatus(id, 'activate');
}

// 停用栏目
export function deactivateColumn(id: number) {
  return updateColumnStatus(id, 'deactivate');
}

// 归档栏目
export function archiveColumn(id: number) {
  return updateColumnStatus(id, 'archive');
}

// 标记栏目错误
export function markColumnError(id: number) {
  return updateColumnStatus(id, 'mark_error');
}

// 切换栏目状态
export function toggleColumnStatus(id: number) {
  return updateColumnStatus(id, 'toggle');
}

// 深度抓取栏目内容
export function deepCrawlColumn(id: number) {
  return crawlColumnContent(id, 'deep');
}

// 更新抓取栏目内容
export function updateCrawlColumn(id: number) {
  return crawlColumnContent(id, 'update');
}
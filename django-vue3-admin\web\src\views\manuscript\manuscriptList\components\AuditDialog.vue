<template>
	<el-dialog title="提交审核" v-model="dialogVisible" width="500px" :before-close="handleClose">
		<el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
			<el-form-item label="审核流程" prop="flow_info_id">
				<el-select v-model="formData.flow_info_id" placeholder="请选择审核流程" style="width: 100%">
					<el-option v-for="item in flowList" :key="item.id" :label="item.name" :value="item.id">
						<span>{{ item.name }}</span>
						<span class="flow-description">{{ item.description }}</span>
					</el-option>
				</el-select>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="props.loading"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { FormInstance } from 'element-plus';

interface Props {
	visible: boolean;
	flowList: Array<{
		id: number;
		name: string;
		description: string;
		content_type: string;
		model_name: string;
	}>;
	loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	flowList: () => [],
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = computed({
	get: () => props.visible,
	set: (val) => emit('update:visible', val),
});

const formRef = ref<FormInstance>();

const formData = ref({
	flow_info_id: '',
	model_type: 'three_review_template', // 默认三审三校类型
});

const rules = {
	flow_info_id: [{ required: true, message: '请选择审核流程', trigger: 'change' }],
};

// 关闭弹窗
const handleClose = () => {
	dialogVisible.value = false;
	formRef.value?.resetFields();
};

// 重置表单
const resetForm = () => {
	formRef.value?.resetFields();
	formData.value.flow_info_id = ''; // 清空选择
};

// 提交表单
const handleSubmit = async () => {
	if (!formRef.value) return;

	await formRef.value.validate(async (valid) => {
		if (valid) {
			emit('submit', formData.value);
		}
	});
};

defineExpose({
	resetForm,
});
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}

.flow-description {
	font-size: 13px;
	color: #999;
	margin-left: 8px;
}

:deep(.el-select-dropdown__item) {
	display: flex;
	align-items: center;
	padding: 8px 12px;
}
</style> 
<template>
	<div class="manuscript-detail-container">
		<!-- 稿件标题 -->
		<div class="manuscript-title">
			<h1>{{ manuscriptData?.title }}</h1>
		</div>
		<!-- 附件区域 -->
		<div v-if="hasAttachment" class="manuscript-attachment">
			<h3>附件</h3>
			<div class="attachment-list">
				<div v-for="(attachment, index) in manuscriptData.attachment_links" :key="attachment.id || index" class="attachment-item">
					<el-button type="primary" @click="handleViewAttachment(attachment)">
						<el-icon><Document /></el-icon>{{ attachment.name || '附件' + (index + 1) }}
					</el-button>
				</div>
			</div>
		</div>

		<!-- 稿件内容 -->
		<div class="manuscript-content">
			<div v-if="manuscriptData?.rich_content" v-html="manuscriptData.rich_content"></div>
			<div v-else class="no-content">暂无内容</div>
		</div>

		<!-- 加载状态 -->
		<div v-if="loading" class="loading-container">
			<el-skeleton :rows="10" animated />
		</div>
	</div>
</template>

<script setup lang="ts" name="ManuscriptDetail">
import { ref, computed, onMounted } from 'vue';
import { Document } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';
import { request } from '/@/utils/service';

const route = useRoute();
const manuscriptId = ref<number | null>(null);
const manuscriptData = ref<any>(null);
const loading = ref(true);

// 获取稿件数据
const fetchManuscriptData = async (id: number) => {
	try {
		loading.value = true;
		const response = await request({
			url: `/api/manuscript/contents/${id}/`,
			method: 'get',
		});

		if (response && response.data) {
			manuscriptData.value = response.data;
		} else {
			ElMessage.error('获取稿件数据失败');
		}
	} catch (error) {
		console.error('获取稿件数据失败', error);
		ElMessage.error('获取稿件数据失败');
	} finally {
		loading.value = false;
	}
};

onMounted(() => {
	// 从路由参数中获取稿件ID
	if (route.query.id) {
		manuscriptId.value = Number(route.query.id);
		fetchManuscriptData(manuscriptId.value);
	} else {
		loading.value = false;
		ElMessage.error('未提供稿件ID');
	}
});

// 判断是否有附件
const hasAttachment = computed(() => {
	return (
		manuscriptData.value?.attachment_links && Array.isArray(manuscriptData.value.attachment_links) && manuscriptData.value.attachment_links.length > 0
	);
});

// 查看附件
const handleViewAttachment = (attachment: any) => {
	if (!attachment) {
		ElMessage.error('附件信息不完整');
		return;
	}

	try {
		let previewUrl = '';
		if (attachment.url) {
			// 备用url字段
			previewUrl = `${attachment.url}?ci-process=doc-preview&dstType=html`;
		} else {
			ElMessage.error('附件链接不可用');
			return;
		}

		// 验证URL格式
		try {
			new URL(previewUrl);
		} catch (urlError) {
			ElMessage.error('附件链接格式错误');
			console.error('URL格式错误:', previewUrl, urlError);
			return;
		}

		// 在新窗口中打开附件
		const newWindow = window.open(previewUrl, '_blank');

		// 检查是否成功打开新窗口
		if (!newWindow) {
			ElMessage.warning('请允许弹出窗口以查看附件');
			// 降级方案：尝试在当前窗口打开
			window.location.href = previewUrl;
		} else {
			// 检查新窗口是否加载成功
			setTimeout(() => {
				try {
					if (newWindow.closed) {
						ElMessage.warning('附件窗口被阻止，请检查浏览器设置');
					}
				} catch (e) {
					// 跨域等原因可能导致检查失败，忽略
					console.warn('无法检查新窗口状态:', e);
				}
			}, 1000);
		}

		console.log('附件预览URL:', previewUrl);
	} catch (error) {
		ElMessage.error('查看附件失败');
		console.error('查看附件失败:', error);

		// 降级方案：如果有基本的file_url，直接尝试打开
		if (attachment.file_url) {
			try {
				window.open(attachment.file_url, '_blank');
			} catch (fallbackError) {
				console.error('降级方案也失败:', fallbackError);
			}
		}
	}
};

// 为了兼容性添加默认导出
defineExpose({
	manuscriptData,
	handleViewAttachment,
	fetchManuscriptData,
	hasAttachment,
});
</script>

<style scoped lang="scss">
.manuscript-detail-container {
	width: 100%;
	height: 100%;
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
	background-color: #fff;
	box-sizing: border-box;

	// 确保容器可以滚动
	overflow-y: auto;
	overflow-x: hidden;

	// 移除固定高度，让内容自然撑开
	min-height: calc(100vh - 40px); // 减去padding

	.manuscript-title {
		margin-bottom: 30px;
		text-align: center;
		border-bottom: 1px solid #eaeaea;
		padding-bottom: 20px;
		flex-shrink: 0; // 防止被压缩

		h1 {
			font-size: 28px;
			font-weight: bold;
			color: #333;
			line-height: 1.5;
			margin: 0;
		}
	}

	.manuscript-attachment {
		padding: 15px;
		background-color: #f0f9ff;
		border-radius: 4px;
		margin-bottom: 20px;
		flex-shrink: 0; // 防止被压缩

		h3 {
			font-size: 16px;
			color: #333;
			margin: 0 0 10px 0;
		}

		.attachment-list {
			display: flex;
			flex-wrap: wrap;
			gap: 10px;

			.attachment-item {
				flex-shrink: 0;

				.el-button {
					cursor: pointer;
					transition: all 0.3s ease;

					&:hover {
						transform: translateY(-1px);
						box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
					}
				}
			}
		}
	}

	.manuscript-content {
		flex: 1; // 占据剩余空间
		min-height: 300px;
		padding: 20px;
		background-color: #f9f9f9;
		border-radius: 4px;
		margin-bottom: 20px;
		line-height: 1.8;
		font-size: 16px;

		// 内容区域独立滚动
		overflow-y: auto;
		word-wrap: break-word;
		word-break: break-all;

		// 富文本内容样式优化
		:deep(img) {
			max-width: 100%;
			height: auto;
			display: block;
			margin: 10px auto;
		}

		:deep(p) {
			margin-bottom: 12px;
		}

		:deep(table) {
			width: 100%;
			border-collapse: collapse;
			margin: 15px 0;

			td,
			th {
				border: 1px solid #ddd;
				padding: 8px;
			}
		}

		.no-content {
			color: #999;
			text-align: center;
			padding: 50px 0;
			font-style: italic;
		}
	}

	.loading-container {
		padding: 20px;
		flex-shrink: 0;
	}
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
	.manuscript-detail-container {
		padding: 15px;
		min-height: calc(100vh - 30px);

		.manuscript-title {
			margin-bottom: 20px;
			padding-bottom: 15px;

			h1 {
				font-size: 22px;
			}
		}

		.manuscript-content {
			padding: 15px;
			font-size: 14px;
			min-height: 250px;
		}

		.manuscript-attachment {
			padding: 12px;
			margin-bottom: 15px;

			.attachment-list {
				flex-direction: column;
				gap: 8px;

				.attachment-item {
					width: 100%;

					.el-button {
						width: 100%;
						justify-content: flex-start;
						text-align: left;
						white-space: normal; // 允许文本换行
						word-break: break-all; // 在任意字符处换行
						height: auto; // 自适应高度以适应多行文本
						padding: 8px 12px; // 调整内边距，确保内容不挤压
						line-height: 1.5; // 增加行高，避免文字粘连
					}
				}
			}
		}
	}
}

/* 平板适配 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
	.manuscript-detail-container {
		padding: 18px;

		.manuscript-title h1 {
			font-size: 24px;
		}

		.manuscript-content {
			font-size: 15px;
		}
	}
}

/* 确保在iframe或嵌入式环境中也能正常滚动 */
html,
body {
	overflow: auto !important;
}
</style>
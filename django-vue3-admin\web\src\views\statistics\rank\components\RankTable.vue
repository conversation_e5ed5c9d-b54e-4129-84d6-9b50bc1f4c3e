<template>
	<div class="rank-table">
		<el-table :data="rankData" style="width: 100%" v-loading="loading" :height="tableHeight" ref="tableRef" :row-key="getRowKey" :key="tableKey">
			<!-- 微信平台表格列 -->
			<template v-if="platform === 'wechat'">
				<el-table-column label="微信指数榜" align="left" min-width="270">
					<template #default="scope">
						<div class="account-info">
							<span class="rank-number" :class="{ 'top-rank': scope.$index < 3 }">{{ scope.$index + 1 }}</span>
							<img referrerpolicy="no-referrer" :src="scope.row.avatar || ''" class="account-avatar" />
							<span class="account-name">{{ scope.row.name }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="publishCount" label="发文量" align="center" />
				<el-table-column prop="readCount" label="阅读量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.readCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="forwardCount" label="转发量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.forwardCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="viewCount" label="推荐量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.viewCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="likeCount" label="点赞量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.likeCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="rankIndex" label="WCI" align="center">
					<template #default="scope">
						<span class="rank-index">{{ scope.row.rankIndex }}</span>
					</template>
				</el-table-column>
			</template>

			<!-- 抖音平台表格列 -->
			<template v-else-if="platform === 'douyin'">
				<el-table-column label="抖音指数榜" align="left" min-width="270">
					<template #default="scope">
						<div class="account-info">
							<span class="rank-number" :class="{ 'top-rank': scope.$index < 3 }">{{ scope.$index + 1 }}</span>
							<img referrerpolicy="no-referrer" :src="scope.row.avatar || ''" class="account-avatar" />
							<span class="account-name">{{ scope.row.name }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="fansCount" label="粉丝量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.fansCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="publishCount" label="发布量" align="center" />
				<!-- <el-table-column prop="playCount" label="播放量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.playCount) }}</span>
					</template>
				</el-table-column> -->
				<el-table-column prop="likeCount" label="点赞量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.likeCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="commentCount" label="评论量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.commentCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="shareCount" label="分享量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.shareCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="collectCount" label="收藏量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.collectCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="dci" label="传播指数" align="center">
					<template #default="scope">
						<span class="rank-index">{{ scope.row.dci }}</span>
					</template>
				</el-table-column>
			</template>

			<!-- 微博平台表格列 -->
			<template v-else-if="platform === 'weibo'">
				<el-table-column label="微博指数榜" align="left" min-width="270">
					<template #default="scope">
						<div class="account-info">
							<span class="rank-number" :class="{ 'top-rank': scope.$index < 3 }">{{ scope.$index + 1 }}</span>
							<img referrerpolicy="no-referrer" :src="`data:image/png;base64,${scope.row.avatar}`" class="account-avatar" />
							<span class="account-name">{{ scope.row.name }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="publishCount" label="发博量" align="center" />
				<el-table-column prop="originalCount" label="原创发博量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.originalCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="likeCount" label="点赞量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.likeCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="commentCount" label="评论量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.commentCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="forwardCount" label="转发量" align="center">
					<template #default="scope">
						<span>{{ tranNum(scope.row.forwardCount) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="bci" label="传播指数" align="center">
					<template #default="scope">
						<span class="rank-index">{{ scope.row.bci }}</span>
					</template>
				</el-table-column>
			</template>
		</el-table>
		<div v-if="loading" class="loading-more">加载中...</div>
		<div v-if="!hasMoreData && rankData.length > 0 && !loading" class="no-more-data">没有更多数据了</div>
		<div v-if="hasMoreData && rankData.length > 0 && !loading" class="load-more-btn" @click="loadMore">点击或滚动加载更多</div>
	</div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted, watch } from 'vue';
import { tranNumber } from '/@/utils/tranNum';

const props = defineProps({
	rankData: {
		type: Array,
		required: true,
	},
	loading: {
		type: Boolean,
		default: false,
	},
	hasMoreData: {
		type: Boolean,
		default: true,
	},
	tableHeight: {
		type: Number,
		default: window.innerHeight - 200,
	},
	platform: {
		type: String,
		default: 'wechat',
	},
});

const emit = defineEmits(['loadMore']);

const tableRef = ref(null);

// 添加row-key和tableKey支持
const getRowKey = (row) => {
	// 如果数据有id字段，使用id；否则使用name或其他唯一标识
	return row.id || row.name || row.accountId || JSON.stringify(row);
};
const tableKey = ref(0);

// 数字转换函数
const tranNum = (num) => {
	if (num !== undefined && num !== null && num !== '') {
		return tranNumber(num);
	}
	return '';
};

// 加载更多数据
const loadMore = () => {
	emit('loadMore');
};

// 检查内容高度是否足够填充表格
const checkContentHeight = () => {
	if (!tableRef.value) return;

	const tableBody = tableRef.value.$el.querySelector('.el-table__body');
	const tableWrapper = tableRef.value.$el.querySelector('.el-scrollbar__wrap');

	if (!tableBody || !tableWrapper) return;

	// 比较表格内容高度与容器高度
	const contentHeight = tableBody.offsetHeight;
	const containerHeight = tableWrapper.clientHeight;

	// 如果内容高度小于容器高度，且有更多数据可加载且不在加载中，则自动加载更多
	if (contentHeight < containerHeight && props.hasMoreData && !props.loading) {
		loadMore();
	}
};

// 表格滚动事件处理
const handleTableScroll = () => {
	if (!tableRef.value) return;

	// 获取表格包装元素
	const tableWrapper = tableRef.value.$el.querySelector('.el-scrollbar__wrap');
	if (!tableWrapper) return;

	const { scrollTop, scrollHeight, clientHeight } = tableWrapper;

	// 当滚动到距离底部50px以内时加载更多数据
	if (scrollHeight - scrollTop - clientHeight < 50 && !props.loading && props.hasMoreData) {
		loadMore();
	}
};

// 窗口大小改变事件处理
const handleResize = () => {
	checkContentHeight();
};

// 监听数据变化，强制重新渲染表格
watch(
	() => props.rankData,
	async (newData, oldData) => {
		if (newData !== oldData) {
			tableKey.value += 1;
			await nextTick();
		}
	},
	{ deep: true }
);

// 监听loading状态变化
watch(
	() => props.loading,
	async (newLoading, oldLoading) => {
		if (oldLoading && !newLoading) {
			await nextTick();
			tableKey.value += 1;
		}
	}
);

// 添加表格滚动事件监听
onMounted(() => {
	nextTick(() => {
		if (tableRef.value) {
			const tableWrapper = tableRef.value.$el.querySelector('.el-scrollbar__wrap');
			if (tableWrapper) {
				tableWrapper.addEventListener('scroll', handleTableScroll);
			}
		}

		// 初次加载时检查内容高度
		checkContentHeight();

		// 添加窗口大小变化监听
		window.addEventListener('resize', handleResize);
	});
});

// 组件卸载时移除事件监听
onUnmounted(() => {
	if (tableRef.value) {
		const tableWrapper = tableRef.value.$el.querySelector('.el-scrollbar__wrap');
		if (tableWrapper) {
			tableWrapper.removeEventListener('scroll', handleTableScroll);
		}
	}

	// 移除窗口大小变化监听
	window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.rank-table {
	background: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.account-info {
	display: flex;
	align-items: center;
}

.rank-number {
	width: 24px;
	height: 24px;
	line-height: 24px;
	text-align: center;
	margin-right: 10px;
	font-weight: bold;
}

.top-rank {
	background-image: url('/@/assets/rank/rankings.png');
	background-size: 100% 100%;
	color: white;
	border-radius: 50%;
	font-size: 10px;
}

.account-avatar {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	margin-right: 10px;
}

.rank-index {
	color: #409eff;
	font-weight: bold;
}

.loading-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #909399;
}

.no-more-data {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #909399;
	background-color: #f5f7fa;
}

.load-more-btn {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #409eff;
	cursor: pointer;
	background-color: #f5f7fa;
}

.load-more-btn:hover {
	background-color: #ecf5ff;
}
</style> 
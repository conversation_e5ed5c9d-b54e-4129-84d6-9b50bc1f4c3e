<template>
	<div class="operate-report-container">
		<div v-loading="pageLoading">
			<!-- 导出PDF按钮 -->
			<div class="export-buttons">
				<el-button type="primary" @click="handleExportPDF" :loading="exporting">导出PDF</el-button>
			</div>

			<!-- 本级微博运营报告 -->
			<my-wb-operate-month-detail
				ref="myWbOperateRef"
				style="margin-bottom: 40px"
				:date="{
					start_date: reportData.start_date,
					end_date: reportData.end_date,
				}"
				:title="reportData.title"
				:self-report-data="reportData.report_data?.my_account"
				:tenant-name="reportData.tenant_name"
				:account-counts="accountCounts"
				v-if="reportData.report_data?.my_account"
			/>

			<!-- 下级微博运营报告 -->
			<operate-wb-report-detail
				ref="operateReportRef"
				:date="{
					start_date: reportData.start_date,
					end_date: reportData.end_date,
				}"
				:title="reportData.title"
				:sub-report-data="reportData.report_data?.sub_account"
				:same-account="reportData.report_data?.peer_account"
				:no-update-account="reportData.report_data?.no_update_days"
				:create-time="reportData?.update_time"
				v-if="reportData.report_data?.sub_account || reportData?.account_publish_info || reportData.report_data?.peer_account"
			/>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElNotification } from 'element-plus';
import OperateWbReportDetail from './components/operateWbReportDetail.vue';
import MyWbOperateMonthDetail from './components/myWbOperateMonthDetail.vue';
import * as api from '/@/views/analysisHelper/operateReport/api';
import { exportToPDF } from '/@/utils/pdfExportHelper';

const route = useRoute();
const reportId = ref('');
const pageLoading = ref(false);
const exporting = ref(false);
const reportData = ref({});
const myWbOperateRef = ref();
const operateReportRef = ref();

// 计算账号数量
const accountCounts = computed(() => {
	const myAccountCount = reportData.value.report_data?.my_account?.account_data?.length || 0;
	const subAccountCount = reportData.value.report_data?.sub_account?.bci?.length || 0;
	const sameAccountCount = reportData.value.report_data?.peer_account?.length || 0;

	return {
		myAccount: myAccountCount,
		subAccount: subAccountCount,
		sameAccount: sameAccountCount,
	};
});

// 获取报告数据
const getReportData = async () => {
	pageLoading.value = true;
	try {
		const res = await api.getReportDetail(reportId.value);
		if (res.code === 2000) {
			reportData.value = res.data;
		}

		console.log('报告数据:', reportData.value);
	} catch (error) {
		console.error('获取报告数据失败:', error);
	} finally {
		pageLoading.value = false;
	}
};

// 导出PDF方法
const handleExportPDF = async () => {
	try {
		exporting.value = true;
		// 导出报告逻辑
		ElNotification.info({
			title: 'PDF正在导出，请耐心等待',
			duration: 1500,
		});
		// 等待下一个渲染周期
		await nextTick();

		// 获取本级和下级报告的模块ID列表
		const myModuleIds = myWbOperateRef.value?.moduleIds || [];
		const subModuleIds = operateReportRef.value?.moduleIds || [];

		console.log('本级模块ID:', myModuleIds);
		console.log('下级模块ID:', subModuleIds);

		// 合并所有模块ID并转换为ModuleConfig格式
		const modules = [...myModuleIds, ...subModuleIds].map((id) => ({
			moduleId: id,
			pageBreak: true, // 每个模块后强制分页
		}));

		if (modules.length === 0) {
			ElMessage.warning('没有找到可导出的内容');
			return;
		}

		// 验证所有模块是否存在
		const missingModules = modules.filter((module) => !document.getElementById(module.moduleId));
		if (missingModules.length > 0) {
			console.warn('以下模块未找到:', missingModules);
		}

		// 导出PDF
		const reportTitle = reportData.value.title || '微博运营报告';
		await exportToPDF(modules, `${reportTitle}.pdf`, {
			orientation: 'landscape',
			format: 'a4',
			quality: 0.3,
			scale: 2.435,
		});

		ElMessage.success('PDF导出完成');
	} catch (error) {
		console.error('导出过程出错:', error);
		ElMessage.error('导出过程出错，请重试');
	} finally {
		exporting.value = false;
	}
};

onMounted(() => {
	const { id } = route.query;
	if (id) {
		reportId.value = String(id);
		console.log('reportId:', reportId.value);
		getReportData();
	}
});
</script>

<style scoped>
.operate-report-container {
	background-color: #fff;
}

.export-buttons {
	position: fixed;
	top: 80px;
	right: 20px;
	z-index: 999;
}
</style>
<template>
	<div class="charts-container">
		<div class="charts-title">
			<h3>数据分析图表</h3>
		</div>

		<!-- 图表网格 -->
		<el-row :gutter="24">
			<!-- 准确率分布饼图 -->
			<el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
				<div class="chart-card">
					<div class="chart-header">
						<h4>准确率分布</h4>
						<p>AI判断结果分布情况</p>
					</div>
					<div class="chart-content">
						<div
							ref="accuracyPieRef"
							class="chart-wrapper"
							v-loading="loading"
						></div>
					</div>
				</div>
			</el-col>

			<!-- 错误类型准确率对比柱状图 -->
			<el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
				<div class="chart-card">
					<div class="chart-header">
						<h4>错误类型准确率</h4>
						<p>不同错误类型的准确率对比</p>
					</div>
					<div class="chart-content">
						<div
							ref="errorTypeBarRef"
							class="chart-wrapper"
							v-loading="loading"
						></div>
					</div>
				</div>
			</el-col>

			<!-- 时间趋势图 -->
			<el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
				<div class="chart-card">
					<div class="chart-header">
						<h4>时间趋势分析</h4>
						<p>准确率和样本数量的时间变化</p>
					</div>
					<div class="chart-content">
						<div
							ref="trendLineRef"
							class="chart-wrapper"
							v-loading="loading"
						></div>
					</div>
				</div>
			</el-col>
		</el-row>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import { StatsData, ErrorTypeAnalysisData, TrendAnalysisData } from '../api';

defineOptions({
	name: 'ChartsContainer',
});

interface Props {
	statsData: StatsData;
	errorTypeData: ErrorTypeAnalysisData[];
	trendData: TrendAnalysisData[];
	loading: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	statsData: () => ({
		total_count: 0,
		accuracy_rate: 0,
		consistent_count: 0,
		ai_false_positive: 0,
		ai_false_negative: 0,
		ai_uncertain: 0,
	}),
	errorTypeData: () => [],
	trendData: () => [],
	loading: false,
});

const emit = defineEmits(['chart-click']);

// Chart refs
const accuracyPieRef = ref<HTMLElement>();
const errorTypeBarRef = ref<HTMLElement>();
const trendLineRef = ref<HTMLElement>();

// Chart instances
let accuracyPieChart: echarts.ECharts | null = null;
let errorTypeBarChart: echarts.ECharts | null = null;
let trendLineChart: echarts.ECharts | null = null;

// 初始化准确率分布饼图
const initAccuracyPieChart = () => {
	if (!accuracyPieRef.value) return;

	accuracyPieChart = echarts.init(accuracyPieRef.value);

	const pieData = [
		{ value: props.statsData.consistent_count, name: '一致判断', itemStyle: { color: '#52c41a' } },
		{ value: props.statsData.ai_false_positive, name: 'AI误报', itemStyle: { color: '#fa8c16' } },
		{ value: props.statsData.ai_false_negative, name: 'AI漏报', itemStyle: { color: '#f5222d' } },
		{ value: props.statsData.ai_uncertain, name: 'AI无法判断', itemStyle: { color: '#999999' } },
	].filter(item => item.value > 0);

	const option = {
		tooltip: {
			trigger: 'item',
			formatter: '{a} <br/>{b}: {c} ({d}%)'
		},
		legend: {
			orient: 'horizontal',
			bottom: 0,
			left: 'center',
			textStyle: {
				fontSize: 12
			}
		},
		series: [
			{
				name: '判断结果',
				type: 'pie',
				radius: ['30%', '70%'],
				center: ['50%', '45%'],
				avoidLabelOverlap: false,
				itemStyle: {
					borderRadius: 8,
					borderColor: '#fff',
					borderWidth: 2
				},
				label: {
					show: false,
					position: 'center'
				},
				emphasis: {
					label: {
						show: true,
						fontSize: '14',
						fontWeight: 'bold'
					}
				},
				labelLine: {
					show: false
				},
				data: pieData
			}
		]
	};

	accuracyPieChart.setOption(option);

	// 添加点击事件
	accuracyPieChart.on('click', (params: any) => {
		const statusMap: Record<string, string> = {
			'一致判断': 'consistent',
			'AI误报': 'ai_false_positive',
			'AI漏报': 'ai_false_negative'
		};
		const judgeStatus = statusMap[params.name];
		if (judgeStatus) {
			emit('chart-click', { type: 'judge_status', value: judgeStatus });
		}
	});
};

// 初始化错误类型准确率柱状图
const initErrorTypeBarChart = () => {
	if (!errorTypeBarRef.value) return;

	errorTypeBarChart = echarts.init(errorTypeBarRef.value);

	const xAxisData = props.errorTypeData.map(item => item.error_type);
	const seriesData = props.errorTypeData.map(item => ({
		value: item.accuracy_rate,
		itemStyle: {
			color: item.accuracy_rate >= 80 ? '#52c41a' : 
				  item.accuracy_rate >= 60 ? '#fa8c16' : '#f5222d'
		}
	}));

	const option = {
		tooltip: {
			trigger: 'axis',
			formatter: (params: any) => {
				const data = params[0];
				const errorType = props.errorTypeData.find(item => item.error_type === data.name);
				return `${data.name}<br/>
					准确率: ${data.value}%<br/>
					总数: ${errorType?.total_count || 0}<br/>
					一致数: ${errorType?.consistent_count || 0}`;
			}
		},
		xAxis: {
			type: 'category',
			data: xAxisData,
			axisLabel: {
				interval: 0,
				rotate: 30,
				fontSize: 10
			}
		},
		yAxis: {
			type: 'value',
			name: '准确率(%)',
			min: 0,
			max: 100,
			axisLabel: {
				formatter: '{value}%'
			}
		},
		grid: {
			left: '15%',
			right: '10%',
			bottom: '25%',
			top: '10%'
		},
		series: [
			{
				type: 'bar',
				data: seriesData,
				barWidth: '60%',
				itemStyle: {
					borderRadius: [4, 4, 0, 0]
				}
			}
		]
	};

	errorTypeBarChart.setOption(option);

	// 添加点击事件
	errorTypeBarChart.on('click', (params: any) => {
		emit('chart-click', { type: 'error_type', value: params.name });
	});
};

// 初始化时间趋势图
const initTrendLineChart = () => {
	if (!trendLineRef.value) return;

	trendLineChart = echarts.init(trendLineRef.value);

	const xAxisData = props.trendData.map(item => item.date);
	const accuracyData = props.trendData.map(item => item.accuracy_rate);
	const countData = props.trendData.map(item => item.total_count);

	const option = {
		tooltip: {
			trigger: 'axis',
			formatter: (params: any) => {
				const date = params[0].name;
				const accuracy = params[0].value;
				const count = params[1].value;
				return `${date}<br/>准确率: ${accuracy}%<br/>样本数: ${count}`;
			}
		},
		legend: {
			data: ['准确率', '样本数'],
			bottom: 0
		},
		xAxis: {
			type: 'category',
			data: xAxisData,
			axisLabel: {
				rotate: 30,
				fontSize: 10
			}
		},
		yAxis: [
			{
				type: 'value',
				name: '准确率(%)',
				min: 0,
				max: 100,
				position: 'left',
				axisLabel: {
					formatter: '{value}%'
				}
			},
			{
				type: 'value',
				name: '样本数',
				position: 'right'
			}
		],
		grid: {
			left: '10%',
			right: '10%',
			bottom: '25%',
			top: '10%'
		},
		series: [
			{
				name: '准确率',
				type: 'line',
				yAxisIndex: 0,
				data: accuracyData,
				smooth: true,
				itemStyle: { color: '#1890ff' },
				lineStyle: { width: 3 }
			},
			{
				name: '样本数',
				type: 'bar',
				yAxisIndex: 1,
				data: countData,
				itemStyle: { 
					color: 'rgba(24, 144, 255, 0.3)',
					borderColor: '#1890ff',
					borderWidth: 1
				}
			}
		]
	};

	trendLineChart.setOption(option);
};

// 初始化所有图表
const initCharts = async () => {
	await nextTick();
	if (!props.loading) {
		initAccuracyPieChart();
		initErrorTypeBarChart();
		initTrendLineChart();
	}
};

// 响应式调整图表大小
const resizeCharts = () => {
	accuracyPieChart?.resize();
	errorTypeBarChart?.resize();
	trendLineChart?.resize();
};

// 销毁图表
const disposeCharts = () => {
	accuracyPieChart?.dispose();
	errorTypeBarChart?.dispose();
	trendLineChart?.dispose();
};

// 监听数据变化重新绘制图表
watch(
	() => [props.statsData, props.errorTypeData, props.trendData, props.loading],
	() => {
		if (!props.loading) {
			initCharts();
		}
	},
	{ deep: true }
);

// 组件挂载
onMounted(() => {
	initCharts();
	window.addEventListener('resize', resizeCharts);
});

// 组件卸载
onUnmounted(() => {
	window.removeEventListener('resize', resizeCharts);
	disposeCharts();
});
</script>

<style lang="scss" scoped>
.charts-container {
	margin-bottom: 24px;

	.charts-title {
		margin-bottom: 16px;

		h3 {
			margin: 0;
			color: #303133;
			font-size: 18px;
			font-weight: 600;
		}
	}

	.chart-card {
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
		border: 1px solid #f0f0f0;
		margin-bottom: 16px;
		overflow: hidden;

		.chart-header {
			padding: 16px 20px 8px;
			border-bottom: 1px solid #f0f0f0;

			h4 {
				margin: 0 0 4px;
				color: #303133;
				font-size: 16px;
				font-weight: 600;
			}

			p {
				margin: 0;
				color: #666666;
				font-size: 12px;
			}
		}

		.chart-content {
			padding: 16px;

			.chart-wrapper {
				width: 100%;
				height: 300px;
				min-height: 300px;
			}
		}
	}
}

// 响应式适配
@media (max-width: 768px) {
	.charts-container {
		.chart-card {
			.chart-content {
				padding: 12px;

				.chart-wrapper {
					height: 250px;
					min-height: 250px;
				}
			}
		}
	}
}
</style>
# 租户模式配置说明

## 功能背景

目前租户功能还处于待完成阶段以及测试阶段，在正式服下先不需要部署这个功能，避免影响正式服务，因此只需要先在测试服进行部署

## 功能概述

系统新增了租户模式环境配置开关，可以控制是否启用租户功能。当租户模式关闭时，登录页面将不显示租户输入框，系统按单租户模式运行。

## 配置方式

### 1. 环境变量配置（推荐）

通过 `.env` 文件配置，适合不同环境的部署需求：

```bash
# 环境变量名：VITE_TENANT_MODE
# 配置值：
#   true 或 "true"  - 启用租户模式（默认）
#   false 或 "false" - 禁用租户模式
```

### 2. 配置文件示例

**开发环境（.env.development）：**

```bash
# 开发环境配置
VITE_TENANT_MODE=true
VITE_PORT=3000
VITE_PUBLIC_PATH=/
```

**生产环境（.env.production）：**

```bash
# 生产环境配置
VITE_TENANT_MODE=false
VITE_PUBLIC_PATH=/
```

**本地环境（.env.local）：**

```bash
# 本地开发配置（优先级最高）
VITE_TENANT_MODE=true
```

### 3. 后备配置

如果环境变量未设置，系统会使用后端系统配置：

```javascript
// 配置键名：base.tenant_mode
// 配置值：true/false，默认为 true
```

## 功能影响

### 启用租户模式时（默认）

- ✅ 显示租户输入框
- ✅ 支持 URL 参数自动租户识别
- ✅ 支持手动输入租户名称
- ✅ 租户名称实时匹配验证
- ✅ 退出登录保持租户上下文

### 禁用租户模式时

- ❌ 隐藏租户输入框
- ❌ 忽略 URL 租户参数
- ❌ 不进行租户相关验证
- ❌ 不保存租户上下文
- ✅ 按单租户模式运行

## 实现原理

### 前端检查逻辑

```javascript
// 检查租户模式是否启用
const isTenantModeEnabled = computed(() => {
	// 优先使用环境变量配置
	const envTenantMode = import.meta.env.VITE_TENANT_MODE;
	if (envTenantMode !== undefined) {
		return envTenantMode === 'true' || envTenantMode === true;
	}

	// 如果环境变量未配置，则使用系统配置，默认为true
	const tenantMode = SystemConfigStore().systemConfig['base.tenant_mode'];
	return tenantMode !== false && tenantMode !== 'false';
});
```

### 条件渲染

```vue
<!-- 只有在租户模式启用且非自动模式时才显示租户输入框 -->
<el-form-item v-if="isTenantModeEnabled && !autoTenant.isAutoMode">
    <!-- 租户输入框 -->
</el-form-item>
```

## 使用场景

### 适合启用租户模式的场景

- 🏢 多租户 SaaS 应用
- 🏛️ 多机构系统
- 🏪 多商户平台
- 🌐 多站点管理系统

### 适合禁用租户模式的场景

- 🏠 单机构内部系统
- 📱 个人应用
- 🔧 工具类系统
- 🎯 专用业务系统

## 配置步骤

### 1. 环境变量配置（推荐）

**步骤 1：创建环境配置文件**

在项目根目录创建对应的环境配置文件：

```bash
# 开发环境
.env.development

# 生产环境
.env.production

# 本地环境（优先级最高，不会被提交到版本控制）
.env.local
```

**步骤 2：添加租户模式配置**

在对应的 `.env` 文件中添加：

```bash
# 启用租户模式
VITE_TENANT_MODE=true

# 或禁用租户模式
VITE_TENANT_MODE=false
```

**步骤 3：重启开发服务器**

```bash
npm run dev
# 或
yarn dev
```

### 2. 后端配置（后备方案）

如果需要通过后端动态配置：

1. 登录系统管理后台
2. 进入系统配置管理
3. 添加或修改配置项：
   - 配置键：`base.tenant_mode`
   - 配置值：`true` 或 `false`
4. 保存配置

### 3. 数据库直接配置

```sql
-- 启用租户模式
INSERT INTO system_config (config_key, config_value)
VALUES ('base.tenant_mode', 'true')
ON DUPLICATE KEY UPDATE config_value = 'true';

-- 禁用租户模式
INSERT INTO system_config (config_key, config_value)
VALUES ('base.tenant_mode', 'false')
ON DUPLICATE KEY UPDATE config_value = 'false';
```

## 配置优先级

配置的优先级从高到低：

1. **环境变量** (`VITE_TENANT_MODE`)
2. **系统配置** (`base.tenant_mode`)
3. **默认值** (`true`)

## 注意事项

### ⚠️ 重要提醒

1. **环境变量变更后需要重启开发服务器**才能生效
2. **生产环境需要重新构建和部署**
3. **禁用租户模式后，所有租户相关功能都会失效**
4. **建议在系统部署前确定租户模式**，避免频繁切换
5. **多租户数据隔离逻辑需要后端配合实现**

### 🔧 故障排除

- 如果配置不生效，检查环境变量名称是否正确：`VITE_TENANT_MODE`
- 确认环境变量值为字符串：`"true"` 或 `"false"`
- 开发环境重启服务器：`npm run dev`
- 生产环境重新构建：`npm run build`
- 检查浏览器缓存，尝试强制刷新

### 📁 文件示例

**.env.development**

```bash
# 开发环境 - 启用租户模式用于测试
VITE_TENANT_MODE=true
VITE_PORT=3000
VITE_PUBLIC_PATH=/
VITE_DIST_PATH=dist
```

**.env.production**

```bash
# 生产环境 - 根据实际需求配置
VITE_TENANT_MODE=false
VITE_PUBLIC_PATH=/
VITE_DIST_PATH=dist
```

**.env.local**

```bash
# 本地环境 - 个人开发配置（不会被提交到版本控制）
VITE_TENANT_MODE=true
```

## 兼容性

- ✅ 向后兼容：未配置时默认启用租户模式
- ✅ 平滑切换：可随时启用或禁用
- ✅ 环境隔离：不同环境可使用不同配置
- ✅ 数据安全：不会影响现有租户数据
- ✅ 开发友好：支持本地覆盖配置

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
	status: Number,
	modelValue: Object,
});

const objData = computed(() => {
	return props.modelValue;
});

const setTaskModeType = (type: string) => {
	const typeDict: { [key: string]: string } = {
		OR: '或签',
	};
	return typeDict[type] || type;
};

const setIcon = (status: Number) => {
	return status == 1 ? 'Check' : 'More';
};
const setType = (status: Number) => {
	return status == 1 ? 'success' : 'warning';
};
</script>

<template>
	<div style="padding-left: 10px">
		<el-timeline>
			<el-timeline-item
				:timestamp="item.create_datetime"
				placement="top"
				size="large"
				:icon="setIcon(item.nodeStatus)"
				:type="setType(item.nodeStatus)"
				v-for="item in objData"
			>
				<el-card>
					<template #header>
						<div class="header-title">
							<span>{{ item.nodeData.name }}</span>
						</div>
						<div style="margin-top: 5px" v-if="item.nodeData.node_type == 'Approval'">
							<span class="mr-2">审批模式</span>
							<el-tag>{{ setTaskModeType(item.nodeData.props.taskMode.type) }}</el-tag>
						</div>
					</template>
					<div v-if="item.nodeData.node_type == 'Start'">
						<div>{{ item.startUserName }}（发起流程）</div>
					</div>
					<div v-if="item.nodeData.node_type == 'Approval'">
						<!-- 指定人员 -->
						<div v-if="item.preInfo.pre_user && item.preInfo.pre_user.length > 0" class="flex justify-start gap-3">
							<div>指定人员（{{ item.preInfo.pre_user.length }}人）:</div>
							<div v-for="user in item.preInfo.pre_user" :key="user.id">
								<el-tag>{{ user.name }}</el-tag>
							</div>
						</div>

						<!-- 指定部门 -->
						<div v-if="item.preInfo.pre_dept && item.preInfo.pre_dept.length > 0" class="flex justify-start gap-3 mt-2">
							<div>指定部门（{{ item.preInfo.pre_dept.length }}个）:</div>
							<div v-for="dept in item.preInfo.pre_dept" :key="dept.id">
								<el-tag type="info">{{ dept.name }}</el-tag>
							</div>
						</div>

						<!-- 指定角色 -->
						<div v-if="item.preInfo.pre_role && item.preInfo.pre_role.length > 0" class="flex justify-start gap-3 mt-2">
							<div>指定角色（{{ item.preInfo.pre_role.length }}个）:</div>
							<div v-for="role in item.preInfo.pre_role" :key="role.id">
								<el-tag type="warning">{{ role.name }}</el-tag>
							</div>
						</div>

						<div class="flex justify-start gap-3 mt-2.5">
							<div>审核完成（{{ item.auditUsers.length }}人）:</div>
							<div v-for="user in item.auditUsers" :key="user.id">
								<el-tag type="success">{{ user.name }}</el-tag>
							</div>
						</div>
						<div class="mt-2.5" v-if="item.auditUsers.length > 0">审核完成时间：{{ item.auditUsers[0].audit_time }}</div>
						<div class="mt-2.5" v-if="item.auditUsers.length > 0">审核意见：{{ item.auditUsers[0].description }}</div>
					</div>
					<div v-if="item.nodeData.node_type == 'Cc'">
						<!-- 抄送指定人员 -->
						<div v-if="item.preInfo && item.preInfo.pre_user && item.preInfo.pre_user.length > 0" class="flex justify-start gap-3 mt-2.5">
							<div>抄送人员（{{ item.preInfo.pre_user.length }}人）:</div>
							<div v-for="user in item.preInfo.pre_user" :key="user.id">
								<el-tag type="success">{{ user.name }}</el-tag>
							</div>
						</div>

						<!-- 抄送指定部门 -->
						<div v-if="item.preInfo && item.preInfo.pre_dept && item.preInfo.pre_dept.length > 0" class="flex justify-start gap-3 mt-2.5">
							<div>抄送部门（{{ item.preInfo.pre_dept.length }}个）:</div>
							<div v-for="dept in item.preInfo.pre_dept" :key="dept.id">
								<el-tag type="info">{{ dept.name }}</el-tag>
							</div>
						</div>

						<!-- 抄送指定角色 -->
						<div v-if="item.preInfo && item.preInfo.pre_role && item.preInfo.pre_role.length > 0" class="flex justify-start gap-3 mt-2.5">
							<div>抄送角色（{{ item.preInfo.pre_role.length }}个）:</div>
							<div v-for="role in item.preInfo.pre_role" :key="role.id">
								<el-tag type="warning">{{ role.name }}</el-tag>
							</div>
						</div>

						<!-- 兼容旧的assignUser格式 -->
						<div
							v-if="item.nodeData.props && item.nodeData.props.assignUser && item.nodeData.props.assignUser.length > 0"
							class="flex justify-start gap-3 mt-2.5"
						>
							<div>抄送（{{ item.nodeData.props.assignUser.length }}人）:</div>
							<div v-for="user in item.nodeData.props.assignUser" :key="user.id">
								<el-tag type="success">{{ user.name }}</el-tag>
							</div>
						</div>
					</div>
				</el-card>
			</el-timeline-item>
			<!--    <el-timeline-item timestamp="2024/12/12" placement="top"  size="large" icon="More" type="warning">-->
			<!--      <el-card>-->
			<!--        <template #header>-->
			<!--          <div class="header-title">-->
			<!--            <span>审核人</span>-->
			<!--          </div>-->
			<!--          <div>2人审批(会签)</div>-->
			<!--        </template>-->
			<!--        <div>-->
			<!--          <div class="flex justify-start gap-3">-->
			<!--            <div>-->
			<!--              <el-tag type="success">猿小天(完成)</el-tag>-->
			<!--            </div>-->
			<!--          </div>-->
			<!--        </div>-->
			<!--      </el-card>-->
			<!--    </el-timeline-item>-->
			<el-timeline-item v-if="props.status == 0" timestamp="" placement="top" size="large" icon="loading" type="warning">
				<div>进行中</div>
			</el-timeline-item>
			<el-timeline-item v-if="props.status == 1" timestamp="" placement="top" size="large" icon="check" type="success">
				<div>审批通过</div>
			</el-timeline-item>
		</el-timeline>
	</div>
</template>

<style scoped lang="less">
.header-title {
	font-size: 1.2em;
	color: #686868;
}
</style>
# Vue3 前端枚举类型使用手册

## 📋 文档概述

本手册规范Vue3前端项目中枚举类型的使用、配置和展示，确保前后端枚举数据的一致性和使用的标准化。

---

## 🎨 前端枚举架构设计

### 系统架构概览
- **统一枚举存储**: 使用Pinia进行枚举数据的本地缓存和管理
- **全局工具方法**: 注册全局方法简化枚举操作
- **标准化展示组件**: dict-tag组件实现枚举值的统一展示
- **持久化缓存**: 避免重复请求，提升性能

### 数据流向
```
后端API接口 → Pinia存储服务 → 持久化缓存 → 全局工具方法 → 组件使用
```

---

## 🔧 枚举配置和存储

### 1. 枚举常量定义

#### 1.1 枚举类型常量文件
```typescript
// web/src/stores/constants/enum.ts

export const ENUM_TYPE = {
    // 任务相关
    TASK_TYPE: 'regulate.TaskType',
    TASK_STATUS: 'regulate.TaskStatus', 
    
    // 汇远轻媒
    TENANT_MEDIA_PERMISSION: 'hyqm.TenantMediaPermission',
    PLATFORM_TYPE: 'hyqm.PlatformType',
    
    // AI校对
    AI_JIAO_ACCOUNT: 'aijiaodui.AccountType',
    SHOW_TYPE: 'aijiaodui.ShowType',
    
    // 微信账号相关
    CRAWL_TASK_STATUS: 'wechat_official_account.CrawlTaskStatus',
    CRAWL_TASK_TYPE: 'wechat_official_account.CrawlTaskType',
    
    // 稿件相关
    MANUSCRIPT_STATUS: 'manuscript.ManuscriptStatus',
    MANUSCRIPT_AUDIT_STATUS: 'manuscript.AuditStatus',
};

// 值到颜色的映射关系
export const VALUE_TO_COLOR_MAPPING: any = {
    0: 'info',
    1: 'success', 
    2: 'warning',
    3: 'danger',
    true: 'success',
    false: 'danger',
};
```

#### 1.2 常量定义规范
- **格式要求**: `{group}.{enum_name}` 与后端分组和枚举名对应
- **命名规范**: 常量名使用UPPER_SNAKE_CASE
- **分组逻辑**: 按业务模块进行分组组织

### 2. Pinia枚举存储服务

#### 2.1 存储服务实现
```typescript
// web/src/stores/enum.ts

import { defineStore } from 'pinia';
import { request } from '/@/utils/service';
import { ENUM_TYPE, VALUE_TO_COLOR_MAPPING } from '/@/stores/constants/enum';

interface EnumState {
  enumMap: Record<string, any[]>;
}

export const useEnumStore = defineStore('Enum', {
  state: (): EnumState => ({
    enumMap: {},
  }),
  actions: {
    // 获取所有枚举
    async getAllEnums() {
      try {
        const response = await request({
          url: '/api/enums/',
          method: 'get',
        });
        
        // 处理返回的数据，将分组的数据扁平化存储
        Object.entries(response.data).forEach(([group, enums]: [string, any]) => {
          Object.entries(enums).forEach(([enumName, values]: [string, any]) => {
            this.enumMap[`${group}.${enumName}`] = values;
          });
        });
      } catch (error) {
        console.error('获取枚举失败:', error);
      }
    },

    // 根据枚举名称获取特定枚举
    async getEnumByName(enumName: string) {
      try {
        const response = await request({
          url: `/api/enums/${enumName}/`,
          method: 'get',
        });
        this.enumMap[enumName] = response.data;
        return response.data;
      } catch (error) {
        console.error(`获取枚举 ${enumName} 失败:`, error);
        return [];
      }
    },

    // 根据分组获取枚举
    async getEnumsByGroup(groupName: string) {
      try {
        const response = await request({
          url: `/api/enums/group/${groupName}/`,
          method: 'get',
        });
        
        // 处理分组数据
        Object.entries(response.data).forEach(([enumName, values]: [string, any]) => {
          this.enumMap[`${groupName}.${enumName}`] = values;
        });
        return response.data;
      } catch (error) {
        console.error(`获取分组 ${groupName} 的枚举失败:`, error);
        return {};
      }
    },
  },
  persist: {
    enabled: true, // 启用持久化存储
  },
});
```

#### 2.2 工具函数
```typescript
// web/src/stores/enum.ts (续)

// 获取枚举数据列表
export function getEnumDatas(enumType: string): any[] {
  const store = useEnumStore();
  return store.enumMap[enumType] || [];
}

// 获取特定枚举值的详细信息
export function getEnumData(enumType: string, value: any) {
  const enumDatas = getEnumDatas(enumType);
  if (!enumDatas || enumDatas.length === 0) {
    return undefined;
  }
  
  // 将值转换为字符串进行比较，避免类型不匹配问题
  const strValue = String(value);
  return enumDatas.find(item => String(item.value) === strValue);
}

// 获取枚举值对应的标签文本
export function getEnumLabel(enumType: string, value: any): string {
  const enumData = getEnumData(enumType, value);
  return enumData ? enumData.label : '';
}

// 获取枚举值对应的颜色
export function getEnumColor(value: any): string {
  return VALUE_TO_COLOR_MAPPING[value] || 'default';
}
```

### 3. 全局插件注册

#### 3.1 插件定义
```typescript
// web/src/utils/enum.ts

import { App } from 'vue';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas, getEnumData, getEnumLabel, getEnumColor } from '/@/stores/enum';

// 定义枚举数据的接口
export interface EnumData {
  value: string | number;
  label: string;
  [key: string]: any;
}

// 声明模块扩充全局属性
declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    $ENUM: typeof ENUM_TYPE;
    $getEnumDatas: (type: string) => EnumData[];
    $getEnumData: (type: string, value: string | number) => EnumData | undefined;
    $getEnumLabel: (type: string, value: string | number) => string;
    $getEnumColor: (type: string, value: string | number) => string;
  }
}

export const enumPlugin = {
  install: (app: App) => {
    // 注册全局属性和方法
    app.config.globalProperties.$ENUM = ENUM_TYPE;
    app.config.globalProperties.$getEnumDatas = getEnumDatas;
    app.config.globalProperties.$getEnumData = getEnumData;
    app.config.globalProperties.$getEnumLabel = getEnumLabel;
    app.config.globalProperties.$getEnumColor = getEnumColor;
  }
};

export default enumPlugin;
```

#### 3.2 插件使用
```typescript
// web/src/main.ts

import { createApp } from 'vue';
import App from './App.vue';
import enumPlugin from '/@/utils/enum';

const app = createApp(App);
app.use(enumPlugin);
app.mount('#app');
```

---

## 🎯 统一展示组件

### 1. dict-tag组件

#### 1.1 组件实现
```vue
<!-- web/src/components/dict-tag/index.vue -->
<template>
  <span>
    <template v-if="currentDictData">
      <!-- 默认样式 -->
      <span v-if="colorType === 'default' || !colorType" :class="currentDictData.cssClass">
        {{ currentDictData.label }}
      </span>
      <!-- Tag样式 -->
      <el-tag v-else :disable-transitions="true" :type="getTagColor" :class="currentDictData.cssClass">
        {{ currentDictData.label }}
      </el-tag>
    </template>
  </span>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue';

interface DictData {
  label: string;
  value: string | number | boolean;
  cssClass?: string;
}

export default defineComponent({
  name: 'DictTag',
  props: {
    // 选项数组
    options: {
      type: Array as PropType<DictData[]>,
      required: true,
    },
    // 当前值
    value: {
      type: [Number, String, Boolean, Array] as PropType<number | string | boolean | Array<any>>,
      required: true,
    },
    // tag显示类型，可以是字符串或数组
    colorType: {
      type: [String, Array] as PropType<string | string[]>,
      default: 'default',
    },
  },
  setup(props) {
    // 当前字典数据
    const currentDictData = computed(() => {
      const valueStr = String(props.value);
      return props.options.find((item: DictData) => String(item.value) === valueStr);
    });

    // 获取标签颜色
    const getTagColor = computed(() => {
      if (!currentDictData.value) return 'default';

      // 如果colorType是字符串，直接返回
      if (typeof props.colorType === 'string') {
        return props.colorType;
      }

      // 如果colorType是数组
      if (Array.isArray(props.colorType)) {
        // 获取当前值在字典列表中的索引
        const index = props.options.findIndex((item: DictData) => 
          String(item.value) === String(props.value)
        );

        // 如果找到了索引，并且colorType数组中有对应的颜色，返回对应颜色
        if (index !== -1 && index < props.colorType.length) {
          return props.colorType[index];
        }
      }

      // 默认返回default
      return 'default';
    });

    return {
      currentDictData,
      getTagColor,
    };
  },
});
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
```

#### 1.2 组件特性
- **灵活的数据源**: 支持字典数据、枚举数据或自定义数据
- **多样的颜色配置**: 支持单一颜色或数组映射
- **类型安全**: 完整的TypeScript类型定义
- **样式可扩展**: 支持自定义CSS类

---

## 💡 使用示例和最佳实践

### 1. 初始化配置

#### 1.1 应用启动时获取枚举
```typescript
// web/src/router/backEnd.ts

import { useEnumStore } from "/@/stores/enum";
import { BtnPermissionStore } from '/@/stores/btnPermission';
import { DictionaryStore } from '/@/stores/dictionary';

export function getBackEndControlRoutes() {
    // 获取按钮权限
    BtnPermissionStore().getBtnPermissionStore();
    // 获取系统字典
    DictionaryStore().getSystemDictionarys();
    // 获取所有枚举 - 关键步骤
    useEnumStore().getAllEnums();
    
    return menuApi.getSystemMenu();
}
```

### 2. Vue组件中使用

#### 2.1 在模板中使用全局方法
```vue
<template>
  <div>
    <!-- 使用dict-tag组件展示枚举值 -->
    <dict-tag
      :options="$getEnumDatas($ENUM.TASK_STATUS)"
      :value="taskStatus"
      :color-type="['info', 'primary', 'success', 'warning', 'danger']"
    />
    
    <!-- 直接显示枚举标签 -->
    <span>{{ $getEnumLabel($ENUM.TASK_TYPE, taskType) }}</span>
    
    <!-- 使用枚举数据的下拉选择器 -->
    <el-select v-model="taskType">
      <el-option
        v-for="item in $getEnumDatas($ENUM.TASK_TYPE)"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  setup() {
    const taskStatus = ref(1);
    const taskType = ref(null);
    
    return { taskStatus, taskType };
  },
});
</script>
```

#### 2.2 在普通表格中使用
```vue
<template>
  <el-table :data="taskList">
    <el-table-column label="任务状态" width="120">
      <template #default="{ row }">
        <dict-tag
          :options="$getEnumDatas($ENUM.TASK_STATUS)"
          :value="row.status"
          :color-type="['info', 'warning', 'primary', 'success', 'danger']"
        />
      </template>
    </el-table-column>
    
    <el-table-column label="任务类型" width="120">
      <template #default="{ row }">
        <span>{{ $getEnumLabel($ENUM.TASK_TYPE, row.task_type) }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>
```

### 3. Fast-CRUD中使用

#### 3.1 在CRUD配置中使用
```typescript
// views/regulate/proofread_task/crud.tsx

import { dict } from '@fast-crud/fast-crud';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';

export default function createCrudOptions() {
  return {
    columns: {
      status: {
        title: '任务状态',
        type: 'dict-select',
        dict: dict({
          data: getEnumDatas(ENUM_TYPE.TASK_STATUS)
        }),
        search: { show: true },
      },
      task_type: {
        title: '任务类型', 
        type: 'dict-select',
        dict: dict({
          data: getEnumDatas(ENUM_TYPE.TASK_TYPE)
        }),
        search: { show: true },
      },
    }
  };
}
```

### 4. 组件内部使用

#### 4.1 使用Composition API
```vue
<script lang="ts">
import { defineComponent, computed } from 'vue';
import { getEnumDatas, getEnumLabel } from '/@/stores/enum';
import { ENUM_TYPE } from '/@/stores/constants/enum';

export default defineComponent({
  setup() {
    // 获取枚举选项
    const statusOptions = computed(() => {
      return getEnumDatas(ENUM_TYPE.TASK_STATUS);
    });
    
    // 获取状态标签的方法
    const getStatusLabel = (status: number) => {
      return getEnumLabel(ENUM_TYPE.TASK_STATUS, status);
    };
    
    return {
      statusOptions,
      getStatusLabel,
    };
  },
});
</script>
```

### 5. dict-tag组件使用示例

#### 5.1 基础用法
```vue
<template>
  <!-- 使用枚举数据 -->
  <dict-tag
    :options="$getEnumDatas($ENUM.OPERATION_REPORT_STATUS)"
    :value="scope.row.status"
    :color-type="['info', 'primary', 'success', 'warning']"
  />

  <!-- 使用自定义数据 -->
  <dict-tag
    :options="[
      { label: '正常', value: 1, cssClass: 'text-success' },
      { label: '禁用', value: 0, cssClass: 'text-danger' }
    ]"
    :value="status"
    :color-type="['success', 'danger']"
  />
  
  <!-- 单一颜色 -->
  <dict-tag
    :options="statusOptions"
    :value="currentStatus"
    color-type="success"
  />
</template>
```

---

## 🔄 维护和更新

### 1. 新增枚举常量流程
1. 确认后端已添加并配置新的枚举类型
2. 在 `web/src/stores/constants/enum.ts` 中添加对应常量
3. 确保常量格式为 `{group}.{enum_name}` 
4. 重启应用，枚举数据会自动加载

### 2. 枚举数据更新
- **自动更新**: 应用启动时会自动从API获取最新枚举数据
- **手动更新**: 可调用 `useEnumStore().getAllEnums()` 强制更新
- **缓存清除**: 清除浏览器本地存储后重新获取

### 3. 颜色映射调整
在 `VALUE_TO_COLOR_MAPPING` 中根据业务需要调整值到颜色的映射关系：
```typescript
export const VALUE_TO_COLOR_MAPPING: any = {
    0: 'info',      // 待处理 - 信息色
    1: 'success',   // 成功 - 成功色  
    2: 'warning',   // 警告 - 警告色
    3: 'danger',    // 失败 - 危险色
    // 根据业务需求继续扩展
};
```

---

## 📝 文档更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|---------|
| 1.0.0 | 2025-08-07 | 初始版本创建，从完整手册拆分前端部分 | AI助手 |
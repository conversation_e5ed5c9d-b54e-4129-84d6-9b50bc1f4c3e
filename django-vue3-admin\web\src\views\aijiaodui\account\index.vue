<template>
	<fs-page class="aijiaodui-account">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<!-- 自定义高速校对字数显示 -->
			<template #cell_high_speed_words="scope">
				<el-tag :type="getWordCountTagType(scope.row.high_speed_words)">
					{{ formatWordCount(scope.row.high_speed_words) }}
				</el-tag>
			</template>

			<!-- 自定义功能角色显示 -->
			<template #cell_function_type="scope">
				<dict-tag :options="$getEnumDatas($ENUM.FUNCTION_TYPE)" :value="scope.row.function_type" color-type="primary" />
			</template>
			<template #cell_show_type="scope">
				<el-select v-model="scope.row.show_type">
					<el-option v-for="item in linkType" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</template>
		</fs-crud>
	</fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { DictionaryStore } from '/@/stores/dictionary';

export default defineComponent({
	name: 'AijiaoduiAccount',
	setup() {
		// crud组件的ref
		const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
		// 获取字典数据
		const dictionaryStore = DictionaryStore();

		// 在组件挂载后加载数据
		onMounted(() => {
			crudExpose.doRefresh();
			if (Object.keys(dictionaryStore.data).length === 0) {
				dictionaryStore.getSystemDictionarys();
			}
		});
		const linkType = dictionaryStore.data.link_type;

		// 格式化字数显示
		const formatWordCount = (words: number | null) => {
			if (words === null) return '未知';
			if (words >= 10000) {
				return `${(words / 10000).toFixed(1)}万字`;
			}
			return `${words}字`;
		};

		// 获取字数标签类型
		const getWordCountTagType = (words: number | null) => {
			if (words === null) return 'info';
			if (words >= 100000) return 'success';
			if (words >= 50000) return 'warning';
			return 'danger';
		};

		// 获取功能角色标签类型
		const getFunctionTypeTagType = (type: string) => {
			const typeMap: { [key: string]: string } = {
				pre_public: 'info',
				pre_private: 'success',
				post_public: 'warning',
			};
			return typeMap[type] || 'info';
		};

		// 获取功能角色显示文本
		const getFunctionTypeLabel = (type: string) => {
			const labelMap: { [key: string]: string } = {
				pre_public: '事前公共',
				pre_private: '事前独立',
				post_public: '公共巡检',
			};
			return labelMap[type] || type;
		};

		return {
			crudBinding,
			crudRef,
			crudExpose,
			linkType,
			formatWordCount,
			getWordCountTagType,
			getFunctionTypeTagType,
			getFunctionTypeLabel,
		};
	},
});
</script>

<style scoped>
:deep(.fs-page) {
	padding: 0;
}
</style> 
# 租户监管账号绑定功能实现文档

## 功能概述

为租户管理系统新增监管账号绑定功能，支持租户与微信公众号、抖音、微博三个平台的账号进行绑定关系管理。

## 实现内容

### 1. API 接口扩展

**文件位置：** `src/views/tenant/api.ts`

**新增接口：**

- `queryMediaAccountsByTenantAndPlatform()` - 查询监管账号列表
- `batchToggleAccountSelection()` - 批量绑定/解绑账号
- `batchSetOperatingType()` - 批量设置运营类型

### 2. 租户管理页面增强

**文件位置：** `src/views/tenant/crud.tsx`

**新增功能：**

- 添加"绑定监管账号"操作按钮
- 使用全局事件通信触发账号绑定弹窗
- 支持权限控制：`auth('TenantMediaAccounts:Bind')`

### 3. 账号绑定弹窗组件

**文件位置：** `src/views/tenant/components/AccountBindDialog.vue`

**核心功能：**

- 平台切换：支持微信公众号、抖音、微博
- 账号管理：基于穿梭框组件实现账号绑定/解绑
- 实时绑定：账号选择变化时立即执行绑定操作
- 响应式设计：适配不同屏幕尺寸

### 4. 主页面集成

**文件位置：** `src/views/tenant/index.vue`

**集成方式：**

- 引入 `AccountBindDialog` 组件
- 通过全局事件监听触发弹窗

## 接口依赖

1. **获取监管账号列表**

   - 接口：`/api/hyqm/tenant-media-accounts/query_by_tenant_and_platform/`
   - 参数：`tenant_id`, `platform_type`

2. **批量绑定账号**
   - 接口：`/api/hyqm/tenant-media-accounts/batch_toggle_selection/`
   - 参数：`media_account_ids`, `tenant_id`, `platform_type`, `is_selected`

## 使用说明

### 操作流程

1. 进入租户管理页面
2. 点击"绑定监管账号"按钮
3. 选择平台类型（微信/抖音/微博）
4. 使用穿梭框选择需要绑定的账号
5. 系统自动实时保存绑定关系

## 功能修正记录

### 关键问题修复

1. **平台切换问题**：修正了 v-model 双向绑定导致的平台切换不生效问题
2. **组件导入问题**：修正了 transfer 组件的导入方式
3. **API 数据处理**：优化了账号列表数据的处理逻辑，支持多种返回格式
4. **实时绑定功能**：实现了账号选择变化时的实时绑定操作
5. **错误处理**：添加了绑定失败时的数据回滚机制

### 数据流程优化

- 响应码判断：使用 `code === 2000` 而非 `code === 200`
- 数据结构：从 `data.all` 获取全部账号，从 `data.selected` 获取已绑定账号
- 字段映射：使用 `media_account_id` 作为主键，兼容 `id` 字段

## 开发记录

### 执行内容

- **选择原因**：采用渐进式升级方案，降低开发风险
- **实施内容**：
  - 修改文件：`src/views/tenant/api.ts` - 新增监管账号相关 API
  - 修改文件：`src/views/tenant/crud.tsx` - 添加绑定账号按钮
  - 创建文件：`src/views/tenant/components/AccountBindDialog.vue` - 账号绑定弹窗
  - 修改文件：`src/views/tenant/index.vue` - 集成弹窗组件
- **关键参数配置**：
  - 平台类型：`wechat`, `douyin`, `weibo`
  - 接口前缀：`/api/hyqm/tenant-media-accounts/`
  - 权限标识：`TenantMediaAccounts:Bind`

---

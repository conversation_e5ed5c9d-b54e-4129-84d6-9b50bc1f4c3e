<template>
	<div class="filter-container">
		<div class="filter-item">
			<span class="label">任务ID</span>
			<el-input v-model="searchId" placeholder="请输入任务ID" clearable @keyup.enter="handleSearch" />
		</div>
		<div class="filter-item">
			<span class="label">任务名称</span>
			<el-input v-model="searchName" placeholder="请输入任务名称" clearable @keyup.enter="handleSearch" />
		</div>
		<div class="filter-item">
			<span class="label">任务状态</span>
			<el-select v-model="searchTaskStatus" placeholder="请选择任务状态" clearable filterable style="width: 220px">
				<el-option v-for="task in $getEnumDatas($ENUM.TASK_STATUS)" :key="task.value" :label="task.label" :value="task.value" />
			</el-select>
		</div>
		<div class="filter-item" v-auth="'Tenants:List'">
			<span class="label">客户</span>
			<el-select v-model="searchTenant" placeholder="请选择客户" clearable filterable style="width: 220px">
				<el-option v-for="tenant in tenants" :key="tenant.id" :label="tenant.name" :value="tenant.id" />
			</el-select>
		</div>
		<div class="filter-buttons">
			<el-button type="primary" @click="handleSearch" icon="Search">查询</el-button>
			<el-button @click="handleReset" icon="Refresh">重置</el-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, PropType } from 'vue';

// 定义属性
const props = defineProps({
	id: {
		type: Number,
		default: undefined,
	},
	name: {
		type: String,
		required: true,
	},
	status: {
		type: Number,
		default: undefined,
	},
	tenants: {
		type: Array as PropType<{ id: number | string; name: string }[]>,
		required: true,
	},
	tenant: {
		type: Number,
		default: undefined,
	},
});

// 定义事件
const emit = defineEmits(['update:id', 'update:name', 'update:status', 'update:tenant', 'search', 'reset']);

// 本地状态
const searchId = ref(props.id);
const searchName = ref(props.name);
const searchTaskStatus = ref(props.status);
const searchTenant = ref(props.tenant);

// 监听属性变化
watch(
	() => props.id,
	(newVal) => {
		searchId.value = newVal;
	}
);

watch(
	() => props.name,
	(newVal) => {
		searchName.value = newVal;
	}
);

watch(
	() => props.status,
	(newVal) => {
		searchTaskStatus.value = newVal;
	}
);

watch(
	() => props.tenant,
	(newVal) => {
		searchTenant.value = newVal;
	}
);
// 同步本地状态到父组件
watch(searchId, (newVal) => {
	emit('update:id', newVal);
});

watch(searchName, (newVal) => {
	emit('update:name', newVal);
});

watch(searchTaskStatus, (newVal) => {
	emit('update:status', newVal);
});

watch(searchTenant, (newVal) => {
	emit('update:tenant', newVal);
});
// 处理搜索
const handleSearch = () => {
	emit('search');
};

// 处理重置
const handleReset = () => {
	searchId.value = undefined;
	searchName.value = '';
	searchTaskStatus.value = undefined;
	searchTenant.value = undefined;
	emit('reset');
};
</script>

<style scoped>
.filter-container {
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	gap: 15px;
	margin-bottom: 10px;
	padding: 15px;
	background-color: #f5f7fa;
	border-radius: 4px;
}

.filter-item {
	display: flex;
	align-items: center;
	margin: 0;
}

.label {
	margin-right: 10px;
	white-space: nowrap;
	color: #606266;
	font-size: 14px;
}

.filter-buttons {
	display: flex;
	gap: 10px;
	margin-left: auto;
}

@media screen and (max-width: 768px) {
	.filter-container {
		flex-direction: column;
		align-items: stretch;
	}

	.filter-item {
		width: 100%;
	}

	.filter-buttons {
		margin-left: 0;
		justify-content: flex-end;
	}
}
</style>

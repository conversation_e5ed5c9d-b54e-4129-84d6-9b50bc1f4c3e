<template>
	<div>
		<!-- 模拟固定在屏幕右侧中间位置的一个导出按钮 -->
		<div class="btn-wrapper">
			<el-button type="primary" plain :icon="Download" v-auth="'correctionDetail:Export'" class="btn" @click="exportReport" v-if="accountList.length > 0" :loading="exportLoading"
				>导出报告</el-button
			>
		</div>
		<div class="wx-container" id="export-content">
			<el-row v-loading="pageLoading" class="report-container">
				<!-- 标题区域使用背景图 -->
				<div class="wx-top">
					<div class="top-title">{{ title }}</div>
					<!-- <div class="date-range">统计时间：{{ dateRange }}</div>
					<div class="company">汇远轻媒出品</div> -->
				</div>

				<!-- 添加空数据判断 -->
				<div v-if="!accountList || accountList.length === 0">
					<div class="no-data">无绑定账号</div>
				</div>

				<div v-else>
					<!-- 排行榜 -->
					<div class="wx-table-wrapper">
						<div v-for="(table, index) in chunkedWci" :key="index">
							<div class="report-section" :id="`correct-table-${index + 1}`">
								<div class="wx-title-wrapper" v-if="index === 0"><div class="wx-title1">账号错误排行榜</div></div>
								<div class="wx-table-wrapper">
									<el-table
										:data="table"
										border
										:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
										:cell-style="{ height: '45px' }"
									>
										<el-table-column label="账号名称" prop="media_account_name" align="center" min-width="300px" />
										<el-table-column label="发文总数" prop="total_articles" align="center" />
										<el-table-column label="错误数量" prop="total_errors" align="center" />
										<el-table-column label="错误率" align="center" prop="error_rate" :formatter="(row) => row.error_rate + '%'"> </el-table-column>
										<el-table-column label="错误率排名" prop="error_rate_rank" align="center" />
										<el-table-column label="错误数排名" prop="error_count_rank" align="center" />
									</el-table>
								</div>
							</div>
						</div>
					</div>
					<div id="correct-1">
						<!-- Top3说明 校对目标没有此部分内容-->
						<div class="wx-title-wrapper"><div class="wx-title1">Top3账号分析</div></div>

						<div class="top-analysis">
							<div class="wx-title2">错误率Top3账号</div>
							<div class="wx-content-wrapper">
								<div class="wx-content">
									<div v-for="(item, index) in errorRateTop3.accounts" :key="'rate-' + index">
										<p style="margin-bottom: 10px">
											{{ errorRateTop3.period }}，发文错误率{{ index === 0 ? '最高的' : index === 1 ? '第二名' : '第三名' }}的账号是
											<span class="text-blue"
												><b>{{ item.media_account_name }}</b></span
											>，文章错误数：<b>{{ item.total_errors }}</b
											>，文章错误率：<b>{{ item.error_rate }}%</b>；错误数量前三种类型分别是
											<span v-if="item.top_error_types[0]">
												<b>{{ item.top_error_types[0].error_type }}</b
												>：<b>{{ item.top_error_types[0].count }}</b
												>次，占<b>{{ item.top_error_types[0].percentage }}</b
												>%；
											</span>
											<span v-if="item.top_error_types[1]">
												<b>{{ item.top_error_types[1].error_type }}</b
												>：<b>{{ item.top_error_types[1].count }}</b
												>次，占<b>{{ item.top_error_types[1].percentage }}</b
												>%；
											</span>
											<span v-if="item.top_error_types[2]">
												<b>{{ item.top_error_types[2].error_type }}</b
												>：<b>{{ item.top_error_types[2].count }}</b
												>次，占<b>{{ item.top_error_types[2].percentage }}</b
												>% 。</span
											>
										</p>
									</div>
								</div>
							</div>
						</div>
						<div class="top-analysis">
							<div class="wx-title2">错误数Top3账号</div>
							<div class="wx-content-wrapper">
								<div class="wx-content">
									<div v-for="(item, index) in errorCountTop3.accounts" :key="'rate-' + index">
										<p style="margin-bottom: 10px">
											{{ errorRateTop3.period }}，发文错误数{{ index === 0 ? '最高的' : index === 1 ? '第二名' : '第三名' }}的账号是
											<span class="text-blue"
												><b>{{ item.media_account_name }}</b></span
											>，账号共发文<b>{{ item.total_articles }}</b
											>篇，文章错误数：<b>{{ item.total_errors }}</b
											>；错误数量前三种类型分别是
											<span v-if="item.top_error_types[0]">
												<b>{{ item.top_error_types[0].error_type }}</b
												>，<b>{{ item.top_error_types[0].count }}</b
												>次，占<b>{{ item.top_error_types[0].percentage }}</b
												>%；
											</span>
											<span v-if="item.top_error_types[1]">
												<b>{{ item.top_error_types[1].error_type }}</b
												>，<b>{{ item.top_error_types[1].count }}</b
												>次，占<b>{{ item.top_error_types[1].percentage }}</b
												>%；
											</span>
											<span v-if="item.top_error_types[2]">
												<b>{{ item.top_error_types[2].error_type }}</b
												>，<b>{{ item.top_error_types[2].count }}</b
												>次，占<b>{{ item.top_error_types[2].percentage }}</b
												>%。
											</span>
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- 重点监管对象 -->
					<!-- <div class="wx-title-wrapper"><div class="wx-title1">重点监管</div></div>
					<div class="wx-content-wrapper">
						<div class="wx-content">
							<div class="top-analysis">
								<div class="wx-title4">重点监管账号：</div>
								<p v-for="(item, index) in errorRateTop3" :key="'rate-' + index">
									{{ item.description }}
								</p>
							</div>
						</div>
					</div> -->

					<!-- 趋势分析 -->
					<div id="correct-2">
						<div class="wx-title-wrapper"><div class="wx-title1">错误分析</div></div>
						<div class="wx-title-wrapper"><div class="wx-title4">错误趋势分析</div></div>
						<div class="chart-container">
							<line-chart :data="trendData" style="width: 95%; height: 350px" :axis-font-size="16" :legend-font-size="16" :axis-name-font-size="16"></line-chart>
						</div>

						<!-- 错误类型分布 -->
						<div class="wx-title-wrapper"><div class="wx-title4">错误类型分布</div></div>
						<div class="chart-container">
							<pie :data="errorTypeData" style="width: 100%; height: 350px" :label-font-size="16"></pie>
						</div>
					</div>
					<div id="correct-3">
						<!-- 高频错误TOP10 -->
						<div class="wx-title-wrapper"><div class="wx-title1">高频错误TOP10</div></div>
						<div class="wx-table-wrapper">
							<el-table :data="errorTop" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }">
								<el-table-column label="序号" type="index" width="80" align="center" />
								<el-table-column label="错误内容" prop="wrong_word" align="center" />
								<el-table-column label="正确词" prop="correct_word" align="center" />
								<el-table-column label="错误类型" prop="error_type" align="center" width="200" />
								<el-table-column label="出现次数" prop="error_count" align="center" width="120" />
							</el-table>
						</div>
					</div>
					<!-- 文章错误TOP10 -->
					<div id="correct-4">
						<div class="wx-title-wrapper"><div class="wx-title1">文章错误TOP10</div></div>
						<div class="wx-table-wrapper">
							<el-table :data="articleError" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }">
								<el-table-column label="排名" prop="rank" width="80" align="center" />
								<el-table-column label="文章标题" prop="article_title" align="center" />
								<el-table-column label="所属账号" prop="media_account_name" align="center" width="200" />
								<el-table-column label="错误数量" prop="error_count" align="center" width="120" />
								<el-table-column label="发文时间" prop="publish_time" align="center" width="180" />
							</el-table>
						</div>
					</div>
				</div>
			</el-row>
		</div>
	</div>
</template>
  
<script setup>
import { ref, onMounted, computed } from 'vue';
import { Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ElNotification } from 'element-plus';
import pie from '/@/components/Echarts/pie.vue';
import lineChart from '/@/components/Echarts/line.vue';
import { getCorrectionDetail } from '/@/views/analysisHelper/correctionAnalysis/api';
import { useRoute } from 'vue-router';
import { exportToPDF } from '/@/utils/pdfExportHelper';

// 状态定义
const route = useRoute();
const pageLoading = ref(false);
const exportLoading = ref(false);
const title = ref('');
const dateRange = ref('');
const accountList = ref([]);
const errorRateTop3 = ref([]);
const errorCountTop3 = ref([]);
const trendData = ref({});
const errorTypeData = ref([]);
const errorTop = ref([]);
const articleError = ref([]);

// 获取报告详情数据
const getReportDetail = async () => {
	try {
		pageLoading.value = true;
		const id = route.query.id;
		console.log(route, id, 'route');
		if (!id) {
			throw new Error('报告ID不能为空');
		}
		const response = await getCorrectionDetail(Number(id));
		console.log(response, 'response');

		// 设置标题和基本信息
		title.value = response.data.report_name || '';
		dateRange.value = `${response.data.start_time?.split('T')[0] || ''} ~ ${response.data.end_time?.split('T')[0] || ''}`;

		// 设置账号列表
		accountList.value = response.data.report_data.account_stats;

		// 设置错误率Top3
		errorRateTop3.value = response.data.report_data.top_error_rate_accounts;
		// 设置错误数Top3
		errorCountTop3.value = response.data.report_data.top_error_count_accounts;

		// 设置趋势数据
		const dates = response.data.report_data.time_series_stats.dates;
		const row = response.data.report_data.time_series_stats.row.map((item, index) => {
			if (index === 1) {
				return { ...item, type: 'bar' };
			}
			return item;
		});
		trendData.value = { dates, row };
		// 设置错误类型分布
		errorTypeData.value = response.data.report_data.error_type_stats;

		// 设置高频错误TOP10
		errorTop.value = response.data.report_data.high_freq_error_stats;
		articleError.value = response.data.report_data.top_error_article_stats;
	} catch (error) {
		console.error('获取报告详情失败:', error);
		ElMessage.error('获取报告详情失败');
	} finally {
		pageLoading.value = false;
	}
};

// 将数据按每10个分割成一个新数组，仅用于导出
const chunkedWci = computed(() => {
	const tables = [];
	const list = accountList.value || [];

	// 先截取前10个
	if (list.length > 0) {
		const firstChunk = list.slice(0, 10);
		tables.push(firstChunk);
	}

	// 从第10个开始，每次截取12个
	for (let i = 10; i < list.length; i += 11) {
		const chunk = list.slice(i, i + 11);
		if (chunk.length > 0) {
			tables.push(chunk);
		}
	}
	return tables;
});

const exportReport = async () => {
	try {
		exportLoading.value = true;
		// 导出报告逻辑
		ElNotification.info({
			title: 'PDF正在导出，请耐心等待',
			duration: 1500,
		});
		const moduleIds = [
			{ moduleId: 'correct-1', pageBreak: true },
			{ moduleId: 'correct-2', pageBreak: true },
			{ moduleId: 'correct-3', pageBreak: true },
			{ moduleId: 'correct-4', pageBreak: true },
		];
		// 根据chunkedWci的长度添加额外的表格ID，但仅在每个表格确实有内容时才添加
		const tableIds = [];
		if (chunkedWci.value && chunkedWci.value.length > 0) {
			for (let i = 0; i < chunkedWci.value.length; i++) {
				// 检查表格是否有内容，只有当表格有内容时才添加ID
				if (chunkedWci.value[i] && chunkedWci.value[i].length > 0) {
					tableIds.push({ moduleId: `correct-table-${i + 1}`, pageBreak: true });
				}
			}
		}

		// 获取当前页面的全部高度
		await exportToPDF([...tableIds, ...moduleIds], `${title.value}`, {
			orientation: 'landscape',
			format: 'a4',
			quality: 0.3,
			scale: 2.435,
			// scale: 3.03,
		});

		ElMessage.success('PDF导出完成');
	} catch (error) {
		console.error('导出过程出错:', error);
		ElMessage.error('导出过程出错，请重试');
	} finally {
		exportLoading.value = false;
	}
};

// 生命周期
onMounted(() => {
	getReportDetail();
});
</script>
  
<style scoped>
.wx-container {
	min-height: 100vh;
	position: relative;
	background-color: #fff;
}
.wx-top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	/* overflow: hidden; */
	height: 200px;
}
.report-container {
	display: block;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.date-range {
	font-size: 16px;
	margin-top: 20px;
}

.company {
	font-size: 14px;
	float: right;
	color: #4678ab;
}

.wx-title-wrapper {
	width: 100%;
}

.wx-title1 {
	font-size: 30px;
	width: 25%;
	margin: 0 auto;
	text-align: center;
	border-bottom: rgb(***********) 2px solid;
	padding-bottom: 5px;
	line-height: 1.5;
}

.wx-title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	margin: 0 auto;
	margin-top: -10px;
	text-align: center;
	padding-bottom: 5px;
	line-height: 1.5;
}
.wx-title3 {
	font-size: 16px;
	margin-left: 40px;
	color: #008ccc;
	font-weight: bold;
}
.wx-title4 {
	font-size: 18px;
	font-weight: bold;
	text-align: center;
	margin: 0 auto;
}
.top-analysis {
	margin-bottom: 20px;
}
.wx-content-wrapper {
	margin: 0 30px 20px;
	padding: 7px 20px 20px 20px;
	border: #9d9c9a 1px solid;
}

.wx-content {
	font-size: 18px;
	line-height: 30px;
	white-space: inherit !important;
}
/* 调整标题下方横线的样式 */
.wx-title1 {
	position: relative;
	padding-bottom: 15px;
	margin: 10px auto;
	border-bottom: #008ccc 2px solid;
}
.wx-title2 {
	position: relative;
	padding-bottom: 10px;
	margin: 0px auto 10px;
}
:deep(.el-table th) {
	font-weight: bold;
	background-color: #f8f8f9;
	color: #000;
	padding: 15px 4px !important;
	height: 45px !important;
	line-height: 2 !important;
}
:deep(.el-table .cell) {
  color: #000;
  font-size: 18px;
}
.wx-table-wrapper {
	margin: 20px 30px;
}

.text-bold {
	font-weight: bold;
}

.chart-container {
	margin: 0 0 15px 10px;
	overflow: visible;
	min-height: 300px;
	position: relative;
	page-break-inside: avoid;
}

.text-blue {
	color: #00abf9;
}
:deep(.el-loading-spinner) {
	top: 300px;
}
.btn-wrapper {
	position: fixed;
	right: 50px;
	top: 95px;
	z-index: 1000;
}
.no-data {
	width: 100%;
	margin: 10px auto;
	font-size: 26px;
	text-align: center;
}
* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}
</style>
  
<template>
	<el-dialog v-model="dialogVisible" title="删除确认" width="400px" :close-on-click-modal="false" @close="handleClose">
		<div class="delete-content">
			<el-icon class="warning-icon"><warning-filled /></el-icon>
			<p class="warning-text">确定要删除任务"{{ taskName }}"吗？此操作不可恢复。</p>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="danger" @click="handleConfirm">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue';

// 定义属性
const props = defineProps({
	visible: {
		type: Boolean,
		required: true,
	},
	taskName: {
		type: String,
		required: true,
	},
});

// 定义事件
const emit = defineEmits(['update:visible', 'confirm']);

// 本地状态
const dialogVisible = ref(props.visible);

// 监听属性变化
watch(
	() => props.visible,
	(newVal) => {
		dialogVisible.value = newVal;
	}
);

// 监听本地状态变化
watch(dialogVisible, (newVal) => {
	emit('update:visible', newVal);
});

// 处理关闭
const handleClose = () => {
	emit('update:visible', false);
};

// 处理确认
const handleConfirm = () => {
	emit('confirm');
	handleClose();
};
</script>

<style scoped>
.delete-content {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px 0;
}

.warning-icon {
	font-size: 24px;
	color: var(--el-color-danger);
}

.warning-text {
	margin: 0;
	font-size: 14px;
	color: var(--el-text-color-regular);
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}
</style> 
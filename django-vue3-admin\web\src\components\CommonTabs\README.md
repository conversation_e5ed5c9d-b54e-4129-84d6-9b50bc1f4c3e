# CommonTabs 通用标签页组件

一个基于 Element Plus 的通用标签页组件，支持自定义样式、图标和禁用状态。

## 特性

- 支持 v-model 双向绑定
- 支持自定义图标（Element Plus 图标或 SvgIcon）
- 可自定义样式（宽度、高度、颜色、字体大小等）
- 支持选项禁用
- 响应式布局
- 支持大、中、小三种尺寸

## 基本用法

```vue
<template>
	<CommonTabs v-model="currentTab" :items="tabItems" @change="handleTabChange" />
</template>

<script setup>
import { ref } from 'vue';
import CommonTabs from '/@/components/CommonTabs/index.vue';

// Tab数据
const tabItems = [
	{ label: '选项一', value: 'tab1' },
	{ label: '选项二', value: 'tab2' },
	{ label: '选项三', value: 'tab3' },
];

// 当前选中值
const currentTab = ref('tab1');

// 变更处理函数
const handleTabChange = (value) => {
	console.log('Tab变更:', value);
};
</script>
```

## 带图标

支持 Element Plus 图标组件或 SVG 图标名称。

```vue
<template>
	<CommonTabs v-model="currentTab" :items="tabItemsWithIcon" />
</template>

<script setup>
import { ref } from 'vue';
import CommonTabs from '/@/components/CommonTabs/index.vue';
import { Search, Setting, Message } from '@element-plus/icons-vue';

// 带图标的Tab数据
const tabItemsWithIcon = [
	{ label: '搜索', value: 'search', icon: Search },
	{ label: '设置', value: 'setting', icon: Setting },
	{ label: '消息', value: 'message', icon: Message },
	{ label: '自定义图标', value: 'custom', icon: 'ele-Menu' },
];

const currentTab = ref('search');
</script>
```

## 自定义样式

可以通过属性自定义样式。

```vue
<CommonTabs
	v-model="currentTab"
	:items="tabItems"
	size="small"
	active-color="#67c23a"
	active-border-color="#67c23a"
	:min-width="100"
	padding="8px 15px"
	:font-size="12"
/>
```

## 带禁用选项

可以设置某些选项为禁用状态。

```vue
<template>
	<CommonTabs v-model="currentTab" :items="tabItemsWithDisabled" />
</template>

<script setup>
import { ref } from 'vue';
import CommonTabs from '/@/components/CommonTabs/index.vue';

// 带禁用选项的数据
const tabItemsWithDisabled = [
	{ label: '选项一', value: 'tab1' },
	{ label: '选项二', value: 'tab2', disabled: true },
	{ label: '选项三', value: 'tab3' },
];

const currentTab = ref('tab1');
</script>
```

## 属性

| 属性名            | 类型            | 默认值      | 说明                                       |
| ----------------- | --------------- | ----------- | ------------------------------------------ |
| modelValue        | String / Number | ''          | 当前选中的值，支持 v-model                 |
| items             | Array           | []          | 选项数据，每项必须包含 label 和 value 属性 |
| size              | String          | 'default'   | 组件大小，可选值: large / default / small  |
| customClass       | String          | ''          | 自定义 CSS 类名                            |
| minWidth          | Number          | 120         | 按钮最小宽度(px)                           |
| padding           | String          | '12px 20px' | 按钮内边距                                 |
| fontSize          | Number          | 14          | 字体大小(px)                               |
| activeColor       | String          | '#409eff'   | 选中状态的文字颜色                         |
| activeBorderColor | String          | '#409eff'   | 选中状态的边框颜色                         |
| bgColor           | String          | '#ffffff'   | 背景颜色                                   |

## 事件

| 事件名            | 参数                    | 说明                |
| ----------------- | ----------------------- | ------------------- |
| change            | (value: string\|number) | 选项变更时触发      |
| update:modelValue | (value: string\|number) | 用于 v-model 的事件 |

## TabItem 类型定义

```typescript
interface TabItem {
	label: string; // 显示的文本
	value: string | number; // 选项的值
	icon?: string | object; // 图标，可以是图标名称或组件对象
	disabled?: boolean; // 是否禁用
}
```

# CommonTabs 权限控制使用指南

## 概述

CommonTabs 组件现已支持基于权限的 Tab 显示控制，可以根据用户权限动态显示或隐藏特定的 Tab 项。

## 功能特性

- ✅ 基于权限字段的 Tab 控制
- ✅ 自动过滤无权限的 Tab 项
- ✅ 智能选择第一个有权限的 Tab
- ✅ 向后兼容，不影响现有代码
- ✅ 当所有 Tab 都无权限时自动隐藏组件

## 接口定义

```typescript
interface TabItem {
	label: string; // Tab显示文本
	value: string | number; // Tab值
	icon?: string | object; // 图标（可选）
	disabled?: boolean; // 是否禁用（可选）
	permission?: string; // 权限字符串（可选）
}
```

## 使用方法

### 1. 基础使用（无权限控制）

```vue
<template>
	<CommonTabs v-model="currentTab" :items="tabItems" @change="handleChange" />
</template>

<script setup>
import { generatePlatformTabs } from '/@/utils/platformTabsHelper';

const currentTab = ref('wechat');
const tabItems = ref([]);

onMounted(() => {
	// 生成不带权限的Tab项
	tabItems.value = generatePlatformTabs();
});
</script>
```

### 2. 使用预定义权限配置

```vue
<template>
	<CommonTabs v-model="currentTab" :items="tabItems" @change="handleChange" />
</template>

<script setup>
import { OPERATE_REPORT_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions } from '/@/utils/platformTabsHelper';

const currentTab = ref('wechat');
const tabItems = ref([]);

onMounted(() => {
	// 使用运营报表页面的权限配置
	tabItems.value = generatePlatformTabsWithPermissions(OPERATE_REPORT_PERMISSIONS);
});
</script>
```

### 3. 手动配置权限

```vue
<template>
	<CommonTabs v-model="currentTab" :items="tabItems" @change="handleChange" />
</template>

<script setup>
const currentTab = ref('wechat');
const tabItems = ref([
	{
		label: '微信',
		value: 'wechat',
		permission: 'custom:wechat:view',
	},
	{
		label: '抖音',
		value: 'douyin',
		permission: 'custom:douyin:view',
	},
	{
		label: '微博',
		value: 'weibo',
		// 不设置权限字段，默认显示
	},
]);
</script>
```

## 权限配置

### 预定义权限常量

项目中已预定义了常用页面的权限配置：

```typescript
// 运营报表页面权限
export const OPERATE_REPORT_PERMISSIONS = {
	[PlatformType.WECHAT]: 'OperateReport:ListWx',
	[PlatformType.WEIBO]: 'OperationReport:ListWb',
	[PlatformType.DOUYIN]: 'OperationReport:ListDy',
};

// 微信公众号管理页面权限
export const WECHAT_ACCOUNT_PERMISSIONS = {
	[PlatformType.WECHAT]: 'WechatAccount:ListWx',
	[PlatformType.DOUYIN]: 'WechatAccount:ListDy',
	[PlatformType.WEIBO]: 'WechatAccount:ListWb',
};
```

### 添加新页面权限

1. 在 `src/stores/constants/platformPermissions.ts` 中添加新的权限配置：

```typescript
export const YOUR_PAGE_PERMISSIONS = {
	[PlatformType.WECHAT]: 'YourPage:ListWx',
	[PlatformType.DOUYIN]: 'YourPage:ListDy',
	[PlatformType.WEIBO]: 'YourPage:ListWb',
};
```

2. 在页面中使用：

```typescript
import { YOUR_PAGE_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions } from '/@/utils/platformTabsHelper';

const tabItems = ref([]);

onMounted(() => {
	tabItems.value = generatePlatformTabsWithPermissions(YOUR_PAGE_PERMISSIONS);
});
```

## 工具函数

### generatePlatformTabsWithPermissions

生成带权限的平台 Tab 项。

```typescript
function generatePlatformTabsWithPermissions(permissions: Record<PlatformType, string>): TabItem[];
```

### generatePlatformTabs

生成不带权限的平台 Tab 项（向后兼容）。

```typescript
function generatePlatformTabs(): TabItem[];
```

## 权限检查逻辑

1. 如果 Tab 项没有设置 `permission` 字段，则默认显示
2. 如果设置了 `permission` 字段，则使用 `auth()` 函数检查权限
3. 只有通过权限检查的 Tab 项才会显示
4. 当当前选中的 Tab 项无权限时，自动选择第一个有权限的 Tab 项
5. 当所有 Tab 项都无权限时，隐藏整个 Tab 组件

## 注意事项

1. 权限字符串格式需要与后端权限系统保持一致
2. 建议使用预定义的权限常量，避免硬编码权限字符串
3. 新增页面权限时，需要在后端权限系统中配置对应的权限
4. 权限检查依赖于项目的权限系统，确保 `auth()` 函数正常工作

## 示例页面

查看 `src/components/CommonTabs/PermissionExample.vue` 获取完整的使用示例。

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { MarkdownPDFExporter } from './markdownPdfExporter';

interface ExportOptions {
	orientation?: 'landscape' | 'portrait' | 'p' | 'l';
	unit?: 'px' | 'pt' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc';
	format?: string;
	compress?: boolean;
	quality?: number;
	scale?: number;
}

interface ModuleConfig {
	moduleId: string;
	title?: string;
	pageBreak?: boolean;
	type?: 'html' | 'markdown';
	markdownContent?: string;
}

export class PDFExporter {
	private pdf: jsPDF;
	private options: ExportOptions;
	private pageWidth: number;
	private pageHeight: number;
	private markdownExporter: MarkdownPDFExporter;

	constructor(options: ExportOptions = {}) {
		this.options = {
			orientation: 'landscape',
			unit: 'px',
			format: 'a4',
			compress: true,
			quality: 0.95,
			scale: 1.5,
			...options,
		};

		this.pdf = new jsPDF({
			orientation: this.options.orientation || 'landscape',
			unit: this.options.unit || 'px',
			format: this.options.format || 'a4',
			compress: this.options.compress,
		});

		// 设置页面尺寸
		const pageSize = this.pdf.internal.pageSize;
		this.pageWidth = pageSize.width;
		this.pageHeight = pageSize.height;

		// 初始化Markdown导出器
		this.markdownExporter = new MarkdownPDFExporter(this.options);
	}

	// 检测模块是否为Markdown类型
	private isMarkdownModule(moduleElement: HTMLElement): boolean {
		// 检查是否包含MarkdownRenderer组件的特征
		const hasMarkdownRenderer = moduleElement.querySelector('.markdown-content, .markdown-renderer');
		const hasWxContentWrapper = moduleElement.querySelector('.wx-content-wrapper');

		// 检查模块ID是否为AI报告相关
		const moduleId = moduleElement.id;
		const isAiReportModule =
			moduleId &&
			(moduleId.includes('ai-report') ||
				moduleId.includes('my-wx-ai-report') ||
				moduleId.includes('same-ai-report') ||
				moduleId.includes('sub-ai-report'));

		// 添加调试信息
		console.log(`模块 ${moduleId} Markdown检测:`, {
			hasMarkdownRenderer: !!hasMarkdownRenderer,
			hasWxContentWrapper: !!hasWxContentWrapper,
			isAiReportModule,
			elementHTML: moduleElement.innerHTML.substring(0, 200) + '...',
		});

		return !!(hasMarkdownRenderer || (hasWxContentWrapper && isAiReportModule));
	}

	private async captureModule(moduleElement: HTMLElement): Promise<HTMLCanvasElement[]> {
		// 优化元素样式以确保正确渲染
		const originalStyle = moduleElement.style.cssText;
		moduleElement.style.width = '100%';
		moduleElement.style.margin = '0';
		moduleElement.style.padding = '0';
		moduleElement.style.backgroundColor = '#ffffff';

		// 处理表格样式
		const tables = moduleElement.querySelectorAll('table');
		tables.forEach((table) => {
			// 基本表格样式
			(table as HTMLElement).style.width = '100%';
			(table as HTMLElement).style.borderCollapse = 'collapse';
			(table as HTMLElement).style.tableLayout = 'fixed';

			// 处理所有表头和单元格
			const cells = table.querySelectorAll('th, td');
			cells.forEach((cell) => {
				(cell as HTMLElement).style.height = '60px';
				(cell as HTMLElement).style.minHeight = '60px';
				(cell as HTMLElement).style.padding = '1px 0 7px 0'; // 上右下左，调整上下padding使文字向上移动3px
				(cell as HTMLElement).style.lineHeight = '1.5';
				(cell as HTMLElement).style.verticalAlign = 'middle';
				(cell as HTMLElement).style.wordWrap = 'break-word';
				(cell as HTMLElement).style.whiteSpace = 'normal';
				(cell as HTMLElement).style.overflow = 'visible';
			});

			// 处理所有行
			const rows = table.querySelectorAll('tr');
			rows.forEach((row) => {
				(row as HTMLElement).style.height = '60px';
				(row as HTMLElement).style.minHeight = '60px';
			});
		});

		// 处理el-table组件
		const elTables = moduleElement.querySelectorAll('.el-table');
		elTables.forEach((elTable) => {
			// 添加特定样式
			elTable.setAttribute(
				'style',
				'width: 100% !important; table-layout: fixed !important; border-collapse: separate !important; border-spacing: 0 !important;'
			);

			// 修复el-table的表头和行高
			const headerCells = elTable.querySelectorAll('.el-table__header-cell, .el-table__cell');
			headerCells.forEach((cell) => {
				cell.setAttribute(
					'style',
					'height: 55px !important; min-height: 55px !important; padding: 1px 0px 7px 0px !important; line-height: 1.5 !important; white-space: normal !important; vertical-align: middle !important; word-break: break-word !important;'
				);
			});

			// 修复行高
			const rows = elTable.querySelectorAll('.el-table__row');
			rows.forEach((row) => {
				row.setAttribute('style', 'height: 60px !important; min-height: 60px !important;');
			});

			// 修复cell内容
			const cells = elTable.querySelectorAll('.cell');
			cells.forEach((cell) => {
				cell.setAttribute(
					'style',
					'line-height: 1.5 !important; white-space: normal !important; word-break: break-word !important; max-height: none !important; overflow: visible !important; padding-top: 0 !important; padding-bottom: 3px !important;'
				);
			});

			// 修复表头和表体包装器
			const headerWrapper = elTable.querySelector('.el-table__header-wrapper');
			if (headerWrapper) {
				headerWrapper.setAttribute('style', 'overflow: visible !important;');
				const headerTable = headerWrapper.querySelector('table');
				if (headerTable) {
					headerTable.setAttribute('style', 'width: 100% !important; table-layout: fixed !important;');
				}
			}

			const bodyWrapper = elTable.querySelector('.el-table__body-wrapper');
			if (bodyWrapper) {
				bodyWrapper.setAttribute('style', 'overflow: visible !important;');
				const bodyTable = bodyWrapper.querySelector('table');
				if (bodyTable) {
					bodyTable.setAttribute('style', 'width: 100% !important; table-layout: fixed !important;');
				}
			}
		});

		try {
			// 获取模块实际高度
			const moduleHeight = moduleElement.scrollHeight;
			const moduleWidth = moduleElement.scrollWidth;
			const currentScale = this.options.scale || 2;
			const pageHeight = this.pageHeight * currentScale;

			// 如果模块高度超过页面高度，则分页处理
			if (moduleHeight > pageHeight) {
				const canvases = [];
				const pageCount = Math.ceil(moduleHeight / pageHeight);

				for (let i = 0; i < pageCount; i++) {
					// 设置临时的裁剪区域，仅显示当前页的内容
					const tempDiv = document.createElement('div');
					tempDiv.style.position = 'absolute';
					tempDiv.style.width = moduleWidth + 'px';
					tempDiv.style.height = pageHeight + 'px';
					tempDiv.style.overflow = 'hidden';
					tempDiv.style.backgroundColor = '#ffffff';

					const clonedModule = moduleElement.cloneNode(true) as HTMLElement;
					clonedModule.style.position = 'relative';
					clonedModule.style.top = -i * pageHeight + 'px';
					clonedModule.style.width = moduleWidth + 'px'; // 确保宽度一致

					// 在克隆模块上重新应用表格样式修复
					this.applyTableFixes(clonedModule);

					tempDiv.appendChild(clonedModule);

					document.body.appendChild(tempDiv);

					// 截取当前页，使用options中传入的scale值
					const canvas = await html2canvas(tempDiv, {
						scale: currentScale,
						useCORS: true,
						allowTaint: true,
						logging: false,
						backgroundColor: '#ffffff',
						windowWidth: moduleWidth,
						windowHeight: pageHeight,
						onclone: (clonedDoc) => {
							// 在克隆文档上添加额外样式
							const style = clonedDoc.createElement('style');
							style.innerHTML = `
								.el-table { width: 100% !important; table-layout: fixed !important; }
								.el-table__header-wrapper table, .el-table__body-wrapper table { table-layout: fixed !important; }
								.el-table th, .el-table td { height: 55px !important; min-height: 55px !important; padding: 1px 0px 7px 0px !important; line-height: 1.5 !important; }
								.el-table__row { height: 55px !important; min-height: 55px !important; }
								.el-table__cell, .el-table__header-cell { padding: 1px 0px 7px 0px !important; line-height: 1.5 !important; vertical-align: middle !important; height: 55px !important; }
								.cell { white-space: normal !important; word-break: break-word !important; line-height: 1.5 !important; padding-top: 0 !important; padding-bottom: 3px !important; }
							`;
							clonedDoc.head.appendChild(style);
							return clonedDoc;
						},
					});

					canvases.push(canvas);
					document.body.removeChild(tempDiv);
				}

				// 恢复原始样式
				moduleElement.style.cssText = originalStyle;
				return canvases;
			} else {
				// 如果不需要分页，按原逻辑处理
				const canvas = await html2canvas(moduleElement, {
					scale: currentScale,
					useCORS: true,
					allowTaint: true,
					logging: false,
					backgroundColor: '#ffffff',
					windowWidth: this.pageWidth * currentScale,
					windowHeight: this.pageHeight * currentScale,
					onclone: (clonedDoc) => {
						// 在克隆文档上添加额外样式
						const style = clonedDoc.createElement('style');
						style.innerHTML = `
							.el-table { width: 100% !important; table-layout: fixed !important; }
							.el-table__header-wrapper table, .el-table__body-wrapper table { table-layout: fixed !important; }
							.el-table th, .el-table td { height: 55px !important; min-height: 55px !important; padding: 1px 0px 7px 0px !important; line-height: 1.5 !important; }
							.el-table__row { height: 55px !important; min-height: 55px !important; }
							.el-table__cell, .el-table__header-cell { padding: 1px 0px 7px 0px !important; line-height: 1.5 !important; vertical-align: middle !important; height: 55px !important; }
							.cell { white-space: normal !important; word-break: break-word !important; line-height: 1.5 !important; padding-top: 0 !important; padding-bottom: 3px !important; }
						`;
						clonedDoc.head.appendChild(style);
						return clonedDoc;
					},
				});

				// 恢复原始样式
				moduleElement.style.cssText = originalStyle;
				return [canvas];
			}
		} catch (error) {
			console.error('模块截图失败:', error);
			moduleElement.style.cssText = originalStyle;
			throw error;
		}
	}

	private addCanvasToPage(canvas: HTMLCanvasElement, isLastPage: boolean = false): void {
		const canvasWidth = canvas.width;
		const canvasHeight = canvas.height;

		// 计算缩放比例以适应页面，保持统一宽度
		const scale = this.pageWidth / canvasWidth;

		const scaledWidth = canvasWidth * scale;
		const scaledHeight = canvasHeight * scale;

		// 计算水平居中的x坐标
		const x = (this.pageWidth - scaledWidth) / 2;
		const y = (this.pageHeight - scaledHeight) / 2; // 上边距为0

		// 添加到PDF
		this.pdf.addImage(canvas.toDataURL('image/jpeg', this.options.quality), 'JPEG', x, y, scaledWidth, scaledHeight);

		// 如果不是最后一页，添加新页
		if (!isLastPage) {
			this.pdf.addPage();
		}
	}

	public async exportModulesToPDF(modules: ModuleConfig[], filename: string = 'export.pdf'): Promise<void> {
		try {
			// 在导出前应用全局样式修复
			const styleElement = document.createElement('style');
			styleElement.innerHTML = `
				@media print {
					.chart-container {
						page-break-inside: avoid;
						min-height: 500px;
						margin-bottom: 40px;
					}
					.el-table { width: 100% !important; table-layout: fixed !important; }
					.el-table__header-wrapper table, .el-table__body-wrapper table { table-layout: fixed !important; }
					.el-table th, .el-table td { height: 60px !important; min-height: 60px !important; padding: 1px 4px 7px 4px !important; line-height: 1.5 !important; }
					.el-table__row { height: 60px !important; min-height: 60px !important; }
					.el-table__cell, .el-table__header-cell { padding: 1px 0px 7px 0px !important; line-height: 1.5 !important; vertical-align: middle !important; height: 55px !important; }
					.cell { white-space: normal !important; word-break: break-word !important; line-height: 1.5 !important; padding-top: 0 !important; padding-bottom: 3px !important; }
					.el-table__body, .el-table__header {
						table-layout: fixed !important;
					}
				}
			`;
			document.head.appendChild(styleElement);

			// 等待图表完全渲染的延迟
			await new Promise((resolve) => setTimeout(resolve, 1000));

			let moduleIndex = 0;
			for (const module of modules) {
				const element = document.getElementById(module.moduleId);

				if (!element) {
					console.warn(`模块 ${module.moduleId} 未找到`);
					continue;
				}

				// 判断模块类型
				const isMarkdown = module.type === 'markdown' || this.isMarkdownModule(element);

				console.log(`模块 ${module.moduleId} 分析:`, {
					configType: module.type,
					isMarkdownFromConfig: module.type === 'markdown',
					isMarkdownFromDOM: this.isMarkdownModule(element),
					finalIsMarkdown: isMarkdown,
					hasMarkdownContent: !!module.markdownContent,
					markdownContentLength: module.markdownContent?.length || 0,
					markdownContentPreview: module.markdownContent?.substring(0, 100) || 'none',
				});

				if (isMarkdown) {
					// 处理Markdown模块
					console.log(`处理Markdown模块: ${module.moduleId}`);

					let markdownCanvases;

					if (module.markdownContent) {
						// 如果有原始Markdown内容，从内容导出（HTML转换）
						console.log('使用原始Markdown内容进行导出，内容长度:', module.markdownContent.length);
						console.log('Markdown内容预览:', module.markdownContent.substring(0, 200));
						markdownCanvases = await this.markdownExporter.exportFromMarkdownContent(module.markdownContent);
					} else {
						// 如果没有原始内容，从DOM元素导出
						console.log('从DOM元素导出Markdown内容');
						markdownCanvases = await this.markdownExporter.captureMarkdownModule(element);
					}

					console.log(`模块 ${module.moduleId} 生成的画布数量:`, markdownCanvases.length);

					// 添加每个画布到当前PDF
					for (let i = 0; i < markdownCanvases.length; i++) {
						const isLastCanvas = i === markdownCanvases.length - 1;
						const isLastModule = moduleIndex === modules.length - 1;
						const isLastPage = isLastCanvas && isLastModule;

						console.log(`添加画布 ${i + 1}/${markdownCanvases.length} 到PDF`);
						// 添加到PDF
						this.addCanvasToPage(markdownCanvases[i], isLastPage);
					}
				} else {
					// 处理HTML模块（原有逻辑）
					console.log(`处理HTML模块: ${module.moduleId}`);

					// 预处理El表格组件
					const elTables = element.querySelectorAll('.el-table');
					elTables.forEach((table) => {
						// 确保表格内容不会溢出
						const cells = table.querySelectorAll('.cell');
						cells.forEach((cell) => {
							// 获取内容长度，如果太长则调整处理
							const text = cell.textContent || '';
							if (text.length > 30) {
								cell.setAttribute(
									'style',
									'white-space: normal !important; word-break: break-word !important; line-height: 1.2 !important; max-height: none !important; overflow: visible !important;'
								);
							}
						});
					});

					// 捕获模块内容，可能返回多个画布（分页处理）
					const canvases = await this.captureModule(element);

					// 添加每个画布到PDF
					for (let i = 0; i < canvases.length; i++) {
						const isLastCanvas = i === canvases.length - 1;
						const isLastModule = moduleIndex === modules.length - 1;
						const isLastPage = isLastCanvas && isLastModule;

						// 添加到PDF
						this.addCanvasToPage(canvases[i], isLastPage);
					}
				}

				moduleIndex++;
			}

			// 移除临时样式
			document.head.removeChild(styleElement);

			// 保存PDF
			this.pdf.save(filename);
		} catch (error) {
			console.error('PDF导出失败:', error);
			throw error;
		}
	}

	// 添加新方法用于处理表格样式修复
	private applyTableFixes(element: HTMLElement): void {
		// 处理表格样式
		const tables = element.querySelectorAll('table');
		tables.forEach((table) => {
			// 基本表格样式
			(table as HTMLElement).style.width = '100%';
			(table as HTMLElement).style.borderCollapse = 'collapse';
			(table as HTMLElement).style.tableLayout = 'fixed';

			// 处理所有表头和单元格
			const cells = table.querySelectorAll('th, td');
			cells.forEach((cell) => {
				(cell as HTMLElement).style.height = '55px';
				(cell as HTMLElement).style.minHeight = '55px';
				(cell as HTMLElement).style.padding = '1px 0 7px 0'; // 上右下左，调整上下padding使文字向上移动3px
				(cell as HTMLElement).style.lineHeight = '1.5';
				(cell as HTMLElement).style.verticalAlign = 'middle';
				(cell as HTMLElement).style.wordWrap = 'break-word';
				(cell as HTMLElement).style.whiteSpace = 'normal';
				(cell as HTMLElement).style.overflow = 'visible';
			});

			// 处理所有行
			const rows = table.querySelectorAll('tr');
			rows.forEach((row) => {
				(row as HTMLElement).style.height = '55px';
				(row as HTMLElement).style.minHeight = '55px';
			});
		});

		// 处理el-table组件
		const elTables = element.querySelectorAll('.el-table');
		elTables.forEach((elTable) => {
			// 修复el-table的表头和行高
			const headerCells = elTable.querySelectorAll('.el-table__header-cell, .el-table__cell');
			headerCells.forEach((cell) => {
				cell.setAttribute(
					'style',
					'height: 60px !important; min-height: 60px !important; padding: 1px 0 7px 0 !important; line-height: 1.5 !important; white-space: normal !important; vertical-align: middle !important; word-break: break-word !important;'
				);
			});

			// 修复行高
			const rows = elTable.querySelectorAll('.el-table__row');
			rows.forEach((row) => {
				row.setAttribute('style', 'height: 55px !important; min-height: 55px !important;');
			});

			// 修复cell内容
			const cells = elTable.querySelectorAll('.cell');
			cells.forEach((cell) => {
				cell.setAttribute(
					'style',
					'line-height: 1.5 !important; white-space: normal !important; word-break: break-word !important; max-height: none !important; overflow: visible !important; padding-top: 0 !important; padding-bottom: 3px !important;'
				);
			});
		});
	}
}

// 导出便捷方法
export const exportToPDF = async (modules: ModuleConfig[], filename?: string, options?: ExportOptions): Promise<void> => {
	const exporter = new PDFExporter(options);
	await exporter.exportModulesToPDF(modules, filename);
};

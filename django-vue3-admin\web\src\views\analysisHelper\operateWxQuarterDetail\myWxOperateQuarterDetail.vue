<template>
	<div>
		<div class="wx-container">
			<div>
				<div class="wx-top">
					<div class="top-title">{{ props.title.replace('运营报告', '微信本级账号运营报告').replace('（微信）', '') }}</div>
					<!-- <div style="font-size: 16px; margin-top: 20px">统计时间：{{ reportDate }}</div>
					<div style="font-size: 14px; float: right; color: #4678ab">汇远轻媒出品</div> -->
				</div>
			</div>
			<!-- 添加空数据判断 -->
			<div v-if="!wciAccountList || wciAccountList.length === 0">
				<div style="width: 100%; margin: 10px auto; font-size: 26px; text-align: center">无绑定账号</div>
			</div>

			<div v-else>
				<!-- 标题区域使用背景图 -->
				<div>
					<!-- 账号分析部分 -->
					<!-- <div class="wx-title-wrapper"><div class="wx-title1">账号分析</div></div> -->
				</div>
				<!-- AI分析报告模块 -->
				<div v-if="aiReportContent">
					<div class="wx-title-wrapper"><div class="wx-title1">AI分析报告</div></div>
					<div id="my-wx-ai-report" class="markdown-report-content">
						<!-- 账号统计信息 -->
						<div v-if="props.tenantName" class="account-stats-container">
							<div class="account-stats-content">
								<span class="tenant-name">{{ props.tenantName }}</span>
								<span class="stats-item"
									>本级账号数量为<span class="count-number">{{ props.accountCounts.myAccount }}</span></span
								>
								<span class="stats-item"
									>下级账号数量为<span class="count-number">{{ props.accountCounts.subAccount }}</span></span
								>
								<span class="stats-item"
									>同类账号数量为<span class="count-number">{{ props.accountCounts.sameAccount }}</span></span
								>
							</div>
						</div>
						<MarkdownRenderer :content="aiReportContent" />
					</div>
				</div>
				<!-- 内容传播部分 -->
				<div v-for="(item, index) in contentIssue" :key="item.account_name">
					<div :id="`my-wx-issue-${index + 1}`">
						<div v-if="index === 0">
							<div class="wx-title-wrapper"><div class="wx-title1">内容传播</div></div>
							<div class="wx-title-wrapper"><div class="wx-title2">发文量</div></div>
						</div>
						<div class="wx-content-wrapper">
							<div class="wx-content1">
								<div>
									<span class="text-blue text-bold">{{ item.account_name }}</span
									>本季发文<span class="text-bold">{{ item.total_count }}篇</span>， 比上季<span class="text-bold"
										>{{ parseInt(item.compare.trend) > 0 ? '增加' : parseInt(item.compare.trend) < 0 ? '减少' : '持平'
										}}{{ item.compare.trend === 0 ? '' : `${tranNum(Math.abs(item.compare.diff))}篇` }}</span
									>， 环比增长率<span class="text-bold">{{ item.compare.rate }}%</span>
								</div>
								<div>
									<span class="text-bold">工作日</span>平均发文：<span class="text-bold">{{ item.workday_avg.value }}篇</span>， 比上季<span
										class="text-bold"
										>{{ parseInt(item.workday_avg.compare.trend) > 0 ? '增加' : parseInt(item.workday_avg.compare.trend) < 0 ? '减少' : '持平'
										}}{{ item.workday_avg.compare.trend === 0 ? '' : `${tranNum(Math.abs(item.workday_avg.compare.diff))}篇` }}</span
									>， 环比增长率<span class="text-bold">{{ item.workday_avg.compare.rate }}%</span>
								</div>
								<div>
									<span class="text-bold">周末</span>平均发文：<span class="text-bold">{{ item.weekend_avg.value }}篇</span>， 比上季<span
										class="text-bold"
										>{{ parseInt(item.weekend_avg.compare.trend) > 0 ? '增加' : parseInt(item.weekend_avg.compare.trend) < 0 ? '减少' : '持平'
										}}{{ item.weekend_avg.compare.trend === 0 ? '' : `${tranNum(Math.abs(item.weekend_avg.compare.diff))}篇` }}</span
									>， 环比增长率<span class="text-bold">{{ item.weekend_avg.compare.rate }}%</span>
								</div>
								<div>
									<span class="text-bold">发文最多</span>的日期是：<span class="text-bold">{{ item.max_day.date }}</span
									>， 发布了<span class="text-bold">{{ item.max_day.count }}</span
									>篇文章， 比上个季度发文最多日<span class="text-bold"
										>{{ parseInt(item.max_day.compare.trend) > 0 ? '增加' : parseInt(item.max_day.compare.trend) < 0 ? '减少' : '持平'
										}}{{ item.max_day.compare.trend === 0 ? '' : `${tranNum(Math.abs(item.max_day.compare.diff))}篇` }}</span
									>， 环比增长率<span class="text-bold">{{ item.max_day.compare.rate }}%</span>
								</div>
								<div>
									<span class="text-bold">零发文</span>天数：<span class="text-bold">{{ item.zero_days.value }}天</span>， 比上季<span
										class="text-bold"
										>{{ parseInt(item.zero_days.compare.trend) > 0 ? '增加' : parseInt(item.zero_days.compare.trend) < 0 ? '减少' : '持平'
										}}{{ item.zero_days.compare.trend === 0 ? '' : `${tranNum(Math.abs(item.zero_days.compare.diff))}天` }}</span
									>， 环比增长率<span class="text-bold">{{ item.zero_days.compare.rate }}%</span>
								</div>
							</div>
						</div>
						<!-- 发文时间分布图 -->
						<div class="wx-title4">发文时间区间情况</div>
						<div class="chart-container">
							<common-line
								id="chart-container"
								:data="formatDateDistribution(item.hour_distribution)"
								style="width: 100%; height: 350px"
								:axis-font-size="16"
								:legend-font-size="16"
								:axis-name-font-size="16"
							></common-line>
						</div>
					</div>
				</div>
				<!-- 阅读量部分 -->
				<div v-for="(item, index) in contentRead" :key="item.account_name">
					<div :id="`my-wx-read-${index + 1}`">
						<div class="wx-title-wrapper" v-if="index === 0"><div class="wx-title2">阅读量</div></div>
						<div class="wx-content-wrapper">
							<div class="wx-content1">
								<span class="text-blue text-bold">{{ item.account_name }}</span
								>本季阅读量<span class="text-bold">{{ tranNum(item.total) }}</span
								>， 比上季{{ parseInt(item.compare.trend) === 1 ? '增加' : parseInt(item.compare.trend) === -1 ? '减少' : '持平'
								}}<span class="text-bold">{{ item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff)) }}</span
								>， 环比增长率<span class="text-bold">{{ item.compare.rate }}%</span>， 日均阅读量<span class="text-bold">{{
									tranNum(item.daily_avg.value)
								}}</span
								>， 篇均阅读量<span class="text-bold">{{ tranNum(item.article_avg.value) }}</span>
							</div>
						</div>
						<div class="wx-title4">阅读量区间占比</div>
						<div class="chart-container">
							<pie
								id="chart-container"
								:data="formatRangeDistribution(item.range_distribution)"
								style="width: 100%; height: 500px"
								:fontSize="20"
								:label-font-size="18"
								:total="{ count: item.article_count, name: '总篇数' }"
							></pie>
						</div>
					</div>
				</div>
				<!-- 运营数据详情表格 -->
				<div v-if="accountDataList && accountDataList.length > 0">
					<!-- 根据账号数量决定分页策略 -->
					<div v-if="accountDataList.length <= 2">
						<!-- 2个或以下账号，在一个容器内 -->
						<div :id="`my-wx-account-detail-page-all`">
							<div v-for="(account, index) in accountDataList" :key="account.account_name">
								<div class="wx-title-wrapper">
									<div class="wx-title2">{{ account.account_name }}运营数据详情表</div>
								</div>
								<div class="wx-table-wrapper">
									<el-table
										:data="formatAccountTableData(account)"
										border
										:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
										:cell-style="{ height: '45px' }"
									>
										<el-table-column label="数据类型" align="center" prop="type" min-width="120px" />
										<el-table-column label="本季值" align="center" prop="total" min-width="100px" />
										<el-table-column label="较上季" align="center" prop="compareChange" min-width="120px" />
										<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
										<el-table-column label="篇均数量" align="center" prop="articleAvg" min-width="100px" />
									</el-table>
								</div>
							</div>
						</div>
					</div>
					<div v-else>
						<!-- 3个或以上账号，每两个一页 -->
						<div v-for="pageIndex in Math.ceil(accountDataList.length / 2)" :key="`page-${pageIndex}`" :id="`my-wx-account-detail-page-${pageIndex}`">
							<div
								v-for="(account, relativeIndex) in accountDataList.slice((pageIndex - 1) * 2, pageIndex * 2)"
								:key="account.account_name"
								:id="`my-wx-account-detail-${(pageIndex - 1) * 2 + relativeIndex + 1}`"
							>
								<div class="wx-title-wrapper">
									<div class="wx-title2">{{ account.account_name }}运营数据详情表</div>
								</div>
								<div class="wx-table-wrapper">
									<el-table
										:data="formatAccountTableData(account)"
										border
										:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
										:cell-style="{ height: '45px' }"
									>
										<el-table-column label="数据类型" align="center" prop="type" min-width="120px" />
										<el-table-column label="本季值" align="center" prop="total" min-width="100px" />
										<el-table-column label="较上季" align="center" prop="compareChange" min-width="120px" />
										<el-table-column label="环比增长率" align="center" prop="compareRate" min-width="120px" />
										<el-table-column label="篇均数量" align="center" prop="articleAvg" min-width="100px" />
									</el-table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- 文章效果分析 -->
				<div id="my-wx-operate-5">
					<div class="wx-title-wrapper"><div class="wx-title1">具体文章</div></div>
					<!-- TOP10列表 -->
					<div class="wx-title2">阅读量Top 10</div>
					<div class="wx-table-wrapper">
						<el-table :data="readTop10" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }" :cell-style="{ height: '45px' }">
							<el-table-column label="文章名称" align="center" prop="title" min-width="400px" />
							<el-table-column label="阅读量" align="center" prop="read_count" min-width="100px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.read_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="所属账号" align="center" prop="account_name" min-width="100px" />
							<el-table-column label="发布时间" align="center" prop="issue_time" min-width="100px" />
						</el-table>
					</div>
				</div>
				<div id="my-wx-operate-6">
					<div class="wx-title2">点赞量Top 10</div>
					<div class="wx-table-wrapper">
						<el-table :data="likeTop10" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }" :cell-style="{ height: '45px' }">
							<el-table-column label="文章名称" align="center" prop="title" min-width="400px" />
							<el-table-column label="点赞量" align="center" prop="like_count" min-width="100px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.like_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="所属账号" align="center" prop="account_name" min-width="100px" />
							<el-table-column label="发布时间" align="center" prop="issue_time" min-width="100px" />
						</el-table>
					</div>
				</div>
				<div id="my-wx-operate-7">
					<div class="wx-title2">推荐量Top 10</div>
					<div class="wx-table-wrapper">
						<el-table :data="viewTop10" border :header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }" :cell-style="{ height: '45px' }">
							<el-table-column label="文章名称" align="center" prop="title" min-width="400px" />
							<el-table-column label="推荐量" align="center" prop="view_count" min-width="100px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.view_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="所属账号" align="center" prop="account_name" min-width="100px" />
							<el-table-column label="发布时间" align="center" prop="issue_time" min-width="100px" />
						</el-table>
					</div>
				</div>
				<div id="my-wx-operate-8">
					<div class="wx-title2">分享量Top 10</div>
					<div class="wx-table-wrapper">
						<el-table
							:data="shareTop10"
							border
							:header-cell-style="{ backgroundColor: '#f8f8f9', color: '#515a6e' }"
							:cell-style="{ height: '45px' }"
						>
							<el-table-column label="文章名称" align="center" prop="title" min-width="400px" />
							<el-table-column label="分享量" align="center" prop="forward_count" min-width="100px">
								<template #default="scope">
									<span>{{ tranNum(scope.row.forward_count) }}</span>
								</template>
							</el-table-column>
							<el-table-column label="所属账号" align="center" prop="account_name" min-width="100px" />
							<el-table-column label="发布时间" align="center" prop="issue_time" min-width="100px" />
						</el-table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import pie from '/@/components/Echarts/pie.vue';
import commonLine from '/@/components/Echarts/line.vue';
import { tranNumber } from '/@/utils/tranNum';
import MarkdownRenderer from '/@/components/MarkdownRenderer/index.vue';

const props = defineProps({
	reportId: {
		type: [String, Number],
		default: '',
	},
	selfReportData: {
		type: Object,
		default: () => ({}),
	},
	title: {
		type: String,
		default: '',
	},
	date: {
		type: Object,
		default: () => ({}),
	},
	tenantName: {
		type: String,
		default: '',
	},
	accountCounts: {
		type: Object,
		default: () => ({
			myAccount: 0,
			subAccount: 0,
			sameAccount: 0,
		}),
	},
});

// 数据定义
const pageLoading = ref(true);
const reportDate = ref('');
const wciAccountList = ref([]); // 指数榜
const contentIssue = ref({}); // 发文量
const contentRead = ref([]); // 阅读量
const contentLike = ref([]); // 点赞量
const contentShare = ref([]); // 分享量
const contentView = ref([]); // 推荐量
const readTop10 = ref([]); // 阅读量Top 10
const likeTop10 = ref([]); // 点赞量Top 10
const shareTop10 = ref([]); // 分享量Top 10
const viewTop10 = ref([]); // 推荐量Top 10
const aiReportContent = ref('');
const accountDataList = ref([]); // 账号数据列表，用于表格展示

// 数字转换函数
const tranNum = (num) => {
	if (num !== undefined && num !== null && num !== '') {
		return tranNumber(num, 1);
	}
	return '';
};

// 处理阅读量区间分布数据格式，转换为饼图所需格式
const formatRangeDistribution = (rangeData) => {
	if (!rangeData || !Array.isArray(rangeData) || rangeData.length === 0) {
		return [];
	}

	// 按照饼图组件需要的格式转换数据
	return rangeData.map((item) => {
		return {
			name: item.name,
			value: item.count,
		};
	});
};

// 处理发文时间区间数据格式
const formatDateDistribution = (distributionData) => {
	if (!distributionData || !Array.isArray(distributionData) || distributionData.length === 0) {
		return {
			row: [],
			dates: [],
		};
	}

	// 提取日期和数量
	const dates = distributionData.map((item) => item.hour);
	const counts = distributionData.map((item) => item.count);

	// 按照line组件需要的格式返回
	return {
		row: [
			{
				name: '发文数量',
				counts: counts,
				type: 'line',
			},
		],
		dates: dates,
	};
};
// 格式化账号表格数据
const formatAccountTableData = (account) => {
	const tableData = [];

	// 点赞量数据
	if (account.like_count_stats) {
		const item = account.like_count_stats;
		tableData.push({
			type: '点赞量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg?.value || 0),
		});
	}

	// 分享量数据
	if (account.share_count_stats) {
		const item = account.share_count_stats;
		tableData.push({
			type: '分享量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg?.value || 0),
		});
	}

	// 推荐量数据
	if (account.viewing_count_stats) {
		const item = account.viewing_count_stats;
		tableData.push({
			type: '推荐量',
			total: tranNum(item.total),
			compareChange: `${parseInt(item.compare.trend) > 0 ? '' : parseInt(item.compare.trend) < 0 ? '-' : '持平'}${
				item.compare.trend === 0 ? '' : tranNum(Math.abs(item.compare.diff))
			}`,
			compareRate: `${item.compare.rate}%`,
			articleAvg: tranNum(item.article_avg?.value || 0),
		});
	}

	// 指数数据
	if (account.wci) {
		const item = account.wci;
		tableData.push({
			type: '指数值',
			total: item.wci || 0,
			compareChange: `${item.compare_trend > 0 ? '' : item.compare_trend < 0 ? '-' : '持平'}${
				item.compare_trend === 0 ? '' : tranNum(Math.abs(item.diff))
			}`,
			compareRate: `${item.compare_trend === 0 ? '0%' : `${item.compare_rate}%`}`,
			articleAvg: '-',
		});
	}

	return tableData;
};

// 处理自身运营报告数据
const processSelfReportData = (data) => {
	pageLoading.value = true;

	try {
		if (!data) {
			ElMessage.error('获取自运营报告失败');
			return;
		}

		// 使用日期区间
		reportDate.value = `${props.date.start_date} 至 ${props.date.end_date}`;

		// 适配新的数据结构 - 从 account_data 数组中提取数据
		if (data && data.account_data && Array.isArray(data.account_data)) {
			// 从 account_data 数组中提取发文量数据
			contentIssue.value = data.account_data.map((item) => item.publish).filter((item) => item);

			// 从 account_data 数组中提取阅读量数据
			contentRead.value = data.account_data.map((item) => item.read_count_stats).filter((item) => item);

			// 从 account_data 数组中提取点赞量数据
			contentLike.value = data.account_data.map((item) => item.like_count_stats).filter((item) => item);

			// 从 account_data 数组中提取分享量数据
			contentShare.value = data.account_data.map((item) => item.share_count_stats).filter((item) => item);

			// 从 account_data 数组中提取推荐量数据
			contentView.value = data.account_data.map((item) => item.viewing_count_stats).filter((item) => item);

			// 从 account_data 数组中提取指数数据
			wciAccountList.value = data.account_data.map((item) => item.wci).filter((item) => item);

			// 保存完整的账号数据用于表格展示
			accountDataList.value = data.account_data || [];

			// 其他数据保持不变
			readTop10.value = data.read_top10 || []; // 阅读量Top 10
			likeTop10.value = data.like_top10 || []; // 点赞量Top 10
			shareTop10.value = data.share_top10 || []; // 分享量Top 10
			viewTop10.value = data.viewing_top10 || []; // 推荐量Top 10
			aiReportContent.value = data.ai_report; // AI报告内容
		} else {
			// 兼容旧的数据结构
			wciAccountList.value = data.wci || []; // 指数榜
			contentIssue.value = data.publish || []; // 发文量
			contentRead.value = data.read_count_stats || []; // 阅读量
			contentLike.value = data.like_count_stats || []; // 点赞量
			contentShare.value = data.share_count_stats || []; // 分享量
			contentView.value = data.viewing_count_stats || []; // 推荐量

			readTop10.value = data.read_top10 || []; // 阅读量Top 10
			likeTop10.value = data.like_top10 || []; // 点赞量Top 10
			shareTop10.value = data.share_top10 || []; // 分享量Top 10
			viewTop10.value = data.viewing_top10 || []; // 推荐量Top 10
			aiReportContent.value = data.ai_report; // AI报告内容

			// 兼容旧数据结构时，清空 accountDataList
			accountDataList.value = [];
		}
	} catch (error) {
		console.error('处理自运营报告数据失败:', error);
		ElMessage.error('处理自运营报告数据失败');
	} finally {
		pageLoading.value = false;
	}
};

// 监听selfReportData变化
watch(
	() => props.selfReportData,
	(newVal) => {
		if (newVal) {
			processSelfReportData(newVal);
		}
	},
	{ immediate: true, deep: true }
);

// 定义模块ID列表供父组件使用
const moduleIds = computed(() => {
	const modules = [];

	// AI分析报告模块
	if (aiReportContent.value) {
		modules.push({
			id: 'my-wx-ai-report',
			name: '本级账号AI分析报告',
			type: 'markdown',
			markdownContent: aiReportContent.value,
			category: 'ai',
		});
	}

	// 发文量模块（合并为一个模块选项）
	if (contentIssue.value && contentIssue.value.length > 0) {
		const issueIds = contentIssue.value.map((item, index) => `my-wx-issue-${index + 1}`);
		modules.push({
			id: issueIds, // 传递所有发文量ID的数组
			name: '发文量分析',
			type: 'html',
			category: 'content',
			multipleIds: true, // 标记为多ID模块
		});
	}

	// 阅读量模块（合并为一个模块选项）
	if (contentRead.value && contentRead.value.length > 0) {
		const readIds = contentRead.value.map((item, index) => `my-wx-read-${index + 1}`);
		modules.push({
			id: readIds, // 传递所有阅读量ID的数组
			name: '阅读量分析',
			type: 'html',
			category: 'content',
			multipleIds: true, // 标记为多ID模块
		});
	}

	// 运营数据详情表格模块 - 按分页规则分组
	if (accountDataList.value && accountDataList.value.length > 0) {
		const totalTables = accountDataList.value.length;

		if (totalTables <= 2) {
			// 如果有两个或以下表格，作为一个模块
			modules.push({
				id: ['my-wx-account-detail-page-all'], // 使用统一的容器ID
				name: '运营数据详情表格',
				type: 'html',
				category: 'content',
				multipleIds: true, // 标记为多ID模块
			});
		} else {
			// 如果有两个以上表格，每两个为一页
			const totalPages = Math.ceil(totalTables / 2);
			for (let i = 1; i <= totalPages; i++) {
				modules.push({
					id: [`my-wx-account-detail-page-${i}`],
					name: `运营数据详情表格（第${i}页）`,
					type: 'html',
					category: 'content',
					multipleIds: true, // 标记为多ID模块
				});
			}
		}
	}

	// Top 10 文章模块
	modules.push(
		{
			id: 'my-wx-operate-5',
			name: '阅读量Top 10',
			type: 'html',
			category: 'ranking',
		},
		{
			id: 'my-wx-operate-6',
			name: '点赞量Top 10',
			type: 'html',
			category: 'ranking',
		},
		{
			id: 'my-wx-operate-7',
			name: '推荐量Top 10',
			type: 'html',
			category: 'ranking',
		},
		{
			id: 'my-wx-operate-8',
			name: '分享量Top 10',
			type: 'html',
			category: 'ranking',
		}
	);

	return modules;
});

// 暴露模块ID列表供父组件使用
defineExpose({
	moduleIds,
});
</script>

<style scoped>
.wx-container {
	width: 100%;
	min-height: 100vh;
	position: relative;
	background-color: #fff;
}
.wx-top {
	width: 100%;
	background: url('/@/assets/media/report_top.jpg') no-repeat center center;
	background-size: cover;
	padding: 30px;
	overflow: hidden;
	height: 200px;
}
.top-title {
	font-family: 'Microsoft YaHei';
	font-size: 36px;
	font-weight: bold;
	color: #000;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.wx-title-wrapper {
	width: 100%;
	/* padding: 10px; */
}

.wx-title1 {
	font-size: 30px;
	width: 20%;
	margin: 0 auto 10px;
	text-align: center;
	border-bottom: #008ccc 2px solid;
	padding-bottom: 10px;
	line-height: 1.5;
}

.wx-title2 {
	font-size: 24px;
	color: #008ccc;
	font-weight: bold;
	margin: 0px auto 10px;
	text-align: center;
	line-height: 1.5;
}
.wx-title3 {
	font-size: 24px;
	margin: 0 auto;
	text-align: center;
	color: #008ccc;
	font-weight: bold;
	margin-bottom: 15px;
	margin-top: 15px;
	display: block;
	width: 100%;
}

.wx-title4 {
	font-size: 18px;
	margin: 0 auto;
	text-align: center;
	font-weight: bold;
}

.wx-content-wrapper {
	position: relative;
	margin: 0 30px 20px;
	padding: 7px 15px;
	border: #9d9c9a 1px solid;
	overflow: visible;
}

.wx-content {
	font-size: 18px;
	line-height: 2;
	padding: 10px 0;
	white-space: inherit !important;
}
.wx-content1 {
	font-size: 20px;
	line-height: 2;
	padding: 10px 0;
	white-space: inherit !important;
}
.wx-table-wrapper {
	margin: 20px 30px;
	overflow-x: auto;
}

.text-bold {
	font-weight: bold;
}

.text-blue {
	color: #00abf9;
}

:deep(.el-loading-spinner) {
	top: 300px;
}
.btn-wrapper {
	position: absolute;
	left: 380px;
	top: 95px;
	z-index: 1000;
}
.el-row {
	display: block;
}

/* 导出PDF时的样式优化 */
#my-wx-operate-5,
#my-wx-operate-6,
#my-wx-operate-7,
#my-wx-operate-8 {
	background-color: #fff;
	/* padding: 20px;
	margin-bottom: 20px; */
	text-rendering: optimizeQuality;
	page-break-before: auto;
}

/* 表格样式优化 */
:deep(.el-table) {
	width: 100% !important;
	table-layout: fixed;
	margin-bottom: 15px;
	border-collapse: separate;
	border-spacing: 0;
}
:deep(.el-table .cell) {
	color: #000;
	font-size: 18px;
}

:deep(.el-table th) {
	font-weight: bold;
	color: #000;
	background-color: #f8f8f9;
	padding: 15px 4px !important;
}

:deep(.el-table td) {
	padding: 15px 4px !important;
}

:deep(.el-table__row) {
	height: auto !important;
}

:deep(.el-table__cell) {
	padding: 15px 4px !important;
	line-height: 1 !important;
	vertical-align: middle !important;
}

:deep(.el-table__header-cell) {
	padding: 15px 0 !important;
	height: auto !important;
	line-height: 1 !important;
}

:deep(.el-table__body),
:deep(.el-table__header) {
	table-layout: fixed !important;
}

.chart-container {
	margin: 20px 30px 40px;
	overflow: visible;
	min-height: 400px;
	position: relative;
	page-break-inside: avoid;
}
* {
	font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

/* AI 分析报告 Markdown 内容样式 */
.markdown-report-content {
	padding: 7px 20px 20px 20px;
	margin: 0 30px 20px;
}

/* 账号统计信息样式 */
.account-stats-container {
	width: 100%;
	background-color: #fff;
	border-bottom: 2px solid #008ccc;
	padding: 20px 0;
	margin-bottom: 20px;
}

.account-stats-content {
	text-align: center;
	font-size: 20px;
	line-height: 1.8;
	color: #333;
}

.tenant-name {
	font-weight: bold;
	color: #008ccc;
	font-size: 20px;
	margin-right: 8px;
}

.stats-item {
	margin: 0 15px;
	color: #555;
}

.count-number {
	font-weight: bold;
	color: #e74c3c;
	font-size: 20px;
}
</style> 
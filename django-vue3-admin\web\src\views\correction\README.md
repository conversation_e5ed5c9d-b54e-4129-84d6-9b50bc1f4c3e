# 校对管理模块

## 核心功能
- 校对任务管理：创建、配置和执行AI校对任务
- 校对目标管理：设定校对范围和目标参数  
- 人工校对结果：展示和处理AI校对结果，支持人工判定
- AI效果分析：量化分析AI校对系统准确性和效果评估
- 导出配置：支持校对结果的多格式数据导出

## 技术实现

### 子模块架构
- article_error_sentence: 人工校对结果管理，基于correction.ts的数据处理和ErrorTable组件展示
- proofread_task: 校对任务管理，通过TaskList和TaskFilter组件实现任务CRUD操作
- proofread_target: 校对目标配置，定义校对范围和参数设置
- ai_accuracy_analysis: AI效果分析，基于ECharts的数据可视化和统计分析
- component: 通用组件，ExportConfigDialog提供导出功能支持

### 核心数据流
- 任务创建流程：proofread_task → proofread_target → article_error_sentence → ai_accuracy_analysis  
- 权限控制：基于correction:*:*的权限体系，支持查看、创建、编辑、删除操作
- 状态管理：统一的加载状态、错误处理和数据刷新机制

### 通用服务集成
- API统一前缀：/api/regulate/* 路径规范
- 认证机制：继承系统RBAC权限控制
- 数据格式：遵循项目标准的ApiResponse<T>响应格式
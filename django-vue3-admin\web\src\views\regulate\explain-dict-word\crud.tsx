import { CrudOptions, AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud'
import * as api from './api'
import { auth } from "/@/utils/authFunction"
import { h } from 'vue'
import { ElTag, ElMessageBox, ElMessage } from 'element-plus'
import { ENUM_TYPE } from '/@/stores/constants/enum'
import { getEnumDatas } from '/@/stores/enum'

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
    // 定义请求方法
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }
        return await api.updateData(form)
    }
    const addRequest = async ({ form }: AddReq) => {
        return await api.createData(form)
    }

    return {
        crudOptions: {
            // 请求配置
            request: {
                pageRequest,
                addRequest,
                editRequest
            },
            // 操作栏配置
            actionbar: {
                buttons: {
                    add: {
                        show: auth('explainWord:Create'),
                    }
                }
            },
            toolbar: {
                show: false,
            },
            // 行操作配置
            rowHandle: {
                width: 150,
                buttons: {
                    view: {
                        show: false,
                        link: true,
                    },
                    edit: {
                        show: auth('explainWord:Update'),
                        link: true,
                    },
                    remove: {
                        link: true,
                        show: auth('explainWord:Delete'),
                        click: ({ row }) => {
                            ElMessageBox.confirm(`确认删除解释词 "${row.correct_word}" 吗？`, '删除确认', {
                                confirmButtonText: '确认',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(async () => {
                                return await api.deleteData(row.id).then(() => {
                                    ElMessage.success('删除成功')
                                    crudExpose.doRefresh()
                                }).catch((err: any) => {
                                    ElMessage.error(err.msg)
                                })
                            }).catch(() => {
                                return Promise.reject('取消删除')
                            })
                        }
                    }
                }
            },
            // 列配置
            columns: {
                id: {
                    title: 'ID',
                    type: 'text',
                    column: {
                        width: 80
                    },
                    
                    form: {
                        show: false
                    },
                    search: { show: true, autoSearchTrigger: false }
                },
                correct_word: {
                    title: '解释词',

                    type: 'text',
                    search: { show: true, autoSearchTrigger: false },
                    column: {
                        minWidth: 250,
                    },
                    form: {
                        col: {
                            span: 24
                        },
                        rules: [{ required: true, message: '请填写同音字或词' }],
                    }
                },
                explanation: {
                    title: '词语解释',
                    type: 'textarea',
                    // search: { show: true, autoSearchTrigger: false },
 
                    column: {
                        minWidth: 430,
                        formatter({ value }) {
                            if (!value) return '';
                            return value.length > 50 ? value.substring(0, 50) + '...' : value;
                        }
                    },
                    form: {
                        col: {
                            span: 24
                        },
                        rules: [{ required: true, message: '请填写词语解释' }],
                        component: {
                            props: {
                                rows: 4,
                                placeholder: '请输入词语解释'
                            }
                        }
                    }
                },
                // status: {
                //     title: '状态',
                //     type: 'dict-select',
                //     dict: dict({
                //         data: getEnumDatas(ENUM_TYPE.ENABLE_STATUS)
                //     }),
                //     column: {
                //         width: 80,
                //         component: {
                //             name: 'fs-dict-select'
                //         }
                //     },
                //     search: { show: true },
                //     form: {
                //         value: 0,
                //         rules: [{ required: true, message: '请选择状态' }]
                //     }
                // },
                creator: {
                    title: '创建者',
                    type: 'text',
                    column: {
                        width: 120
                    },
                    form: {
                        show: false // 创建者由后端自动填充
                    },
                    search: { show: true, autoSearchTrigger: false }
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: 160,
                        align: 'center',
                        formatter({ value }) {
                            if (!value) return '';
                            return new Date(value).toLocaleString('zh-CN');
                        }
                    },
                    form: {
                        show: false
                    },
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'datetimerange',
                                rangeSeparator: '至',
                                startPlaceholder: '开始时间',
                                endPlaceholder: '结束时间',
                                format: 'YYYY-MM-DD HH:mm:ss',
                                valueFormat: 'YYYY-MM-DD HH:mm:ss'
                            }
                        }
                    }
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime',
                    column: {
                        width: 160,
                        align: 'center',
                        formatter({ value }) {
                            if (!value) return '';
                            return new Date(value).toLocaleString('zh-CN');
                        }
                    },
                    form: {
                        show: false
                    }
                }
            }
        }
    }
} 
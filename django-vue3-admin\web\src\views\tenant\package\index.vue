<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding" />
	</fs-page>
</template>

<script lang="ts" setup name="tenantPackage">
import { defineAsyncComponent, onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
const PackagePermissionConfig = defineAsyncComponent(() => import('./components/PackagePermissionConfig.vue'));

const { crudBinding, crudRef, crudExpose } = useFs({
	createCrudOptions,
	context: { PackagePermissionConfig },
});

// 页面打开后获取列表数据
onMounted(async () => {
	// 刷新
	crudExpose.doRefresh();
});
</script> 
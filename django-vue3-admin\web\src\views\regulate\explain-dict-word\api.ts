import { request } from '/@/utils/service';

// 定义解释词词库数据类型接口
export interface ExplainDictWordType {
	id: number;
	words: string; // 同音字或词，用斜杠分隔
	explanation: string; // 词语解释
	status: number; // 状态：0-启用，1-禁用
	creator: string; // 创建者
	create_time?: string;
	update_time?: string;
}

// 定义查询参数接口
export interface QueryParams {
	page?: number;
	limit?: number;
	words?: string;
	explanation?: string;
	status?: number;
	creator?: string;
}

// 定义API前缀
export const apiPrefix = '/api/regulate/word-explains/';

// 获取解释词列表数据
export async function getList(query: QueryParams) {
	return await request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 创建解释词数据
export async function createData(obj: Partial<ExplainDictWordType>) {
	return await request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新解释词数据
export async function updateData(obj: Partial<ExplainDictWordType>) {
	return await request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 删除解释词数据
export async function deleteData(id: number) {
	return await request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}

// 解释词下拉列表数据获取（用于其他模块选择）
export async function getExplainWordSelectList() {
	return await request({
		url: apiPrefix + 'select_list/',
		method: 'get',
	});
}

// 根据词语获取解释词数据
export async function getByWords(words: string) {
	return await request({
		url: apiPrefix + 'get_by_words/',
		method: 'get',
	});
}
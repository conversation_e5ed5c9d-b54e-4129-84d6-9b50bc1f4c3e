<template>
	<div class="app-container">
		<!-- 搜索表单 -->
		<SearchForm
			v-model="listQuery"
			:error-types="errorTypes"
			:user-list="userList"
			:error-word-list="errorWordList"
			:correct-word-list="correctWordList"
			:tenant-list="tenantList"
			@search="handleFilter"
			@reset="resetSearch"
		/>

		<!-- 数据表格 -->
		<ErrorTable
			:list="list"
			:loading="listLoading"
			:table-height="tableHeight"
			@update-judge="handleUpdateJudge"
			@error-preview="handleErrorPreview"
			@update-sensitivity="handleUpdateSensitivity"
			@update-judge-reason="handleUpdateJudgeReason"
		/>

		<!-- 分页 -->
		<Pagination v-model="listQuery" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />

		<!-- 人工校对弹窗 -->
		<JudgeDialog v-model:visible="judgeDialogVisible" :error-info="currentRow" @submit="submitJudge" />

		<!-- 导出配置弹窗 -->
		<ExportConfigDialog
			v-if="exportDialogVisible"
			:load-task="true"
			:load-target="true"
			v-model:visible="exportDialogVisible"
			@success="handleExportSuccess"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineAsyncComponent, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import {
	setWarning,
	getSystemUserMap,
	getArticleErrorSentences,
	getErrorTypes,
	updateJudge,
	updateJudgeReason,
	ErrorSentenceInfo,
	QueryParams,
} from './correction';
import { getCorrectWordSelectList } from '/@/views/regulate/correct-dict-word/api';
import { getErrorWordSelectList } from '/@/views/regulate/error-dict-word/api';
import { getAllTenants } from '/@/views/tenant/api';

// 导入组件
import SearchForm from './components/SearchForm.vue';
import ErrorTable from './components/ErrorTable.vue';
import Pagination from './components/Pagination.vue';
import JudgeDialog from './components/JudgeDialog.vue';
import { auth } from "/@/utils/authFunction"

// const SearchForm = defineAsyncComponent(() => import('/@/views/correction/article_error_sentence/components/SearchForm.vue'));
// const ErrorTable = defineAsyncComponent(() => import('/@/views/correction/article_error_sentence/components/ErrorTable.vue'));
// const Pagination = defineAsyncComponent(() => import('/@/views/correction/article_error_sentence/components/Pagination.vue'));
// const JudgeDialog = defineAsyncComponent(() => import('/@/views/correction/article_error_sentence/components/JudgeDialog.vue'));

// 异步加载导出组件
const ExportConfigDialog = defineAsyncComponent(() => import('/@/views/correction/component/ExportConfigDialog.vue'));

const router = useRouter();
const route = useRoute();

// 错误类型列表
const errorTypes = ref<{ label: string; value: string }[]>([]);
// 表格高度
const tableHeight = ref(window.innerHeight - 300);
// 列表数据
const list = ref<ErrorSentenceInfo[]>([]);
const listLoading = ref(false);
const total = ref(0);

// 查询参数
const listQuery = ref<QueryParams>({
	id: undefined,
	target_id: route.query.target_id ? String(route.query.target_id) : undefined,
	task_id: route.query.task_id ? String(route.query.task_id) : undefined,
	page: 1,
	page_size: 20,
	error_type: '',
	account_name: '',
	article_title: '',
	human_judge: undefined,
	ai_judge: '',
	has_ai_judge: undefined,
	start_time: '',
	end_time: '',
	wrong_word: '',
	judge_user_id: undefined,
	correct_word_id: undefined,
	has_correct_word: 0,
	sensitivity_level: undefined,
	tenant_id: undefined,
});

// 判断对话框
const judgeDialogVisible = ref(false);
const currentRow = ref<ErrorSentenceInfo>({
	id: 0,
	ai_checks: [],
	article_uid: '',
	article_title: '',
	article_url: '',
	sentence: '',
	sentence_start_pos: 0,
	sentence_end_pos: 0,
	error_type: '',
	wrong_word: '',
	correct_word: '',
	human_judge: 0,
	account_name: '',
	judge_user: '',
	create_time: '',
});

// 导出相关
const exportDialogVisible = ref(false);

// 用户列表
const userList = ref<{ id: number; name: string }[]>([]);

// 错误词和正确词列表
const errorWordList = ref<{ id: number; word: string }[]>([]);
const correctWordList = ref<{ id: number; word: string }[]>([]);
// 租户列表
const tenantList = ref<{ id: number; name: string }[]>([]);

// 处理错误预览
const handleErrorPreview = (articleId: string) => {
	// 构造URL
	let baseUrl = window.location.origin;
	const url = window.location.href;

	// 判断是否包含端口号
	const [protocol, domain] = url.split('://');
	const domainParts = domain.split('/')[0]; // 去掉路径部分
	if (domainParts.includes(':')) {
		// 如果包含端口号，则强制使用8000端口
		baseUrl = `${protocol}://${domainParts.split(':')[0]}:8000`;
	} else {
		baseUrl = `${protocol}://${domainParts}`;
	}
	console.log(baseUrl);
	// 构建完整URL
	const previewUrl = `${baseUrl}/api/regulate/article-content-html/error-position-preview?article_id=${articleId}`;

	// 打开新窗口
	window.open(previewUrl, '_blank');
};

// 获取错误类型列表
const getErrorTypesArray = async () => {
	const res = await getErrorTypes();
	errorTypes.value =
		res.data.map((item: string) => ({
			label: item,
			value: item,
		})) || [];
};

// 更新人工判定
const handleUpdateJudge = async ({ id, value }: { id: number; value: number }) => {
	try {
		await updateJudge({
			id: id,
			human_judge: value,
		});
		ElMessage.success('更新成功');
		getList();
	} catch (error) {
		console.error('更新失败', error);
		ElMessage.error('更新失败');
		getList();
	}
};

// 获取数据列表
const getList = async () => {
	listLoading.value = true;
	try {
		const res = await getArticleErrorSentences(listQuery.value);

		// 确保数据是数组格式
		const newList = res.data.results || [];
		const newTotal = res.data.total || 0;

		// 直接更新数据，不清空列表避免DOM重新渲染
		list.value = newList;
		total.value = newTotal;

		console.log(list.value, '纠错列表');
	} catch (error) {
		console.error('获取列表失败', error);
		ElMessage.error('获取列表失败');
		// 出错时也要清空列表
		list.value = [];
		total.value = 0;
	} finally {
		listLoading.value = false;
	}
};

// 搜索
const handleFilter = async () => {
	listQuery.value.page = 1;
	await nextTick();
	getList();
};

// 获取错误词列表
const fetchErrorWordList = async () => {
	try {
		const res = await getErrorWordSelectList();
		errorWordList.value = res.data || [];
	} catch (error) {
		console.error('获取错误词列表失败', error);
	}
};

// 获取正确词列表
const fetchCorrectWordList = async () => {
	try {
		const res = await getCorrectWordSelectList();
		correctWordList.value = res.data || [];
	} catch (error) {
		console.error('获取正确词列表失败', error);
	}
};

// 处理敏感程度更新
const handleUpdateSensitivity = async ({ id, value }: { id: number; value: number }) => {
	try {
		await setWarning({
			id: id,
			sensitivity_level: value,
		});
		ElMessage.success('敏感程度更新成功');
		getList(); // 更新列表数据
	} catch (error) {
		console.error('更新敏感程度失败', error);
		ElMessage.error('更新敏感程度失败');
		getList(); // 即使失败也尝试更新列表，保持数据同步
	}
};

// 更新判定原因
const handleUpdateJudgeReason = async ({ id, judge_reason }: { id: number; judge_reason: string }) => {
	try {
		await updateJudgeReason({ id, judge_reason });
		ElMessage.success('更新判定原因成功');
		getList();
	} catch (error) {
		console.error('更新判定原因失败', error);
		ElMessage.error('更新判定原因失败');
		getList();
	}
};

// 重置搜索
const resetSearch = async () => {
	Object.assign(listQuery.value, {
		id: undefined,
		target_id: route.query.target_id || undefined,
		task_id: route.query.task_id || undefined,
		page: 1,
		page_size: 20,
		error_type: '',
		account_name: '',
		article_title: '',
		human_judge: '',
		start_time: '',
		end_time: '',
		ai_judge: '',
		has_ai_judge: undefined,
		wrong_word: '',
		judge_user_id: undefined,
		error_word_id: undefined,
		correct_word_id: undefined,
		has_correct_word: 0,
		sensitivity_level: undefined,
		tenant_id: undefined,
	});
	await nextTick();
	getList();
};

// 处理分页变化
const handleSizeChange = async (val: number) => {
	listQuery.value.page_size = val;
	listQuery.value.page = 1; // 重置到第一页
	// 清空当前列表，避免DOM冲突
	list.value = [];
	await nextTick();
	getList();
};

const handleCurrentChange = async (val: number) => {
	listQuery.value.page = val;
	// 清空当前列表，避免DOM冲突
	list.value = [];
	await nextTick();
	getList();
};

// 人工校对
const handleJudge = (row: ErrorSentenceInfo) => {
	currentRow.value = row;
	judgeDialogVisible.value = true;
};

// 提交校对结果
const submitJudge = async (data: { id: number; human_judge: number }) => {
	try {
		await updateJudge(data);
		ElMessage.success('人工校对成功');
		getList();
	} catch (error) {
		console.error('人工校对失败', error);
		ElMessage.error('人工校对失败');
	}
};

// 显示导出弹窗
const showExportDialog = () => {
	exportDialogVisible.value = true;
};

// 处理导出成功
const handleExportSuccess = () => {
	ElMessage.success('导出任务已提交');
};

// 处理窗口大小变化
const handleResize = () => {
	tableHeight.value = window.innerHeight - 300;
};

// 获取用户列表
const getUserList = async () => {
	try {
		const users = await getSystemUserMap();
		userList.value = Object.entries(users.data).map(([id, name]) => ({
			id: parseInt(id),
			name: name as string,
		}));
	} catch (error) {
		console.error('获取用户列表失败', error);
	}
};

// 获取租户列表
const getTenantList = async () => {
	if (auth('Tenants:result')) {
		try {
			const res = await getAllTenants();
			tenantList.value = res.data || [];
		} catch (error) {
			console.error('获取租户列表失败', error);
		}
	}
};

// 初始化
onMounted(() => {
	getList();
	getErrorTypesArray();
	getUserList();
	getTenantList();
	// fetchErrorWordList();
	// fetchCorrectWordList();
	window.addEventListener('resize', handleResize);
});

// 组件卸载时移除监听器
onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
.app-container {
	min-height: calc(100vh - 84px);
}
</style>

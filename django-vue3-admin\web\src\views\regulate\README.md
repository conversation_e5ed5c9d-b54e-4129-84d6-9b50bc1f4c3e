# 内容规范管理模块

## 核心功能
- 文章内容分析：展示和分析抓取的文章内容，提供创作元信息和来源标记
- 错误词典管理：维护错误词汇库，支持错误词汇的增删改查操作
- 正确词典管理：维护正确词汇库，提供错误词汇的标准化替换方案
- 解释词典管理：维护词汇解释库，为校对结果提供详细的解释说明
- 内容质量评估：基于词典和规则对文章内容进行质量评估和标准化

## 技术实现

### API接口层 (api.ts)
- getArticleContent(): 获取文章内容列表，支持内容分析和元信息展示
- getErrorDictWords(): 获取错误词典数据，支持模糊搜索和分类筛选
- getCorrectDictWords(): 获取正确词典数据，提供错误词的标准替换方案
- getExplainDictWords(): 获取解释词典数据，为词汇提供详细说明
- updateContent(): 更新内容分析结果，记录质量评估和修改建议

### 组件架构 (Fast-CRUD)
- crud.tsx: Fast-CRUD配置，定义表格列、搜索条件和操作按钮
- 自定义模板: cell_source渲染来源标签，cell_article_title处理标题截断
- 元信息展示: create_meta_info_reference和create_meta_info的格式化展示
- 词典管理: 统一的CRUD操作界面，支持批量导入和导出功能

### 主页面控制 (index.vue)
- 数据管理: useFs()集成Fast-CRUD，实现数据的增删改查操作
- 模板定制: 通过slot自定义单元格渲染，支持复杂数据格式展示
- 交互优化: el-tooltip提供长文本的悬浮显示，提升用户体验
<template>
	<fs-page class="PageWechatOfficialAccount">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #actionbar-left>
				<common-tabs v-model="currentTab" :items="platformItems" @change="handleTabChange" />
			</template>
			<template #actionbar-right>
				<div v-auth="'CrawlArticles:Batch'">
					<fs-button
						v-if="currentTab === 'wechat'"
						style="margin-left: 20px"
						type="primary"
						:disabled="selectedIds.length === 0"
						@click="showBatchCrawlDialog"
					>
						批量抓取数据
					</fs-button>
				</div>
				<div v-auth="'DyCrawlArticles:Batch'">
					<fs-button
						v-if="currentTab === 'douyin'"
						style="margin-left: 20px"
						type="primary"
						:disabled="selectedIds.length === 0"
						@click="showBatchCrawlDialog"
					>
						批量抓取数据
					</fs-button>
				</div>
				<div v-auth="'WbCrawlArticles:Batch'">
					<fs-button
						v-if="currentTab === 'weibo'"
						style="margin-left: 20px"
						type="primary"
						:disabled="selectedIds.length === 0"
						@click="showBatchCrawlDialog"
					>
						批量抓取数据
					</fs-button>
				</div>

				<!-- 导出模板按钮 -->
				<div v-auth="'BatchAccounts:Export'">
					<fs-button style="margin-left: 10px" type="success" @click="exportTemplate" :loading="exportLoading"> 导出模板 </fs-button>
				</div>

				<!-- 导入账号按钮 -->
				<div v-auth="'BatchAccounts:Import'">
					<fs-button style="margin-left: 10px" type="warning" @click="showImportDialog"> 导入账号 </fs-button>
				</div>
			</template>
			<template #cell_logo_url="scope">
				<el-image
					style="display: block; margin: 0 auto"
					:src="scope.row.logo_url"
					:preview-src-list="[scope.row.logo_url]"
					:z-index="99999"
					fit="contain"
					preview-teleported
					:crossorigin="null"
					referrerpolicy="no-referrer"
				/>
			</template>
			<template #cell_avatar_url="scope">
				<el-image
					style="display: block; margin: 0 auto"
					:src="scope.row.avatar_url"
					:preview-src-list="[scope.row.avatar_url]"
					:z-index="99999"
					fit="contain"
					preview-teleported
					:crossorigin="null"
					referrerpolicy="no-referrer"
				/>
			</template>
			<template #cell_avatar_base64="scope">
				<el-image
					style="display: block; margin: 0 auto"
					:src="`data:image/png;base64,${scope.row.avatar_base64}`"
					:preview-src-list="[`data:image/png;base64,${scope.row.avatar_base64}`]"
					:z-index="99999"
					fit="contain"
					preview-teleported
					:crossorigin="null"
					referrerpolicy="no-referrer"
				/>
			</template>
			<template #cell_is_earliest_crawled="scope">
				<el-tag :type="scope.row.is_earliest_crawled === 1 ? 'success' : 'warning'" size="small">
					{{ scope.row.is_earliest_crawled === 1 ? '是' : '否' }}
				</el-tag>
			</template>
			<!-- <template #cell_status="scope">
                <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'" size="small">
                    {{ scope.row.status === 0 ? '正常' : scope.row.status === 1 ? '已禁用' : scope.row.status === 4 ? '公众号已迁移' : '未知' + scope.row.status }}
                </el-tag>
            </template> -->
		</fs-crud>

		<!-- 批量抓取数据对话框 -->
		<el-dialog v-model="batchCrawlDialogVisible" title="批量抓取数据" width="500px" :close-on-click-modal="false" :close-on-press-escape="false">
			<el-form ref="batchCrawlFormRef" :model="batchCrawlForm" :rules="batchCrawlRules" label-width="100px">
				<el-form-item label="抓取时间" prop="dateRange">
					<el-date-picker
						v-model="batchCrawlForm.dateRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD"
						style="width: 100%"
					/>
				</el-form-item>
				<!-- <el-form-item>
					<div class="batch-info">
						<el-icon><InfoFilled /></el-icon>
						<span>将抓取 {{ selectedIds.length }} 个账号的数据</span>
					</div>
				</el-form-item> -->
			</el-form>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeBatchCrawlDialog">取消</el-button>
					<el-button type="primary" :loading="batchCrawlLoading" @click="confirmBatchCrawl"> 确认抓取 </el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 导入账号对话框 -->
		<el-dialog v-model="importDialogVisible" title="批量导入账号" width="500px" :close-on-click-modal="false" :close-on-press-escape="false">
			<el-upload
				ref="uploadRef"
				class="upload-demo"
				:auto-upload="false"
				:limit="1"
				:accept="'.xlsx,.xls'"
				:on-change="handleFileChange"
				:on-exceed="handleExceed"
				drag
			>
				<el-icon class="el-icon--upload"><upload-filled /></el-icon>
				<div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
				<template #tip>
					<div class="el-upload__tip">只能上传Excel文件(.xlsx, .xls)，且不超过10MB</div>
				</template>
			</el-upload>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeImportDialog">取消</el-button>
					<el-button type="primary" :loading="importLoading" @click="confirmImport" :disabled="!selectedFile"> 确认导入 </el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 导入结果展示对话框 -->
		<ImportResultDialog v-model:visible="importResultDialogVisible" :import-result="importResult" @close="handleImportResultClose" />
	</fs-page>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, defineAsyncComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';
import { useRoute, useRouter } from 'vue-router';
import { warningMessage, successMessage, errorMessage } from '/@/utils/message';
import { InfoFilled, UploadFilled } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import * as api from './api';
import download from '/@/utils/download';
import { WECHAT_ACCOUNT_PERMISSIONS } from '/@/stores/constants/platformPermissions';
import { generatePlatformTabsWithPermissions, TabItem } from '/@/utils/platformTabsHelper';
// 动态导入CommonTabs组件
const CommonTabs = defineAsyncComponent(() => import('/@/components/CommonTabs/index.vue'));
// 动态导入ImportResultDialog组件
const ImportResultDialog = defineAsyncComponent(() => import('./ImportResultDialog.vue'));

// 定义批量抓取表单接口
interface BatchCrawlForm {
	dateRange: string[];
}

// 获取路由实例
const route = useRoute();
const router = useRouter();
// 当前选中的Tab，优先使用platform_type参数，其次使用tab_type参数，如果都没有则默认为'wechat'
const currentTab = ref((route.query.platform_type as string) || (route.query.tab_type as string) || 'wechat');
// 定义当前平台类型
const currentPlatformType = ref(currentTab.value);
// 获取路由中的tenant_id
const tenantId = route.query.tenant_id ? Number(route.query.tenant_id) : undefined;
// 平台类型选项数据
const platformItems = ref<TabItem[]>([]);
// 选中的账号ID数组
const selectedIds = ref<number[]>([]);

// 批量抓取对话框相关状态
const batchCrawlDialogVisible = ref(false);
const batchCrawlLoading = ref(false);
const batchCrawlFormRef = ref<FormInstance>();
const batchCrawlForm = reactive<BatchCrawlForm>({
	dateRange: [],
});

// 导入导出相关状态
const importDialogVisible = ref(false);
const importLoading = ref(false);
const exportLoading = ref(false);
const selectedFile = ref<File | null>(null);
const uploadRef = ref();

// 导入结果展示相关状态
const importResultDialogVisible = ref(false);
const importResult = ref<any>({});

// 表单验证规则
const batchCrawlRules: FormRules<BatchCrawlForm> = {
	dateRange: [{ required: true, message: '请选择抓取时间范围', trigger: 'change' }],
};

// 获取平台类型数据
const loadPlatformTypes = () => {
	platformItems.value = generatePlatformTabsWithPermissions(WECHAT_ACCOUNT_PERMISSIONS);
};

// crud组件的ref
const { crudBinding, crudRef, crudExpose } = useFs({
	createCrudOptions,
	context: {
		currentPlatformType,
		tenantId,
		// 传递选择变更回调
		onSelectionChange: (selection: any[]) => {
			selectedIds.value = selection.map((item) => item.id);
			console.log('选中的账号ID:', selectedIds.value);
		},
	},
});

// 显示批量抓取对话框
const showBatchCrawlDialog = () => {
	if (selectedIds.value.length === 0) {
		warningMessage('请选择要抓取的账号');
		return;
	}
	batchCrawlDialogVisible.value = true;
};

// 关闭批量抓取对话框
const closeBatchCrawlDialog = () => {
	batchCrawlDialogVisible.value = false;
	batchCrawlForm.dateRange = [];
	batchCrawlFormRef.value?.resetFields();
};

// 确认批量抓取
const confirmBatchCrawl = async () => {
	if (!batchCrawlFormRef.value) return;

	try {
		// 表单验证
		await batchCrawlFormRef.value.validate();

		batchCrawlLoading.value = true;

		let response;
		switch (currentPlatformType.value) {
			case 'weibo':
				response = await api.batchCrawlWeiboAccountData(selectedIds.value.join(','), batchCrawlForm.dateRange[0], batchCrawlForm.dateRange[1]);
				break;
			case 'douyin':
				response = await api.batchCrawlDouyinAccountData(selectedIds.value.join(','), batchCrawlForm.dateRange[0], batchCrawlForm.dateRange[1]);
				break;
			default:
				response = await api.batchCrawlWechatOfficialAccountData(
					selectedIds.value.join(','),
					batchCrawlForm.dateRange[0],
					batchCrawlForm.dateRange[1]
				);
				break;
		}

		if (response.code === 2000) {
			console.log(response, '批量抓取数据成功');
			successMessage(response.msg || `批量抓取任务已提交，正在后台异步执行`);
			// 接口调用失败后，刷新数据
			crudExpose.doRefresh();
		} else {
			console.log(response, '批量抓取数据失败');
			errorMessage(response.msg || '批量抓取数据失败');
			// 接口调用失败后，刷新数据
			crudExpose.doRefresh();
		}

		// 关闭对话框
		closeBatchCrawlDialog();
	} catch (error: any) {
		console.log(error, '批量抓取数据失败');
	} finally {
		batchCrawlLoading.value = false;
	}
};

// 导出模板
const exportTemplate = async () => {
	try {
		exportLoading.value = true;
		const response = await api.downloadImportTemplate();
		const downloadResult = await download.excel(response.data, '批量导入账号模板.xlsx');
		if (downloadResult.success) {
			successMessage('导出成功');
		}
	} catch (error: any) {
		console.error('导出模板失败:', error);
		errorMessage('导出模板失败');
	} finally {
		exportLoading.value = false;
	}
};

// 显示导入对话框
const showImportDialog = () => {
	importDialogVisible.value = true;
};

// 关闭导入对话框
const closeImportDialog = () => {
	importDialogVisible.value = false;
	selectedFile.value = null;
	uploadRef.value?.clearFiles();
};

// 文件选择变更处理
const handleFileChange = (file: any) => {
	const fileObj = file.raw;
	// 验证文件类型
	const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];

	if (!allowedTypes.includes(fileObj.type)) {
		warningMessage('请选择Excel文件(.xlsx, .xls)');
		uploadRef.value?.clearFiles();
		return;
	}

	// 验证文件大小 (10MB)
	if (fileObj.size > 10 * 1024 * 1024) {
		warningMessage('文件大小不能超过10MB');
		uploadRef.value?.clearFiles();
		return;
	}

	selectedFile.value = fileObj;
};

// 文件数量超限处理
const handleExceed = () => {
	warningMessage('只能选择一个文件');
};

// 确认导入
const confirmImport = async () => {
	if (!selectedFile.value) {
		warningMessage('请选择要导入的文件');
		return;
	}

	try {
		importLoading.value = true;

		const formData = new FormData();
		formData.append('file', selectedFile.value);
		formData.append('platform_type', currentPlatformType.value);

		const response = await api.batchImportAccount(formData);

		if (response.code === 2000) {
			// 处理导入结果
			const resultData = response.data;

			// 检查是否有失败的账号或有警告的账号
			const hasFailedAccounts = resultData && resultData.overall_failed > 0;
			const hasWarningAccounts = resultData && resultData.platforms && Object.values(resultData.platforms).some(
				(platform: any) => platform.success_accounts && platform.success_accounts.some(
					(account: any) => account.warnings && account.warnings.length > 0
				)
			);

			if (hasFailedAccounts || hasWarningAccounts) {
				// 有失败账号或警告账号，显示详细结果
				importResult.value = resultData;
				importResultDialogVisible.value = true;
				if (hasFailedAccounts && hasWarningAccounts) {
					successMessage(`导入完成！成功: ${resultData.overall_success}，失败: ${resultData.overall_failed}，部分成功的账号有警告信息`);
				} else if (hasFailedAccounts) {
					successMessage(`导入完成！成功: ${resultData.overall_success}，失败: ${resultData.overall_failed}`);
				} else {
					successMessage(`导入完成！成功: ${resultData.overall_success}，但部分账号有警告信息`);
				}
			} else {
				// 全部成功且无警告
				successMessage(response.msg || '导入成功');
			}

			// 刷新数据
			crudExpose.doRefresh();
			// 关闭导入对话框
			closeImportDialog();
		} else {
			errorMessage(response.msg || '导入失败');
		}
	} catch (error: any) {
		console.error('导入失败:', error);
		errorMessage('导入失败');
	} finally {
		importLoading.value = false;
	}
};

// 处理导入结果对话框关闭事件
const handleImportResultClose = () => {
	importResultDialogVisible.value = false;
	importResult.value = {};
};

// Tab变更处理函数
const handleTabChange = (value: string) => {
	console.log(value, '切换平台类型');
	// 更新当前选中的Tab
	currentTab.value = value;
	// 更新当前平台类型
	currentPlatformType.value = value;

	// 获取当前的搜索表单数据，保留tenant_id
	const currentSearchForm = crudExpose.getSearchFormData();
	const currentTenantId = currentSearchForm?.tenant_id !== undefined ? currentSearchForm.tenant_id : tenantId;

	// 设置查询参数，确保保留tenant_id
	crudExpose.setSearchFormData({
		form: {
			platform_type: value,
			tenant_id: currentTenantId,
		},
	});

	// 更新路由参数，保留现有参数
	const query = { ...route.query, tab_type: value, tenant_id: currentTenantId };
	router.replace({ query });

	// 刷新数据
	// crudExpose.doRefresh();
};

// 在组件挂载后加载数据
onMounted(() => {
	loadPlatformTypes();
	crudExpose.doRefresh();
});
</script>

<style scoped>
:deep(.fs-page) {
	padding: 0;
}

.batch-info {
	display: flex;
	align-items: center;
	color: #666;
	font-size: 14px;
}

.batch-info .el-icon {
	margin-right: 8px;
	color: #409eff;
}

.dialog-footer {
	text-align: right;
}

.upload-demo {
	width: 100%;
}

.upload-demo :deep(.el-upload) {
	width: 100%;
}

.upload-demo :deep(.el-upload-dragger) {
	width: 100%;
}
</style> 
import { request } from '/@/utils/service'

// 定义数据类型接口
export interface AccountData {
  id: number
  tenant: string              // 客户名称
  phone: string              // 手机号
  function_type: string      // 功能角色
  account_type_display: string // 账号类型
  high_speed_words: number   // 高速校对字数
  remark?: string           // 备注
}

// 定义查询参数接口
export interface QueryParams {
  page?: number
  limit?: number
  tenant?: string
  function_type?: string
}

// API前缀
export const apiPrefix = '/api/aijiaodui/account/'

// 获取账号列表
export async function getList(query: QueryParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
}

// 创建账号
export async function createAccount(data: Partial<AccountData>) {
  return await request({
    url: apiPrefix,
    method: 'post',
    data
  })
}

// 更新账号
export async function updateAccount(data: Partial<AccountData>) {
  return await request({
    url: `${apiPrefix}${data.id}/`,
    method: 'put',
    data
  })
}

// 删除账号
export async function deleteAccount(id: number) {
  return await request({
    url: `${apiPrefix}${id}/`,
    method: 'delete'
  })
}

// 刷新账号校对字数
export async function refreshWordCount(id: number) {
  return await request({
    url: `${apiPrefix}${id}/refresh_word_count/`,
    method: 'post'
  })
}

// 生成校对链接
export async function generateLink({id, show_type = 1101}: {id: number, show_type?: number}) {
  return await request({
    url: `${apiPrefix}${id}/generate_auth_link/`,
    method: 'get',
    params: {
      show_type
    }
  })
}

// 获取客户账号列表
export async function getCustomerAccounts() {
  return await request({
    url: `${apiPrefix}customer_accounts/`,
    method: 'get'
  })
}

// 获取公共账号列表
export async function getPublicAccounts() {
  return await request({
    url: `${apiPrefix}public_accounts/`,
    method: 'get'
  })
} 
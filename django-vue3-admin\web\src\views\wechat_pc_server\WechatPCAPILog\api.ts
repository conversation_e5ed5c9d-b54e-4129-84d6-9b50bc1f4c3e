import { request } from '/@/utils/service'

export interface WechatPCAPILogType {
  id: number
  wechat_pc_account: number
  wechat_pc_account_nick_name: string
  business_type: number
  request_time: string
  request_data: string
  response_data: string
  request_status: number
}

export interface WechatPCAPILogListParams {
  page?: number
  limit?: number
  wechat_pc_account?: number
  business_type?: number
  request_status?: number
}

export const apiPrefix = '/api/wechat_pc_server/wechat_pc_api_log/'

export async function getWechatPCAPILogList(query: WechatPCAPILogListParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
}

export async function createWechatPCAPILog(obj: Partial<WechatPCAPILogType>) {
  return await request({
    url: apiPrefix,
    method: 'post',
    data: obj
  })
}

export async function updateWechatPCAPILog(obj: Partial<WechatPCAPILogType>) {
  return await request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj
  })
}

export async function deleteWechatPCAPILog(id: number) {
  return await request({
    url: apiPrefix + id + '/',
    method: 'delete'
  })
} 
<template>
	<el-dialog :title="dialogTitle" v-model="dialogVisible" width="65%" :before-close="handleClose">
		<el-form ref="formRef" :model="formData" :rules="rules" label-width="110px">
			<el-form-item label="稿件标题" prop="title">
				<el-input v-model="formData.title" placeholder="请输入稿件标题" />
			</el-form-item>

			<el-form-item label="附件" prop="attachment_links">
				<fs-file-uploader
					v-model="formData.attachment_links"
					:upload-tip="'支持上传Word格式文件'"
					:accept="'.doc,.docx'"
					:limit="10"
					:multiple="true"
					:value-type="'object'"
					@success="handleUploadSuccess"
				/>
			</el-form-item>

			<el-form-item label="稿件内容" prop="rich_content">
				<fs-editor-wang v-model="formData.rich_content" :height="300" />
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="loading"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { FormInstance } from 'element-plus';

interface Props {
	visible: boolean;
	editData?: any;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	editData: undefined,
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = computed({
	get: () => props.visible,
	set: (val) => emit('update:visible', val),
});

const dialogTitle = computed(() => (props.editData ? '编辑稿件' : '新增稿件'));

const formRef = ref<FormInstance>();
const loading = ref(false);

const formData = ref({
	title: '',
	attachment_links: [],
	rich_content: '',
});

const rules = {
	title: [{ required: true, message: '请输入稿件标题', trigger: 'blur' }],
};

// 监听编辑数据变化
watch(
	() => props.editData,
	(newVal) => {
		if (newVal) {
			formData.value = {
				...formData.value,
				...newVal,
				attachment_links: (newVal.attachment_links || []).map((item: any) => ({
					url: item.url,
					name: item.name,
					status: item.status || 'success',
				})),
			};
		} else {
			formData.value = {
				title: '',
				attachment_links: [],
				rich_content: '',
			};
		}
	},
	{ immediate: true }
);

// 上传成功回调
const handleUploadSuccess = (res: any) => {
	return {
		url: res.url,
		name: res.name || '稿件',
		status: 'success',
	};
};

// 关闭弹窗
const handleClose = () => {
	dialogVisible.value = false;
	formRef.value?.resetFields();
};

// 重置表单
const resetForm = () => {
	formData.value = {
		title: '',
		attachment_links: [],
		rich_content: '',
	};
	formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
	if (!formRef.value) return;

	await formRef.value.validate(async (valid) => {
		if (valid) {
			loading.value = true;
			try {
				emit('submit', {
					...formData.value,
					id: props.editData?.id,
				});
			} finally {
				loading.value = false;
			}
		}
	});
};

defineExpose({
	resetForm,
});
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}
</style> 
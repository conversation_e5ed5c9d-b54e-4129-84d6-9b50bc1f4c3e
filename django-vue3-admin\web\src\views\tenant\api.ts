import { request } from '/@/utils/service';

export interface TenantInfo {
	id?: number;
	name: string; // 租户名称
	package_id?: number | null; // 所属租户套餐ID，可为空
	contact_name?: string | null; // 联系人姓名，最大50字符
	contact_phone?: string | null; // 联系人电话，最大20字符
	admin_username?: string; // 账号名称
	admin_password?: string; // 密码
	account_quota?: number; // 账号额度
	expire_time?: string; // 过期时间，日期时间格式
	status: boolean; // 租户状态
	type?: string;
	priority?: number;
	stage?: number;
	logo?: string;
	create_time?: string;
	update_time?: string;
	delete_time?: string;
}

export interface TenantListParams {
	page?: number;
	limit?: number;
	status?: boolean;
	name?: string;
	type?: string;
}

export interface SyncResult {
	code: number;
	message: string;
	data: {
		relation_sync: {
			success_count: number;
			fail_count: number;
			error_messages: string[];
		}; // 关系同步
		tenant_sync: {
			success_count: number;
			fail_count: number;
			error_messages: string[];
		}; // 租户同步
	};
}

export interface TenantBindAccountResult {
	code: number;
	message: string;
	data: string[]; // 账号ID列表
}

export interface TenantListResponse {
	code: number;
	message: string;
	data: TenantInfo[];
	page: number;
	limit: number;
	total: number;
	is_next: boolean;
	is_previous: boolean;
}

export const apiPrefix = '/api/hyqm/tenants/';
// /api/hyqm/tenants/select_list/
// 获取所有租户
export async function getAllTenants() {
	return request({
		url: apiPrefix + 'select_list/',
		method: 'get',
	});
}
// 获取租户列表
export async function getList(query: TenantListParams): Promise<TenantListResponse> {
	return request({
		url: apiPrefix,
		method: 'get',
		params: {
			...query,
		},
	});
}

// 同步租户数据 /api/regulate/tenants/sync_tenant_and_relation_from_hyqm/
export async function syncTenants(): Promise<SyncResult> {
	return request({
		url: apiPrefix + 'sync_tenant_and_relation_from_hyqm/',
		method: 'get',
	});
}

// 获取租户详情
export async function getTenantDetail(id: number): Promise<TenantInfo> {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

// 获取租户绑定账号/api/regulate/tenants/bound_wechat_accounts/
export async function getTenantBindAccount(id: number): Promise<TenantBindAccountResult> {
	return request({
		url: apiPrefix + 'bound_wechat_accounts/',
		method: 'get',
		params: {
			tenant_id: id,
		},
	});
}

// 更新租户 /api/regulate/tenants/update_tenant/
export async function updateTenant(form: TenantInfo): Promise<TenantInfo> {
	return request({
		url: apiPrefix + form.id + '/',
		method: 'put',
		data: form,
	});
}
// 同步错误句子到汇远轻媒 /api/hyqm/tenants/error_sentences/
export async function syncErrorSentences(id: number): Promise<SyncResult> {
	return request({
		url: apiPrefix + 'error_sentences/',
		method: 'get',
		params: { tenant_id: id },
	});
}

// 新增租户
export async function addTenant(form: TenantInfo): Promise<TenantInfo> {
	return request({
		url: apiPrefix,
		method: 'post',
		data: form,
	});
}

// 删除租户
export async function deleteTenant(id: number): Promise<any> {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}
// 根据名称获取租户
export async function getTenantByName(name: string) {
	return request({
		url: apiPrefix + 'get_tenant_by_name/',
		method: 'get',
		params: { name },
	});
}

// ==================== 监管账号相关API ====================

// 监管账号接口前缀
export const tenantMediaAccountsPrefix = '/api/hyqm/tenant-media-accounts/';

// 监管账号数据接口
export interface MediaAccountInfo {
	id: number;
	account_id: string;
	account_name: string;
	nickname: string;
	unique_identifier: string;
	platform_type: string;
	operating_type?: string;
	is_selected?: boolean;
	tenant_id?: number;
}

// 查询监管账号列表参数
export interface QueryMediaAccountsParams {
	tenant_id?: number;
	platform_type: string; // 'wechat' | 'douyin' | 'weibo'
	page?: number;
	limit?: number;
}


// 批量切换选择参数
export interface BatchToggleSelectionParams {
	media_account_ids: number[];
	tenant_id: number;
	platform_type: string;
	is_selected: boolean;
}

// 监管账号列表响应
export interface MediaAccountListResponse {
	code: number;
	message: string;
	data: MediaAccountInfo[];
	page: number;
	limit: number;
	total: number;
	is_next: boolean;
	is_previous: boolean;
}

/**
 * 根据租户和平台查询监管账号列表
 * @param params 查询参数
 * @returns 监管账号列表
 */
export async function queryMediaAccountsByTenantAndPlatform(params: QueryMediaAccountsParams): Promise<MediaAccountListResponse> {
	return request({
		url: tenantMediaAccountsPrefix + 'query_by_tenant_and_platform/',
		method: 'get',
		params: {
			tenant_id: params.tenant_id,
			platform_type: params.platform_type,
		},
	});
}

/**
 * 租户绑定/解绑账号（批量切换选择状态）
 * @param params 批量切换选择参数
 * @returns 操作结果
 */
export async function batchToggleAccountSelection(params: BatchToggleSelectionParams): Promise<any> {
	return request({
		url: tenantMediaAccountsPrefix + 'batch_toggle_selection/',
		method: 'post',
		data: {
			media_account_ids: params.media_account_ids,
			tenant_id: params.tenant_id,
			platform_type: params.platform_type,
			is_selected: params.is_selected,
		},
	});
}

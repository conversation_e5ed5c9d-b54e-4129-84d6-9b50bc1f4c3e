import { request } from '/@/utils/service';

// 站点状态枚举
export enum SiteStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
  ARCHIVED = 'archived',
  ERROR = 'error',
  PENDING = 'pending'
}

// 站点数据类型
export interface SiteType {
  id: number;
  name: string;
  domain: string;
  start_url?: string;
  description?: string;
  status: SiteStatus;
  status_display: string;
  parent_site_id?: number;
  created_at: string;
  updated_at: string;
  
  // 扩展字段
  sitemap_count: number;
  initialization_status: {
    is_initialized: boolean;
    status: string;
    status_display: string;
    sitemap_count: number;
    content_classified_count: number;
    total_content_count: number;
    suggestions: string[];
  };
  media_account_status: {
    is_synced: boolean;
    media_account_id?: number;
    sync_time?: string;
  };
}

// 查询参数类型
export interface SiteQuery {
  page?: number;
  page_size?: number;
  search?: string;
  domain?: string;
  status?: SiteStatus | string;
  ordering?: string;
}

// 创建站点数据类型
export interface SiteCreateData {
  name: string;
  domain: string;
  start_url?: string;
  description?: string;
  status?: SiteStatus;
}

// 统一API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 站点统计类型
export interface SiteStatistics {
  total_sites: number;
  filtered_count: number;
  filtered_pages: number;
  status_counts: {
    active: number;
    inactive: number;
    maintenance: number;
    archived: number;
    error: number;
    pending: number;
  };
}

// 批量操作结果类型
export interface BatchOperationResult {
  total_count: number;
  success_count: number;
  failed_count: number;
  results: Array<{
    site_id: number;
    site_name: string;
    success: boolean;
    old_status?: string;
    new_status?: string;
    error?: string;
  }>;
}

// ========== 基础CRUD操作 ==========

// 获取站点列表
export function getSiteList(params?: SiteQuery) {
  return request<ApiResponse<PaginatedResponse<SiteType>>>({
    url: '/api/crawl_all/sites/',
    method: 'get',
    params,
  });
}

// 创建站点
export function createSite(data: SiteCreateData) {
  return request<ApiResponse<SiteType>>({
    url: '/api/crawl_all/sites/',
    method: 'post',
    data,
  });
}

// 更新站点
export function updateSite(id: number, data: Partial<SiteCreateData>) {
  return request<ApiResponse<SiteType>>({
    url: `/api/crawl_all/sites/${id}/`,
    method: 'put',
    data,
  });
}

// 部分更新站点
export function patchSite(id: number, data: Partial<SiteCreateData>) {
  return request<ApiResponse<SiteType>>({
    url: `/api/crawl_all/sites/${id}/`,
    method: 'patch',
    data,
  });
}

// 删除站点
export function deleteSite(id: number) {
  return request<ApiResponse<{ deleted_site_name: string }>>({
    url: `/api/crawl_all/sites/${id}/`,
    method: 'delete',
  });
}

// 获取单个站点详情
export function getSiteDetail(id: number) {
  return request<ApiResponse<SiteType>>({
    url: `/api/crawl_all/sites/${id}/`,
    method: 'get',
  });
}

// ========== 统计信息 ==========

// 获取站点统计信息
export function getSiteStatistics(params?: SiteQuery) {
  return request<ApiResponse<SiteStatistics>>({
    url: '/api/website/sites/statistics/',
    method: 'get',
    params,
  });
}

// ========== 状态管理 ==========

// 更新站点状态
export function updateSiteStatus(id: number, action: string) {
  return request<ApiResponse<{
    site: SiteType;
    old_status: string;
    new_status: string;
  }>>({
    url: `/api/website/sites/${id}/status/`,
    method: 'patch',
    data: { action },
  });
}

// 批量状态管理
export function batchUpdateSiteStatus(site_ids: number[], action: string) {
  return request<ApiResponse<BatchOperationResult>>({
    url: '/api/website/sites/batch_status/',
    method: 'post',
    data: { site_ids, action },
  });
}

// ========== 站点操作 ==========

// 初始化站点
export function initializeSite(id: number) {
  return request<ApiResponse<{
    site_id: number;
    site_name: string;
    statistics: any;
    created: boolean;
  }>>({
    url: `/api/website/sites/${id}/initialize/`,
    method: 'post',
  });
}

// 同步站点到MediaAccount
export function syncSiteToMediaAccount(id: number) {
  return request<ApiResponse<{
    site_id: number;
    site_name: string;
    media_account_id: number;
    already_exists: boolean;
  }>>({
    url: `/api/website/sites/${id}/sync_media_account/`,
    method: 'post',
  });
}

// ========== 批量操作 ==========

// 批量导入站点
export function batchImportSites(urls: string[]) {
  return request<ApiResponse<{
    created_count: number;
    error_count: number;
    created_sites: Array<{
      site_id: number;
      site_name: string;
      domain: string;
      url: string;
    }>;
    errors: string[];
  }>>({
    url: '/api/website/sites/batch_import/',
    method: 'post',
    data: { urls },
  });
}

// ========== 快速操作函数 ==========

// 激活站点
export function activateSite(id: number) {
  return updateSiteStatus(id, 'activate');
}

// 停用站点
export function deactivateSite(id: number) {
  return updateSiteStatus(id, 'deactivate');
}

// 设置维护状态
export function setSiteMaintenance(id: number) {
  return updateSiteStatus(id, 'set_maintenance');
}

// 归档站点
export function archiveSite(id: number) {
  return updateSiteStatus(id, 'archive');
}

// 标记站点异常
export function markSiteError(id: number) {
  return updateSiteStatus(id, 'mark_error');
}

// 设置站点为待审核
export function setSitePending(id: number) {
  return updateSiteStatus(id, 'set_pending');
}
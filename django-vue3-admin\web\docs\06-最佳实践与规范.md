# 最佳实践与规范

## 📂 代码组织规范

### 1. 目录结构
```
src/views/[module]/[feature]/
├── index.vue          # 主页面组件
├── crud.tsx          # CRUD配置  
├── api.ts           # API接口
├── types.ts         # TypeScript类型定义
└── components/      # 特定组件
    ├── [Feature]Form.vue
    ├── [Feature]Dialog.vue
    └── [Feature]Status.vue
```

### 2. 文件命名规范
- **页面组件**: PascalCase (如: `SiteManage.vue`)
- **普通组件**: PascalCase (如: `StatusDropdown.vue`)  
- **API文件**: camelCase (如: `siteApi.ts`)
- **类型文件**: camelCase (如: `siteTypes.ts`)
- **工具函数**: camelCase (如: `formatUtils.ts`)

### 3. 变量命名规范
- **函数名**: camelCase (如: `getSiteList`, `updateSiteStatus`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **权限码**: 模块:资源:操作 (如: `website:site:create`)
- **路由名**: PascalCase (如: `WebsiteSiteManage`)

## 💻 TypeScript规范

### 1. 类型定义
```typescript
// 统一的响应接口
export interface APIResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next: string | null;
  previous: string | null;
}

// 业务数据接口
export interface SiteData {
  id: number;
  name: string;
  domain: string;
  status: 'active' | 'inactive' | 'maintenance' | 'archived' | 'error' | 'pending';
  created_at: string;
  updated_at: string;
}

// 查询参数接口
export interface SiteQuery {
  page?: number;
  page_size?: number;
  search?: string;
  status?: string;
}
```

### 2. 组件类型定义
```typescript
// 组件定义
export default defineComponent({
  props: {
    modelValue: { type: String, required: true },
    options: { type: Array as PropType<SelectOption[]>, required: true },
    disabled: { type: Boolean, default: false },
    size: { type: String as PropType<ComponentSize>, default: 'default' },
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const handleChange = (value: string) => {
      emit('update:modelValue', value);
      emit('change', value);
    };

    return { handleChange };
  },
});
```

## 🛡 错误处理规范

### 1. 统一错误处理
```typescript
// 错误处理工具
import { ElMessage } from 'element-plus';

export const handleApiError = (error: any) => {
  console.error('API Error:', error);
  
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        ElMessage.error(data.message || '请求参数错误');
        break;
      case 401:
        ElMessage.error('未授权访问');
        // 跳转到登录页
        break;
      case 403:
        ElMessage.error('权限不足');
        break;
      case 404:
        ElMessage.error('请求的资源不存在');
        break;
      case 500:
        ElMessage.error('服务器内部错误');
        break;
      default:
        ElMessage.error(data.message || '请求失败');
    }
  } else {
    ElMessage.error(error.message || '网络连接失败');
  }
};

// 在组件中使用
try {
  await api.updateSiteStatus(siteId, data);
  ElMessage.success('操作成功');
} catch (error) {
  handleApiError(error);
}
```

### 2. 表单验证错误处理
```typescript
// 表单提交错误处理
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    
    loading.value = true;
    const result = await api.createSite(form);
    
    ElMessage.success('创建成功');
    emit('success', result);
    
  } catch (error) {
    if (error.errors) {
      // 处理验证错误
      Object.keys(error.errors).forEach(field => {
        ElMessage.error(`${field}: ${error.errors[field][0]}`);
      });
    } else {
      handleApiError(error);
    }
  } finally {
    loading.value = false;
  }
};
```

## 🚀 性能优化

### 1. 懒加载
```typescript
// 路由懒加载
{
  path: '/website/sites',
  component: () => import('/@/views/website/site/index.vue'),
}

// 组件懒加载
const StatusDialog = defineAsyncComponent(() => import('./StatusDialog.vue'));
```

### 2. 虚拟滚动
```vue
<!-- 大列表使用虚拟滚动 -->
<template>
  <el-auto-resizer>
    <template #default="{ height, width }">
      <el-virtual-list
        :height="height"
        :width="width"
        :data="largeDataList"
        :item-size="60"
      >
        <template #default="{ item, index }">
          <ListItem :data="item" :index="index" />
        </template>
      </el-virtual-list>
    </template>
  </el-auto-resizer>
</template>
```

### 3. 缓存策略
```vue
<!-- 页面缓存 -->
<router-view v-slot="{ Component }">
  <keep-alive :include="cachedViews">
    <component :is="Component" :key="$route.fullPath" />
  </keep-alive>
</router-view>
```

### 4. 防抖节流
```typescript
import { debounce, throttle } from 'lodash-es';

// 搜索防抖
const handleSearch = debounce((keyword: string) => {
  // 搜索逻辑
}, 300);

// 滚动节流
const handleScroll = throttle(() => {
  // 滚动处理
}, 100);
```

## 🔧 开发调试

### 1. 开发环境配置
```typescript
// vite.config.ts
export default defineConfig({
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
});

// 使用调试变量
if (__DEV__) {
  console.log('开发环境调试信息');
}
```

### 2. 调试工具
```typescript
// 全局调试助手
if (process.env.NODE_ENV === 'development') {
  // 挂载到 window 对象便于调试
  window.$api = api;
  window.$auth = auth;
  window.$router = router;
  window.$store = store;
}
```

## 📋 代码质量

### 1. ESLint配置
```json
{
  "extends": [
    "@vue/typescript/recommended",
    "@vue/prettier"
  ],
  "rules": {
    "vue/multi-word-component-names": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error"
  }
}
```

### 2. 提交规范
```
feat: 新增功能
fix: 修复bug  
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建/配置相关

示例:
feat: 新增站点状态管理功能
fix: 修复权限验证失败的问题
docs: 更新组件使用文档
```

### 3. 代码Review清单
- [ ] 功能是否正常工作
- [ ] 代码是否符合规范
- [ ] 是否有潜在性能问题
- [ ] 错误处理是否完善
- [ ] 权限控制是否正确
- [ ] 是否需要添加测试
- [ ] 文档是否需要更新
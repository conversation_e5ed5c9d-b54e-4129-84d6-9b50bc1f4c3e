import { request } from '/@/utils/service';

// AI报告接口返回数据结构
export interface AiReport {
	id: number;
	modifier_name: string;
	creator_name: string;
	create_datetime: string;
	update_datetime: string;
	tenant_name: string;
	title: string;
	prompt_template: string;
	model_name: string;
	input_info: string;
	output_report: string;
	edited_report?: string;
	report_type: number;
	report_type_display: string;
	tenant_id: number;
	create_time: string;
	update_time: string;
	delete_time?: string;
}

export interface AiReportResponse {
	code: number;
	message: string;
	data: AiReport;
}

export interface AiReportListParams {
	page?: number;
	limit?: number;
	id?: string;
	ids?: string;
	title?: string;
	tenant_id?: number;
	report_type?: number;
	platform_type?: string;
}

export interface AiReportListResponse {
	code: number;
	message: string;
	data: AiReport[];
	page: number;
	limit: number;
	total: number;
	is_next: boolean;
	is_previous: boolean;
}

export interface AiReportEditRequest {
	edited_report: string;
}

export interface AiReportContentResponse {
	code: number;
	message: string;
	data: {
		report_id: number;
		content: string;
		is_edited?: boolean;
	};
}

export const apiPrefix = '/api/report/ai-reports/';

// 获取AI报告列表
export async function getList(query: AiReportListParams): Promise<AiReportListResponse> {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 获取AI报告详情
export async function getReportDetail(id: string): Promise<AiReportResponse> {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

// 获取报告内容（优先返回编辑后的内容）
export async function getReportContent(id: string): Promise<AiReportContentResponse> {
	return request({
		url: apiPrefix + id + '/get_content/',
		method: 'get',
	});
}

// 获取原始报告内容
export async function getOriginalContent(id: string): Promise<AiReportContentResponse> {
	return request({
		url: apiPrefix + id + '/get_original/',
		method: 'get',
	});
}

// 编辑报告内容
export async function editReportContent(id: string, data: AiReportEditRequest): Promise<AiReportResponse> {
	return request({
		url: apiPrefix + id + '/edit_report/',
		method: 'post',
		data,
	});
}

// 删除AI报告
export async function deleteReport(id: number) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
	});
}

// 创建AI报告
export async function createReport(obj: Partial<AiReport>) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

// 更新AI报告
export async function updateReport(obj: Partial<AiReport>) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

// 使用报表 /api/report/ai-reports/set_operation_report_ai/
export async function useReport(reportId: string, operationReportId: string) {
	return request({
		url: apiPrefix + 'set_operation_report_ai/',
		method: 'post',
		data: {
			ai_report_id: reportId,
			operation_report_id: operationReportId,
		},
	});
}

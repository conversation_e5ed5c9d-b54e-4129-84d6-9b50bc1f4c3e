<template>
	<el-dialog title="投稿" v-model="dialogVisible" width="45%" :before-close="handleClose">
		<el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
			<el-form-item label="投稿部门" prop="dept_id">
				<el-tree
					ref="menuTreeRef"
					:data="deptList"
					:props="defaultTreeProps"
					:default-checked-keys="defaultCheckedKeys"
					@check="handleNodeCheck"
					node-key="id"
					show-checkbox
					check-strictly
					default-expand-all
					class="dept-tree"
				>
				</el-tree>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="loading"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import type { FormInstance, ElTree } from 'element-plus';
import { request } from '/@/utils/service';

interface Props {
	visible: boolean;
	manuscriptData?: any;
	loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	manuscriptData: undefined,
	loading: false,
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = computed({
	get: () => props.visible,
	set: (val) => emit('update:visible', val),
});

const formRef = ref<FormInstance>();
const menuTreeRef = ref<InstanceType<typeof ElTree>>();
const loading = ref(false);
const deptList = ref<any[]>([]);
const defaultCheckedKeys = ref<number[]>([]);

const formData = ref({
	dept_id: '',
	manuscript_id: '',
});

const rules = {
	dept_id: [{ required: true, message: '请选择投稿部门', trigger: 'change' }],
};

const defaultTreeProps = {
	children: 'children',
	label: 'name',
	value: 'id',
};

// 获取部门列表
const getDeptList = async () => {
	try {
		const res = await request({
			url: '/api/system/dept/all_tenant_dept/',
			method: 'get',
		});

		if (res && res.data) {
			deptList.value = buildTreeStructure(Array.isArray(res.data) ? res.data : []);
		}
	} catch (error) {
		console.error('获取部门列表失败', error);
	}
};
// 构建树形结构
const buildTreeStructure = (flatData: any[]): any[] => {
	const nodeMap = new Map();
	const rootNodes: any[] = [];

	// 创建节点映射
	flatData.forEach((item) => {
		const node: any = {
			...item,
			children: [],
		};
		nodeMap.set(item.id, node);
	});

	// 构建父子关系
	flatData.forEach((item) => {
		const node = nodeMap.get(item.id);
		if (item.parent && item.parent !== null && nodeMap.has(item.parent)) {
			const parentNode = nodeMap.get(item.parent);
			if (!parentNode.children) {
				parentNode.children = [];
			}
			parentNode.children.push(node);
		} else {
			// 根节点，设置disabled为true
			node.disabled = true;
			rootNodes.push(node);
		}
	});

	return rootNodes;
};

// 监听数据变化
watch(
	() => props.manuscriptData,
	(newVal) => {
		if (newVal && newVal.id) {
			formData.value.manuscript_id = newVal.id;
		}
	},
	{ immediate: true }
);

// 关闭弹窗
const handleClose = () => {
	dialogVisible.value = false;
	resetForm();
};

// 重置表单
const resetForm = () => {
	if (formRef.value) {
		formRef.value.resetFields();
	}
	formData.value = {
		dept_id: '',
		manuscript_id: '',
	};
	// 重置选中的部门
	if (menuTreeRef.value) {
		menuTreeRef.value.setCheckedKeys([]);
	}
};

// 处理节点选中状态变化
const handleNodeCheck = (node: any) => {
	if (!menuTreeRef.value) return;

	// 获取当前选中的节点
	const checkedNodes = menuTreeRef.value.getCheckedNodes();

	// 如果有选中的节点，取第一个节点的ID作为部门ID
	if (checkedNodes && checkedNodes.length > 0) {
		formData.value.dept_id = checkedNodes[0].id;
	} else {
		formData.value.dept_id = '';
	}
};

// 展开全部
const expandAll = () => {
	const allKeys = getAllNodeKeys(deptList.value);
	allKeys.forEach((key) => {
		const node = menuTreeRef.value?.getNode(key);
		if (node) {
			node.expanded = true;
		}
	});
};

// 收起全部
const collapseAll = () => {
	const allKeys = getAllNodeKeys(deptList.value);
	allKeys.forEach((key) => {
		const node = menuTreeRef.value?.getNode(key);
		if (node) {
			node.expanded = false;
		}
	});
};

// 获取所有节点的key
const getAllNodeKeys = (nodes: any[]): number[] => {
	const keys: number[] = [];
	const collectKeys = (nodeList: any[]) => {
		nodeList.forEach((node) => {
			keys.push(node.id);
			if (node.children) {
				collectKeys(node.children);
			}
		});
	};
	collectKeys(nodes);
	return keys;
};

// 提交表单
const handleSubmit = async () => {
	if (!formRef.value) return;

	await formRef.value.validate(async (valid) => {
		if (valid) {
			emit('submit', formData.value);
		}
	});
};

// 组件挂载时获取部门列表
onMounted(() => {
	getDeptList();
});

// 暴露方法给父组件
defineExpose({
	resetForm,
});
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}

.tree-operations {
	display: flex;
	gap: 8px;
	margin-bottom: 10px;
}

.dept-tree {
	width: 100%;
	max-height: 300px;
	overflow-y: auto;
	border-radius: 4px;
	border: 1px solid #e4e7ed;
	padding: 10px;
}
</style> 
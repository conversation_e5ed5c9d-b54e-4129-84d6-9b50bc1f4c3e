# 开发流程指南

## 🚀 标准开发流程

### 第1步: 创建API接口层
```typescript
// src/api/[module]/[feature].ts
import { request } from '/@/utils/service';

export interface FeatureData {
  id: number;
  name: string;
  // ... 其他字段
}

export interface FeatureQuery {
  search?: string;
  status?: string;
  page?: number;
}

// 获取列表
export function getFeatureList(params?: FeatureQuery) {
  return request<{
    data: FeatureData[];
    total: number;
  }>({
    url: '/api/[module]/[feature]/',
    method: 'get',
    params,
  });
}

// CRUD操作
export function createFeature(data: Partial<FeatureData>) {
  return request({
    url: '/api/[module]/[feature]/',
    method: 'post',
    data,
  });
}

export function updateFeature(id: number, data: Partial<FeatureData>) {
  return request({
    url: `/api/[module]/[feature]/${id}/`,
    method: 'put',
    data,
  });
}

export function deleteFeature(id: number) {
  return request({
    url: `/api/[module]/[feature]/${id}/`,
    method: 'delete',
  });
}
```

### 第2步: 创建CRUD配置
```typescript
// src/views/[module]/[feature]/crud.tsx
import * as api from './api';
import { CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';

export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      request: {
        pageRequest: async (query) => await api.getFeatureList(query),
        addRequest: async ({ form }) => await api.createFeature(form),
        editRequest: async ({ form, row }) => await api.updateFeature(row.id, form),
        delRequest: async ({ row }) => await api.deleteFeature(row.id),
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('[module]:[feature]:create'),
            text: '新增',
          },
        }
      },
      columns: {
        id: {
          title: 'ID',
          type: 'number',
          form: { show: false },
        },
        name: {
          title: '名称',
          type: 'input',
          search: { show: true },
          form: {
            rules: [{ required: true, message: '请输入名称' }],
          },
        },
        // ... 其他列配置
      },
    },
  };
};
```

### 第3步: 创建页面组件
```vue
<!-- src/views/[module]/[feature]/index.vue -->
<template>
  <fs-page>
    <fs-crud 
      ref="crudRef" 
      v-bind="crudBinding"
    />
  </fs-page>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions from './crud';

export default defineComponent({
  name: '[Feature]Manage',
  setup() {
    const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
    
    return {
      crudBinding,
      crudRef,
      crudExpose,
    };
  },
});
</script>
```

### 第4步: 配置路由
```typescript
// 在对应模块的路由文件中添加
{
  path: '/[module]/[feature]',
  name: '[Feature]Manage',
  component: () => import('/@/views/[module]/[feature]/index.vue'),
  meta: {
    title: '[功能名称]',
    icon: 'iconfont icon-[icon]',
    roles: ['admin'],
    isKeepAlive: true,
  },
}
```

### 第5步: 配置权限
在Django后台配置菜单和按钮权限:
- `[module]:[feature]:view` - 查看
- `[module]:[feature]:create` - 创建  
- `[module]:[feature]:update` - 编辑
- `[module]:[feature]:delete` - 删除

## 开发检查清单
- [ ] API接口定义完整
- [ ] TypeScript类型定义
- [ ] CRUD配置正确
- [ ] 权限控制完善
- [ ] 路由配置正确
- [ ] 国际化文本添加
- [ ] 测试功能正常
- [ ] 代码review通过
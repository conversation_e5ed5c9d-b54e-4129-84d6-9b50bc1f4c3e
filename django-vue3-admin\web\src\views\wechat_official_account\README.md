# 微信公众号管理模块

## 核心功能
- 公众号管理：维护微信公众号基础信息，支持多平台账号统一管理
- 文章抓取：批量抓取公众号文章数据，支持历史文章和实时更新
- 数据统计：展示文章抓取统计数据，提供阅读量、点赞数等指标分析
- 快照管理：保存公众号历史快照，追踪账号变化和数据变更
- 媒体账号关联：管理租户与媒体账号的关联关系，支持权限隔离
- 平台切换：支持微信、抖音、微博等多平台数据管理

## 技术实现

### API接口层 (api.ts)
- getWechatAccounts(): 获取微信公众号列表，支持平台筛选和搜索
- batchCrawlArticles(): 批量抓取文章接口，处理异步抓取任务
- getArticleStats(): 获取文章统计数据，提供数据分析支持
- getAccountSnapshots(): 获取账号快照数据，展示历史变化
- getTenantMediaAccounts(): 获取租户媒体账号关联数据

### 组件架构 (components/)
- PlatformTabs: 平台切换组件，currentTab控制显示内容，handleTabChange()处理切换逻辑
- ImportResultDialog: 导入结果弹窗，显示批量操作的执行结果
- BatchCrawlDialog: 批量抓取配置弹窗，showBatchCrawlDialog()触发抓取任务
- 权限控制: v-auth指令控制按钮显示，支持不同平台的权限隔离

### 主页面控制 (index.vue)
- 标签管理: platformItems定义平台选项，支持微信、抖音、微博切换
- 批量操作: selectedIds管理选中项，disabled状态控制批量操作按钮
- 数据联动: 平台切换时自动刷新对应数据，保持数据同步
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
import { useCompute } from '@fast-crud/fast-crud'
const { compute } = useCompute();
const crudOptions = {
    rowHandle:{
        viewAiReport: {
            text: '查看 AI 生成报告',
            show: compute(({ row }) => {
                return row.ai_report_ids != null && row.ai_report_ids.length > 0 && auth('OperateReport:ViewAiReport');
            }),
            link: true,
            type: 'primary',
            click: ({ row }) => {
                // 跳转到 AI 报告列表页面，并传递 ai_report_ids 参数
                router.push({
                    path: '/analysisHelper/aiReport',
                    query: { ids: row.ai_report_ids }
                });
            }
        }
    }







}
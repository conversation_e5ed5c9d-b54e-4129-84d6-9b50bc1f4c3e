<template>
    <fs-page class="PageWechatPCAPILog">
        <fs-crud ref="crudRef" v-bind="crudBinding">
            <template #cell_request_status="scope">
                <el-tag :type="scope.row.request_status === 200 ? 'success' : 'danger'" size="small">
                    {{ scope.row.request_status === 200 ? '成功' : '失败' }}
                </el-tag>
            </template>
        </fs-crud>
    </fs-page>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue'
import { useFs } from '@fast-crud/fast-crud'
import createCrudOptions from './crud'

export default defineComponent({
    name: "WechatPCAPILog",
    setup() {
        // crud组件的ref
        const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions })

        // 在组件挂载后加载数据
        onMounted(() => {
            crudExpose.doRefresh();
        });

        return {
            crudBinding,
            crudRef,
            crudExpose
        }
    }
})
</script>

<style scoped>
:deep(.fs-page) {
    padding: 0;
}
</style>
import { CrudOptions } from '@fast-crud/fast-crud'
import * as api from './api'

export default function ({ crudExpose }: any) {
    const pageRequest = async (query: any) => {
        return await api.getList(query)
    }

    return {
        crudOptions: {
            request: {
                pageRequest
            },
            actionbar: {
                show: false
            },
            search: {
                show: false
            },
            table: {
                show: false
            },
            pagination: {
                show: false
            },            
            toolbar:{
                show: false,
              },
        }
    }
} 
import { request } from '/@/utils/service'

export interface WechatPCAccountType {
  id: number
  wechat_id: string
  nick_name: string
  ip: string
  port: number
  status: number
  last_active_time: string
  created_time: string
  updated_time: string
}

export interface WechatPCAccountListParams {
  page?: number
  limit?: number
  status?: number
  wechat_id?: string
  nick_name?: string
}

export const apiPrefix = '/api/wechat_pc_server/wechat_pc_account/'

export async function getWechatPCAccountList(query: WechatPCAccountListParams) {
  return await request({
    url: apiPrefix,
    method: 'get',
    params: query
  })
}

export async function createWechatPCAccount(obj: Partial<WechatPCAccountType>) {
  return await request({
    url: apiPrefix,
    method: 'post',
    data: obj
  })
}

export async function updateWechatPCAccount(obj: Partial<WechatPCAccountType>) {
  return await request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj
  })
}

export async function deleteWechatPCAccount(id: number) {
  return await request({
    url: apiPrefix + id + '/',
    method: 'delete'
  })
}

export async function updateWechatPCAccountStatus(id: number, status: number) {
  return await request({
    url: apiPrefix + id + '/update_status/',
    method: 'post',
    data: { status }
  })
}

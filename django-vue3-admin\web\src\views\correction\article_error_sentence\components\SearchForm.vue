<template>
	<div class="filter-container">
		<el-form :inline="true" :model="searchForm" class="search-form">
			<el-form-item>
				<el-input v-model="searchForm.id" placeholder="请输入ID" clearable style="width: 220px"></el-input>
			</el-form-item>
			<el-form-item>
				<el-select v-model="searchForm.error_type" placeholder="请选择错误类型" clearable filterable collapse-tags style="width: 220px">
					<el-option v-for="type in errorTypes" :key="type.value" :label="type.label" :value="type.value" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-input v-model="searchForm.account_name" placeholder="请输入账号名称" clearable style="width: 220px"></el-input>
			</el-form-item>
			<el-form-item>
				<el-input v-model="searchForm.article_title" placeholder="请输入文章标题" clearable style="width: 220px"></el-input>
			</el-form-item>
			<el-form-item>
				<el-select v-model="searchForm.human_judge" placeholder="请选择人工判定" clearable style="width: 220px">
					<el-option v-for="item in $getEnumDatas($ENUM.JUDGE_STATUS)" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-select v-model="ai_judge_arr" placeholder="请选择AI判定" clearable multiple style="width: 220px">
					<el-option v-for="item in $getEnumDatas($ENUM.JUDGE_STATUS)" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-select v-model="searchForm.has_ai_judge" placeholder="是否显示AI判定为空" clearable style="width: 220px">
					<el-option :label="'是'" :value="0" />
					<el-option :label="'否'" :value="1" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-select v-model="searchForm.sensitivity_level" placeholder="请选择敏感程度" clearable style="width: 220px">
					<el-option v-for="item in $getEnumDatas($ENUM.SENSITIVITY_LEVEL)" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-input v-model="searchForm.wrong_word" placeholder="请输入错误词" clearable style="width: 220px"></el-input>
			</el-form-item>
			<el-form-item>
				<el-select v-model="searchForm.has_correct_word" placeholder="是否显示正确词命中" clearable style="width: 220px">
					<el-option :label="'否'" :value="0" />
					<el-option :label="'是'" :value="1" />
				</el-select>
			</el-form-item>
			<!-- <el-form-item>
				<el-select v-model="searchForm.error_word_id" placeholder="请选择词库中错误词" clearable filterable style="width: 220px">
					<el-option v-for="word in errorWordList" :key="word.id" :label="word.word" :value="word.id" />
				</el-select>
			</el-form-item> -->
			<!-- <el-form-item>
				<el-select v-model="searchForm.correct_word_id" placeholder="请选择词库中正确词" clearable filterable style="width: 220px">
					<el-option v-for="word in correctWordList" :key="word.id" :label="word.word" :value="word.id" />
				</el-select>
			</el-form-item> -->

			<el-form-item>
				<el-select v-model="searchForm.judge_user_id" placeholder="请选择校对人" clearable filterable style="width: 220px">
					<el-option v-for="user in userList" :key="user.id" :label="user.name" :value="user.id" />
				</el-select>
			</el-form-item>
			<el-form-item v-auth="'Tenants:result'">
				<el-select v-model="searchForm.tenant_id" placeholder="请选择租户" clearable filterable style="width: 220px">
					<el-option v-for="tenant in tenantList" :key="tenant.id" :label="tenant.name" :value="tenant.id" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
				<el-button icon="Refresh" @click="handleReset">重置</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { QueryParams } from '../correction';

defineOptions({
	name: 'SearchForm',
});

interface Props {
	modelValue: QueryParams;
	errorTypes: { label: string; value: string }[];
	userList: { id: number; name: string }[];
	errorWordList: { id: number; word: string }[];
	correctWordList: { id: number; word: string }[];
	tenantList: { id: number; name: string }[];
}

const props = defineProps<Props>();

const ai_judge_arr = ref<string[]>([]);
const emit = defineEmits(['update:modelValue', 'search', 'reset']);

const searchForm = computed({
	get: () => props.modelValue,
	set: (val) => emit('update:modelValue', val),
});

const handleSearch = () => {
	emit('search');
};

watch(
	() => searchForm.value.ai_judge,
	(val) => {
		if (!val) {
			ai_judge_arr.value = [];
		}
	}
);

watch(
	() => ai_judge_arr.value,
	(val) => {
		if (val.length > 0) {
			searchForm.value.ai_judge = val.join(',');
		} else {
			searchForm.value.ai_judge = '';
		}
	},
	{ deep: true }
);

const handleReset = () => {
	emit('reset');
};
</script>

<style lang="scss" scoped>
.filter-container {
	padding: 20px;
	background-color: #f5f7fa;
	border-radius: 4px;

	.search-form {
		display: flex;
		flex-wrap: wrap;
	}
}
</style> 
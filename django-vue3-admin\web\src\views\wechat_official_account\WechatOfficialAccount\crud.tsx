import { asyncCompute, CrudOptions, AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet, dict, compute } from '@fast-crud/fast-crud'
import * as api from './api'
import * as tenantApi from '/@/views/tenant/api'
import { auth } from "/@/utils/authFunction"
import { h, ref, computed } from 'vue'
import { ElMessageBox, ElDatePicker, ElImage } from 'element-plus'
import { successMessage, errorMessage, warningMessage } from '/@/utils/message';
import { ENUM_TYPE } from '/@/stores/constants/enum';
import { getEnumDatas } from '/@/stores/enum';
import { useRoute } from 'vue-router'
import router from '/@/router/index';


export default function ({ crudExpose, context }: { crudExpose: CrudExpose, context?: any }): CreateCrudOptionsRet {
    const route = useRoute()
    const tenantId = route.query.tenant_id as string

    // 从外部传入或使用默认值
    const currentPlatformType = context?.currentPlatformType || ref('wechat');

    const pageRequest = async (query: any) => {
        // 添加platform_type到查询参数
        query.platform_type = currentPlatformType.value;
        console.log('查询参数:', query);
        return await api.getWechatOfficialAccountList(query)
    }

    // 根据平台类型选择不同的编辑请求方法
    const editRequest = async ({ form, row }: EditReq) => {
        if (row.id) {
            form.id = row.id
        }

        switch (currentPlatformType.value) {
            case 'weibo':
                return await api.updateWeiboAccount(form);
            case 'douyin':
                return await api.updateDouyinAccount(form);
            default:
                return await api.updateWechatOfficialAccount(form);
        }
    }

    // 根据平台类型选择不同的删除请求方法
    const delRequest = async ({ row }: DelReq) => {
        switch (currentPlatformType.value) {
            case 'weibo':
                return await api.deleteWeiboAccount(row.id);
            case 'douyin':
                return await api.deleteDouyinAccount(row.id);
            default:
                return await api.deleteWechatOfficialAccount(row.id);
        }
    }

    // 根据平台类型选择不同的添加请求方法
    const addRequest = async ({ form }: AddReq) => {
        switch (currentPlatformType.value) {
            case 'weibo':
                return await api.createWeiboAccount(form);
            case 'douyin':
                return await api.createDouyinAccount(form);
            default:
                return await api.createWechatOfficialAccount(form);
        }
    }

    // 单个账号抓取数据方法
    const handleCrawlData = async (row: any) => {
        try {
            if (!row?.id) {
                errorMessage('账号信息无效')
                return
            }

            const dateRange = ref('')

            // 自定义弹窗内容
            const content = () => {
                return h('div', { style: 'width: 100%;height:150px' }, [
                    h('div', {
                        style: 'display: flex; align-items: center; margin-bottom: 10px;'
                    }, [
                        h('span', { style: 'margin-right: 10px; white-space: nowrap;' }, '抓取时间：'),
                        h(ElDatePicker, {
                            type: 'daterange',
                            rangeSeparator: '至',
                            startPlaceholder: '开始日期',
                            endPlaceholder: '结束日期',
                            valueFormat: 'YYYY-MM-DD',
                            style: 'width: 300px;',
                            modelValue: dateRange.value,
                            'onUpdate:modelValue': (val: any) => {
                                dateRange.value = val
                            }
                        })
                    ])
                ])
            }

            await ElMessageBox({
                title: '抓取数据',
                message: content,
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                customClass: 'crawl-data-dialog', // 添加自定义类名，用于设置样式
                beforeClose: async (action, instance, done) => {
                    if (action === 'confirm') {
                        if (!dateRange.value || !Array.isArray(dateRange.value) || dateRange.value.length !== 2) {
                            errorMessage('请选择时间范围')
                            return
                        }

                        try {
                            done()
                            let response
                            switch (currentPlatformType.value) {
                                case 'weibo':
                                    response = await api.crawlWeiboAccountData(row.id, dateRange.value[0], dateRange.value[1])
                                    break
                                case 'douyin':
                                    response = await api.crawlDouyinAccountData(row.id, dateRange.value[0], dateRange.value[1])
                                    break
                                default:
                                    response = await api.crawlWechatOfficialAccountData(row.id, dateRange.value[0], dateRange.value[1])
                                    break
                            }

                            if (response.data && currentPlatformType.value === 'wechat') {
                                const { account_name, article_count, article_datas_count } = response.data
                                successMessage(`成功抓取了账号 ${account_name} 的 ${article_count} 篇文章的二维数据和 ${article_datas_count} 篇文章的五维数据`)
                            } else {
                                successMessage('抓取任务已提交')
                            }
                        } catch (error: any) {
                            console.log(error, '抓取数据失败');
                        }
                    } else {
                        done()
                    }
                }
            })
        } catch (error: any) {
            if (error !== 'cancel') {
                errorMessage(error.message || '抓取数据失败')
            }
        }
    }
    const selectedIds = ref([]);

    const onSelectionChange = (changed: any) => {
        selectedIds.value = changed.map((item: any) => item.id);
        console.log("selection", changed, selectedIds.value);

        // 如果有外部传入的选择变更回调，则调用它
        if (context?.onSelectionChange) {
            context.onSelectionChange(changed);
        }
    };

    return {
        currentPlatformType, // 将currentPlatformType导出，以便在index.vue中使用
        // 导出抓取相关方法供 index.vue 使用
        handleCrawlData,
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest
            },
            search: {
                initialForm: {
                    tenant_id: tenantId ? Number(tenantId) : undefined
                },
                onReset: () => {
                    // 重置时清空路由参数中的tenant_id
                    const currentQuery = { ...route.query };
                    delete currentQuery.tenant_id;
                    router.replace({ query: currentQuery });
                }
            },
            actionbar: {
                buttons: {
                    add: {
                        show: computed(() => {
                            if (currentPlatformType.value === 'wechat') {
                                return auth('WechatOfficialAccount:Create')
                            } else if (currentPlatformType.value === 'weibo') {
                                return auth('WbAccount:Add')
                            } else if (currentPlatformType.value === 'douyin') {
                                return auth('DyAccount:Add')
                            } else {
                                return false
                            }
                        })
                    }
                }
            },
            toolbar: {
                show: false,
            },
            rowHandle: {
                width: 280,
                fixed: 'right',
                buttons: {
                    view: {
                        show: false
                    },
                    edit: {
                        text: '编辑',
                        type: 'primary',
                        link: true,
                        show: computed(() => {
                            if (currentPlatformType.value === 'wechat') {
                                return auth('WechatOfficialAccount:Update')
                            } else if (currentPlatformType.value === 'weibo') {
                                return auth('WbAccount:Update')
                            } else if (currentPlatformType.value === 'douyin') {
                                return auth('DyAccount:Update')
                            } else {
                                return false
                            }
                        })
                    },
                    remove: {
                        text: '删除',
                        type: 'primary',
                        link: true,
                        show: computed(() => {
                            if (currentPlatformType.value === 'wechat') {
                                return auth('WechatOfficialAccount:Delete')
                            } else if (currentPlatformType.value === 'weibo') {
                                return auth('WbAccount:Delete')
                            } else if (currentPlatformType.value === 'douyin') {
                                return auth('DyAccount:Delete')
                            } else {
                                return false
                            }
                        })
                    },
                    viewArticles: {
                        text: '查看账号文章',
                        type: 'primary',
                        link: true,
                        click: async ({ row }) => {
                            router.push({
                                path: '/wechat_official_account/article',
                                query: {
                                    id: row.id,
                                    unique_id: row.unique_id,
                                    platform_type: currentPlatformType.value
                                },
                            });
                            // }
                        },
                        show: auth('ShowArticles:Button')
                    },
                    crawlData: {
                        text: '抓取数据',
                        type: 'primary',
                        link: true,
                        show: computed(() => {
                            if (currentPlatformType.value === 'wechat') {
                                return auth('Articles:Crawl')
                            } else if (currentPlatformType.value === 'weibo') {
                                return auth('WbArticles:Crawl')
                            } else if (currentPlatformType.value === 'douyin') {
                                return auth('DyArticles:Crawl')
                            } else {
                                return false
                            }
                        }),
                        click: ({ row }) => {
                            handleCrawlData(row)
                        }
                    }
                }
            },
            table: {
                rowKey: "id", //设置你的主键id， 默认rowKey=id
                onSelectionChange
            },
            columns: {
                $checked: {
                    title: "选择",
                    form: { show: false },
                    column: {
                        type: "selection",
                        align: "center",
                        width: "55px",
                    }
                },
                id: {
                    title: '账号ID',
                    type: 'text',
                    search: { show: true },
                    form: { show: false }
                },
                nick_name: {
                    title: '公众号昵称',
                    type: 'text',
                    column: {
                        width: 150,
                        show: computed(() => currentPlatformType.value === 'wechat'),
                    },
                    search: {
                        show: computed(() => currentPlatformType.value === 'wechat'),
                    },
                    form: {
                        rules: [{ required: true, message: '账号名称必填' }],
                        show: computed(() => currentPlatformType.value === 'wechat'),
                    }
                },
                nickname: {
                    title: '账号名称',
                    type: 'text',
                    column: {
                        width: 150,
                        show: computed(() => currentPlatformType.value === 'weibo' || currentPlatformType.value === 'douyin'),
                    },
                    search: {
                        show: computed(() => currentPlatformType.value === 'weibo' || currentPlatformType.value === 'douyin'),
                    },
                    form: {
                        rules: [{ required: true, message: '账号名称必填' }],
                        show: computed(() => currentPlatformType.value === 'weibo' || currentPlatformType.value === 'douyin'),
                    }
                },
                wechat_id: {
                    title: '微信ID',
                    type: 'text',
                    form: {
                        rules: [{ required: true, message: '微信ID必填' }],
                        show: computed(() => currentPlatformType.value === 'wechat')
                    },
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                wechat_original_id: {
                    title: '原始ID',
                    type: 'text',
                    search: { show: false },
                    form: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    },
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                unique_id: {
                    title: '唯一标识码',
                    type: 'text',
                    column: {
                        width: 100,
                    }

                },
                logo_url: {
                    title: '头像',
                    type: 'text',
                    column: {
                        width: 100,
                        show: computed(() => currentPlatformType.value === 'wechat')
                    },
                    search: { show: false },
                    form: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                avatar_url: {
                    title: '头像',
                    type: 'text',
                    column: {
                        width: 100,
                        show: computed(() => currentPlatformType.value === 'douyin')
                    },
                    search: { show: false },
                    form: {
                        show: computed(() => currentPlatformType.value === 'douyin')
                    }
                },
                avatar_base64: {
                    title: '头像',
                    type: 'text',
                    column: {
                        width: 100,
                        show: computed(() => currentPlatformType.value === 'weibo')
                    },
                    search: { show: false },
                    form: {
                        show: false
                    }
                },
                original_article_count: {
                    title: '原创文章数',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    },
                    search: { show: false },
                    form: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    }
                },
                crawl_priority: {
                    title: '抓取优先级',
                    type: 'number'
                },
                tenant_priority: {
                    title: '租户优先级',
                    type: 'number',
                    column: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    },
                    search: { show: false },
                    form: {
                        show: computed(() => currentPlatformType.value === 'wechat')
                    },
                },
                status: {
                    title: '状态',
                    type: 'dict-select',
                    search: {
                        show: true,
                        component: {
                            props: {
                                clearable: true,
                            },
                        },
                    },
                    dict: dict({
                        data: getEnumDatas(ENUM_TYPE.ACCOUNT_STATUS)
                    }),
                    column: {
                        minWidth: 100,
                        component: {
                            name: 'fs-dict-switch',
                            activeText: '',
                            inactiveText: '',
                            style: '--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6',
                            onChange: compute((context) => {
                                return () => {
                                    console.log(context.row);
                                    if (context.row.id && (context.row.nick_name !== undefined || context.row.nickname !== undefined)) {
                                        api.updateWechatOfficialAccount(context.row).then((res: APIResponseData) => {
                                            successMessage(res.msg as string);
                                        });
                                    }
                                };
                            }),
                        },
                    },
                    form: {
                        rules: [
                            {
                                required: true,
                                message: '必填项',
                            },
                        ],
                        component: {
                            placeholder: '请选择状态',
                        },
                    },
                    component: { props: { color: 'auto' } },
                },
                tenant_id: {
                    title: '租户',
                    type: 'dict-select',
                    search: {
                        show: true,
                        component: {
                            props: {
                                clearable: true, // 可清除
                                filterable: true, // 可过滤
                                options: asyncCompute({
                                    asyncFn: async () => {
                                        const res = await tenantApi.getAllTenants()
                                        return res.data.map((item: any) => ({
                                            label: item.name,
                                            value: item.id
                                        })) // 转换数据格式
                                    }
                                })
                            }
                        }
                    },
                    column: {
                        minWidth: 120,
                        show: false
                    },
                    form: { show: false }
                },
                earliest_post_time: {
                    title: '抓取最早作品时间',
                    type: 'datetime',
                    form: { show: false }
                },
                create_time: {
                    title: '创建时间',
                    type: 'datetime',
                    form: { show: false }
                },
                update_time: {
                    title: '更新时间',
                    type: 'datetime',
                    form: { show: false }
                }
            }
        }
    }
}
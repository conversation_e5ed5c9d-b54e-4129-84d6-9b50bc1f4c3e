import { CrudOptions, utils, CrudExpose, CreateCrudOptionsRet } from '@fast-crud/fast-crud'
import * as api from './api'
import { h } from 'vue'
import { ElTag } from 'element-plus'

export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
  // 请求配置
  const pageRequest = async (query: any) => {
    return await api.getList(query)
  }

  return {
    crudOptions: {
      request: {
        pageRequest // 仅提供列表请求，只读模式
      },
      // 禁用操作栏
      actionbar: {
        buttons: {
          add: {
            show: false // 隐藏添加按钮
          }
        },
      },
      toolbar:{
        show: false,
      },
      // 禁用行操作按钮
      rowHandle: {
        show: false
      },
      search:{
        autoSearch: false 
      },
      // 表格
      columns: {
        id: {
          title: 'ID',
          type: 'number',
          column: {
            minWidth: 60
          },
          form: {
            show: false
          },
          search: { show: true }
        },
        article_content_id: {
          title: '文章内容ID',
          type: 'text',
          search: { show: true },
          column: {
            minWidth: 80
          }
        },
        account_name: {
          title: '账号名称',
          type: 'text',
          search: { 
            show: true,
          },
          column: {
            width: 120
          }
        },
        article_title:{
            title: '文章标题',
            type: 'text',
            search: { 
              show: true,
            },
            column: {
              minWidth: 170
            }
        },
        source: {
          title: '转载来源',
          type: 'text',
          search: { show: true },
          column: {
            // 使用自定义渲染，如果为空则显示"原创"
            component: {
              render: ({ row }) => {
                return h(
                  'div',
                  {},
                  row.source ? row.source : h(ElTag, { type: 'success', size: 'small' }, () => '原创')
                )
              }
            }
          }
        },
        create_meta_info: {
          title: '创作元信息',
          type: 'text',
          column: {
            minWidth: 280,
            component: {
              render: ({ row }) => {
                if (!row.create_meta_info) return '-'
                try {
                  const info = typeof row.create_meta_info === 'string' 
                    ? JSON.parse(row.create_meta_info) 
                    : row.create_meta_info
                  
                  // 将JSON对象转换为可读性更好的格式
                  const elements = []
                  for (const key in info) {
                    if (Object.prototype.hasOwnProperty.call(info, key)) {
                      elements.push(
                        h(ElTag, { type: 'info', size: 'small', style: 'margin-right: 5px; margin-bottom: 5px; white-space: normal; display: inline-block;' }, 
                          () => `${key}: ${info[key]}`)
                      )
                    }
                  }
                  return h('div', { style: { whiteSpace: 'normal' } }, elements.length ? elements : '-')
                } catch (e) {
                  return row.create_meta_info || '-'
                }
              }
            }
          }
        },
        create_meta_info_reference: {
          title: '创作元信息参考',
          type: 'text',
          column: {
            minWidth: 350,
          }
        },
        issue_time:{
            title: '文章发文时间',
            type: 'datetime',
            search: {
              show: true,
              component: {
                  name: 'el-date-picker',
                  props: {
                      type: 'daterange',
                      valueFormat: 'YYYY-MM-DD'
                  }
              }
          },
            column: {
              width: 130
            }
        },
        create_time: {
          title: '创建时间',
          type: 'datetime',
          search: {
              show: true,
              component: {
                  name: 'el-date-picker',
                  props: {
                      type: 'daterange',
                      valueFormat: 'YYYY-MM-DD'
                  }
              }
          },
          column: {
            width: 130
          }
        },
        update_time: {
          title: '更新时间',
          type: 'datetime',
          column: {
            width: 130
          }
        }
      }
    }
  }
} 
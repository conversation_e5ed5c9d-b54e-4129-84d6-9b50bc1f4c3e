<template>
	<div class="proofread-target-container">
		<!-- 顶部筛选框 -->
		<TargetFilter
			v-model:id="queryParams.id"
			v-model:taskId="queryParams.task_id"
			v-model:wechatName="queryParams.wechat_name"
			v-model:status="queryParams.status"
			:taskOptions="taskList"
			@search="handleSearch"
			@reset="handleReset"
		/>
		<div class="operation-bar">
			<el-button type="primary" icon="Plus" @click="handleAddTarget" v-auth="'correctionTarget:Create'">新增校对目标</el-button>
		</div>
		<!-- 中间内容区 -->
		<div class="content-container">
			<!-- 目标列表 -->
			<TargetList
				:list="targetList"
				:loading="loading"
				:total="total"
				:taskList="taskList"
				:currentPage="queryParams.page"
				:pageSize="queryParams.limit"
				@update:currentPage="handlePageChange"
				@update:pageSize="handleSizeChange"
				@start="handleStart"
				@stop="handleStop"
				@delete="handleDelete"
				@view-errors="handleViewErrors"
			/>
		</div>

		<!-- 新增目标弹窗 -->
		<TaskTargetDialog
			v-model:visible="targetDialogVisible"
			dialog-mode="target"
			:tenants="tenantList"
			:task="taskFromRoute"
			@submit="handleDialogSubmit"
		/>

		<!-- 编辑时间弹窗 -->
		<TargetTimeEditDialog
			v-model:visible="timeEditDialogVisible"
			:taskList="taskList"
			:tenants="tenantList"
			:target="selectedTarget"
			@submit="handleTimeEditSubmit"
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, shallowReactive, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { successMessage, errorMessage } from '/@/utils/message';
import { ElMessageBox } from 'element-plus';
import {
	getTargetList,
	getTaskAll,
	createTargets,
	deleteTarget,
	startTarget,
	stopTarget,
	updateTarget,
	type CorrectionTargetInfo,
	type CorrectionTaskInfo,
	type TargetQueryParams,
	type CreateTargetParams,
} from './api';
import { getAllTenants, type TenantInfo } from '/@/views/tenant/api';

// 导入组件
import TargetFilter from './components/TargetFilter.vue';
import TargetList from './components/TargetList.vue';
import TaskTargetDialog from '../proofread_task/components/TaskTargetDialog.vue';
import { auth } from "/@/utils/authFunction"

const route = useRoute();
const router = useRouter();

// 从路由参数中获取任务ID
const taskIdFromRoute = computed(() => {
	// 优先从路由query中获取
	const taskIdFromQuery = route.query.task_id;
	if (taskIdFromQuery) {
		return Number(taskIdFromQuery);
	}
	// 其次从路由参数中获取
	const taskId = route.query.taskId;
	return taskId ? Number(taskId) : undefined;
});

// 查询参数
const queryParams = shallowReactive<TargetQueryParams>({
	id: undefined,
	page: 1,
	limit: 10,
	task_id: taskIdFromRoute.value || undefined,
	wechat_name: '',
});

// 监听路由参数变化，更新查询条件
watch(
	() => route.query,
	() => {
		if (taskIdFromRoute.value) {
			queryParams.task_id = taskIdFromRoute.value;
			// 重新加载数据
			loadTargetList();
			// 如果已加载任务列表，则更新当前选中的任务
			if (taskList.value.length > 0) {
				updateSelectedTask();
			}
		}
	}
);

// 更新当前选中的任务
const updateSelectedTask = () => {
	if (taskIdFromRoute.value && taskList.value.length > 0) {
		taskFromRoute.value = taskList.value.find((task) => task.id === taskIdFromRoute.value) || null;
	}
};

// 数据列表
const targetList = ref<CorrectionTargetInfo[]>([]);
const taskList = ref<CorrectionTaskInfo[]>([]);
const tenantList = ref<TenantInfo[]>([]);
const total = ref(0);
const loading = ref(false);

// 当前选中的任务（从路由获取）
const taskFromRoute = ref<CorrectionTaskInfo | null>(null);

// 当前选中的目标（用于编辑和删除）
const selectedTarget = ref<CorrectionTargetInfo | null>(null);

// 对话框控制
const targetDialogVisible = ref(false);
const timeEditDialogVisible = ref(false);

// 加载任务列表
const loadTaskList = async () => {
	try {
		const res = await getTaskAll();
		taskList.value = res.data;

		// 更新当前选中的任务
		updateSelectedTask();
	} catch (error) {
		console.error('获取任务列表失败', error);
		errorMessage('获取任务列表失败');
	}
};

// 加载租户列表
const loadTenantList = async () => {
	if (auth('Tenant:SelectList')) {
	try {
		const res = await getAllTenants();
		tenantList.value = res.data;
	} catch (error) {
		console.error('获取租户列表失败', error);
		errorMessage('获取租户列表失败');
	}
}
};

// 加载目标列表
const loadTargetList = async () => {
	loading.value = true;
	try {
		const res = await getTargetList(queryParams);
		targetList.value = res.data;
		total.value = res.total || 0;
	} catch (error) {
		console.error('获取目标列表失败', error);
		errorMessage('获取目标列表失败');
	} finally {
		loading.value = false;
	}
};

// 搜索处理
const handleSearch = () => {
	queryParams.page = 1;
	loadTargetList();
};

// 重置处理
const handleReset = () => {
	queryParams.id = undefined;
	queryParams.task_id = undefined;
	queryParams.wechat_name = '';
	queryParams.status = undefined;
	queryParams.page = 1;
	loadTargetList();
};

// 新增目标处理
const handleAddTarget = () => {
	targetDialogVisible.value = true;
};

// 提交新增目标处理
const handleDialogSubmit = async (data: CreateTargetParams) => {
	try {
		const res = await createTargets(data);
		if (res.code === 2000) {
			successMessage('新增校对目标成功');
			loadTargetList();
		} else {
			errorMessage(`新增失败: ${res.message}`);
		}
	} catch (error) {
		console.error('新增校对目标失败', error);
		errorMessage('新增校对目标失败');
	}
};

// 处理开启目标
const handleStart = async (target: CorrectionTargetInfo) => {
	const confirm = await ElMessageBox.confirm('您确定要开启此校对目标吗？', '确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	});

	if (confirm) {
		try {
			const res = await startTarget(target.id);
			if (res.code === 2000) {
				successMessage('开启校对目标成功');
				loadTargetList();
			} else {
				errorMessage(`开启失败: ${res.message}`);
			}
		} catch (error) {
			console.error('开启校对目标失败', error);
			errorMessage('开启校对目标失败');
		}
	}
};

// 处理暂停目标
const handleStop = async (target: CorrectionTargetInfo) => {
	const confirm = await ElMessageBox.confirm('您确定要暂停此校对目标吗？', '确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	});
	if (confirm) {
		try {
			const res = await stopTarget(target.id);
			if (res.code === 2000) {
				successMessage('暂停校对目标成功');
				loadTargetList();
			} else {
				errorMessage(`暂停失败: ${res.message}`);
			}
		} catch (error) {
			console.error('暂停校对目标失败', error);
			errorMessage('暂停校对目标失败');
		}
	}
};

// 处理编辑时间提交
const handleTimeEditSubmit = async (data: { start_time: string; end_time: string }) => {
	if (!selectedTarget.value) return;

	try {
		const res = await updateTarget(selectedTarget.value.id, data);
		if (res.code === 2000) {
			successMessage('更新时间范围成功');
			loadTargetList();
		} else {
			errorMessage(`更新失败: ${res.message}`);
		}
	} catch (error) {
		console.error('更新时间范围失败', error);
		errorMessage('更新时间范围失败');
	}
};

// 处理删除目标
const handleDelete = (target: CorrectionTargetInfo) => {
	selectedTarget.value = target;
	confirmDelete();
};

// 确认删除处理
const confirmDelete = async () => {
	if (!selectedTarget.value) return;

	// 使用 Element Plus 的 ElMessageBox 进行确认
	const confirm = await ElMessageBox.confirm('您确定要删除该校对目标吗?', '确认删除', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	});

	if (confirm) {
		try {
			const res = await deleteTarget(selectedTarget.value.id);
			if (res.code === 2000) {
				successMessage('删除校对目标成功');
				loadTargetList();
			} else {
				errorMessage(`删除失败: ${res.message}`);
			}
		} catch (error) {
			console.error('删除校对目标失败', error);
			errorMessage('删除校对目标失败');
		}
	}
};

// 处理查看错误
const handleViewErrors = (target: CorrectionTargetInfo) => {
	// 跳转到错误列表页面，并带上目标ID
	router.push({
		path: '/correction/article_error',
		query: {
			target_id: target.id,
		},
	});
};

// 处理页码变化
const handlePageChange = (page: number) => {
	queryParams.page = page;
	loadTargetList();
};

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
	queryParams.limit = size;
	loadTargetList();
};

// 组件挂载时加载数据
onMounted(() => {
	// 三个列表完全并行加载，互不阻塞
	loadTargetList();
	loadTaskList();
	loadTenantList();
});
</script>

<style scoped>
.proofread-target-container {
	padding: 20px;
	height: 100%;
	display: flex;
	flex-direction: column;
}

.content-container {
	flex: 1;
	background-color: #fff;
	border-radius: 4px;
	padding: 20px;
	margin-top: 10px;
}

.operation-bar {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}
</style>
# 校对任务模块

## 核心功能
- 任务创建管理：新增、配置和删除校对任务，支持租户隔离
- 任务状态控制：监控任务执行状态，支持手动启动和停止
- 目标关联：为任务添加校对目标，定义具体的校对范围
- 报告生成：生成任务执行报告，提供效果评估数据
- 多维度筛选：按任务ID、名称、状态、租户等条件筛选任务

## 技术实现

### API接口层 (api.ts)  
- getTasks(): 获取校对任务列表数据，支持分页和多条件筛选
- createTask(): 创建新的校对任务，处理表单验证和数据提交
- deleteTask(): 删除指定任务，级联删除关联的目标数据
- updateTaskStats(): 更新任务统计信息，刷新执行状态
- generateReport(): 生成任务报告，触发后端报告生成流程

### 组件架构 (components/)
- TaskFilter: 筛选组件，handleSearch()和handleReset()处理查询条件
- TaskList: 任务列表组件，集成分页、操作按钮和状态展示
- TaskTargetDialog: 任务配置弹窗，handleDialogSubmit()处理表单提交逻辑  
- DeleteDialog: 删除确认弹窗，confirmDelete()执行删除操作

### 主页面控制 (index.vue)
- 状态管理：loading控制加载状态，queryParams管理筛选和分页参数
- 操作流程：handleAddTask()创建任务，handleAddTarget()添加目标配置
- 数据联动：handleViewTargets()跳转目标页面，handleViewErrors()查看错误结果
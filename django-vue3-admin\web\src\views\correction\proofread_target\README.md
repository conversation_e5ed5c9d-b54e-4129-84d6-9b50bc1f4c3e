# 校对目标模块

## 核心功能
- 目标创建管理：为校对任务创建具体的校对目标，指定微信公众号等数据源
- 执行控制：启动和停止目标的校对执行，实时监控执行状态
- 进度跟踪：展示目标执行进度、处理数量和错误统计
- 多维筛选：按目标ID、任务ID、微信名称、执行状态等条件筛选
- 错误查看：直接跳转查看目标产生的错误校对结果

## 技术实现

### API接口层 (api.ts)
- getTargets(): 获取校对目标列表，支持任务关联查询和状态筛选
- createTarget(): 创建校对目标，配置数据源和校对参数
- startTarget(): 启动目标执行，触发后端校对流程
- stopTarget(): 停止目标执行，更新执行状态为停止
- deleteTarget(): 删除校对目标，清理关联数据

### 组件架构 (components/)  
- TargetFilter: 筛选组件，handleSearch()和handleReset()管理查询条件
- TargetList: 目标列表组件，handleStart()和handleStop()控制执行状态
- TaskTargetDialog: 目标配置弹窗，处理表单验证和数据提交逻辑

### 主页面控制 (index.vue)
- 状态管理：loading和targetDialogVisible控制页面状态
- 操作流程：handleAddTarget()创建目标，handleViewErrors()查看错误结果  
- 数据联动：taskFromRoute从路由获取任务信息，实现任务-目标关联
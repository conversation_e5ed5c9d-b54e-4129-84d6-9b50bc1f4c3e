// 定义响应类型接口
interface DownloadResponse {
	success: boolean;
	message?: string;
}

// 创建下载工具类
const download = {
	// 通用响应处理方法
	async handleResponse(response: any, fileName: string, mimeType: string): Promise<DownloadResponse> {
		try {
			console.log('下载处理开始 - 参数:', { fileName, mimeType });
			console.log('响应类型:', response instanceof Blob ? 'Blob' : typeof response);

			// 检查响应类型是否为Blob
			if (response instanceof Blob) {
				// 检查content-type
				const contentType = response.type;
				console.log('响应Content-Type:', contentType);

				// 如果是JSON格式，说明是错误信息
				if (contentType.includes('application/json')) {
					const text = await response.text();
					console.log('JSON响应内容:', text);
					const errorData = JSON.parse(text);
					return {
						success: false,
						message: errorData.message || errorData.msg || '下载失败',
					};
				}

				// Excel文件可能有多种MIME类型
				if (mimeType === 'application/vnd.ms-excel') {
					const validExcelTypes = [
						'application/vnd.ms-excel', // .xls
						'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
						'application/octet-stream', // 通用二进制流
					];

					console.log(
						'检查Excel类型:',
						validExcelTypes.some((type) => contentType.includes(type))
					);
					if (validExcelTypes.some((type) => contentType.includes(type))) {
						this.download0(response, fileName, contentType);
						return {
							success: true,
						};
					}
				} else if (mimeType === 'application/msword') {
					// Word文件可能有多种MIME类型
					const validWordTypes = [
						'application/msword', // .doc
						'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
						'application/octet-stream', // 通用二进制流
					];

					console.log(
						'检查Word类型:',
						validWordTypes.some((type) => contentType.includes(type))
					);
					if (validWordTypes.some((type) => contentType.includes(type))) {
						console.log('Word类型匹配成功，开始下载');
						this.download0(response, fileName, contentType);
						return {
							success: true,
						};
					} else {
						console.log('Word类型不匹配, 实际类型:', contentType, '期望类型中的一种:', validWordTypes);
					}
				} else if (contentType.includes(mimeType)) {
					// 其他类型文件按原来的逻辑处理
					console.log('其他类型匹配成功');
					this.download0(response, fileName, mimeType);
					return {
						success: true,
					};
				}

				console.log('未知的文件类型, 实际类型:', contentType, '期望类型:', mimeType);
				return {
					success: false,
					message: '下载失败：未知的文件类型',
				};
			}

			// 处理普通JSON响应
			if (typeof response === 'object') {
				console.log('对象响应:', response);
				if (response.code === 4004 || response.code !== 2000) {
					return {
						success: false,
						message: response.message || '下载失败',
					};
				}
			}

			console.log('无效的响应格式:', response);
			return {
				success: false,
				message: '下载失败：无效的响应格式',
			};
		} catch (error: any) {
			console.error('下载处理错误', error);
			return {
				success: false,
				message: error.message || '下载失败：未知错误',
			};
		}
	},

	// 下载 Excel 方法
	async excel(response: any, fileName: string): Promise<DownloadResponse> {
		console.log('调用Excel下载方法');
		return this.handleResponse(response, fileName, 'application/vnd.ms-excel');
	},

	// 下载 Word 方法
	async word(response: any, fileName: string): Promise<DownloadResponse> {
		console.log('调用Word下载方法, 响应:', response);
		if (response.data) {
			console.log('响应包含data属性，使用response.data');
			return this.handleResponse(response.data, fileName, 'application/msword');
		}
		return this.handleResponse(response, fileName, 'application/msword');
	},

	// 下载 Zip 方法
	async zip(response: any, fileName: string): Promise<DownloadResponse> {
		console.log('调用Zip下载方法');
		return this.handleResponse(response, fileName, 'application/zip');
	},

	// 下载 Html 方法
	async html(response: any, fileName: string): Promise<DownloadResponse> {
		console.log('调用Html下载方法');
		return this.handleResponse(response, fileName, 'text/html');
	},

	// 下载 Markdown 方法
	async markdown(response: any, fileName: string): Promise<DownloadResponse> {
		console.log('调用Markdown下载方法');
		return this.handleResponse(response, fileName, 'text/markdown');
	},

	// 下载通用方法
	download0(data: BlobPart, fileName: string, mimeType: string): void {
		console.log('执行下载0方法:', { fileName, mimeType });
		const blob = new Blob([data], { type: mimeType });
		window.URL = window.URL || window.webkitURL;
		const href = URL.createObjectURL(blob);
		console.log('创建下载链接:', href);
		const downA = document.createElement('a');
		downA.href = href;
		downA.download = fileName;
		downA.click();
		window.URL.revokeObjectURL(href);
		console.log('下载操作完成');
	},
};

export const installDownload = {
	install: (app: any) => {
		app.config.globalProperties.$download = download;
	},
};

export default download;

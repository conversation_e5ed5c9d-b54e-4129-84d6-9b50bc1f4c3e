# 单选/多选组件使用规范

## 单选按钮组

```typescript
fieldName: {
    title: '字段名称',
    type: 'dict-radio',
    dict: dict({
        data: dictionary('dictionary_key'),
    }),
    form: {
        value: 0, // 默认值
        component: {
            span: 12,
        },
    },
}
```

## 开关组件

```typescript
is_active: {
    title: '状态',
    search: {
        show: true,
    },
    type: 'dict-radio',
    column: {
        component: {
            name: 'fs-dict-switch',
            activeText: '',
            inactiveText: '',
            style: '--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6',
            onChange: compute((context) => {
                return () => {
                    api.UpdateObj(context.row).then((res: APIResponseData) => {
                        successMessage(res.msg as string);
                    });
                };
            }),
        },
    },
    dict: dict({
        data: dictionary('button_status_bool'),
    }),
}
```

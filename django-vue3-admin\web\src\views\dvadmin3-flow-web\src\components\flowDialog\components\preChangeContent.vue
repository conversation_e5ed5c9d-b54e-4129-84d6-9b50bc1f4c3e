<script setup lang="ts">
import { computed, ref } from 'vue';
import { getTemplateById } from '../../../flowSubmitted/templates/index';
import type { FlowTemplate, FlowField } from '../../../flowSubmitted/templates/index';

const props = defineProps({
	modelValue: Object,
});

const objData = computed(() => {
	console.log('原始数据:', props.modelValue);
	console.log('是否为三审三校模板:', props.modelValue?.model_type === 'three_review_template');
	return props.modelValue;
});

// 获取流程模板定义
const template = computed((): FlowTemplate | null => {
	console.log('当前流程类型:', objData.value?.model_type);
	if (!objData.value?.model_type) return null;
	const tmpl = getTemplateById(objData.value.model_type) || null;
	console.log('获取到的模板:', tmpl);
	return tmpl;
});

// 获取字段的中文标签
const getFieldLabel = (fieldKey: string): string => {
	if (!template.value) return fieldKey;

	const field = template.value.fields.find((f: FlowField) => f.key === fieldKey);
	return field?.label || fieldKey;
};

// 格式化字段值显示
const formatFieldValue = (fieldKey: string, value: any): string => {
	if (!value && value !== 0) return '-';

	if (!template.value) return String(value);

	const field = template.value.fields.find((f: FlowField) => f.key === fieldKey);
	if (!field) return String(value);

	// 如果是选择类型，显示选项的标签
	if (field.type === 'select' && field.options) {
		const option = field.options.find((opt) => opt.value === value);
		return option?.label || String(value);
	}

	// 日期格式化
	if (field.type === 'date' || field.type === 'datetime') {
		try {
			const date = new Date(value);
			if (field.type === 'datetime') {
				return date.toLocaleString('zh-CN');
			} else {
				return date.toLocaleDateString('zh-CN');
			}
		} catch {
			return String(value);
		}
	}

	// 数字格式化
	if (field.type === 'number') {
		return Number(value).toLocaleString('zh-CN');
	}

	return String(value);
};

// 跳转到稿件详情页面
const handleViewManuscript = () => {
	if (!objData.value?.id) return;

	// 直接传递稿件ID
	const manuscriptId = objData.value.id;
	// 在新窗口中打开稿件详情页面
	window.open(`#/manuscript-detail?id=${manuscriptId}`, '_blank');
};

// 判断是否为三审三校流程
const isThreeReviewTemplate = computed(() => {
	const result = objData.value?.model_type === 'three_review_template';
	console.log('isThreeReviewTemplate计算结果:', result);
	return result;
});
</script>

<template>
	<div>
		<div class="operation-title">
			<div>流程类型:</div>
			<div v-if="template" class="template-info">
				<el-tag type="info" size="large">{{ template.name }}</el-tag>
				<div class="template-description">{{ template.description }}</div>
			</div>
		</div>

		<!-- 显示模板信息 -->

		<el-descriptions title="数据信息" border :column="2">
			<!-- 三审三校流程特殊处理 -->
			<template v-if="isThreeReviewTemplate">
				<el-descriptions-item label="稿件ID" :span="2">
					{{ objData?.id || '-' }}
				</el-descriptions-item>
				<el-descriptions-item label="稿件标题" :span="2">
					{{ objData?.title || '-' }}
				</el-descriptions-item>
				<el-descriptions-item label="操作" :span="2">
					<el-button type="primary" @click="handleViewManuscript" :disabled="!objData?.id">查看稿件详情</el-button>
				</el-descriptions-item>
			</template>

			<!-- 其他流程类型的通用处理 -->
			<template v-else>
				<el-descriptions-item v-for="(value, key) in objData?.form_data" :key="key" :label="getFieldLabel(String(key))" :span="2">
					{{ formatFieldValue(String(key), value) }}
				</el-descriptions-item>
			</template>
		</el-descriptions>
	</div>
</template>

<style scoped lang="scss">
.operation-title {
	font-size: 1.2em;
	font-weight: bold;
	margin-bottom: 20px;
}

.template-info {
	margin: 10px 0;
	display: flex;
	align-items: center;
	gap: 12px;

	.template-description {
		font-size: 0.9em;
		color: #666;
	}
}
</style>
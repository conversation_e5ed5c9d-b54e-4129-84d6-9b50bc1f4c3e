<template>
	<el-dialog v-model="visible" title="选择导出模块" width="50%" :close-on-click-modal="false" @close="handleClose">
		<div class="module-selector">
			<div class="selector-header">
				<el-button size="small" @click="selectAll">全选</el-button>
				<el-button size="small" @click="selectNone">取消全选</el-button>
				<span class="selected-count">已选择：{{ selectedCount }}/{{ totalCount }}</span>
			</div>

			<div class="module-list">
				<div class="module-category" v-for="category in categorizedModules" :key="category.name">
					<h4 class="category-title">{{ category.name }}</h4>
					<div class="module-items">
						<el-checkbox v-for="module in category.modules" :key="module.uniqueId" v-model="moduleStates[module.uniqueId]" class="module-item">
							{{ module.name }}
						</el-checkbox>
					</div>
				</div>
			</div>
		</div>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleConfirm" :disabled="selectedCount === 0"> 确定导出 ({{ selectedCount }}) </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	modules: {
		type: Array,
		default: () => [],
	},
});

const emit = defineEmits(['update:modelValue', 'confirm']);

const visible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value),
});

// 使用响应式对象管理模块选择状态
const moduleStates = ref({});
const processedModules = ref([]);

// 初始化模块数据和状态
const initializeModules = () => {
	const newModuleStates = {};
	const newProcessedModules = [];

	props.modules.forEach((module) => {
		// 生成唯一ID用于状态管理
		const uniqueId = Array.isArray(module.id) ? module.id.join('-') : module.id;

		const moduleData = {
			...module,
			uniqueId,
		};

		newProcessedModules.push(moduleData);
		// 默认全选
		newModuleStates[uniqueId] = module.checked !== false;
	});

	processedModules.value = newProcessedModules;
	moduleStates.value = newModuleStates;
};

// 监听 props.modules 变化
watch(
	() => props.modules,
	(newModules) => {
		if (newModules.length > 0) {
			initializeModules();
		}
	},
	{ immediate: true, deep: true }
);

// 将模块按类别分组
const categorizedModules = computed(() => {
	const categoryMap = {
		本级AI分析报告: [],
		本级账号分析: [],
		同类AI分析报告: [],
		同类账号分析: [],
		下级AI分析报告: [],
		下级账号分析: [],
		其他: [],
	};

	processedModules.value.forEach((module) => {
		// 根据模块ID或名称进行细分分类
		const isMyModule =
			(typeof module.id === 'string' && module.id.startsWith('my-')) || (Array.isArray(module.id) && module.id.some((id) => id.startsWith('my-')));

		const isSameModule = module.name?.includes('同类') || (typeof module.id === 'string' && module.id.includes('same'));

		const isSubModule =
			(typeof module.id === 'string' && module.id.startsWith('sub-')) ||
			(Array.isArray(module.id) && module.id.some((id) => id.startsWith('sub-'))) ||
			module.name?.includes('下级') ||
			module.name?.includes('指数排行榜');

		if (isMyModule) {
			// 本级账号相关
			if (module.name?.includes('AI') || (typeof module.id === 'string' && module.id.includes('ai-report'))) {
				categoryMap['本级AI分析报告'].push(module);
			} else {
				categoryMap['本级账号分析'].push(module);
			}
		} else if (isSameModule) {
			// 同类账号相关
			if (module.name?.includes('AI') || (typeof module.id === 'string' && module.id.includes('ai-report'))) {
				categoryMap['同类AI分析报告'].push(module);
			} else {
				categoryMap['同类账号分析'].push(module);
			}
		} else if (isSubModule) {
			// 下级账号相关
			if (module.name?.includes('AI') || (typeof module.id === 'string' && module.id.includes('ai-report'))) {
				categoryMap['下级AI分析报告'].push(module);
			} else {
				categoryMap['下级账号分析'].push(module);
			}
		} else {
			// 其他模块（如未更新账号列表）
			categoryMap['其他'].push(module);
		}
	});

	// 转换为数组格式，过滤掉空的类别
	return Object.entries(categoryMap)
		.filter(([_, modules]) => modules.length > 0)
		.map(([name, modules]) => ({ name, modules }));
});

// 计算选中模块数量
const selectedCount = computed(() => {
	return Object.values(moduleStates.value).filter(Boolean).length;
});

// 总模块数量
const totalCount = computed(() => {
	return processedModules.value.length;
});

// 已选择的模块
const selectedModules = computed(() => {
	return processedModules.value.filter((module) => moduleStates.value[module.uniqueId]);
});

// 全选
const selectAll = () => {
	processedModules.value.forEach((module) => {
		moduleStates.value[module.uniqueId] = true;
	});
};

// 全不选
const selectNone = () => {
	processedModules.value.forEach((module) => {
		moduleStates.value[module.uniqueId] = false;
	});
};

// 关闭对话框
const handleClose = () => {
	visible.value = false;
};

// 确认选择
const handleConfirm = () => {
	const selected = [];
	selectedModules.value.forEach((module) => {
		// 处理多ID模块
		if (module.multipleIds && Array.isArray(module.id)) {
			module.id.forEach((id) => {
				selected.push({
					moduleId: id,
					type: module.type || 'html',
					pageBreak: true,
				});
			});
		} else {
			// 统一处理所有模块，都转换为 moduleId 字段
			if (module.type === 'markdown') {
				selected.push({
					moduleId: module.id,
					type: module.type,
					markdownContent: module.markdownContent,
				});
			} else {
				selected.push({
					moduleId: module.id,
					type: module.type || 'html',
					pageBreak: true,
				});
			}
		}
	});

	emit('confirm', selected);
	visible.value = false;
};
</script>

<style scoped>
.module-selector {
	max-height: 500px;
	overflow-y: auto;
}

.selector-header {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 20px;
	padding-bottom: 10px;
	border-bottom: 1px solid #ebeef5;
}

.selected-count {
	color: #606266;
	font-size: 14px;
	margin-left: auto;
}

.module-list {
	space-y: 20px;
}

.module-category {
	margin-bottom: 25px;
}

.category-title {
	font-size: 16px;
	font-weight: bold;
	color: #303133;
	margin: 0 0 15px 0;
	padding-bottom: 8px;
	border-bottom: 2px solid #409eff;
}

.module-items {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.module-item {
	margin-left: 20px;
}

:deep(.el-checkbox__label) {
	font-size: 14px;
	color: #606266;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
	background-color: #409eff;
	border-color: #409eff;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}
</style> 
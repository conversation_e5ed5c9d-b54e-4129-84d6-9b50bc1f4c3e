# 文件上传组件使用规范

## 头像上传

```typescript
avatar: {
    title: '头像',
    type: 'avatar-uploader',
    form: {
        component: {
            props: {
                limit: 1,
                uploader: {
                    action: '/api/system/upload/',
                },
            },
        },
    },
    column: {
        width: 100,
        component: {
            name: 'fs-avatar',
        },
    },
}
```

## 文件上传

```typescript
attachment: {
    title: '附件',
    type: 'file-uploader',
    form: {
        component: {
            props: {
                limit: 5,
                uploader: {
                    action: '/api/system/upload/',
                },
                accept: '.jpg,.png,.pdf,.doc,.docx',
            },
        },
    },
    column: {
        width: 200,
        component: {
            name: 'fs-files',
        },
    },
}
```

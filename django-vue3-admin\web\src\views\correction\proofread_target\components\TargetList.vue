<template>
	<div class="target-list-container">
		<el-table :data="list" border v-loading="loading" style="width: 100%" :height="tableHeight" :row-key="getRowKey" :key="tableKey">
			<el-table-column prop="id" label="ID" width="60" />
			<!-- 所属任务 -->
			<el-table-column label="所属任务" min-width="180">
				<template #default="{ row }">
					{{ getTaskName(row.task) }}
				</template>
			</el-table-column>

			<!-- 校对账号 -->
			<el-table-column prop="wechat_name" label="校对账号" width="180" />

			<!-- 时间范围 -->
			<el-table-column label="时间范围" min-width="120">
				<template #default="{ row }">
					{{ formatDateRange(row.start_time, row.end_time) }}
				</template>
			</el-table-column>

			<!-- 目标状态 -->
			<el-table-column label="目标状态" min-width="90">
				<template #default="{ row }">
					<dict-tag :options="$getEnumDatas($ENUM.TARGET_STATUS)" :value="row.status" :color-type="statusTagTypeMap" />
				</template>
			</el-table-column>

			<!-- 总文章数 -->
			<el-table-column prop="total_count" label="文章数" min-width="70" align="center" />

			<!-- 已抓取数 -->
			<el-table-column prop="crawled_count" label="抓取数" min-width="70" align="center" />

			<!-- 已纠错数 -->
			<el-table-column prop="corrected_count" label="纠错数" min-width="70" align="center" />

			<!-- 操作 -->
			<el-table-column label="操作" min-width="200" fixed="right">
				<template #default="{ row }">
					<div class="operation-btns">
						<!-- 开启目标按钮：只有未开始状态才可操作 -->
						<el-button type="primary" link @click="handleStart(row)" v-auth="'correctionTarget:Start'"> 开启目标 </el-button>
						<el-button
							type="primary"
							link
							:disabled="row.status !== 1 && row.status !== 0"
							@click="handleStop(row)"
							v-auth="'correctionTarget:Pause'"
						>
							暂停目标
						</el-button>

						<!-- 查看错误：跳转到校对结果页面 -->
						<el-button type="primary" link @click="handleViewErrors(row)" v-auth="'ShowArticleError:Button'">查看校对结果</el-button>

						<!-- 导出错误 -->
						<el-button type="success" link @click="handleExport(row)" v-auth="'correctionTarget:Export'">导出</el-button>

						<!-- 删除按钮：只有未开始状态才可操作 -->
						<el-button type="danger" link @click="handleDelete(row)" v-auth="'correctionTarget:Delete'"> 删除 </el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-container">
			<el-pagination
				:current-page="currentPage"
				:page-size="pageSize"
				:page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 导出配置弹窗 -->
		<ExportConfigDialog
			v-if="exportDialogVisible"
			v-model:visible="exportDialogVisible"
			:targetName="exportTargetName"
			:targetId="exportTargetId"
			@success="handleExportSuccess"
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import type { CorrectionTargetInfo, CorrectionTaskInfo } from '/@/views/correction/proofread_task/api';
import ExportConfigDialog from '/@/views/correction/component/ExportConfigDialog.vue';
// 定义属性
const props = defineProps({
	list: {
		type: Array as () => CorrectionTargetInfo[],
		required: true,
	},
	loading: {
		type: Boolean,
		default: false,
	},
	total: {
		type: Number,
		default: 0,
	},
	currentPage: {
		type: Number,
		default: 1,
	},
	pageSize: {
		type: Number,
		default: 10,
	},
	taskList: {
		type: Array as () => CorrectionTaskInfo[],
		required: true,
	},
});

const getRowKey = (row: CorrectionTargetInfo) => row.id;
const tableKey = ref(0);
// 定义事件
const emit = defineEmits(['update:currentPage', 'update:pageSize', 'start', 'stop', 'edit', 'delete', 'view-errors']);
const tableHeight = ref(window.innerHeight - 320);
// 导出相关
const exportDialogVisible = ref(false);
const exportTargetName = ref('');
const exportTargetId = ref<number | undefined>(undefined);

// 格式化日期函数
const formatDate = (date: string, format: string = 'YYYY-MM-DD'): string => {
	if (!date) return '';
	const d = new Date(date);
	const year = d.getFullYear();
	const month = String(d.getMonth() + 1).padStart(2, '0');
	const day = String(d.getDate()).padStart(2, '0');

	return format.replace('YYYY', String(year)).replace('MM', month).replace('DD', day);
};

// 格式化日期范围
const formatDateRange = (startTime: string, endTime: string): string => {
	return `${formatDate(startTime)} ~ ${formatDate(endTime)}`;
};

// 获取任务名称
const getTaskName = (taskId: number): string => {
	const task = props.taskList.find((t) => t.id === taskId);
	return task ? task.name : `任务ID: ${taskId}`;
};

// 状态标签类型映射
const statusTagTypeMap = ['info', 'warning', 'primary', 'success', 'danger', 'danger', 'warning', 'warning'];

// 处理分页
const handleSizeChange = (val: number) => {
	emit('update:pageSize', val);
};

const handleCurrentChange = (val: number) => {
	emit('update:currentPage', val);
};

// 处理各种操作
const handleStart = (row: CorrectionTargetInfo) => {
	emit('start', row);
};

const handleStop = (row: CorrectionTargetInfo) => {
	emit('stop', row);
};

const handleDelete = (row: CorrectionTargetInfo) => {
	emit('delete', row);
};

const handleViewErrors = (row: CorrectionTargetInfo) => {
	emit('view-errors', row);
};

// 导出处理
const handleExport = (row: CorrectionTargetInfo) => {
	exportTargetName.value = `${row.wechat_name}`;
	exportTargetId.value = row.id;
	exportDialogVisible.value = true;
};

// 导出成功处理
const handleExportSuccess = () => {
	// 可以添加额外逻辑，如刷新列表等
};

// 处理窗口大小变化
const handleResize = () => {
	tableHeight.value = window.innerHeight - 320;
};

// 添加和移除窗口大小变化监听器
onMounted(() => {
	window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
	window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
::v-deep(.el-table__header th) {
	color: #606266; /* 设置为你想要的颜色 */
}
.target-list-container {
	width: 100%;
}

.operation-btns {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 5px;
}

.pagination-container {
	margin-top: 20px;
}
</style>